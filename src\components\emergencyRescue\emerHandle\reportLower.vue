<template>
  <div id="">
    <div class="background-style">

      <!--搜索区-->
      <div class="search-bar">
        <div style="padding:10px 10px 0 10px;float: left">
          <el-button
            @click="$router.back();"
            type="primary" style="margin-left: 20px">返回</el-button>
        </div>
      </div>

      <!--表格区-->
      <div style="width: 100%;">
        <div style="padding: 20px 10px 20px 10px">
          <el-table
            :data="tableData"
            border
            style="width: 100%">
            <el-table-column
              type="index"
              label="编号"
              width="60"
              align="center"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="eventName"
              label="名称"
              min-width="300"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="eventTime"
              label="事发时间"
              :formatter="formatDateTime"
              width="150"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="incidentUnit"
              label="事发单位"
              width="150"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="eventLocation"
              label="事发地址"
              width="150"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              fixed="right" label="操作"
              label-class-name="header-style"
              align="center" width="100">
              <template slot-scope="scope">
                  <el-button size="mini" type="success" @click.native="itemViewClick(scope.row)">查看</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <!--<div style="margin-top: 10px">
          <el-pagination
            background
            layout="prev, pager, next"
            :current-page="tableData.pageNum"
            :page-size="form.pageSize"
            :total="tableData.total"
            @current-change ="disasterPageChangeHandle">
          </el-pagination>
        </div>-->
      </div>
    </div>
  </div>
</template>
<script>
  export default {
    data() {
      return {
        form : {
          // 公司ID
          reportToUnitId : '',
          // 当前页
          pageCurrent : 1,
          // 页数大小
          pageSize : 5,
        },
        tableData : [],
      }
    },
    mounted(){
      this.init();
    },
    watch:{
      $route(to,from){
        if(to.name === 'demandSurveyPublishLower') {
          this.init();
        }
      }
    },
    methods:{
      // 初始化
      init(){

        let user = this.$tool.getStorage('LOGIN_USER');
        this.form.reportToUnitId = user.companyId;
        // 搜索
        this.searchBtnClickHandle();
      },
      // 格式化时间
      formatDateTime(row, column, cellValue){
        let pro = column.property;
        let num = 10;
        // 年份4位 1999
        if(pro === 'createYear') num = 4;
        let str = this.$tool.formatDateTime(row[pro] || 0);
        return str ? str.substring(0, num) : str;
      },
      // 分页
      disasterPageChangeHandle(page){
        this.form.pageCurrent = page;
        this.searchBtnClickHandle();
      },
      // 搜索按钮
      searchBtnClickHandle(){
        this.$store.dispatch('emgHandleReportFindAction', this.form).then(function(res){
          if(res.success){
            this.tableData = res.data;
          }
        }.bind(this));
      },
      // 查看
      itemViewClick(row){

        this.$router.push({ name : 'emerHandleLookReport', params : {
          id : row.id,
          row : row
        }})

      },

    }
  }
</script>
<style>
</style>
