<template>
    <el-container class="container">
      <el-main>
        <el-form ref="form" label-width="100px" :rules="rules" :model="form">
          <el-row type="flex">
            <el-col :span="8">
              <el-form-item label="姓名" prop="eduUser.username">
                <span v-if="pageStatus === 'view'">{{form.eduUser.username}}</span>
                <el-select
                  v-model="form.eduUser.username"
                  v-if="pageStatus === 'edit'"
                  @change="userChange"certificate-training-add
                  filterable
                  remote
                  reserve-keyword
                  placeholder="请输入关键词"
                  :remote-method="remoteMethod">
                  <el-option
                    v-for="item in assist.staffList"
                    :key="item.userId"
                    :label="item.username"
                    :value="item">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="职务(岗位)" prop="eduUser.duty">
                <span>{{form.eduUser.duty}}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="性别" prop="eduUser.gender">
                <span>{{ form.eduUser.gender ? '男': '女' }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex">
            <el-col :span="8">
              <el-form-item label="证件名称" prop="certificateName">
                <span v-if="pageStatus === 'view'">{{form.certificateName}}</span>
                <el-autocomplete
                  v-model="form.certificateName"
                  v-if="pageStatus === 'edit'"
                  :fetch-suggestions="certificateNameSearch"
                  @select="certificateNameSelect"
                  placeholder="请输入内容">
                  <i class="el-icon-plus el-input__icon"
                     @click="certificateNameAdd"
                    slot="suffix">
                  </i>
                  <template slot-scope="{ item }">
                    <div>{{ item.certName }}</div>
                  </template>
                </el-autocomplete>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="证书编号" prop="certificateId">
                <span v-if="pageStatus === 'view'">{{form.certificateId}}</span>
                <el-input  v-if="pageStatus === 'edit'" v-model="form.certificateId"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="取证时间" prop="getDate">
                <span v-if="pageStatus === 'view'">{{this.$tool.formatDateTime(form.getDate).substring(0,10)}}</span>
                <el-date-picker
                  v-if="pageStatus === 'edit'"
                  v-model="form.getDate"
                  type="date"
                  placeholder="选择日期">
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex">
            <el-col :span="8">
              <el-form-item label="有效期限" prop="expiryDate">
                <span v-if="pageStatus === 'view'">{{this.$tool.formatDateTime(form.expiryDate).substring(0,10)}}</span>
                <el-date-picker
                  v-if="pageStatus === 'edit'"
                  v-model="form.expiryDate"
                  type="date"
                  placeholder="选择日期">
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="发证单位" prop="issuingDepartment">
                <span v-if="pageStatus === 'view'">{{form.issuingDepartment}}</span>
                <el-autocomplete
                  v-model="form.issuingDepartment"
                  v-if="pageStatus === 'edit'"
                  :fetch-suggestions="issuingDepartmentSearch"
                  @select="issuingDepartmentSelect"
                  placeholder="请输入内容">
                  <i class="el-icon-plus el-input__icon"
                     @click="issuingDepartmentAdd"
                     slot="suffix">
                  </i>
                  <template slot-scope="{ item }">
                    <div>{{ item.deptName }}</div>
                  </template>
                </el-autocomplete>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="复训周期" prop="retrainCycle">
                <span v-if="pageStatus === 'view'">{{form.retrainCycle}}</span>
                <el-input  v-if="pageStatus === 'edit'" v-model="form.retrainCycle">
                  <template slot="append">年</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex">
            <el-col :span="24">
              <el-form-item label="备注">
                <span v-if="pageStatus === 'view'">{{form.remark}}</span>
                <el-input
                  v-if="pageStatus === 'edit'"
                  v-model="form.remark" type="textarea" :rows="2"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <!--添加的时候没有，仅编辑-->
          <template v-if="$route.params.id">
            <el-row type="flex">
              <el-col :span="16">
                <el-form-item label="复训记录">
                  <el-table
                    :data="form.eduCertificateRetrains">
                    <el-table-column
                      label="序号"
                      width="100"
                      align="center"
                      type="index">
                    </el-table-column>
                    <el-table-column
                      prop="retrainRecord"
                      :formatter="formatDateTime"
                      label="复训时间"
                      align="center"
                      width="120">
                    </el-table-column>
                    <el-table-column
                      prop="createUser"
                      label="操作人"
                      align="center"
                      width="120">
                    </el-table-column>
                    <el-table-column
                      prop="createDate"
                      :formatter="formatDateTime"
                      label="操作时间"
                      align="center"
                      width="180">
                    </el-table-column>
                    <el-table-column
                      v-if="pageStatus === 'edit'"
                      fixed="right" label="操作"
                      align="center" width="90">
                      <template slot-scope="scope">
                        <el-button size="mini" type="danger" @click="itemDeleteClick(scope.$index)">删除</el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row type="flex" v-if="pageStatus === 'edit'">
            <el-col :span="8">
              <el-form-item label="复训日期">
                <el-date-picker
                  v-model="assist.retrainsDate"
                  type="date"
                  placeholder="选择日期">
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="1">
              <el-form-item>
                <el-button
                  @click="addRetrainsBtnHandle"
                  size="small" type="primary" >新增复训记录</el-button>
              </el-form-item>
            </el-col>
          </el-row>
          </template>
          <el-row type="flex" class="row" justify="center">
            <el-button
              size="small" :span="2" type="primary"
              v-if="pageStatus === 'edit'"
              @click="submitBtnClickHandle">提交</el-button>
            <el-button size="small" :span="2"  @click="$router.back();">返回</el-button>
          </el-row>
        </el-form>
      </el-main>
    </el-container>
</template>

<script>

    export default {
      data(){
        return {
          form : {
            id : '',
            // 用户数据
            eduUser : {
              userId : -1,
              username : '',
              duty : '',
              gender : false,
            },
            // 用户 ID
            userId : -1,
            // 证件名称
            certificateName : '',
            // 证书id
            certificateTypeId : '',
            // 证书编号
            certificateId : '',
            // 取证时间
            getDate : '',
            // 有效期限
            expiryDate : '',
            // 发证单位
            issuingDepartment : '',
            // 发证单位ID
            issuingDepartmentId : '',
            // 复训周期--单位年
            retrainCycle : '',
            // 备注
            remark : '',
            // 复训记录
            eduCertificateRetrains : []
          },
          // 表单辅助字段
          assist : {
            // 员工数组
            staffList : [],
            // 发证单位
            issuingDepartment : [
              { value: '宁波江东区安监局', label: '宁波江东区安监局'},
              { value: '宁波鄞州区安监局', label: '宁波鄞州区安监局'},
            ],
            // 复训日期
            retrainsDate : '',
            eduCertificateRetrains : '',
          },
          rules : {
            'eduUser.username' : [
              {required: true, message: '请输入内容', trigger: 'blur'},
            ],
            certificateName : [
              {required: true, message: '请输入内容', trigger: 'change'},
            ],
            certificateId : [
              {required: true, message: '请输入内容', trigger: 'blur'},
            ],
            retrainCycle : [
              {required: true, message: '请输入内容', trigger: 'blur'},
            ],
            getDate : [
              { type : 'date', required: true, message: '请选择日期', trigger: 'blur'},
            ],
            expiryDate : [
              { type : 'date', required: true, message: '请选择日期', trigger: 'blur'},
            ],
            issuingDepartment : [
              {required: true, message: '请输入内容', trigger: 'change'},
            ],
          },
          pageStatus : 'edit',
        }
      },
      watch:{
        $route(to,from){
          // 如果来至列表页
          if(from.name === 'certificateTrainingIndex'){
            this.init();
          }
        }
      },
      created(){
        this.init();
      },
      mounted(){
        this.init();
      },
      // 离开本页面
      beforeRouteLeave(to, form, next) {
        // 清空表单信息
        this.$refs['form'].resetFields();
        next()
      },
      methods:{
        // 初始化
        init(){
          if(this.$route.params.status){
            this.searchBtnClickHandle();
          } else {
            this.clear();

            this.form.getDate = this.$tool.getNowTime();
            this.form.expiryDate = this.$tool.getNowTime();
          }
        },
        // 用户选择
        userChange(val){
          this.$tool.cloneObj(this.form.eduUser, val);
          this.form.userId = this.form.eduUser.userId;
        },
        // 员工模糊搜索
        remoteMethod(query) {
          let params = new URLSearchParams();
          if (query !== '') {
            params.append('username', query)
            setTimeout(function(){
              this.$store.dispatch('userFind', params).then(function(res){
                if(res.success){
                  this.assist.staffList = res.data.list;
                } else {
                  this.$message({
                    type : 'error',
                    message : res.message || '错误'
                  })
                }
              }.bind(this));
            }.bind(this), 200);
          } else {
            this.assist.staffList = [];
          }
        },
        // 清空数据
        clear(){
          this.form = this.$tool.clearObj({},this.form);
          this.assist.staffList = [];
          this.assist.eduCertificateRetrains = '';
          this.assist.retrainsDate = '';
          this.pageStatus = 'edit';
        },
        // 格式化时间
        formatDateTime(row, column, cellValue){
          let pro = column.property;
          let num = 10;
          // 年份4位 1999
          if(pro === 'createTime') num = 20;
          let str = this.$tool.formatDateTime(row[pro] || 0);
          return str ? str.substring(0, num) : str;
        },
        // 新增复训记录
        addRetrainsBtnHandle(){
          let user = this.$tool.getStorage('LOGIN_USER');
          this.form.eduCertificateRetrains.push({
            retrainRecord : this.assist.retrainsDate,
            createId : user.userId,
            createUser : user.loginName,
            createDate : new Date()
          });
        },
        // 删除复训记录
        itemDeleteClick(index){
          this.form.eduCertificateRetrains.splice(index, 1);
        },
        // 提交---添加或更新
        submitBtnClickHandle(){
          this.form.getDate = new Date(this.form.getDate);
          this.form.expiryDate = new Date(this.form.expiryDate);
//          let params = Object.assign({}, this.form);
          let params = this.$tool.filterObj({}, this.form);
          this.$refs['form'].validate(function(valid) {
            if(valid){
              this.$store.dispatch('eduCertAddOrUpdate', params).then(function (res) {
                if(res.success){
                  this.$message({
                    type : 'success',
                    message : '操作成功'
                  })
                  this.$router.push({ name : 'certificateTrainingIndex' })
                } else {
                  this.$message({
                    type : 'error',
                    message : res.message || '错误'
                  })
                }
              }.bind(this))
            } else {
              return false;
            }
          }.bind(this))
        },
        // 根据id搜索信息
        searchBtnClickHandle(){
          this.clear();
          let id = this.$route.params.id;
          this.$store.dispatch('eduCertFind', { id : id }).then(function(res){
            if(res.success){
              let list = res.data.list[0];
              this.$tool.cloneObj(this.form, list);

              this.pageStatus = this.$route.params.status || 'edit';
            } else {
              this.$message({
                type : 'error',
                message : res.message || '错误'
              })
            }
          }.bind(this));
        },
        // 证书模糊搜索
        certificateNameSearch(str, cb) {
          let timeout = null;
          let params = {
            certName : str || ''
          }
          this.$store.dispatch('eduCertTypeFind', params).then(function(res){
            if(res.success){
              let list = res.data.list;
              clearTimeout(timeout);
              timeout = setTimeout(function(){
                  cb(list);
              }, 300 * Math.random());
            } else {
              this.$message({
                type : 'error',
                message : res.message || '错误'
              })
            }
          }.bind(this));
        },
        // 模糊证书选择
        certificateNameSelect(item){
          this.form.certificateName = item.certName;
          this.form.certificateTypeId = item.id;
        },
        // 模糊证书添加
        certificateNameAdd(){
          let name = this.form.certificateName;
          if(!name) return;
          let params = {
            certName : name
          }
          this.$confirm(`是否要将“${name}”添加到知识库中?`, '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
          }).then(function(){
            this.$store.dispatch('eduCertTypeAddOrUpdate', params).then(function(res){
              if(res.success) {
                this.form.certificateTypeId  = res.data.id;
                this.$message({
                  type: 'success',
                  message: '操作成功'
                })
              }  else {
                this.$message({
                  type : 'error',
                  message : res.message || '错误'
                })
              }
            }.bind(this));
          }.bind(this))
        },
        // 发证部门模糊搜索
        issuingDepartmentSearch(str, cb) {
          let timeout = null;
          let params = {
            deptName : str || ''
          }
          this.$store.dispatch('eduCertIssueDeptFind', params).then(function(res){
            if(res.success){
              let list = res.data.list;
              clearTimeout(timeout);
              timeout = setTimeout(function(){
                cb(list);
              }, 300 * Math.random());
            } else {
              this.$message({
                type : 'error',
                message : res.message || '错误'
              })
            }
          }.bind(this));
        },
        // 发证部门选择
        issuingDepartmentSelect(item){
          this.form.issuingDepartment = item.deptName;
          this.form.issuingDepartmentId = item.id;
        },
        // 发证部门添加
        issuingDepartmentAdd(){
          let name = this.form.issuingDepartment;
          if(!name) return;
          let params = {
            deptName : name
          }
          this.$confirm(`是否要将“${name}”添加到知识库中?`, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(function(){
            this.$store.dispatch('eduCertIssueDeptAddOrUpdate', params).then(function(res){
              if(res.success) {
                this.form.issuingDepartmentId = res.data.id;
                this.$message({
                  type: 'success',
                  message: '操作成功'
                })
              } else {
                this.$message({
                  type: 'error',
                  message: '操作失败'
                })
              }
            }.bind(this));
          }.bind(this))
        }
      }
    }
</script>

<style>
  .container{
    background:#fff;
    padding:0px 20px 20px;
  }
  .row{
    margin-top:10px;
  }
</style>
