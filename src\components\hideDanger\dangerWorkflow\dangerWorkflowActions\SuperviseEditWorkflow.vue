<template>
  <div id="superviseEditWorkflow">
    <div class="background-style">
      <el-col :span="16" :offset="4" class="primary-background-title">{{titleStr}}</el-col>
      <el-col :span="16" :offset="4">
        <el-form :model="form" ref="ruleForm" label-width="120px" class="demo-ruleForm" label-position="right">
          <el-collapse v-model="activeName" accordion>
            <el-collapse-item  name="1">
              <template slot="title">
                <div style="font-size: 16px;color: #2d57ae;font-weight: bold;padding-left: 20px">隐患信息</div>
              </template>
              <el-col :span="24">
                <el-col :span="12">
                  <el-form-item label="检查单编号：" prop="checkNum" style="margin: 0">
                    {{form.checkNum}}
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="检查类型：" style="margin: 0">
                    {{dangerData.typeName}}
                  </el-form-item>
                </el-col>
              </el-col>
              <div v-if="dangerData.typeName!='自查'">
                <el-col :span="24">
                  <el-col :span="12">
                    <el-form-item label="受检单位：" prop="targetDeptName" style="margin: 0;">
                      {{form.targetDeptName}}
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="检查单位：" prop="publicDeptName" style="margin: 0;">
                      {{form.publicDeptName}}
                    </el-form-item>
                  </el-col>
                </el-col>
                <el-col :span="24">
                  <el-col :span="12">
                    <el-form-item label="受检单位承办人：" prop="targetContractorUserName" style="margin: 0;" label-width="160px">
                      {{form.targetContractorUserName}}
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="受检单位现场负责人：" prop="targetLiveChargeUser" style="margin: 0" label-width="160px">
                      {{form.targetLiveChargeUser}}
                    </el-form-item>
                  </el-col>
                </el-col>
              </div>
            </el-collapse-item>
            <el-collapse-item  name="2">
              <template slot="title">
                <div style="font-size: 16px;color: #2d57ae;font-weight: bold;padding-left: 20px">整改信息</div>
              </template>
              <el-col :span="24">
                <el-col :span="12">
                  <el-form-item label="临时处理时间：" prop="tempMeasureTime" style="margin: 0;">
                    {{tempMeasureTime}}
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="整改通知单：" style="margin: 0;">
                    <el-button type="success" size="small" @click="viewNoticeForm">查看通知单</el-button>
                  </el-form-item>
                </el-col>
              </el-col>
              <el-col :span="24">
                <el-form-item label="临时措施：" prop="tempMeasure">
                  {{tempMeasure}}
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="临时措施照片：" style="margin: 0;">
                  <picture-card :picFileList="tempPictureList"></picture-card>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="整改回执：" prop="reformReply" style="margin: 0;">
                  {{reformReply}}
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="整改附件：">
                  <file-list :fileList="fileList"></file-list>
                </el-form-item>
              </el-col>
            </el-collapse-item>
            <el-collapse-item  name="3">
              <template slot="title">
                <div style="font-size: 16px;color: #2d57ae;font-weight: bold;padding-left: 20px">督办整改</div>
              </template>
              <el-col :span="24">
                <el-form-item label="督办回执：" prop="reformReply">
                  <el-button type="primary" size="mini" @click="chooseReformTime">填入参考内容</el-button>
                  <el-input type="textarea" :autosize="{ minRows: 3}" v-model="superviseReply"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="督办附件：">
                  <upload-file :fileList="superviseFileList" :data="{contentId:replyId,contentType:13}"></upload-file>
                </el-form-item>
              </el-col>
            </el-collapse-item>
          </el-collapse>
        </el-form>
      </el-col>
      <el-col :span="22" :offset="1">
        <el-table
          border
          v-loading="loading"
          :row-class-name="judgeOverTime"
          :data="tableData">
          <el-table-column
            type="index"
            label="序号"
            width="50"
            fixed
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            prop="inspectProject"
            label="检查项目"
            width="150"
            fixed
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            prop="inspectContent"
            min-width="400"
            label="检查标准内容"
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            prop="inspectResult"
            width="300"
            label="检查结果记录"
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            prop="hiddenDangerLevel"
            width="150"
            label="隐患级别"
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            width="320"
            label="隐患照片"
            label-class-name="inner-header-style">
            <template slot-scope="scope">
              <picture-card :picFileList="scope.row.dangerPics"></picture-card>
            </template>
          </el-table-column>
          <el-table-column
            prop="dangerType"
            width="150"
            label="隐患类型"
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            prop="changeRequire"
            width="200"
            label="整改要求"
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            prop="deadline"
            width="120"
            label="整改期限"
            :formatter="deadlineFormat"
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            width="320"
            label="整改照片"
            label-class-name="inner-header-style">
            <template slot-scope="scope">
              <picture-card :picFileList="scope.row.changePics"></picture-card>
            </template>
          </el-table-column>
          <el-table-column
            prop="changeTime"
            width="120"
            label="整改时间"
            :formatter="changeFormat"
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            prop="changeExplain"
            width="180"
            label="整改说明"
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            width="320"
            label="督办照片"
            label-class-name="inner-header-style">
            <template slot-scope="scope">
              <picture-card :picFileList="scope.row.supervisePics"></picture-card>
            </template>
          </el-table-column>
          <el-table-column
            prop="superviseReformDate"
            width="120"
            label="督办时间"
            :formatter="superviseFormat"
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            prop="superviseReformExplain"
            width="180"
            label="督办说明"
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            width="100"
            label="操作"
            fixed="right"
            label-class-name="inner-header-style">
            <template slot-scope="scope">
              <el-button size="mini" type="primary" @click="itemEditClick(scope.row,scope.$index)">记录</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-col>

      <el-col :span="22" :offset="1">
        <div style="float: right;margin: 20px">
          <el-button type="primary" @click="submitClick">提交</el-button>
          <el-button type="success" @click="saveClick">保存</el-button>
          <el-button @click="$router.push({name:'hideDangerWorkflow'})">返回</el-button>
        </div>
      </el-col>
    </div>

    <!--记录对话框-->
    <el-dialog :title="recordTitle" :visible.sync="recordVisible">
      <el-form :model="recordForm" ref="recordForm" label-position="left" class="demo-ruleForm">
        <el-form-item label="检查结果记录：" prop="inspectResult" label-position="left" style="margin: 5px">
          {{recordForm.inspectResult}}
        </el-form-item>
        <el-form-item label="整改要求：" prop="changeRequire" label-position="left" style="margin: 5px">
          {{recordForm.changeRequire}}
        </el-form-item>
        <el-form-item label="督办时间：" prop="superviseReformDate" label-position="left" style="margin: 5px">
          <el-date-picker
            v-model="recordForm.superviseReformDate"
            type="date"
            placeholder="选择日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="督办图片：" label-position="left" style="margin: 5px" >
          <upload-picture :data="supervisePictureParams" :picFileList="picFileList"></upload-picture>
        </el-form-item>
        <el-form-item label="督办说明：" prop="superviseReformExplain" label-position="top">
          <el-input type="textarea" :rows="2" v-model="recordForm.superviseReformExplain"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="success" @click="determineSave">保存</el-button>
        <el-button @click="recordDialogClose">返回</el-button>
      </div>
    </el-dialog>
    <!--记录对话框结束-->

    <!--选择负责人-->
    <search-people-dialog @determineClick="selectPersonClick" :data="selectPersonData" :defaultPersonId="selectPersonData.defaultPerson.value"></search-people-dialog>

    <!--是否审核对话框-->
    <judge-dialog ref="judgeDialog" @buttonClick="judgeExamine"></judge-dialog>

  </div>
</template>
<script>
  import SearchPeopleDialog from '../../../common/smallComponent/searchSinglePeople.vue'
  import PictureCard from '../../../common/smallComponent/pictureCard.vue'
  import UploadFile from '../../../common/smallComponent/uploadFile.vue'
  import UploadPicture from '../../../common/smallComponent/uploadPicture.vue'
  import FileList from '../../../common/smallComponent/fileList.vue'
  import JudgeDialog from '../../../common/smallComponent/judgeDialog.vue'
  export default {
    name: 'superviseEditWorkflow',
    data() {
      return {
        titleStr:'',
        currentId:'',

        activeName:'3',
        tableData:[],
        loading:false,
        form:{},
        //整改单的内容
        reformReply:'',//整改回执
        tempMeasureTime:'',//临时措施时间
        tempMeasure:'',//临时措施
        superviseReply:'',//督办回执

        personLoading:false,
        //记录对话框
        recordTitle:'检查结果记录',
        recordVisible:false,
        recordForm:{
          id:'',
          inspectResult:'',
          changeRequire:'',
          superviseReformDate:'',
          superviseReformExplain:'',
        },
        currentIndex:'',
        levelOption:['1级','2级','3级','4级','5级'],
        replyArray:[],
        replyId:-1,//整改是否已经存在，在则为整改单ID
        //--------------------------上传和下载-----------------------------
        supervisePictureParams:{
          contentId: 0,
          contentType: 12
        },
        fileList:[],//整改的附件
        picFileList:[],//对话框的督办照片
        tempPictureList:[],//临时措施照片
        superviseFileList:[],//督办的附件
        //------------------选择负责人的对话框-----------------------
        selectPersonData:{title:'请选择负责人',isShow:false,defaultPerson:{value:0,label:''}},
        //当前整改单数据
        dangerData:{},
        nodeData:{},
        //参考审核人
        leaderUser:{},

      }
    },
    computed:{
      personOptions:function () {
        return this.$store.state.sysManageData.personOptions;
      },
    },
    components : {
      FileList,
      SearchPeopleDialog,
      PictureCard,
      UploadFile,
      UploadPicture,
      JudgeDialog
    },
    created:function () {
      if(this.$route.params.dangerData){
        this.titleStr=this.$route.params.dangerData.name;
        this.nodeData=this.$route.params.dangerData.nodeData;
        this.dangerData=this.$route.params.dangerData;
        this.leaderUser={value:this.$route.params.dangerData.examineUserId,label:this.$route.params.dangerData.examineUsername};
        this.searchDataById(this.$route.params.dangerData.id);
      }
    },
    watch:{
      $route(to, from){
        if(from.name==='hideDangerWorkflow'&&this.$route.name==='superviseEditWorkflow'||from.name==='taskNotice') {
          if(this.$route.params.dangerData){
            this.titleStr=this.$route.params.dangerData.name;
            this.nodeData=this.$route.params.dangerData.nodeData;
            this.dangerData=this.$route.params.dangerData;
            this.leaderUser={value:this.$route.params.dangerData.examineUserId,label:this.$route.params.dangerData.examineUsername};
            this.searchDataById(this.$route.params.dangerData.id);
          }
        }
      }
    },
    methods: {
      searchDataById:function (id) {
        //清除之前数据
        this.tableData.splice(0);
        this.personOptions.splice(0);
        this.fileList.splice(0);
        this.tempPictureList.splice(0);
        this.superviseFileList.splice(0);
        for(let item in this.form){
          delete this.form[item];
        }
        this.replyId=-1;//整改单的ID
        this.reformReply='';
        this.tempMeasureTime='';
        this.tempMeasure='';
        this.superviseReply='';
        this.activeName='3';

        this.currentId=id;
        this.$http.post('danger/inspectPublic/detail', {id:id}).then(function (res) {
          if (res.data.success) {
            this.form=res.data.data;
            this.personOptions.push({value:this.form.reformChargeUserId,label:this.form.reformChargeUsername});
            this.replyArray[0]=this.form.publicDeptName+'：\n  '+this.form.name+'所列整改项目，已于';
            this.replyArray[1]='督办完毕，请验收。';
          }
        }.bind(this)).catch(function (err) {
          this.$message.error('danger/inspectPublic/detail');
          console.log(err);
        });
        //获取整改单内容
        this.$http.get('danger/reformSheet/find?dangerInspectPublicId='+id+'&applyUserId='+this.dangerData.subProcessStartUserId).then(function (res) {
          if (res.data.success) {
            if(res.data.data.length){
              this.replyId=res.data.data[0].id;//整改单的ID
              this.reformReply=res.data.data[0].reformReply;
              this.tempMeasureTime=this.transferTime(res.data.data[0].tempMeasureTime);
              this.tempMeasure=res.data.data[0].tempMeasure;
              this.superviseReply=res.data.data[0].superviseReply;
              this.fileList=res.data.data[0].reformAttachments;
              for(let i=0;i<this.fileList.length;i++){
                this.fileList[i].name=this.fileList[i].fileName;
              }
              this.tempPictureList=res.data.data[0].measurePics;
              this.superviseFileList=res.data.data[0].superviseAttachments;
              this.superviseFileList.forEach(function (item) {
                item.name=item.fileName;
              })
            }else{
              let replyParams=new URLSearchParams;
              replyParams.append("dangerInspectPublicId",this.currentId);
              replyParams.append("processInstanceId",this.dangerData.processInstanceId);
              replyParams.append("applyUserId",this.$tool.getStorage('LOGIN_USER').userId);
              this.$http.post('danger/reformSheet/add',replyParams).then(function (res) {
                if (res.data.success) {
                  this.replyId=res.data.data.id;
                }
              }.bind(this)).catch(function (err) {
                this.$message.error('danger/reformSheet/add');
                console.log(err);
              });
            }
          }
        }.bind(this)).catch(function (err) {
          this.$message.error('/danger/reformReply/find');
          console.log(err);
        });

        this.loading=true;//让表格缓冲显示
        this.$http.post('danger/inspectListPublic/find', {applyUserId:this.$tool.getStorage('LOGIN_USER').userId,inspectPublicId:id,needChange:1}).then(function (res) {
          if (res.data.success) {
            this.tableData=res.data.data;
            this.loading=false;
          }
        }.bind(this)).catch(function (err) {
          this.$message.error('danger/inspectListPublic/find');
          console.log(err);
        });
      },
      //自动填入整改回执
      chooseReformTime:function () {
        let currentTime=new Date();
        this.superviseReply=this.replyArray[0]+this.transferTime(currentTime,'y年m月d日')+this.replyArray[1];
      },
      //查找人员
      remotePerson:function (val) {
        this.$store.dispatch('getPersonOptions',val);
      },
      viewNoticeForm:function () {
        this.$router.push({name:'investigationViewWorkflow',params:{dangerData:this.dangerData,onlyShow:true}})
      },
      //-----------------------------------表格功能---------------------------------
      //改时间格式
      deadlineFormat:function (row) {
        return this.transferTime(row.deadline);
      },
      changeFormat:function (row) {
        return this.transferTime(row.changeTime);
      },
      superviseFormat:function (row) {
        return this.transferTime(row.superviseReformDate);
      },
      //判断该条记录是否过期了
      judgeOverTime:function ({row}) {
        if(row.superviseChangeOverTime){
          return 'warning-row';
        }else{
          return '';
        }
      },

      itemEditClick:function (row,index) {
        this.picFileList.splice(0);
        this.recordForm.id=row.id;
        this.supervisePictureParams.contentId=row.id;//上传整改照片关联的隐患ID
        this.recordForm.inspectResult=row.inspectResult;
        this.recordForm.changeRequire=row.changeRequire;
        if(row.superviseReformDate){this.recordForm.superviseReformDate=row.superviseReformDate;}else{
          let currentDate=new Date();
          this.recordForm.superviseReformDate=currentDate.getTime();
        }
        this.recordForm.superviseReformExplain=row.superviseReformExplain;
        if(row.supervisePics){
          this.picFileList=row.supervisePics.slice(0);
          this.picFileList.forEach(function (item) {
            item.name=item.fileName;
            item.oriUrl=this.fileHttp.defaults.baseURL+item.path;
            item.url=item.oriUrl+'?x-oss-process=image/resize,m_fixed,h_70,w_70';
          });
        }else{
          this.picFileList=[];
        }
        this.currentIndex=index;
        this.recordVisible=true;
      },
      //检查结果记录确定
      determineSave:function () {
        this.$refs["recordForm"].validate((valid) => {
          if (valid) {
            let tempObj={id:this.recordForm.id,superviseReformDate:this.recordForm.superviseReformDate,superviseReformExplain:this.recordForm.superviseReformExplain};
            this.$http.post('danger/inspectListPublic/update', tempObj).then(function (res) {
              if (res.data.success) {
                this.loading=true;//让表格缓冲显示
                this.$http.post('danger/inspectListPublic/find', {applyUserId:this.$tool.getStorage('LOGIN_USER').userId,inspectPublicId:this.currentId,needChange:1}).then(function (res) {
                  if (res.data.success) {
                    this.tableData=res.data.data;
                    this.loading=false;
                  }
                }.bind(this)).catch(function (err) {
                  this.$message.error('danger/inspectListPublic/find');
                  console.log(err);
                });
                this.recordVisible=false;
              }
            }.bind(this)).catch(function (err) {
              console.log('danger/inspectListPublic/update');
              console.log(err);
            });
          } else {
            console.log('error submit!!');
            return false;
          }
        });
      },
      //对话框返回
      recordDialogClose:function () {
        this.loading=true;//让表格缓冲显示
        this.$http.post('danger/inspectListPublic/find', {applyUserId:this.$tool.getStorage('LOGIN_USER').userId,inspectPublicId:this.currentId,needChange:1}).then(function (res) {
          if (res.data.success) {
            this.tableData=res.data.data;
            this.loading=false;
          }
        }.bind(this)).catch(function (err) {
          this.$message.error('danger/inspectListPublic/find');
          console.log(err);
        });
        this.recordVisible=false;
      },
      //--------------------------------提交----------------------------------
      saveClick:function () {
        this.newOrUpdateReformSheet();
      },
      submitClick:function () {
        if(this.reformReply){//回执填了没有
          if(this.nodeData.check){
            this.$refs.judgeDialog.openJudgeDialog();
          }else{//不需要审核
            this.doTaskClick(false,0);
          }
        }else{
          this.$message.warning('请填写督办回执！');
        }
      },
      judgeExamine:function (val) {
        if(val){
          this.selectPersonData.title='请选择审核人';
          this.selectPersonData.defaultPerson=this.leaderUser;
          this.selectPersonData.isShow=true;
        }else{
          this.doTaskClick(false,0);
        }
      },
      selectPersonClick:function (val) {
        if(val){
          this.selectPersonData.isShow=false;
          this.doTaskClick(true,val);
        }else{
          this.$message.warning('请选择审核人');
        }
      },
      doTaskClick:function (checkFlag,examinePerson) {
        let flowParams=new URLSearchParams;
        flowParams.append("taskId",this.dangerData.taskId);
        flowParams.append("check",checkFlag);
        if(this.nodeData.reform){
          flowParams.append("reform",true);
        }else{
          flowParams.append("reform",false);
        }
        if(examinePerson){
          flowParams.append("applyUserId",examinePerson);
        }else {
          if(this.form.checkUserId){flowParams.append("applyUserId",this.form.checkUserId)}//直接验收->验收人}
        }
        this.$http.post('dangerFlow/doTask',flowParams).then(function (res) {
          if(res.data.success) {
            this.newOrUpdateReformSheet();
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message.error('流程执行失败');
        }.bind(this));
      },
      //新建或者修改整改单
      newOrUpdateReformSheet:function () {
        let urlStr='danger/reformSheet/update';
        let replyParams=new URLSearchParams;
        replyParams.append("dangerInspectPublicId",this.currentId);
        replyParams.append("applyUserId",this.$tool.getStorage('LOGIN_USER').userId);
        replyParams.append("processInstanceId",this.dangerData.processInstanceId);
        replyParams.append("superviseReply",this.superviseReply);
        replyParams.append("id",this.replyId);
        this.$http.post(urlStr,replyParams).then(function (res) {
          if (res.data.success) {
            this.$message.success('操作成功！');
            this.$router.push({name:'hideDangerWorkflow'});
          }
        }.bind(this)).catch(function (err) {
          this.$message.error(urlStr);
          console.log(err);
        });
      },
    }

  }
</script>
<style>
  .el-table .warning-row {
    color: #f00;
  }
</style>
