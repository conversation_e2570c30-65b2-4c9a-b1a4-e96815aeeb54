<template>
  <div class="map">
    <el-container>
      <el-main>
        <el-row style="margin:0">
          <el-col :span="6">
            <el-button type="primary" size="mini" @click="$router.back()">返回</el-button>
          </el-col>
        </el-row>
        <el-row style="margin:10px 0 0 0;">
          <el-col :span="24">
            <egrid class="egrid"
                   stripe border
                   maxHeight="500"
                   :data="egrid.data"
                   :columns="egrid.columns"
                   :column-type="egrid.columnType">
            </egrid>
          </el-col>
        </el-row>
      </el-main>
    </el-container>
  </div>
</template>

<script>


  export default {
    data(){
      return {
        form : {
          year : '',
        },
        // 表格
        egrid : {
          data : [],
          columns : [
            { label: '姓名', prop: 'name' },
            { label: '受伤情况', prop: 'injuryDescribe' },
            { label: '伤残等级', prop: 'disabilityClasses' },
            { label: '获赔时间', prop: 'payTime' },
            { label: '获赔金额', prop: 'paySum' },
          ],
          // columnsProps 用于定义所有 columns 公共的属性
          columnsProps: {
            fit : true,
            sortable: true,
            align : 'center',
          },
          columnsSchema : {

          },
          columnType : 'index'
        }
      }
    },
    created(){
      this.init();
    },
    watch:{
      $route(to,from){
        let year = to.params && to.params.row && to.params.row.year;
        if(to.name === 'accountSafeItemInjury') {
          if(year){
            let date = new Date();
            date.setFullYear(year);
            this.form.year = date;
            this.searchBtnClickHandle();
          }
        }
      }
    },
    methods:{
      // 初始化
      init(){
        let year = this.$route.params.row.year;
        let date = new Date();
        date.setFullYear(year);
        this.form.year = date;
        // 搜索
        this.searchBtnClickHandle();
      },
      // 搜索按钮
      searchBtnClickHandle(){
        this.$store.dispatch('costInjuryClaimSearchByYear', this.form).then(function(res){
          if(res.success){
            let list = res.data.costInjuryClaimRecords.map(function(it){
              return {
                payTime : this.$tool.formatDateTime(it.payTime).substring(0,10),
                name : it.name || '',
                injuryDescribe : it.injuryDescribe || 0,
                disabilityClasses : it.disabilityClasses || '',
                paySum : it.paySum || '',
              }
            }.bind(this));
            this.egrid.data = list;
          } else {
            this.egrid.data = [];
          }
        }.bind(this));
      },
    }
  }
</script>

<style>
  .map{
    background:#fff;
    height:100%;
    overflow: hidden;
    padding:50px 20px;
  }
</style>
