<template>
    <el-container class="container">
      <el-main>
        <el-form ref="form" label-width="100px">
          <el-row type="flex">
            <el-col :span="8">
              <el-form-item class="formItem" label="姓名">
                <el-input></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item class="formItem" label="性别">
                <span></span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item class="formItem" label="出身年月">
                <span></span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex">
            <el-col :span="8">
              <el-form-item class="formItem" label="文化程度">
                <span></span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item class="formItem" label="入职时间">
                <span></span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item class="formItem" label="部门">
                <span></span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="公司培训：" class="title">
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex">
            <el-col :offset="2" :span="7">
              <el-form-item class="formItem" label="培训时间">
                <el-input></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="7">
              <el-form-item class="formItem" label="学时">
                <el-input></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="7">
              <el-form-item class="formItem" label="教育者">
                <el-input></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex">
            <el-col :offset="2" :span="22">
              <el-form-item class="formItem" label="备注">
                <el-input autosize type="textarea" :rows="2"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item  label="部门培训：" class="title">
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex">
            <el-col :offset="2" :span="7">
              <el-form-item class="formItem" label="培训时间">
                <el-input></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="7">
              <el-form-item class="formItem" label="学时">
                <el-input></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="7">
              <el-form-item class="formItem" label="教育者">
                <el-input></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex">
            <el-col :offset="2" :span="22">
              <el-form-item class="formItem" label="备注">
                <el-input type="textarea" autosize :rows="2"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item  label="班组培训：" class="title">
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex">
            <el-col :offset="2" :span="7">
              <el-form-item class="formItem" label="培训时间">
                <el-input></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="7">
              <el-form-item class="formItem" label="学时">
                <el-input></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="7">
              <el-form-item  class="formItem" label="教育者">
                <el-input></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex">
            <el-col :offset="2" :span="22">
              <el-form-item class="formItem" label="备注">
                <el-input autosize type="textarea" autosize :rows="2"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex">
            <el-col :span="6">
              <el-form-item class="formItem" label="考试成绩">
                <el-input></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex">
            <el-form-item class="formItem" label="受训员工">
              <el-table>
                <el-table-column type="selection"  width="50"></el-table-column>
                <el-table-column type="index" label="序号" width="100"></el-table-column>
                <el-table-column property="deptName" label="姓名" width="150"></el-table-column>
                <el-table-column property="deptName" label="工号" width="150"></el-table-column>
              </el-table>
            </el-form-item>
          </el-row>
          <el-row type="flex" class="row" justify="center">
            <el-button
              @click="saveBtnClickHandle"
              size="small" :span="2" type="success" >保存</el-button>
            <el-button size="small" :span="2" type="primary">提交</el-button>
            <el-button size="small" :span="2" @click="$router.back();">返回</el-button>
          </el-row>
        </el-form>
      </el-main>
    </el-container>
</template>

<script>
    import userList from '@/components/common/userList.vue'
    export default {
      components : {
        userList
      },
      data(){
        return {
          form : {
            // 员工
            eduUser : {

            },
          }
        }
      },
      computed:{

      },
      watch:{

      },
      created(){

      },
      mounted(){

      },
      methods:{}
    }
</script>

<style>
  .container{
    background:#fff;
    padding:0px 20px 20px;
  }
  .title{
    background:rgba(64,158,255,.1);
    color:#0f6fc6;
    border: 1px solid rgba(64,158,255,.2);
    border-radius:5px;
    margin:5px;
  }
  .row{
    margin-top:10px;
  }
  .formItem{
    margin:2px;
  }
</style>
