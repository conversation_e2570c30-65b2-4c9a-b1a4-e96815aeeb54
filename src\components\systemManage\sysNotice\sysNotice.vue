<template>
  <div id="sysNotice" style="height: 100%;width: 100%;background-color: #fff">
    <!-- quill-editor插件标签 分别绑定各个事件-->
    <quill-editor v-model="content" ref="myQuillEditor"
                  @blur="onEditorBlur($event)"
                  @focus="onEditorFocus($event)"
                  @change="onEditorChange($event)">
    </quill-editor>
  </div>
</template>

<script>
  export default {

    name: "sysNotice",

    data() {
      return {
        content:''
      }
    },
    mounted: function () {
    },
    methods: {
      onEditorBlur(editor) {
        console.log('editor blur!', editor)
      },
      onEditorFocus(editor) {
        console.log('editor focus!', editor)
      },
      onEditorReady(editor) {
        console.log('editor ready!', editor)
      },
      onEditorChange({ editor, html, text }) {
        // console.log('editor change!', editor, html, text)
        this.content = html
      }
    }
  }
</script>

<style scoped>

</style>
