<template>
  <div id="emergencyProcess">
    <div
      class="background-style">
      <el-col :span="20" :offset="2" class="primary-background-title">
        应急响应详情
      </el-col>
      <el-col :span="20" :offset="2">
        <el-form :model="form" label-width="120px" ref="form">
          <el-collapse v-model="activeNames" accordion>
            <el-collapse-item  name="1">
              <template slot="title">
                <div style="font-size: 16px;color: #2d57ae;font-weight: bold;padding-left: 20px">基本信息</div>
              </template>
              <!--信息卡-->
              <el-col :span="24">
                <el-col :span="12">
                  <el-form-item label="名称：" prop="emerResponseName" style="margin: 0">
                    {{form.emerResponseName}}
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="分类：" prop="emerType" style="margin: 0">
                    {{form.emerType}}
                  </el-form-item>
                </el-col>
              </el-col>
              <el-col :span="24">
                <el-col :span="12">
                  <el-form-item label="响应级别：" prop="respLevel" style="margin: 0">
                    {{form.respLevel}}
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="预警信号：" prop="emerFlag" style="margin: 0">
                    {{form.emerFlag}}
                  </el-form-item>
                </el-col>
              </el-col>
              <el-col :span="24">
                <el-col :span="12">
                  <el-form-item label="状态：" prop="emerResponseStatus" style="margin: 0">
                    {{form.emerResponseStatus}}
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="发布时间：" prop="signDate" style="margin: 0">
                    {{form.signDate}}
                  </el-form-item>
                </el-col>
              </el-col>
              <el-col :span="24">
                <el-col :span="12" >
                  <el-form-item label="启动通知：" style="margin: 0">
                    <el-button type="text" style="color: #5daf34" @click="viewBeginMessage">查看启动通知</el-button>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="调整通知：" style="margin: 0">
                    <el-button type="text" style="color: #5daf34;margin-right: 10px"
                               @click="viewAdjustMessage">查看调整通知
                    </el-button>
                  </el-form-item>
                </el-col>
              </el-col>
              <el-col :span="24">
                <el-col :span="12">
                  <el-form-item label="解除通知：" style="margin: 0">
                    <el-button type="text" style="color: #5daf34;margin-right: 10px" @click="viewRelieveClick">查看解除通知</el-button>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="总结评估：" style="margin: 0">
                    <el-button type="text" style="color: #5daf34;margin-right: 10px" @click="viewSum">查看总结评估
                    </el-button>
                  </el-form-item>
                </el-col>
              </el-col>
              <!--信息卡结束-->
            </el-collapse-item>
            <el-collapse-item  name="2">
              <template slot="title">
                <div style="font-size: 16px;color: #2d57ae;font-weight: bold;padding-left: 20px">响应信息和值班表</div>
              </template>
              <el-col :span="24">
                <span>响应情况列表</span>
                <el-table
                  :data="responseTable"
                  border
                  highlight-current-row
                  style="width: 100%">
                  <el-table-column
                    type="index"
                    align="center"
                    label-class-name="inner-header-style"
                    width="50">
                  </el-table-column>
                  <el-table-column
                    label="状态"
                    align="center"
                    label-class-name="inner-header-style"
                    width="100">
                    <template slot-scope="scope">
                      <el-tag :type="scope.row.response?'success':'danger'">
                        {{scope.row.response?'已响应':'未响应'}}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column
                    label="公司"
                    prop="company"
                    label-class-name="inner-header-style"
                    min-width="150">
                  </el-table-column>
                  <el-table-column
                    label="响应时间"
                    prop="responseTime"
                    align="center"
                    :formatter="responseTimeFormat"
                    label-class-name="inner-header-style"
                    width="180">
                  </el-table-column>
                </el-table>
              </el-col>
              <el-col :span="24" style="margin-bottom: 10px">
                <span>值班列表</span>
                <el-table
                  :data="allDutyTable"
                  border
                  highlight-current-row
                  style="width: 100%">
                  <el-table-column
                    type="index"
                    align="center"
                    label-class-name="inner-header-style"
                    width="50">
                  </el-table-column>
                  <el-table-column
                    label="值班日期"
                    prop="dutyDate"
                    align="center"
                    :formatter="dutyTimeFormat"
                    label-class-name="inner-header-style"
                    width="120">
                  </el-table-column>
                  <el-table-column
                    label="公司"
                    prop="company"
                    label-class-name="inner-header-style"
                    min-width="300">
                  </el-table-column>
                  <el-table-column
                    label="值班人员"
                    prop="name"
                    align="center"
                    label-class-name="inner-header-style"
                    width="120">
                  </el-table-column>
                  <el-table-column
                    label="手机长号"
                    prop="phone"
                    align="center"
                    label-class-name="inner-header-style"
                    width="130">
                  </el-table-column>
                  <el-table-column
                    label="手机短号"
                    prop="shortPhone"
                    align="center"
                    label-class-name="inner-header-style"
                    width="120">
                  </el-table-column>
                </el-table>
              </el-col>
            </el-collapse-item>
          </el-collapse>

          <!--工作情况简报-->
          <el-col :span="24" class="card-shadow-style">
            <div style="width: 100%;padding-top: 10px;padding-bottom:10px;float: left;background-color: #f2f2f2">
              <i class="el-icon-document" style="color:#049ff1;float: left;margin:3px 10px 0 20px"></i>
              <span style="color:#049ff1;width: 200px;float: left;">应急工作情况简报</span>
              <div style="float: right;margin-right: 20px;display: inline-block">
                <el-button type="success" size="small" @click="viewWorkBrief">查看简报</el-button>
              </div>
            </div>
            <div style="width: 100%;float:left;">
              <el-col :span="24" style="margin-top: 10px">
                <el-col :span="12">
                  <el-form-item label="编制公司：" prop="company" style="margin: 0">
                    {{form.company}}
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="规定上报频率：" prop="interval" style="margin: 0">
                    {{form.interval}}
                  </el-form-item>
                </el-col>
              </el-col>
              <el-col :span="24" style="margin-bottom: 10px">
                <el-col :span="12">
                  <el-form-item label="最近上报时间：" prop="lastTime" style="margin: 0">
                    {{form.lastTime}}
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="下次上报期限：" prop="nextTime" style="margin: 0">
                    {{form.nextTime}}
                  </el-form-item>
                </el-col>
              </el-col>
              <el-col :span="24" style="padding:0 5px 20px 5px">
                <el-table
                  :data="form.workBriefTable"
                  border
                  highlight-current-row
                  style="width: 100%">
                  <el-table-column
                    type="index"
                    align="center"
                    label-class-name="inner-header-style"
                    width="50">
                  </el-table-column>
                  <el-table-column
                    label="状态"
                    align="center"
                    label-class-name="inner-header-style"
                    width="100">
                    <template slot-scope="scope">
                      <el-tag :type="workBriefStatus[scope.row.status].labelType">
                        {{workBriefStatus[scope.row.status].name}}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="reportTime"
                    label="上报时间"
                    align="center"
                    label-class-name="inner-header-style"
                    width="180">
                  </el-table-column>
                  <el-table-column
                    prop="defenseWork"
                    label="现阶段防御工作部署落实情况"
                    label-class-name="inner-header-style"
                    width="180">
                  </el-table-column>
                  <el-table-column
                    prop="dutyPeople"
                    label="值班人员到位情况"
                    align="center"
                    label-class-name="inner-header-style"
                    width="180">
                  </el-table-column>
                  <el-table-column
                    prop="disasterSituation"
                    label="受损受灾情况"
                    align="center"
                    label-class-name="inner-header-style"
                    width="180">
                  </el-table-column>
                  <el-table-column
                    prop="emergency"
                    label="受损受灾情况"
                    align="center"
                    label-class-name="inner-header-style"
                    width="180">
                  </el-table-column>
                  <el-table-column
                    prop="emergencyHandle"
                    label="受损受灾情况"
                    align="center"
                    label-class-name="inner-header-style"
                    width="180">
                  </el-table-column>
                  <el-table-column
                    prop="emergencyAfter"
                    label="值班人员到位情况"
                    align="center"
                    label-class-name="inner-header-style"
                    min-width="180">
                  </el-table-column>
                  <el-table-column
                    label="操作"
                    align="center"
                    fixed="right"
                    width="100"
                    label-class-name="inner-header-style">
                    <template slot-scope="scope">
                      <el-button type="text" size="medium" style="color: #5daf34" @click="workItemView(scope.row)">
                        查看
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
                <div style="margin-top: 10px">
                  <el-pagination
                    background
                    layout="prev, pager, next"
                    :current-page="workCurrentPage"
                    :total="workTotalItem"
                    :page-size="workPageSize"
                    @current-change="workCurrentPageClick">
                  </el-pagination>
                </div>
              </el-col>
            </div>
          </el-col>
          <!--工作情况简报结束-->

          <!--物资储备情况-->
          <el-col :span="24" class="card-shadow-style">
            <div style="width: 100%;padding-top: 10px;padding-bottom:10px;float: left;background-color: #f2f2f2">
              <i class="el-icon-menu" style="color:#049ff1;float: left;margin:3px 10px 0 20px"></i>
              <span style="color:#049ff1;width: 200px;float: left;">应急物资储备情况</span>
              <div style="float: right;margin-right: 20px;display: inline-block">
                <el-button type="success" size="small" @click="viewSummary">查看汇总</el-button>
              </div>
            </div>
            <div style="width: 100%;float:left;">
              <el-col :span="24" style="padding:20px 5px 20px 5px">
                <el-table
                  :data="form.goodsTable"
                  border
                  style="width: 100%">
                  <el-table-column
                    type="index"
                    align="center"
                    label-class-name="inner-header-style"
                    width="50">
                  </el-table-column>
                  <el-table-column
                    prop="name"
                    label="物资名称"
                    align="center"
                    label-class-name="inner-header-style"
                    width="120">
                  </el-table-column>
                  <el-table-column
                    prop="stock"
                    label="物资数量"
                    align="center"
                    label-class-name="inner-header-style"
                    min-width="80">
                  </el-table-column>
                  <el-table-column
                    label="操作"
                    align="center"
                    fixed="right"
                    width="140"
                    label-class-name="inner-header-style">
                    <template slot-scope="scope">
                      <el-button type="text" size="medium" style="color: #5daf34" @click="goodsItemView(scope.row)">详情
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
                <div style="margin-top: 10px">
                  <el-pagination
                    background
                    layout="prev, pager, next"
                    :current-page="goodsCurrentPage"
                    :total="goodsTotalItem"
                    @current-change="goodsCurrentPageClick">
                  </el-pagination>
                </div>
              </el-col>
            </div>
          </el-col>
          <!--物资储备情况结束-->

          <!--文件上传和下载-->
          <el-col :span="24" class="card-shadow-style">
            <div style="width: 100%;padding-top: 10px;padding-bottom:10px;float: left;background-color: #f2f2f2">
              <i class="el-icon-upload" style="color:#049ff1;float: left;margin:3px 10px 0 20px"></i>
              <span style="color:#049ff1;width: 200px;float: left;">其他文件资料</span>
            </div>
            <div style="width: 100%;float:left;padding: 20px">
              <el-col :span="20">
                <file-list-table :fileList="form.fileData"></file-list-table>
              </el-col>
            </div>
          </el-col>

          <el-col :span="24">
            <div style="float: right;margin-bottom: 20px">
              <el-button type="primary" @click="returnClick">返回</el-button>
            </div>
          </el-col>

        </el-form>
      </el-col>
    </div>

    <!--查看工作简报对话框-->
    <el-dialog :title="workDialTitle" :visible.sync="workBriefVisible">
      <el-form :model="workTempForm" label-position="top" :rules="workRules" ref="workRuleForm" class="demo-ruleForm">
        <el-form-item label="上报日期时间：" prop="signTime">
          <el-date-picker
            v-model="workTempForm.signTime"
            type="datetime"
            placeholder="选择日期时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="现阶段防御工作部署落实情况：" prop="defenseWork">
          <el-input type="textarea" :autosize="{ minRows: 1}" placeholder="例：已启动预案，施工现场停工，停止路上作业。"
                    v-model="workTempForm.defenseWork"></el-input>
        </el-form-item>
        <el-form-item label="值班人员到位情况：" prop="dutyPeople">
          <el-input type="textarea" :autosize="{ minRows: 1}" placeholder="例：已进岗到位。"
                    v-model="workTempForm.dutyPeople"></el-input>
        </el-form-item>
        <el-form-item label="受损受灾情况：" prop="disasterSituation">
          <el-input type="textarea" :autosize="{ minRows: 1}" placeholder="无相应情况请填写“无”"
                    v-model="workTempForm.disasterSituation"></el-input>
        </el-form-item>
        <el-form-item label="突发事件情况：" prop="emergency">
          <el-input type="textarea" :autosize="{ minRows: 1}" placeholder="无相应情况请填写“无”"
                    v-model="workTempForm.emergency"></el-input>
        </el-form-item>
        <el-form-item label="事件现场应急处置及抢险救灾情况：" prop="emergencyHandle">
          <el-input type="textarea" :autosize="{ minRows: 1}" placeholder="无相应情况请填写“无”"
                    v-model="workTempForm.emergencyHandle"></el-input>
        </el-form-item>
        <el-form-item label="事件应急结束善后处置情况：" prop="emergencyAfter">
          <el-input type="textarea" :autosize="{ minRows: 1}" placeholder="无相应情况请填写“无”"
                    v-model="workTempForm.emergencyAfter"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <div v-if="workUpdateFlag">
          <el-button type="primary" @click="sendRequest(1)">发布</el-button>
          <el-button type="success" @click="sendRequest(0)">保存</el-button>
          <el-button @click="$refs['workRuleForm'].resetFields();workBriefVisible = false;workUpdateFlag=false">返回</el-button>
        </div>
        <div v-else>
          <el-button @click="$refs['workRuleForm'].resetFields();workBriefVisible = false;workUpdateFlag=false">返回</el-button>
        </div>
      </div>
    </el-dialog>
    <!--查看工作简报对话框结束-->

    <!--查看不同级别简报对话框-->
    <el-dialog title="选择简报编辑部门" :visible.sync="deptBriefVisible">
      <el-form :model="deptTempForm">
        <el-form-item label="选择编辑部门：" label-position="top">
          <el-select v-model="deptTempForm.editDept" placeholder="请选择">
            <el-option
              v-for="item in deptTempForm.deptOptions"
              :key="item.value"
              :label="item.label"
              :value="item">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="$router.push({name:'viewWorkBrief',params:{dept:deptTempForm.editDept,emerName:form.emerResponseName,startId:startEmerId}});deptBriefVisible=false">确定</el-button>
      </div>
    </el-dialog>
    <!--查看不同级别简报对话框结束-->

    <!--查看汇总对话框-->
    <el-dialog title="物资汇总" :visible.sync="goodsSummaryVisible">
      <el-table
        :data="goodsSummaryData"
        border
        style="width: 100%">
        <el-table-column
          type="index"
          align="center"
          fixed
          label-class-name="inner-header-style"
          width="50">
        </el-table-column>
        <el-table-column
          prop="goodsName"
          label="物资名称"
          align="center"
          label-class-name="inner-header-style"
          width="120">
        </el-table-column>
        <el-table-column
          prop="count"
          label="数量"
          align="center"
          label-class-name="inner-header-style"
          width="80">
        </el-table-column>
        <el-table-column
          prop="goodsLocation"
          label="存放地点"
          align="center"
          label-class-name="inner-header-style"
          width="200">
        </el-table-column>
        <el-table-column
          prop="deptName"
          label="所属部门"
          align="center"
          label-class-name="inner-header-style"
          width="200">
        </el-table-column>
        <el-table-column
          prop="manageUser"
          label="管理员"
          align="center"
          label-class-name="inner-header-style"
          width="120">
        </el-table-column>
        <el-table-column
          prop="contact"
          label="联系方式"
          align="center"
          label-class-name="inner-header-style"
          width="140">
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="goodsSummaryVisible = false">返回</el-button>
      </div>
    </el-dialog>
    <!--查看汇总对话框结束-->
    <!--查询物资详情-->
    <el-dialog title="物资详情" :visible.sync="goodsDetailVisible">
      <el-form :model="goodsTempForm">
        <el-form-item label-position="top">
          <el-table
            :data="goodsTempForm.resultArray"
            border
            style="width: 100%">
            <el-table-column
              type="index"
              align="center"
              fixed
              label-class-name="inner-header-style"
              width="50">
            </el-table-column>
            <el-table-column
              prop="goodsName"
              label="物资名称"
              align="center"
              label-class-name="inner-header-style"
              width="120">
            </el-table-column>
            <el-table-column
              prop="count"
              label="数量"
              align="center"
              label-class-name="inner-header-style"
              width="80">
            </el-table-column>
            <el-table-column
              prop="goodsLocation"
              label="存放地点"
              align="center"
              label-class-name="inner-header-style"
              width="200">
            </el-table-column>
            <el-table-column
              prop="deptName"
              label="所属部门"
              align="center"
              label-class-name="inner-header-style"
              width="200">
            </el-table-column>
            <el-table-column
              prop="manageUser"
              label="管理员"
              align="center"
              label-class-name="inner-header-style"
              width="120">
            </el-table-column>
            <el-table-column
              prop="contact"
              label="联系方式"
              align="center"
              label-class-name="inner-header-style"
              width="140">
            </el-table-column>
          </el-table>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="goodsDetailVisible = false;goodsTempForm.resultArray=[]">返回</el-button>
      </div>
    </el-dialog>
    <!--查询物资详情结束-->

    <!--查看调整应急-->
    <el-dialog title="查看调整应急" :visible.sync="viewAdjustVisible">
      <el-table
        :data="adjustArray"
        border
        style="width: 100%">
        <el-table-column
          type="index"
          align="center"
          label-class-name="inner-header-style"
          width="50">
        </el-table-column>
        <el-table-column
          prop="name"
          label="应急响应名称"
          align="center"
          label-class-name="inner-header-style"
          min-width="200">
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
          fixed="right"
          label-class-name="inner-header-style"
          width="140">
          <template slot-scope="scope">
            <el-button type="text" size="medium" style="color: #5daf34" @click="viewAdjustItemClick(scope.row)">查看</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="viewAdjustVisible = false;adjustArray=[]">返回</el-button>
      </div>
    </el-dialog>
    <!--查看调整应急结束-->

    <!--查看不同级别的总结对话框-->
    <el-dialog title="选择总结编辑部门" :visible.sync="sumVisible">
      <el-form :model="sumTempForm">
        <el-form-item label="选择编辑部门：" label-position="top">
          <el-select v-model="sumTempForm.editDept" placeholder="请选择">
            <el-option
              v-for="item in sumTempForm.deptOptions"
              :key="item.value"
              :label="item.label"
              :value="item">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="$router.push({name:'viewSummary',params:{dept:sumTempForm.editDept,emerName:form.emerResponseName,emerId:currentEmerId}});sumVisible=false">确定</el-button>
        <el-button @click="sumVisible=false">取消</el-button>
      </div>
    </el-dialog>
    <!--查看不同级别的总结对话框结束-->


  </div>
</template>
<script>
  import {mapGetters} from 'vuex'
  import FileListTable from '../../../common/smallComponent/fileListTable.vue'
  export default {
    name: 'emergencyProcess',
    data() {
      return {
        activeNames:'1',
        //信息卡数据
        form: {
          emerResponseName: '',
          emerType: '',
          emerFlag: '',
          respLevel: '',
          emerResponseStatus: '',
          signDate: '',

          //工作简报数据
          company:'',//以工位单位进行工作简报编写
          interval: '',
          lastTime: '',
          nextTime: '',
          //简报表格
          workBriefTable: [],
          //物资储备数据
          goodsTable: [],
          //其他文件资料
          fileData: []
        },

        //响应和值班表
        responseTable:[],
        allDutyTable:[],

        viewAdjustFlag: false,//是否可以查看调整过的应急----
        viewRelieveFlag: false,//查看解除----
        finishView:true,//已完结时，加入内容的按钮消失----
        operateFlag:false,//是否可以调整应急和解除应急

        //工作简报对话框
        workCurrentPage: 0,
        workPageSize:5,
        workTotalItem: 0,
        workBriefVisible: false,
        workTempForm: {
          id: '',
          signTime: '',
          defenseWork: '',
          dutyPeople: '',
          disasterSituation: '',
          emergency: '',
          emergencyHandle: '',
          emergencyAfter: ''
        },
        workRules: {
          signTime: [{required: true, message: '请选择上报时间', trigger: 'change'}],
          defenseWork: [{required: true, message: '请选择填入内容', trigger: 'change'}],
          dutyPeople: [{required: true, message: '请选择填入内容', trigger: 'change'}],
          disasterSituation: [{required: true, message: '请选择填入内容', trigger: 'change'}],
          emergency: [{required: true, message: '请选择填入内容', trigger: 'change'}],
          emergencyHandle: [{required: true, message: '请选择填入内容', trigger: 'change'}],
          emergencyAfter: [{required: true, message: '请选择填入内容', trigger: 'change'}],
        },
        workDialTitle: '',
        workUpdateFlag: false,

        //查看本公司和子公司的工作简报
        deptBriefVisible:false,
        deptTempForm:{
          editDept:'',
          deptOptions:[]
        },

        //物资表格数据
        goodsCurrentPage: 0,
        goodsTotalItem: 0,
        //添加物资对话框
        goodsVisible: false,
        goodsSummaryVisible: false,
        goodsTempForm: {
          searchName: '',
          resultArray: []
        },
        goodsLoading: false,
        goodsOptions: [],
        goodsDetailVisible: false,
        goodsSummaryData:[],
        //简报数据状态对应表
        workBriefStatus: [
          {id: 0, name: '未发布', labelType: 'danger'},
          {id: 1, name: '已发布', labelType: 'success'},
        ],

        //调整应急的数据
        viewAdjustVisible:false,
        adjustArray:[],

        //解除应急的数据
        relieveVisible:false,
        relieveForm:{
          relieveTime:'',
          relieveContent: '',
          relieveIssuer:''
        },
        relieveContentDefault:['公司各部门、主要控股企业：\n' + '      鉴于____(灾害名称)对宁波的影响逐渐减弱，经研究决定，于',
          '解除_____(类别)_____(级别)应急响应，同时要求各单位：\n' +
          '     1、抓紧做好恢复生产工作，确保正常生产开展。\n' +
          '     2、做好受损、受灾统计、汇总上报工作。\n' +
          '     3、对本次防御工作进行全面梳理，发现不足，总结经验，持续完善。'],
        personLoading:false,
        personOptions:[],
        relieveRules:{
          relieveTime: [{required: true, message: '请选择上报时间', trigger: 'change'}],
          relieveContent: [{required: true, message: '请填写上报内容', trigger: 'change'}],
          relieveIssuer: [{required: true, message: '请选择签发人', trigger: 'change'}],
        },

        //总结评价的数据
        viewSumFlag:false,//是否有总结记录----
        editSumFlag:false,//是否可编写总结----
        sumVisible:false,//对话框的开闭
        sumTempForm:{
          editDept:'',
          deptOptions:[]
        },

        //状态对应表
        statusTable:[
          {id:0,name:'未提交',labelType:'primary'},
          {id:1,name:'待审核',labelType:'warning'},
          {id:2,name:'被退回',labelType:'danger'},

          {id:3,name:'已签发',labelType:'success'},
          {id:4,name:'调整未提交',labelType:'primary'},
          {id:5,name:'调整待审核',labelType:'warning'},
          {id:6,name:'调整被退回',labelType:'danger'},
          {id:7,name:'调整已签发',labelType:'success'},

          {id:8,name:'解除未提交',labelType:'primary'},
          {id:9,name:'解除被退回',labelType:'success'},
          {id:10,name:'解除待审核',labelType:'warning'},

          {id:11,name:'待总结',labelType:'success'},
          {id:12,name:'总结未提交',labelType:'primary'},
          {id:13,name:'总结已提交',labelType:'success'},
          {id:14,name:'已完结',labelType:'info'},
        ],

        //缓存数据
        currentEmerId: '',
        startEmerId:'',
        currentPlanId: '',
        currentCompanyId:'',
        currentCompanyName:'',
        currentStatus:'',
        relieveContent:'',
        currentIssuer:{},
        currentInterval: '',
        leaderViewFlag:'',//领导视图，所有内容只能看
        publicCompanyId:'',//应急响应的发布公司

        //上传和下载
        fileUploadParams: {
          contentId: 0,
          contentType: 1
        },
      }
    },
    components:{
      FileListTable
    },
    created: function () {
      if (this.$route.params.emergencyId) {
        this.currentEmerId = this.$route.params.emergencyId;
        this.leaderViewFlag=this.$route.params.leaderViewFlag;
        this.searchEmerById();
      }
    },
    watch: {
      $route(to, from) {
        if (from.name === 'emerResponse' && this.$route.name === 'emergencyProcess') {
          if (this.$route.params.emergencyId) {
            this.currentEmerId = this.$route.params.emergencyId;
            this.leaderViewFlag=this.$route.params.leaderViewFlag;
            this.searchEmerById();
          }
        }
      }
    },
    methods: {
      //--------------------------------初始化操作-------------------------------
      searchEmerById: function () {
        let params = new URLSearchParams;
        params.append("id", this.currentEmerId);
        this.$http.post('planPublic/find', params).then(function (res) {
          if (res.data.data) {
            this.editEmerForm(res.data.data.list[0]);
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message({
            showClose: true,
            message: '网络错误，请尝试重登录',
            type: 'error'
          });
        }.bind(this));
      },
      editEmerForm: function (val) {
        this.currentPlanId = val.planId;
        this.form.emerResponseName = val.name;
        this.form.emerType = val.topTypeName + "/" + val.typeName;
        this.form.emerFlag = val.warnSignal;
        this.form.respLevel = val.respLevel;
        this.currentStatus=Number(val.status);
        this.form.emerResponseStatus = this.statusTable[Number(val.status)].name;
        this.form.signDate = this.transferTime(val.signDate, null, true);
        this.publicCompanyId=val.companyId;
        this.currentIssuer={value:val.signerUserId,label:val.signerUserName};
        this.currentCompanyId=this.$tool.getStorage('LOGIN_USER').companyId;
        this.currentCompanyName=this.form.company=this.$tool.getStorage('LOGIN_USER').companyName;
        this.currentInterval = val.timeInterval;
        this.form.interval = val.timeInterval + " 小时/次";
        this.startEmerId=val.startPlanId?val.startPlanId:this.currentEmerId;
        if(val.relieveNotice){
          this.relieveContent=val.relieveNotice.content;
        }
        this.fileUploadParams.contentId = this.startEmerId;
        this.loadFile();

        this.searchWorkBrief();
        this.searchEmerGoods();
        this.searchResponse();
        this.searchAllDutyTable();


//        不做判断，全都为只能查看模式


      },
      searchWorkBrief: function () {
        let params = new URLSearchParams;
        params.append("pageCurrent", 1);
        this.workCurrentPage = 1;
        this.workBriefSendRequest(params);
      },
      searchEmerGoods: function () {
        let params = new URLSearchParams;
        params.append("pageCurrent", 1);
        this.goodsCurrentPage = 1;
        this.emerGoodsSendRequest(params);
      },

      //--------------------------------响应应急------------------------------
      searchResponse:function () {
        this.responseTable=[];
        this.$http.get('planPublic/getEmgPlanInfo/'+this.currentEmerId).then(function (res) {
          if(res.data.success){
            this.responseTable=res.data.data.emgPlanPublicResponses;
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
        }.bind(this));
      },
      searchAllDutyTable:function () {
        this.allDutyTable=[];
        let params = new URLSearchParams;
        params.append("planPublicId", this.currentEmerId);
        this.$http.post('duty/find', params).then(function (res) {
          if (res.data.success) {
            this.allDutyTable=res.data.data.list;
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
        }.bind(this));
      },
      responseTimeFormat:function (row) {
        return this.transferTime(row.responseTime,null,true);
      },
      dutyTimeFormat:function (row) {
        return this.transferTime(row.dutyDate);
      },
      //-------------------------------表单事件----------------------------------
      viewBeginMessage: function () {
        this.$router.push({name: 'viewEmergency', params: {emergencyId: this.startEmerId, onlyShow: true}});
      },
      viewAdjustMessage: function () {
        let params=new URLSearchParams;
        params.append("startPlanId",this.startEmerId);
        params.append("history",1);
        this.$http.post('planPublic/find', params).then(function (res) {
          this.adjustArray=[];
          if(this.currentStatus<7){
            this.adjustArray.push({id:this.currentEmerId,name:this.form.emerResponseName,onlyShow:false});
          }else{
            this.adjustArray.push({id:this.currentEmerId,name:this.form.emerResponseName,onlyShow:true});
          }
          if (res.data.success) {
            for(let i=0;i<res.data.data.list.length;i++){
              this.adjustArray.push({id:res.data.data.list[i].id,name:res.data.data.list[i].name,onlyShow:true});
            }
          }
          this.viewAdjustVisible=true;
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message({
            showClose: true,
            message: '网络错误，请尝试重登录',
            type: 'error'
          });
        }.bind(this));
      },
      viewSum:function () {
        let params=new URLSearchParams;
        params.append("parentId",this.currentCompanyId);
        params.append("type",1);//查子公司
        this.$http.post('dept/find', params).then(function (res) {
          this.sumTempForm.deptOptions=[];//这里存的都是公司
          if (res.data.success) {
            this.sumTempForm.deptOptions.push({value:this.currentCompanyId,label:this.currentCompanyName});
            this.sumTempForm.editDept={value:this.currentCompanyId,label:this.currentCompanyName};
            for(let i=0;i<res.data.data.length;i++){
              this.sumTempForm.deptOptions.push({value:res.data.data[i].id,label:res.data.data[i].name});
            }
            this.sumVisible=true;
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message({
            showClose: true,
            message: '查找公司失败',
            type: 'error'
          });
        }.bind(this));
      },

      //--------------------------------调整应急响应--------------------------
      viewAdjustItemClick:function (row) {
        this.viewAdjustVisible = false;
        this.adjustArray=[];
        this.$router.push({name: 'viewEmergency', params: {emergencyId: row.id, onlyShow: row.onlyShow}});
      },

      //-------------------------------工作简报----------------------------------
      viewWorkBrief: function () {
        let params=new URLSearchParams;
        params.append("parentId",this.currentCompanyId);
        params.append("type",1);
        this.$http.post('dept/find', params).then(function (res) {
          this.deptTempForm.deptOptions=[];
          if (res.data.success) {
            this.deptTempForm.deptOptions.push({value:this.currentCompanyId,label:this.currentCompanyName});
            this.deptTempForm.editDept={value:this.currentCompanyId,label:this.currentCompanyName};
            for(let i=0;i<res.data.data.length;i++){
              this.deptTempForm.deptOptions.push({value:res.data.data[i].id,label:res.data.data[i].name});
            }
            this.deptBriefVisible=true;
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message({
            showClose: true,
            message: '网络错误，请尝试重登录',
            type: 'error'
          });
        }.bind(this));
      },
      workItemView: function (row) {
        this.workUpdateFlag = false;
        this.workDialTitle = '查看工作简报';
        let dateTemp=new Date(row.originalTime);
        this.workTempForm.signTime = dateTemp;
        this.workTempForm.defenseWork = row.defenseWork;
        this.workTempForm.dutyPeople = row.dutyPeople;
        this.workTempForm.disasterSituation = row.disasterSituation;
        this.workTempForm.emergency = row.emergency;
        this.workTempForm.emergencyHandle = row.emergencyHandle;
        this.workTempForm.emergencyAfter = row.emergencyAfter;
        this.workBriefVisible = true;
      },
      workCurrentPageClick: function (val) {
        if (val) {
          this.workCurrentPage = val;
          let params = new URLSearchParams;
          params.append("pageCurrent", Number(val));
          this.workBriefSendRequest(params);
        }
      },
      workBriefSendRequest: function (params) {
        params.append("pageSize",5);
        params.append("companyId",this.$tool.getStorage('LOGIN_USER').companyId);
        params.append("planPublicId", this.startEmerId);
        this.$http.post('workBrief/find', params).then(function (res) {
          if (res.data.success) {
            this.workTotalItem = res.data.data.page.total;
            if(res.data.data.latelyReportTime){
              this.form.lastTime = this.transferTime(res.data.data.latelyReportTime, null, true);
              this.form.nextTime = this.transferTime(res.data.data.latelyReportTime + 3600 * 1000 * this.currentInterval, null, true);
            }
            this.form.workBriefTable = res.data.data.page.list;
            for (let i = 0; i < this.form.workBriefTable.length; i++) {
              this.form.workBriefTable[i].originalTime = this.form.workBriefTable[i].reportTime;
              this.form.workBriefTable[i].reportTime = this.transferTime(this.form.workBriefTable[i].reportTime, null, true);
            }
          } else {
            this.workCurrentPage = 0;
            this.workTotalItem = 0;
            this.form.lastTime = '无';
            this.form.nextTime = '无';
            this.form.workBriefTable = [];
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message({
            showClose: true,
            message: '网络错误，请尝试重登录',
            type: 'error'
          });
        }.bind(this));
      },
      //--------------------------------应急物资-----------------------------------
      viewSummary: function () {
        this.goodsSummaryVisible=true
        this.goodsSummaryData=[]
        var params=new URLSearchParams()
        var emgGoodsNames=new Array()
        if(this.form.goodsTable.length>0){
          for(var i=0;i<this.form.goodsTable.length;i++){
            emgGoodsNames[i]=this.form.goodsTable[i].name
          }
          params.append("emgGoodsNames",emgGoodsNames)
        }
        this.$http.post("emgGoods/findPlanPublicGoods",params).then(function (res) {
          if(res.data.success){
            this.goodsSummaryData=res.data.data
          }
        }.bind(this))
      },
      goodsItemView: function (row) {
        this.handleGoodsClick(row.name);
        this.goodsDetailVisible = true;
      },
      goodsCurrentPageClick: function (val) {
        if (val) {
          this.goodsCurrentPage = val;
          let params = new URLSearchParams;
          params.append("pageCurrent", Number(val));
          this.emerGoodsSendRequest(params);
        }
      },
      emerGoodsSendRequest: function (params) {
        params.append("planPublicId", this.startEmerId);
        this.$http.post('planPublicGoods/find', params).then(function (res) {
          if (res.data.success) {
            this.goodsTotalItem = res.data.data.total;
            this.form.goodsTable = res.data.data.list;
          } else {
            this.goodsCurrentPage = 0;
            this.goodsTotalItem = 0;
            this.form.goodsTable = [];
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message({
            showClose: true,
            message: '网络错误，请尝试重登录',
            type: 'error'
          });
        }.bind(this));
      },
      //查找该物资详情
      handleGoodsClick: function (val) {
        if (val) {
          this.$http.get('emgGoods/find?goodsName=' + val).then(function (res) {
            if (res.data.success) {
              this.goodsTempForm.resultArray = [];
              this.goodsTempForm.resultArray = res.data.data.list;
            } else {
              this.goodsTempForm.resultArray = [];
            }
          }.bind(this)).catch(function (err) {
            console.log(err);
          });
        }
      },
      //-----------------------------------上传文件---------------------------------
      loadFile: function () {
        var params = new URLSearchParams()
        params.append("contentId", this.fileUploadParams.contentId)
        params.append("contentType", this.fileUploadParams.contentType)
        this.$http.post("file/find", params).then(function (res) {
          if (res.data.success) {
            this.form.fileData = res.data.data
          }
        }.bind(this)).catch(function (err) {
        }.bind(this))
      },
      downloadFile: function (row) {
        const loading = this.$loading({
          lock: true,
          text: 'Loading',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        var params = new URLSearchParams()
        params.append("fId", row.fId)
        this.$http({ // 用axios发送post请求
          method: 'post',
          url: '/file/download', // 请求地址
          data: params, // 参数
          responseType: 'blob' // 表明返回服务器返回的数据类型
        }).then((res) => { // 处理返回的文件流
          //console.info(res)
          loading.close()
          const content = res
          const elink = document.createElement('a') // 创建a标签
          elink.download = row.fileName // 文件名
          elink.style.display = 'none'
          const blob = new Blob([res.data])
          elink.href = URL.createObjectURL(blob)
          document.body.appendChild(elink)
          elink.click() // 触发点击a标签事件
          document.body.removeChild(elink)
        })
      },

      //-------------------------------解除应急---------------------------------
      viewRelieveClick:function () {
        if(this.relieveContent){
          this.$router.push({name:'viewRelieve',params:{title:'关于解除'+this.form.emerType+this.form.respLevel+'的通知',content:this.relieveContent}})
        }else{
          this.$message.warning('暂无应急通知！');
        }
      },

      //---------------------------------退出---------------------------------
      returnClick: function () {
        this.$router.push({name:'emerResponse'});
      },
      dateFormat(row, column) {
        //.replace(/年|月/g, "-").replace(/日/g, " ")
        return new Date(row.uploadTime).Format("yyyy-MM-dd hh:mm").toLocaleString();
      },
    }
  }
</script>
<style>
</style>
