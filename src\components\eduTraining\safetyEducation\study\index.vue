<template>
  <el-container class="container">
    <el-main>

      <el-tabs v-model="activeName" @tab-click="changeType">
        <el-tab-pane label="文章学习" name="article">
          <el-tabs :tab-position="tabPosition" style="height: 450px;"@tab-click="changeStatusArticle">
            <el-tab-pane label="未学习">
              <el-row style="margin-top:20px;">
                <el-table
                  border
                  :data="type.article.tableData.list"
                  style="width: 100%">
                  <el-table-column
                    type="index"
                    label="编号"
                    width="100"
                    align="center"
                    label-class-name="header-style">
                  </el-table-column>
                  <el-table-column
                    prop="studyName"
                    label="文章标题"
                    min-width="150"
                    label-class-name="header-style">
                  </el-table-column>
                  <el-table-column
                    prop="updateTime"
                    :formatter="formatDateTime"
                    label="时间"
                    width="150"
                    label-class-name="header-style">
                  </el-table-column>
                  <el-table-column
                    prop="integral"
                    label="积分"
                    width="150"
                    label-class-name="header-style">
                  </el-table-column>
                  <el-table-column
                    fixed="right" label="操作"
                    label-class-name="header-style"
                    align="left" width="100">
                    <template slot-scope="scope">
                      <el-button size="mini" type="success" @click.native="readArticle(scope.row)">查看</el-button>
                    </template>
                  </el-table-column>
                </el-table>
                <el-pagination
                  background
                  layout="prev, pager, next"
                  :current-page="type.article.tableData.pageNum"
                  :page-size="type.article.form.pageSize"
                  :total="type.article.tableData.total"
                  @current-change ="disasterPageArticle">
                </el-pagination>
              </el-row>
            </el-tab-pane>
            <el-tab-pane label="已学习">
              <el-row style="margin-top:20px;">
                <el-table
                  border
                  :data="type.article.tableData.list"
                  style="width: 100%">
                  <el-table-column
                    type="index"
                    label="编号"
                    width="100"
                    align="center"
                    label-class-name="header-style">
                  </el-table-column>
                  <el-table-column
                    prop="studyName"
                    label="文章标题"
                    min-width="150"
                    label-class-name="header-style">
                  </el-table-column>
                  <el-table-column
                    prop="updateTime"
                    :formatter="formatDateTime"
                    label="时间"
                    width="150"
                    label-class-name="header-style">
                  </el-table-column>
                  <el-table-column
                    prop="integral"
                    label="积分"
                    width="150"
                    label-class-name="header-style">
                  </el-table-column>
                  <el-table-column
                    fixed="right" label="操作"
                    label-class-name="header-style"
                    align="left" width="100">
                    <template slot-scope="scope">
                      <el-button size="mini" type="success" @click.native="readArticle(scope.row)">查看</el-button>
                    </template>
                  </el-table-column>
                </el-table>
                <el-pagination
                  background
                  layout="prev, pager, next"
                  :current-page="type.article.tableData.pageNum"
                  :page-size="type.article.form.pageSize"
                  :total="type.article.tableData.total"
                  @current-change ="disasterPageArticle">
                </el-pagination>
              </el-row>
            </el-tab-pane>
          </el-tabs>
        </el-tab-pane>
        <el-tab-pane label="视频学习" name="video">
          <el-tabs :tab-position="tabPosition" style="height: 450px;"@tab-click="changeStatusVideo">
            <el-tab-pane label="未学习">
              <el-row style="margin-top:20px;">
                <el-table
                  border
                  :data="type.video.tableData.list"
                  style="width: 100%">
                  <el-table-column
                    type="index"
                    label="编号"
                    width="100"
                    align="center"
                    label-class-name="header-style">
                  </el-table-column>
                  <el-table-column
                    prop="studyName"
                    label="视频标题"
                    min-width="150"
                    label-class-name="header-style">
                  </el-table-column>
                  <el-table-column
                    prop="updateTime"
                    :formatter="formatDateTime"
                    label="时间"
                    width="150"
                    label-class-name="header-style">
                  </el-table-column>
                  <el-table-column
                    label="缩略图"
                    width="200"
                    label-class-name="header-style">
                    <template slot-scope="scope">
                      <img :src="scope.row.studyImg" style="height:100px;width:200px;" />
                    </template>
                  </el-table-column>
                  <el-table-column
                    fixed="right" label="操作"
                    label-class-name="header-style"
                    align="left" width="100">
                    <template slot-scope="scope">
                      <el-button size="mini" type="success" @click.native="readVideo(scope.row)">查看</el-button>
                    </template>
                  </el-table-column>
                </el-table>
                <el-pagination
                  background
                  layout="prev, pager, next"
                  :current-page="type.video.tableData.pageNum"
                  :page-size="type.video.form.pageSize"
                  :total="type.video.tableData.total"
                  @current-change ="disasterPageVideo">
                </el-pagination>
              </el-row>
            </el-tab-pane>
            <el-tab-pane label="学习中">
              <el-row style="margin-top:20px;">
                <el-table
                  border
                  :data="type.video.tableData.list"
                  style="width: 100%">
                  <el-table-column
                    type="index"
                    label="编号"
                    width="100"
                    align="center"
                    label-class-name="header-style">
                  </el-table-column>
                  <el-table-column
                    prop="studyName"
                    label="视频标题"
                    min-width="150"
                    label-class-name="header-style">
                  </el-table-column>
                  <el-table-column
                    label="进度"
                    min-width="150"
                    label-class-name="header-style">
                    <template slot-scope="scope">
                      <el-progress :text-inside="true" :stroke-width="24" :percentage="parseInt(scope.row.courseSchedule) || 0" status="success"></el-progress>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="updateTime"
                    :formatter="formatDateTime"
                    label="时间"
                    width="150"
                    label-class-name="header-style">
                  </el-table-column>
                  <el-table-column
                    label="缩略图"
                    width="200"
                    label-class-name="header-style">
                    <template slot-scope="scope">
                      <img :src="scope.row.studyImg" style="height:100px;width:200px;" />
                    </template>
                  </el-table-column>
                  <el-table-column
                    fixed="right" label="操作"
                    label-class-name="header-style"
                    align="left" width="100">
                    <template slot-scope="scope">
                      <el-button size="mini" type="success" @click.native="readVideo(scope.row)">查看</el-button>
                    </template>
                  </el-table-column>
                </el-table>
                <el-pagination
                  background
                  layout="prev, pager, next"
                  :current-page="type.video.tableData.pageNum"
                  :page-size="type.video.form.pageSize"
                  :total="type.video.tableData.total"
                  @current-change ="disasterPageVideo">
                </el-pagination>
              </el-row>
            </el-tab-pane>
            <el-tab-pane label="已学习">
              <el-row style="margin-top:20px;">
                <el-table
                  border
                  :data="type.video.tableData.list"
                  style="width: 100%">
                  <el-table-column
                    type="index"
                    label="编号"
                    width="100"
                    align="center"
                    label-class-name="header-style">
                  </el-table-column>
                  <el-table-column
                    prop="studyName"
                    label="视频标题"
                    min-width="150"
                    label-class-name="header-style">
                  </el-table-column>
                  <el-table-column
                    prop="updateTime"
                    :formatter="formatDateTime"
                    label="时间"
                    width="150"
                    label-class-name="header-style">
                  </el-table-column>
                  <el-table-column
                    label="缩略图"
                    width="200"
                    label-class-name="header-style">
                    <template slot-scope="scope">
                      <img :src="scope.row.studyImg" style="height:100px;width:200px;" />
                    </template>
                  </el-table-column>
                  <el-table-column
                    fixed="right" label="操作"
                    label-class-name="header-style"
                    align="left" width="100">
                    <template slot-scope="scope">
                      <el-button size="mini" type="success" @click.native="readVideo(scope.row)">查看</el-button>
                    </template>
                  </el-table-column>
                </el-table>
                <el-pagination
                  background
                  layout="prev, pager, next"
                  :current-page="type.video.tableData.pageNum"
                  :page-size="type.video.form.pageSize"
                  :total="type.video.tableData.total"
                  @current-change ="disasterPageVideo">
                </el-pagination>
              </el-row>
            </el-tab-pane>
          </el-tabs>
        </el-tab-pane>
        <el-tab-pane label="用户积分记录" name="integral">
          <el-row style="margin-top:20px;">
            <el-table
              border
              :data="type.integral.tableData.list"
              style="width: 100%">
              <el-table-column
                type="index"
                label="编号"
                width="100"
                align="center"
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                label="类型"
                min-width="150"
                label-class-name="header-style">
                <template slot-scope="scope">
                  <span v-if="scope.row.studyType == 0">视频学习</span>
                  <span v-if="scope.row.studyType == 1">文章学习</span>
                </template>
              </el-table-column>
              <el-table-column
                prop="studyName"
                label="标题"
                min-width="150"
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="updateTime"
                :formatter="formatDateTime"
                label="时间"
                width="150"
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="integral"
                label="积分"
                width="150"
                label-class-name="header-style">
              </el-table-column>
            </el-table>
            <el-pagination
              background
              layout="prev, pager, next"
              :current-page="type.integral.tableData.pageNum"
              :page-size="type.integral.form.pageSize"
              :total="type.integral.tableData.total"
              @current-change ="disasterPageIntegral">
            </el-pagination>
          </el-row>
        </el-tab-pane>
      </el-tabs>

    </el-main>
  </el-container>
</template>

<script>
  export default {
    name: '',
    data() {
      return {
        activeName: 'article',
        tabPosition : 'left',
        type : {
          // 文章---未学习、已学习用的是同一个表格
          article : {
            // 当前tabs状态
            tabsStatus : '未学习',
            // 搜索
            form : {
              // 当前页
              pageCurrent : 1,
              // 页数大小
              pageSize : 3,
            },
            tableData : {},
          },
          // 视频---未学习、学习中，已学习用的是同一个表格
          video : {
            // 当前tabs状态
            tabsStatus : '未学习',
            // 搜索
            form : {
              // 当前页
              pageCurrent : 1,
              // 页数大小
              pageSize : 3,
            },
            tableData : {},
          },
          // 用户积分
          integral : {
            // 搜索
            form : {
              // 当前页
              pageCurrent : 1,
              // 页数大小
              pageSize : 3,
            },
            tableData : {},
          },
        },
        // 角色 0 组织者或公司      1 部门        2  班组
        role : 0,
      }
    },
    mounted(){
      this.init();
    },
    watch:{
      $route(to,from){
        if(to.name === 'safetyEducationStudyIndex') {
         if (from.name == 'safetyEducationStudyVideoView'){
            // 搜索
            this.searchVideo();
          } else {
            // 搜索
            this.searchArticle();
          }
        }
      }
    },

    methods:{
      // 改变类型--文章、视频
      changeType(tab, event) {
//        console.log(tab, event);
      /*  if(tab.label == '文章学习'){
          this.searchArticle();
        } else if (tab.label == '视频学习'){
          this.searchVideo();
        } else if (tab.label == '用户积分记录'){
          this.searchIntegral();
        }*/
        if(tab.name == 'article'){
          this.searchArticle();
        } else if (tab.name == 'video'){
          this.searchVideo();
        } else if (tab.name == 'integral'){
          this.searchIntegral();
        }
      },

      // 初始化
      init(){
        // 搜索
        this.searchArticle();
      },
      // 格式化时间
      formatDateTime(row, column, cellValue){
        let pro = column.property;
        let num = 10;
        let str = this.$tool.formatDateTime(row[pro]) || '';
        return str ? str.substring(0, num) : str;
      },

      // 文章--改变状态--进行中、已完成
      changeStatusArticle(tab, event){
        this.type.article.form.pageCurrent = 1;
        this.type.article.tabsStatus = tab.label;
        this.searchArticle();
      },
      // 搜索--文章--已完成
      searchArticle(){
        let url = '';
        if (this.type.article.tabsStatus == '未学习'){
          url = 'eduUserStudyGetUnStudyNewsList';
        } else if (this.type.article.tabsStatus == '已学习'){
          url = 'eduUserStudyGetCompleteStudyNewsList';
        }
        if (url == ''){
          this.$message({
            type : 'error',
            message : '请选择学习状态'
          })
          return
        }
        let article = this.type.article;
//        console.log(111,article)
        if(!article.form){
          return
        }
        this.$store.dispatch(url, article.form).then(function(res){
          if(res.success){
            article.tableData = res.data;
          } else {
            this.$message({
              type : 'error',
              message : res.message || '错误'
            })
          }
        }.bind(this));
      },
      // 分页--文章--已完成
      disasterPageArticle(page){
        this.type.article.form.pageCurrent = page;
        this.searchArticle();
      },
      // 查看--文章--进行中
      readArticle(row){
        let name = 'safetyEducationStudyArticleView';
        let params = {
//          id : row.id,
          id : row.studyId,
          progress : row.status,
          status : 'view',
        }
        this.$router.push({ name : name, params : params})
      },






      // 视频--改变状态--进行中、已完成
      changeStatusVideo(tab, event){
        this.type.video.form.pageCurrent = 1;
        this.type.video.tabsStatus = tab.label;
        this.searchVideo();
      },
      // 搜索--文章--已完成
      searchVideo(){
        let url = '';
        let status = this.type.video.tabsStatus;
        if (status == '未学习'){
          url = 'eduUserStudyGetUnStudyCourseList';
        } else if (status == '学习中'){
          url = 'eduUserStudyGetPendingStudyCourseList';
        } else if (status == '已学习'){
          url = 'eduUserStudyGetCompleteStudyCourseList';
        }
        if (url == ''){
          this.$message({
            type : 'error',
            message : '请选择学习状态'
          })
          return
        }
        let video = this.type.video;
        this.$store.dispatch(url, video.form).then(function(res){
          if(res.success){
            video.tableData = res.data;
          } else {
            this.$message({
              type : 'error',
              message : res.message || '错误'
            })
          }
        }.bind(this));
      },
      // 分页--文章--已完成
      disasterPageVideo(page){
        this.type.video.form.pageCurrent = page;
        this.searchVideo();
      },
      // 查看--文章--进行中
      readVideo(row){
        let name = 'safetyEducationStudyVideoView';
        let params = {
          id : row.studyId,
          progress : row.status,
          status : 'view',
          courseStop : row.courseStop,
        }
        this.$router.push({ name : name, params : params})
      },




      // 搜索--积分--已完成
      searchIntegral(){
        let url = 'eduUserStudyGetUserStudyLogs';
        let integral = this.type.integral;
        this.$store.dispatch(url, integral.form).then(function(res){
          if(res.success){
            integral.tableData = res.data;
          } else {
            this.$message({
              type : 'error',
              message : res.message || '错误'
            })
          }
        }.bind(this));
      },
      // 分页--积分--已完成
      disasterPageIntegral(page){
        this.type.integral.form.pageCurrent = page;
        this.searchIntegral();
      },

    }
  }
</script>

<style>
  .container{
    background:#fff;
    padding:0 20px;
  }
  .row{
    margin-top:10px;
  }
</style>
