<template>
  <div id="updateRole">
    <div style="top: 20px;left:10px;right:10px;position:absolute;background-color: white;padding-bottom: 40px;min-height: 100%">

      <el-col :span="12" :offset="6" style="text-align: center;margin-bottom: 20px;margin-top:30px;font-size: large;letter-spacing: 2px;color:#3576AA;border-left:5px solid #EB9E05">修改角色</el-col>

      <el-col :span="12" :offset="6">
        <el-form ref="ruleForm" :model="form" :rules="rules" label-width="80px">
          <el-col :span="24">
            <el-form-item label="角色名称" prop="roleName">
              <el-input v-model="form.roleName" placeholder="请填入角色名称"></el-input>
            </el-form-item>
            <el-form-item label="角色排序" prop="sort">
              <el-input v-model="form.sort" placeholder="请填入角色排序"></el-input>
            </el-form-item>
            <el-form-item label="备注" prop="other">
              <el-input v-model="form.other"></el-input>
            </el-form-item>
            <p style="font-size: 15px;margin-left: 10px">功能权限:</p>
            <el-tree
              :data="permissionList"
              :props="defaultProps"
              node-key="id"
              ref="tree"
              highlight-current
              default-expand-all
              show-checkbox>
            </el-tree>
            <el-form-item style="margin-top: 100px">
              <el-button style="float: right;margin-left: 20px" @click="returnClick">返回</el-button>
              <el-button v-if="powerBtns.includes('updateRoleBtn')"type="primary" style="float: right" @click="submitClick('ruleForm')">修改</el-button>
            </el-form-item>
          </el-col>
        </el-form>
      </el-col>

    </div>
  </div>
</template>
<script>
  export default {
    name: 'updateRole',
    data() {
      return {
        form:{
          roleName:null,
          sort:null,
          other:null
        },
        roleId:'',
        // 权限按钮
        powerBtns : [],
        checkFlag:false,
        permissionIds:[],
        defaultProps: {
          children: 'list',
          label: 'permissionName'
        },
        rules:{
          roleName:[
            {required:true,message:'请输入角色名称',trigger:'change'}
          ],
        }
      }
    },
    computed:{
      permissionList:function () {
        this.init();
        return this.$store.state.sysManageData.permissionList;
      }
    },
    mounted:function () {
      this.init();
      this.$store.dispatch("getPermissionList");
    },
    created:function () {
      this.init();
      if(this.$route.params.roleId){
        this.roleId=this.$route.params.roleId;
        this.getRoleData();
      }
    },
    watch:{
      $route(to, from) {
        if ((from.path === '/manage-menu/role-manage')&&this.$route.name==='updateRole') {
          this.init();
          if(this.$route.params.roleId){
            this.roleId=this.$route.params.roleId;
            this.getRoleData();
          }
        }
      }
    },
    methods:{
      init(){
        this.powerBtns = this.$tool.getPowerBtns2URL('manageMenu', '/manage-menu/user-manage', '/manage-menu/role-manage');

      },
      getRoleData:function () {
        let params=new URLSearchParams;
        params.append("roleId",this.roleId);
        this.$http.post('role/detail',params).then(function (res) {
          if(res.data.data){
            this.form.roleName=res.data.data.role;
            this.form.sort =res.data.data.sort;
            this.form.other=res.data.data.description;
            this.permissionIds=res.data.data.leafIds;
            this.setCheckedId();
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message({
            showClose: true,
            message: '网络错误，请尝试重登录',
            type: 'error'
          });
        }.bind(this));
      },
      setCheckedId:function () {
        this.$refs.tree.setCheckedKeys(this.permissionIds);
      },
      submitClick:function (formName) {
        this.$refs[formName].validate((valid) => {
          if (valid) {
            if(this.$refs.tree.getCheckedKeys().length){
//              let onlyLeafArray=this.$refs.tree.getCheckedKeys(true);
              let permissionArray=this.$refs.tree.getCheckedKeys();
              permissionArray=permissionArray.concat(this.$refs.tree.getHalfCheckedKeys());//子节点没有全选的节点，不加上就没这个大目录了
              let params = new URLSearchParams;
              params.append("id",this.roleId);
              params.append("role",this.form.roleName);
              params.append("sort",this.form.sort);
              if(this.form.other!==null){
                params.append("description",this.form.other);
              }
              let startStr='sysRolePermissions[';
              let endStr='].permissionId';
              for(let i=0;i<permissionArray.length;i++){
                params.append(startStr+i+endStr,permissionArray[i]);
                params.append(startStr+i+'].roleId',this.roleId);
              }
              this.$http.post('role/update',params).then(function (res) {
                this.$message({
                  showClose: true,
                  message: '修改角色成功！',
                  type: 'success'
                });
                this.$router.push({name:'roleManage'});
              }.bind(this)).catch(function (err) {
                console.log(err);
                this.$message({
                  showClose: true,
                  message: '修改失败',
                  type: 'error'
                });
              }.bind(this));
            }else {
              this.$message({
                showClose: true,
                message: '请选择至少一项权限！',
                type: 'warning'
              });
            }
          } else {
            return false;
          }
        });
      },
      returnClick:function () {
        this.resetForm('ruleForm');
        this.$router.go(-1);
      },
      resetForm(formName) {
        this.$refs[formName].resetFields();
        this.$refs.tree.setCheckedKeys([]);
      }
    }
  }
</script>
<style>
</style>
