/*created by m<PERSON><PERSON><PERSON> on 2018.4.10*/
/*background ,button ,table style*/
/*整体页面布局，适用于子模块*/
*{
  margin: 0;padding: 0;
}
.background-body{height:1920px;overflow:hidden;}

.title-fixed{position:absolute;top:0;left:80px;right:0;height:49px;min-width: 1000px;background-color: #fff;border-bottom: 1px solid #ccc;}
.subtitle-fixed{position:absolute;top:50px;left:0;right:0;height:20px;min-width: 1000px;background-color: #ECECEC}
.menu-fixed{position:absolute;top:0;left:0;bottom:-20px;width:150px;background-color: #0f6fc6;overflow:auto;}
.home-block{width: 100%;height: 50px;}
.home-block:hover{background-color: #0f6fc6;cursor: pointer}
.content-fixed{position:absolute;left:150px;right:0;top:50px;bottom:-20px;overflow:auto;min-width: 1000px;background-color: #f2f2f2}

.user-btn { background-color: rgba(0,0,0,0) !important; border-color: rgba(0,0,0,0) !important; text-decoration: underline !important; margin-left: 0 !important; }
/*具体内容页面的背景样式*/
.background-style{top: 0;left:0;right:0;position:absolute;background-color: #fff;padding-bottom: 40px;min-height: 100%;}
.search-bar{width: 100%;min-height: 50px;padding: 20px 20px 0 20px;display: block}
/*新增区域，*/
/*搜索按钮，和搜索按钮样式相近的副按钮*/
.search-btn { background-color: #0168B7 !important; border-color: #0168B7 !important; width: 120px !important; color: white !important; }
.button-group { background-color: #40A6E8 !important; border-color: #40A6E8 !important; width: 100px !important; color: white !important; margin-right: 20px !important; }
.button-group:hover { background-color: #0168B7 !important; border-color: #0168B7 !important; }
/*带背景色的大标题*/
.primary-background-title{text-align: center;margin-bottom: 20px;margin-top:20px;font-size: large;letter-spacing: 2px;color:#3576AA;border-left:5px solid #049ff1;border-radius: 5px;background-color: rgb(236,248,255);height: 50px;line-height: 50px}
.success-background-title{text-align: center;margin-bottom: 20px;margin-top:20px;font-size: large;letter-spacing: 2px;color:#10A54A;border-left:5px solid #67C23A;border-radius: 5px;background-color: rgb(240,249,235);height: 50px;line-height: 50px}
.warning-background-title{text-align: center;margin-bottom: 20px;margin-top:20px;font-size: large;letter-spacing: 2px;color:#D98D1C;border-left:5px solid #E6A23C;border-radius: 5px;background-color: rgb(253,246,236);height: 50px;line-height: 50px}
/*表格样式*/
.header-style {background-color: #f2f2f2 !important;height: 20px !important; color: #0f6fc6 !important; font-size: 13px !important; padding-top: 4px !important; text-align: center !important; line-height: 20px !important; }
.header-style-former {background-color: rgb(45, 87, 174) !important;height: 30px !important; color: white !important; font-size: 15px !important; padding-top: 2px !important; text-align: center !important; line-height: 30px !important; }
.el-table__body tr > td { padding: 3px !important;}
.el-table__body tr.current-row>td {background: rgb(250,229,164)!important;}
/*内嵌表格头样式*/
.inner-header-style { height: 20px !important; font-size: 14px !important; padding-top: 2px !important; text-align: center !important; line-height: 20px !important; }
/*台账表格样式，很原始很朴素*/
.simple-table { width: 100%; line-height: 40px; border-collapse: collapse; padding:2px;}
.simple-table, .simple-table tr th, .simple-table tr td { border:1px solid #000; min-height: 50px;min-width:50px;padding: 0px;}
.simple-table tr th { text-align: center;font-weight:bold;}
.simple-table tr td { text-align: center;}
/*侧边框按钮样式*/
.cancel-icon{ margin: 15px 20px 15px 0; float: right; }
.cancel-icon:hover{ cursor: pointer; }
/*标签卡样式，可用于内容过多的工作台面*/
.card-shadow-style {border: 1px solid #EFEDED; box-shadow: 5px 5px 5px #dfdfdf; border-radius: 5px; margin:10px 0 10px 0}
