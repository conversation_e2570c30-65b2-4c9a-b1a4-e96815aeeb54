<template>
    <el-container class="container">
      <el-main>
        <el-form ref="form" label-width="100px" :rules="rules" :model="form">
          <el-row type="flex" class="row">
            <el-col :span="8">
              <el-form-item label="名称" prop="title" style="margin: 0px"  >
                <span v-if="pageStatus === 'view'">{{form.title}}</span>
                <el-input v-if="pageStatus === 'edit'" v-model="form.title"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="年份" prop="createYear"style="margin: 0px">
                <span v-if="pageStatus === 'view'">{{$tool.formatDateTime(form.createYear).substring(0,4)}}</span>
                <el-date-picker
                  v-if="pageStatus === 'edit'"
                  v-model="form.createYear"
                  type="year"
                  placeholder="选择日期">
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="开始时间" prop="startTime" style="margin: 0px">
                <span v-if="pageStatus === 'view'">{{$tool.formatDateTime(form.startTime).substring(0,10)}}</span>
                <el-date-picker
                  v-if="pageStatus === 'edit'"
                  v-model="form.startTime"
                  type="date"
                  placeholder="选择日期">
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex" class="row">
            <el-col :span="8">
              <el-form-item label="截止时间" prop="endTime" style="margin: 0px">
                <span v-if="pageStatus === 'view'">{{$tool.formatDateTime(form.endTime).substring(0,10)}}</span>
                <el-date-picker
                  v-if="pageStatus === 'edit'"
                  v-model="form.endTime"
                  type="date"
                  placeholder="选择日期">
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="调查对象" style="margin: 0px">
                <span v-if="pageStatus === 'view'">{{form.respondent}}</span>
                <el-input
                  v-if="pageStatus === 'edit'" v-model="form.respondent">
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <!--<template v-if="form.eduRequirementSurveyItems.length > 0 && form.id">-->
          <template>
            <el-row type="flex" class="row">
              <el-col :span="24">
                <el-form-item label="目前参与调查总人数" label-width="200px" style="margin-bottom: 0px;">
                  <span>{{num}}</span>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row type="flex" class="row" v-if="pageStatus === 'edit'">
              <el-button size="mini" icon="el-icon-plus" type="primary"
                         @click="assist.eduRequirementSurveyItems.isShow = true">调查项目</el-button>
              <el-button type="danger"  @click="deleteBatch" size="mini">批量删除</el-button>
              <template v-if="assist.eduRequirementSurveyItems.isShow">
                <el-input v-model="assist.eduRequirementSurveyItems.data" style="width:200px;margin:0 10px;"></el-input>
                <el-button type="success" size="mini" icon="el-icon-check" @click="eduRequirementSurveyItemsOkBtnClickHandle"></el-button>
                <el-button type="info" size="mini" icon="el-icon-close"
                           @click="assist.eduRequirementSurveyItems.isShow = false"></el-button>
              </template>
            </el-row>
            <el-row type="flex" class="row">
              <el-col :span="24">
                <el-form-item  prop="eduRequirementSurveyItems">
                  <el-table
                    @selection-change="handleSelectionChange"
                    :data="form.eduRequirementSurveyItems">
                    <el-table-column
                      type="selection"
                      width="50"
                      fixed
                      label-class-name="header-style">
                    </el-table-column>
                    <el-table-column
                      type="index"
                      label="序号"
                      width="100">
                    </el-table-column>
                    <el-table-column
                      prop="items"
                      label="项目"
                      show-overflow-tooltip
                      width="300">
                    </el-table-column>
                    <el-table-column
                      prop="count"
                      v-if="form.status > 0"
                      label="选择人数"
                      width="100">
                    </el-table-column>
                    <el-table-column
                      label="操作"
                      width="300"
                      align="center"
                      label-class-name="inner-header-style">
                      <template slot-scope="scope">
                        <el-button type="primary"
                                   v-if="form.status > 0"
                                   size="mini" @click="lookNumHandle(scope.row)">查看选择人员</el-button>
                        <el-button type="danger"  size="mini"
                                   v-if="pageStatus === 'edit'"
                                   @click.native.prevent="emgHandleListsDelHandle(scope.$index, scope.row, form.eduRequirementSurveyItems)">删除</el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-form-item>
              </el-col>
            </el-row>
          </template>
          <chooseStaff
            ref="chooseStaff"
            @selectedRows="selectedRows"></chooseStaff>
          <el-row type="flex" class="row" justify="center">
            <template v-if="pageStatus === 'edit'">
              <!--未发布-->
              <template v-if="form.status === 0">
                <el-button
                  @click="saveBtnClickHandle({ status : '0'})"
                  size="small" :span="2" type="success" >保存</el-button>
                <!--仅当未发布的时候存在-->
                <el-button
                  @click="submitBtnClickHandle()"
                  size="small" :span="2" type="primary">提交</el-button>
              </template>
              <!--进行中-->
              <!--<template v-if="form.status === 1">
                &lt;!&ndash;发布者&ndash;&gt;
                <el-button
                  @click="saveBtnClickHandle({ status : '0' })"
                  size="small" :span="2" type="primary" >保存2</el-button>
              </template>-->
            </template>
            <template v-if="pageStatus === 'view' && !$route.params.companyId">
              <!--进行中-->
              <template v-if="form.status === 1">
                <el-button
                  @click="overBtnClickHandle"
                  size="small" :span="2" type="danger" >结束</el-button>
                <el-button
                  @click="suggestHandle"
                  size="small" :span="2" type="primary" >建议需求</el-button>
              </template>
              <!--进行中-->
              <template v-if="form.status === 2">
                <el-button
                  @click="suggestHandle"
                  size="small" :span="2" type="primary" >建议需求</el-button>
              </template>
            </template>
            <el-button size="small" :span="2"  @click="$router.back();">返回</el-button>
          </el-row>
        </el-form>
      </el-main>
      <el-footer>
        <!--查看人员-->
        <el-dialog width="70%" :title="lookNumDialog.title" :visible.sync="lookNumDialog.isShow">
          <el-table :data="lookNumDialog.tableData.list">
            <el-table-column label="序号" type="index" width="150"></el-table-column>

            <el-table-column property="eduUser.username" label="姓名"></el-table-column>
            <el-table-column property="department" label="所属部门" show-overflow-tooltip></el-table-column>
            <el-table-column property="otherRequirement" label="其他需求" width="150"></el-table-column>
            <el-table-column property="advice" label="意见建议" width="150"></el-table-column>
            <el-table-column property="eduRequirementItemsSurveyResultss[0].people" label="代表人数" show-overflow-tooltip></el-table-column>
          </el-table>
          <el-pagination
            background
            layout="prev, pager, next"
            :current-page="lookNumDialog.tableData.pageNum"
            :page-size="lookNumDialog.form.pageSize"
            :total="lookNumDialog.tableData.total"
            @current-change ="lookNumDialogChangeHandle">
          </el-pagination>
        </el-dialog>
        <!--建议需求-->
        <el-dialog :title="suggestDialog.title" :visible.sync="suggestDialog.isShow">
          <el-table :data="suggestDialog.tableData.list">
            <el-table-column label="序号" type="index" width="100"></el-table-column>
            <el-table-column property="eduUser.username" label="姓名"></el-table-column>
            <el-table-column property="department" label="所属部门"></el-table-column>
            <el-table-column property="otherRequirement" label="其他需求" width="150"></el-table-column>
            <el-table-column property="advice" label="意见建议" width="150"></el-table-column>
          </el-table>
          <el-pagination
            background
            layout="prev, pager, next"
            :current-page="suggestDialog.tableData.pageNum"
            :page-size="suggestDialog.form.pageSize"
            :total="suggestDialog.tableData.total"
            @current-change ="suggestDialogChangeHandle">
          </el-pagination>
        </el-dialog>
      </el-footer>
    </el-container>
</template>

<script>

    import chooseStaff from '@/components/common/chooseStaff'
    export default {
      components: {
        chooseStaff,
      },
      data(){
        let that = this;
        return {
          // 发布者发布的数据
          form : {
            id : '',
            // 名称
            title : '',
            // 年份
            createYear : '',
            // 开始时间
            startTime : '',
            // 截止时间
            endTime : '',
            // 调查对象
            respondent  : '全体员工',
            // 调查对象---表格
            eduRequirementRespondents : [],
            // 调查项目
            eduRequirementSurveyItems : [],
            // 状态  0 未发布 1 进行中 2 已完成
            status : 0,
          },
          rules: {
            title: [
              {required: true, message: '请输入名称', trigger: 'blur'},
            ],
            createYear: [
              { type: 'date', required: true, message: '请选择日期', trigger: 'blur' }
            ],
            startTime: [
              { type: 'date', required: true, message: '请选择日期', trigger: 'blur' }
            ],
            endTime: [
              { type: 'date', required: true, message: '请选择日期', trigger: 'blur' }
            ],
          },
          // 辅助字段
          assist:{
            // 参与人员表格
            eduRequirementRespondents : [],

            // 参与项目
            eduRequirementSurveyItems : {
              data : '',
              isShow : false,
            },
          },
          // 查看人数对话框
          lookNumDialog : {
            // 搜索条件
            form : {
              id : '',
              // 当前页
              pageCurrent : 1,
              // 页数大小
              pageSize : 10,
            },
            // 是否显示
            isShow : false,
            // 标题
            title : '',
            // 表格数据
            tableData : []
          },
          // 需求建议对话框
          suggestDialog : {
            // 搜索条件
            form : {
              reqInvestId : '',
              // 当前页
              pageCurrent : 1,
              // 页数大小
              pageSize : 10,
            },
            // 是否显示
            isShow : false,
            // 标题
            title : '',
            // 表格数据
            tableData : []
          },
          // 页面状态
          pageStatus : 'edit',
          // 参与人员对话框数据
          staffDialog : {
            isShow : false,
          },
          // 目前参与调查总人数
          num : 0,

          //选中的项目
          selectedItem: [],
        }
      },
      watch:{
        $route(to,from){
          // 如果来至列表页
          if(from.name === 'demandSurveyPublishIndex' || from.name === 'demandSurveyPublishLower'){
            this.init();
          }
        }
      },
      created(){
        this.init();
      },
      mounted(){
        this.init();
      },
      methods:{
        // 初始化
        init(){
          if(this.$route.params.status){
            this.searchBtnClickHandle();
          } else {
            this.clear();
            this.num = 0;
            this.getTemplateList();
          }
        },
        // 清空数据
        clear(){
          this.form = this.$tool.clearObj({}, this.form);
          this.lookNumDialog.title = '';
          this.lookNumDialog.tableData = [];
          this.suggestDialog.title = '';
          this.suggestDialog.tableData = [];

          this.assist.eduRequirementSurveyItems = this.$tool.clearObj({}, this.assist.eduRequirementSurveyItems);
          this.assist.eduRequirementRespondents = [];
          // 默认值
          this.form.createYear = new Date();
          this.form.startTime = new Date();
          this.form.endTime = new Date();
          this.form.title = this.$tool.formatDateTime(new Date()).substring(0, 4) + '年员工安全培训需求调查';
          this.form.respondent = '全体人员';

          // 组件 chooseStaff
          if(this.$refs['chooseStaff'] && this.$refs['chooseStaff'].changeTableDataHandle){
            this.$refs['chooseStaff'].changeTableDataHandle([]);
          }
          if(this.$refs['chooseStaff'] && this.$refs['chooseStaff'].isShowBtnHandle){
            this.$refs['chooseStaff'].isShowBtnHandle(true);
          }

          this.pageStatus = 'edit';
        },
        // 获取项目模板---添加页面
        getTemplateList(){
          // 获取项目模板
          this.$store.dispatch('eduReqTempletFind', {}).then(function(res){
            if(res.success){
              this.form.eduRequirementSurveyItems = res.data.map(function(it){
                return {
                  items : it.item
                }
              }.bind(this));

            } else {
              this.$message({
                type : 'error',
                message : res.message || '错误'
              })
            }
          }.bind(this));
        },
        // 调查项目--确定按钮
        eduRequirementSurveyItemsOkBtnClickHandle(){
          this.form.eduRequirementSurveyItems.push({
            items : this.assist.eduRequirementSurveyItems.data
          })
        },
        // 调查项目--删除按钮--处理函数
        emgHandleListsDelHandle(index, item, rows) {
          rows.splice(index, 1);
        },
        // 保存按钮--发布者
        saveBtnClickHandle(setting){
          let id = this.$route.params.id;
          let params = {};
          Object.entries(this.form).forEach(function(it){
            params[it[0]] = it[1];
          })
          // 保存状态
          params['status'] = setting.status;
          if(id) {
            params['id'] = id;
          } else {
            delete params.id;
          }
          this.$store.dispatch('eduReqInvAddOrUpdate', params).then(function(res){
            if(res.success){
              this.$message({
                type : 'success',
                message : '操作成功'
              })
              this.$router.push({ name : 'demandSurveyPublishIndex' })
            } else {
              this.$message({
                type : 'error',
                message : res.message || '操作失败！！！！'
              })
            }
          }.bind(this));
        },
        // 提交按钮---发布者
        submitBtnClickHandle(){
          let id = this.$route.params.id;
          let params = {};
          Object.entries(this.form).forEach(function(it){
            params[it[0]] = it[1];
          })
          // 保存状态
          params['status'] = 1;
          if(id) {
            params['id'] = id;
          } else {
            delete params.id;
          }
          this.$store.dispatch('eduReqInvAddOrUpdate', params).then(function(res){
            if(res.success){
              this.$message({
                type : 'success',
                message : '操作成功'
              })
              this.$router.push({ name : 'demandSurveyPublishIndex' })
            }else {
              this.$message({
                type : 'error',
                message : res.message || '操作失败！'
              })
            }
          }.bind(this));
        },
        // 添加到已完成
        overBtnClickHandle(){
          this.$confirm('是否结束该需求调查?', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            })
            .then(function(){
              this.saveBtnClickHandle({ status : 2 });
            }.bind(this))
        },
        // 根据id搜索信息
        searchBtnClickHandle(){
          this.clear();
          let companyId = this.$route.params.companyId;
          let id = this.$route.params.id;
          let params = {
            id : id,
          }
          if(companyId) {
            params['companyId'] = companyId;
          }

          // 获取需求调查参与的总人数
          this.$store.dispatch('eduReqInvTotalRespondent', { id : id }).then(function(res){
            if(res.success){
              this.num = res.data || 0;
            } else {
              this.$message({
                type : 'error',
                message : res.message || '错误'
              })
            }
          }.bind(this));
          this.$store.dispatch('eduReqInvManagerFind', params).then(function(res){
            if(res.success){
              let list = res.data.list[0];
              this.$tool.cloneObj(this.form, list);


              this.$nextTick(function(){
                // 辅助参与人员表格
                let staffList = list.status == 0 ? list.eduRequirementRespondents : list.eduRequirementUserSurveyResults;
                this.assist.eduRequirementRespondents = staffList.map(function(it){
                  if(it.eduUser != null && it.eduUser.userId != null){
                    return {
                      userId : it.eduUser.userId,
                      companyId : it.eduUser.companyId,
                      parentCompanyId : it.eduUser.parentCompanyId,
                      companyName : it.eduUser.companyName,
                      deptName : it.eduUser.deptName,
                      username : it.eduUser.username,
                      status : it.status
                    }
                  }

                })
                if(this.$refs['chooseStaff'].changeTableDataHandle){
                  this.$refs['chooseStaff'].changeTableDataHandle(this.assist.eduRequirementRespondents);
                }
                // edit 添加和修改的时候，按钮显示；view 查看的时候，按钮隐藏
                if(this.$refs['chooseStaff'].isShowBtnHandle){
                  this.$refs['chooseStaff'].isShowBtnHandle(this.pageStatus === 'view' ? false : true);
                }

                if(this.$refs['chooseStaff'].isShowJoinHandle){
//                  console.log('sdlfjsdfkjdls')
                  this.$refs['chooseStaff'].isShowJoinHandle(true);
                }

              }.bind(this))
              this.pageStatus = this.$route.params.status || 'edit';
            } else {
              this.$message({
                type : 'error',
                message : res.message || '错误'
              })
            }
          }.bind(this));


        },
        // 查看选择人数
        lookNumHandle(row){
          this.lookNumDialog.form.id = row.id;
          this.lookNumDialog.isShow = true;
          this.lookNumDialog.title = row.items;
          this.getEduReqUserRltShowItemResList();
        },
        // 查看选择人员--对话框---分页
        lookNumDialogChangeHandle(page){
          this.lookNumDialog.form.pageCurrent = page;
          this.getEduReqUserRltShowItemResList();
        },
        // 查找选择人员列表
        getEduReqUserRltShowItemResList(){
          this.$store.dispatch('eduReqUserRltShowItemRes', this.lookNumDialog.form).then(function(res){
            if(res.success){
              this.lookNumDialog.tableData = res.data;
            } else {
              this.$message({
                type : 'error',
                message : res.message || '错误'
              })
            }
          }.bind(this))
        },

        // 需求建议--对话框---分页
        suggestDialogChangeHandle(page){
          this.suggestDialog.form.pageCurrent = page;
          this.getSuggestDialogList();
        },
        // 需求建议
        suggestHandle(){
          this.suggestDialog.form.reqInvestId = this.$route.params.id;
          this.suggestDialog.isShow = true;
          this.suggestDialog.title = this.form.title;
          this.getSuggestDialogList();
        },
        // 需求建议列表
        getSuggestDialogList(){
          this.$store.dispatch('eduReqUserRltShowAdviceRes', this.suggestDialog.form).then(function(res){
            if(res.success){
              this.suggestDialog.tableData = res.data;
            } else {
              this.$message({
                type : 'error',
                message : res.message || '错误'
              })
            }
          }.bind(this));
        },
        // 参与人员---对话框---确定按钮
        currentChange(val){
          // 参与人员列表
          this.form.eduRequirementRespondents = val.map(function(it){
            return {
              userId : it.userId,
            }
          })
          // 辅助参与人员表格
          this.assist.eduRequirementRespondents = val.map(function(it){
            return {
              companyName : it.companyName,
              deptName : it.deptName,
              username : it.username,
            }
          })

        },

        // 人员列表选择组件处理函数
        selectedRows(rows){
          console.log(11111111111111111,rows);
          // 参与人员列表----用户userId列表
          let userIds = rows.map(function(it){
            return {
              userId : it.userId,
            }
          })
          this.form.eduRequirementRespondents = userIds;
        },


        // add by pdn
        handleSelectionChange(val){
          this.selectedItem = val;
          //console.log(this.form.dangerInspectListPublicList.length);
          //console.log(this.selectedItem);
        },


        //  批量删除选中项
        deleteBatch(){
          if(this.selectedItem.length == 0){
            this.$message({
              type : 'error',
              message : '请选择要删除的项目'
            })
          }else{
            this.$confirm('此操作将批量删除检查项, 是否继续?', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(() => {

              //遍历List，删除id相同的项   T-T
              for(var i=0;i<this.selectedItem.length;i++){
                for(var j=0;j<this.form.eduRequirementSurveyItems.length;j++){
                  if(this.selectedItem[i].items == this.form.eduRequirementSurveyItems[j].items){
                    this.form.eduRequirementSurveyItems.splice(j,1);
                    console.log("deleted 1 item");
                  }
                }
              }

              console.log("333");
              this.$message({
                type: 'success',
                message: '删除成功'
              });
              //this.$refs.checkTable.clearSelection();

            }).catch(() => {
              this.$message({
                type: 'info',
                message: '已取消删除'
              });
            });

            //console.log(this.form.dangerInspectListPublicList);
          }
        },

      }
    }
</script>

<style>
  .container{
    background:#fff;
    padding:0px 20px 20px;
  }
  .row{
    margin-top:10px;
  }

</style>
