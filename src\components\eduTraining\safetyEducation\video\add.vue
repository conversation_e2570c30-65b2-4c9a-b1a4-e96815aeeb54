<template>
  <el-container class="container">
    <el-main>
      <!--发布-->
      <el-form ref="info" label-width="100px" :model="info">
        <el-row type="flex">
          <el-col :span="8">
            <el-form-item label="课程名称">
              <template v-if="!viewOrEdit">
                <el-input v-model="info.courseName"></el-input>
              </template>
              <template v-if="viewOrEdit">
                <span>{{info.courseName}}</span>
              </template>
            </el-form-item>
          </el-col>
        <!--  <el-col :span="8">
            <el-form-item label="课程类型">
              <el-input v-model="info.courseType"></el-input>
            </el-form-item>
          </el-col>-->
          <!--<el-col :span="8">
            <el-form-item label="主讲教师">
              <el-input v-model="info.courseTeacher"></el-input>
            </el-form-item>
          </el-col>-->

          <el-col :span="8">
            <el-form-item label="学时">
              <el-input-number v-model="info.courseTime"></el-input-number>
            </el-form-item>
          </el-col>
          <!--<el-col :span="8">
            <el-form-item label="视频优先级">
              <el-input v-model="info.courseNum"></el-input>
            </el-form-item>
          </el-col>-->
          <el-col :span="8">
            <el-form-item label="视频积分">
              <el-input-number v-model="info.courseIntegration"></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="课程图片">
              <span>{{videoImageName}}</span>
              <el-upload
                class="avatar-uploader"
                :action='upload.uploadUrl'
                :show-file-list="false"
                :with-credentials="upload.uploadCookies"
                :http-request="ossUploadImage"
                :data="upload.params">
                <el-button size="mini" type="text" style="margin-left: 5px">更换图片</el-button>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>


        <el-row>
          <el-col :span="24">
            <el-form-item label="视频选择">
              <el-select
                v-model="assist.selectedArticleList"
                filterable
                multiple
                remote
                reserve-keyword
                @change="articleChangeFn"
                :remote-method="remoteMethod"
                placeholder="请输入关键词">
                <el-option
                  v-for="item in assist.articleList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex">
          <el-col :span="24">
            <chooseStaff
              ref="chooseStaff"
              @selectedRows="selectedRows"></chooseStaff>
          </el-col>
        </el-row>


        <el-row type="flex" class="row" justify="center">
          <el-button
            @click="saveBtnClickHandle()"
            size="small" :span="2" type="success" >保存</el-button>
         <!-- <el-button
            @click="saveBtnClickHandle({ status : '1' })"
            size="small" :span="2" type="primary">提交</el-button>-->
          <el-button size="small" :span="2"  @click="$router.back();">返回</el-button>
        </el-row>
      </el-form>
    </el-main>
  </el-container>
</template>

<script>
  import { VueEditor } from 'vue2-editor'
  import chooseStaff from '@/components/common/chooseStaff'
  import fileUpload from '@/components/common/fileUpload'
  import dealData from '@/assets/functions/dealData'
  export default {
    components: {
      chooseStaff,
      VueEditor,
      fileUpload
    },
    data(){
      return {
        // info表
        info : {
          // ID
          id : '',
          // 视频名称
          courseName : '',
          // 视频类型
//          courseType : '',
          // 视频讲解人
//          courseTeacher : '',
          // 视频学时
          courseTime : 0,
          // 视频封面图片
          courseImg : '',
          // 视频地址
          courseSrc : '',
          // 视频内容
          courseText : '',
          // 视频优先级
//          courseNum : 0,
          // 视频积分
          courseIntegration : 0,

          userIds : [],

        },
        // 辅助字段
        assist:{
          // 文章列表
          articleList : [],
          // 文章
          selectedArticleList : [],

        },

        // 上传视频的列表
        fileList : [],
        // 辅助字段
     /*   assist:{
          // 上传图片
          params : {
            contentId: 200,
            contentType: 3
          },
          limit : 1,
       },*/
        // 图片名称
        videoImageName : '',


        upload:{
          params:{
            contentId:'',
            contentType:19
          },
          uploadUrl:'',
          uploadCookies:true,
          fileList : [],
        },

        viewOrEdit : false,
        // 权限按钮
        powerBtns : [],

        loginLoading : ''
      }
    },
    watch:{
      $route(to,from){
        // 如果来至列表页
        if(from.name === 'safetyEducationVideoIndex'
          &&this.$route.name==='safetyEducationVideoAdd'){
          this.init();
        }
      },
    },
    created(){
      this.init();
    },
    methods:{
      //上传图片
      ossUploadImage:function (item) {
        this.loginLoading=this.$loading({
          lock: true,
          text: '上传图片中',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.5)'
        });
        //获取该文件对应的sign
        this.$http.get('sys/oss/sign?contentId='+ this.$tool.getStorage('LOGIN_USER').userId +'&contentType=19&realName=' + item.file.name).then(function (res) {
          if(res.data){
            let params=new FormData();
            // 视频名称
            this.videoImageName = item.file.name;
            params.append("name",item.file.name);
            params.append("key",res.data.dir + item.file.name);
            params.append("policy",res.data.policy);
            params.append("OSSAccessKeyId",res.data.accessid);
            params.append('success_action_status','200');
            params.append("callback",res.data.callback);
            params.append("signature",res.data.signature);
            params.append("file",item.file);
            this.fileHttp.post('',params,{headers: {'Content-Type': 'multipart/form-data'}}).then(function (res) {
              if(res.data.file){
                let resultStr=dealData.decode(res.data.file);
                let resultJson=JSON.parse(resultStr);
                this.info.courseImg = resultJson.fId;
                this.loginLoading.close();//关闭登陆加载
                this.$message.success('上传成功');
//                this.saveBtnClickHandle();
                /*this.$http.post("/course/addOrUpdate?userId="+this.$tool.getStorage('LOGIN_USER').userId+'&headFileId='+resultJson.fId).then(function (res) {
                 let userObj=this.$tool.getStorage('LOGIN_USER');
                 userObj.headFileId=resultJson.fId;
                 userObj.headPath=this.fileHttp.defaults.baseURL+resultJson.path;
                 this.$tool.setStorage('LOGIN_USER', userObj);
                 this.$store.dispatch('sendHeadLogoPath',userObj.headPath);
                 }.bind(this)).catch( () => {
                 this.$message.error('人员信息未更新成功');
                 })*/
              }else{
                this.loginLoading.close();//关闭登陆加载
                this.$message.error('上传图片失败');
              }

            }.bind(this))
          }
        }.bind(this)).catch(function (err) {
          this.loginLoading.close();//关闭登陆加载
          this.$message.error('获取唯一标识失败');
        }.bind(this));
      },
      // 初始化
      init(){
        // 获取知识库列表
        this.getArticleList('');
//        console.log(2)
        if(this.$route.params.status == 'edit'){
//          console.log(3)
          this.searchBtnClickHandle();
          //刘杰1130 终
        } else {
//          console.log(4);
          this.clear();
        }
        this.judgeUserRole();
      },
      // 获取知识库文章类型列表
      getArticleList(name){
        let params = {
          type : '0',
          name : name,
        }
        this.$store.dispatch('safeEduMediumLibraryQueryMedium', params).then(function(res){
          if(res.success){
            this.assist.articleList = res.data.list.map(function(item){
              return {
                label : item.name,
                value : item.fid
              }
            })
            console.log(111,this.assist.articleList)
          } else {
            this.$message({
              type : 'error',
              message : res.message || '错误'
            })
          }
        }.bind(this));
      },
      // 知识库远程搜索
      remoteMethod(query) {
        this.getArticleList(query);
      },
      // 人员列表选择组件处理函数
      selectedRows(rows){
        // 参与人员列表----用户userId列表
        let userIds = rows.map(function(it){
          return it.userId;
        })
        this.info.userIds = userIds;
      },
      articleChangeFn(item){
        if(item.length > 1){
          this.$message({
            type : 'error',
            message : '只能选择一个视频'
          })
          this.assist.selectedArticleList = [];

        } else if(item.length == 1) {
          this.assist.selectedArticleList = [item[0]];
        } else {
          this.assist.selectedArticleList = [];
        }
//
      },
      judgeUserRole(){
        // 获取权限按钮
        let url = '/edu-training-menu/daily-training-index';
        this.powerBtns = this.$tool.getPowerBtns('eduTrainingMenu', url);
      },
      // 清空数据
      clear(){
//        console.log(5)

        this.videoImageName = '';
        this.info = this.$tool.clearObj({}, this.info);
        this.viewOrEdit = false;
//        this.assist = this.$tool.clearObj({}, this.assist);
        this.assist.selectedArticleList = [];
        // 清空人员列表
        if(this.$refs['chooseStaff'].changeTableDataHandle){
          this.$refs['chooseStaff'].changeTableDataHandle([]);
        }

      },
      // 根据id搜索信息
      searchBtnClickHandle(){
//        this.clear();
        let id = this.$route.params.id;
        this.viewOrEdit = true;
        this.$store.dispatch('eduCourseFindById', { id : id }).then(function(res){
          if(res.success){

            let list = res.data;
            // 发布培训信息
            Object.entries(list).forEach(function(it){
              if(it[1] && this.info.hasOwnProperty(it[0])){
                this.info[it[0]] = it[1];
              }
            }.bind(this));
            this.videoImageName = list.courseImgName;

            // 视频选择
            this.assist.selectedArticleList = [Number(list.courseSrc)];
            // 人员列表
            let userList = list.userList.map(function(it){
              return {
                userId : it.userId,
                companyId : it.companyId,
                parentCompanyId : it.parentCompanyId,
                companyName : it.companyName,
                deptName : it.deptName,
                username : it.username,
              }
            }.bind(this));
            if(this.$refs['chooseStaff'].changeTableDataHandle){
              this.$refs['chooseStaff'].changeTableDataHandle(userList);
            }


          } else {
            this.$message({
              type : 'error',
              message : res.message || '错误'
            })
          }
        }.bind(this));
      },
      // 未发布/已发布/进行中【开始按钮】--培训发布--保存按钮
      saveBtnClickHandle(){
        let params = this.$tool.filterObj({},this.$tool.filterObj({},this.info));

        if(params.courseIntegration <= 0 || params.courseTime <= 0){
          this.$message({
            type : 'error',
            message : "停留时间或积分必须大于0"
          })
          return;
        }
//        delete params['sourceImg']
        if(this.assist.selectedArticleList && this.assist.selectedArticleList.length == 1){
          params['courseSrc'] =  this.assist.selectedArticleList[0];
        }
        if(!params.courseSrc){
          this.$message({
            type : 'error',
            message : "请选择一个视频"
          })
          return;
        }
        console.log(params);
//        return;
//        return;
        this.$store.dispatch('eduCourseAddOrUpdate', params).then(function (res) {
          if(res.success){
            this.$message({
              type : 'success',
              message : '操作成功'
            })
            this.$router.push({ name : 'safetyEducationVideoIndex' })
          }  else {
            this.$message({
              type : 'error',
              dangerouslyUseHTMLString: true,
//              message: '<strong>这是 <i>HTML</i> 片段</strong>'
              message : res.message || '视频不存在'
            })
          }
        }.bind(this))
      },
    }
  }
</script>

<style>
  .container{
    background:#fff;
    padding:0px 20px 20px;
  }
  .title{
    background:rgba(64,158,255,.1);
    color:#0f6fc6;
    border: 1px solid rgba(64,158,255,.2);
    border-radius:5px;
  }
  .row{
    margin-top:10px;
  }
</style>
