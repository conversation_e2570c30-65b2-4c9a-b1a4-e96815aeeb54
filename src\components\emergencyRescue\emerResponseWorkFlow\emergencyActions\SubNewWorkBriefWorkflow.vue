<template>
  <div id="subNewWorkBriefWorkflow">
    <div
      class="background-style">
      <el-col :span="20" :offset="2" class="primary-background-title">
        应急响应详情
      </el-col>
      <el-col :span="20" :offset="2">
        <el-form :model="form" label-width="120px" ref="form">
          <el-collapse v-model="activeNames" accordion>
            <el-collapse-item  name="1">
              <template slot="title">
                <div style="font-size: 16px;color: #2d57ae;font-weight: bold;padding-left: 20px">基本信息
                  <el-button type="warning" size="mini" style="margin: 5px" @click="forwardEmergency" v-if="forwardFlag">转发应急</el-button>
                  <el-button type="text" size="mini" style="margin: 5px" v-else @click="$message.warning('该应急已转发')">已转发</el-button>
                </div>
              </template>
              <!--信息卡-->
              <el-col :span="24">
                <el-col :span="12">
                  <el-form-item label="名称：" prop="emerResponseName" style="margin: 0">
                    {{form.emerResponseName}}
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="分类：" prop="emerType" style="margin: 0">
                    {{form.emerType}}
                  </el-form-item>
                </el-col>
              </el-col>
              <el-col :span="24">
                <el-col :span="12">
                  <el-form-item label="响应级别：" prop="respLevel" style="margin: 0">
                    {{form.respLevel}}
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="预警信号：" prop="emerFlag" style="margin: 0">
                    {{form.emerFlag}}
                  </el-form-item>
                </el-col>
              </el-col>
              <el-col :span="24">
                <el-col :span="12">
                  <el-form-item label="状态：" prop="emerResponseStatus" style="margin: 0">
                    {{form.emerResponseStatus}}
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="发布时间：" prop="signDate" style="margin: 0">
                    {{form.signDate}}
                  </el-form-item>
                </el-col>
              </el-col>
              <el-col :span="24">
                <el-col :span="12">
                  <el-form-item label="启动通知：">
                    <el-button type="text" style="color: #5daf34" @click="viewBeginMessage">查看启动通知</el-button>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <!--<el-form-item label="调整通知：">-->
                    <!--<el-button type="text" style="color: #5daf34;margin-right: 10px" @click="viewAdjustMessage">查看调整通知-->
                    <!--</el-button>-->
                  <!--</el-form-item>-->
                </el-col>
              </el-col>
              <!--信息卡结束-->
            </el-collapse-item>
            <el-collapse-item  name="2">
              <template slot="title">
                <div style="font-size: 16px;color: #2d57ae;font-weight: bold;padding-left: 20px">响应和值班表
                  <el-button size="mini" type="warning" style="margin: 5px" v-if="needResponseFlag" @click="responseInspectClick">响应</el-button>
                  <el-button size="mini" type="text" style="margin: 5px" v-else>已响应</el-button>
                </div>
              </template>
              <el-col :span="24">
                <span>本级值班列表</span>
              <dutyTable :dutyData="dutyTableData" ref="dutyTable"></dutyTable>
              </el-col>
              <el-col :span="24" style="margin-bottom: 10px">
                <span>上级值班列表</span>
                <el-table
                  :data="upperDutyTable"
                  border
                  highlight-current-row
                  style="width: 100%">
                  <el-table-column
                    type="index"
                    align="center"
                    label-class-name="inner-header-style"
                    width="50">
                  </el-table-column>
                  <el-table-column
                    label="值班日期"
                    prop="dutyDate"
                    align="center"
                    :formatter="dutyTimeFormat"
                    label-class-name="inner-header-style"
                    width="120">
                  </el-table-column>
                  <el-table-column
                    label="值班人员"
                    prop="name"
                    align="center"
                    label-class-name="inner-header-style"
                    width="120">
                  </el-table-column>
                  <el-table-column
                    label="公司"
                    prop="company"
                    label-class-name="inner-header-style"
                    min-width="300">
                  </el-table-column>
                  <el-table-column
                    label="手机长号"
                    prop="phone"
                    align="center"
                    label-class-name="inner-header-style"
                    width="130">
                  </el-table-column>
                  <el-table-column
                    label="手机短号"
                    prop="shortPhone"
                    align="center"
                    label-class-name="inner-header-style"
                    width="120">
                  </el-table-column>
                </el-table>
              </el-col>
            </el-collapse-item>
          </el-collapse>

          <!--工作情况简报-->
          <el-col :span="24" class="card-shadow-style">
            <div style="width: 100%;padding-top: 10px;padding-bottom:10px;float: left;background-color: #f2f2f2">
              <i class="el-icon-document" style="color:#049ff1;float: left;margin:3px 10px 0 20px"></i>
              <span style="color:#049ff1;width: 200px;float: left;">应急工作情况简报</span>
              <div style="float: right;margin-right: 20px;display: inline-block">
                <el-button type="warning" size="small" @click="autoSubmitBrief" v-if="autoSubmitJobId<0">开启自动提交</el-button>
                <el-button type="info" size="small" @click="closeAutoSubmitBrief" v-else>关闭自动提交</el-button>
                <el-button type="primary" size="small" @click="newWorkBrief">新增简报</el-button>
                <el-button type="success" size="small" @click="viewWorkBrief">查看简报</el-button>
              </div>
            </div>
            <div style="width: 100%;float:left;">
              <el-col :span="24" style="margin-top: 10px">
                <el-col :span="12">
                  <el-form-item label="编制公司：" prop="company" style="margin: 0">
                    {{form.company}}
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="规定上报频率：" prop="interval" style="margin: 0">
                    {{form.interval}}
                  </el-form-item>
                </el-col>
              </el-col>
              <el-col :span="24" style="margin-bottom: 10px">
                <el-col :span="12">
                  <el-form-item label="最近上报时间：" prop="lastTime" style="margin: 0">
                    {{form.lastTime}}
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="下次上报期限：" prop="nextTime" style="margin: 0">
                    {{form.nextTime}}
                  </el-form-item>
                </el-col>
              </el-col>
              <el-col :span="24" style="padding:0 5px 20px 5px">
                <el-table
                  :data="form.workBriefTable"
                  border
                  highlight-current-row
                  style="width: 100%">
                  <el-table-column
                    type="index"
                    align="center"
                    label-class-name="inner-header-style"
                    width="50">
                  </el-table-column>
                  <el-table-column
                    label="状态"
                    align="center"
                    label-class-name="inner-header-style"
                    width="100">
                    <template slot-scope="scope">
                      <el-tag :type="scope.row.labelType">
                        {{scope.row.labelName}}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="reportTime"
                    label="上报时间"
                    align="center"
                    label-class-name="inner-header-style"
                    width="180">
                  </el-table-column>
                  <el-table-column
                    prop="defenseWork"
                    label="现阶段防御工作部署落实情况"
                    label-class-name="inner-header-style"
                    width="180">
                  </el-table-column>
                  <el-table-column
                    prop="dutyPeople"
                    label="值班人员到位情况"
                    align="center"
                    label-class-name="inner-header-style"
                    width="180">
                  </el-table-column>
                  <el-table-column
                    prop="disasterSituation"
                    label="受损受灾情况"
                    align="center"
                    label-class-name="inner-header-style"
                    width="180">
                  </el-table-column>
                  <el-table-column
                    prop="emergency"
                    label="突发事件情况"
                    align="center"
                    label-class-name="inner-header-style"
                    width="180">
                  </el-table-column>
                  <el-table-column
                    prop="emergencyHandle"
                    label="事件现场应急处置及抢险救灾情况"
                    align="center"
                    label-class-name="inner-header-style"
                    width="180">
                  </el-table-column>
                  <el-table-column
                    prop="emergencyAfter"
                    label="事件应急结束善后处置情况"
                    align="center"
                    label-class-name="inner-header-style"
                    min-width="180">
                  </el-table-column>
                  <el-table-column
                    label="操作"
                    align="center"
                    fixed="right"
                    width="140"
                    label-class-name="inner-header-style">
                    <template slot-scope="scope">
                      <div v-if="scope.row.status===0">
                        <el-button type="text" size="medium" @click="workItemUpdate(scope.row)">修改</el-button>
                        <el-button type="text" size="medium" style="color: #dd6161" @click="workItemDelete(scope.row)">
                          删除
                        </el-button>
                      </div>
                      <div v-else>
                        <el-button type="text" size="medium" style="color: #5daf34" @click="workItemView(scope.row)">
                          查看
                        </el-button>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
                <div style="margin-top: 10px">
                  <el-pagination
                    background
                    layout="prev, pager, next"
                    :current-page="workCurrentPage"
                    :total="workTotalItem"
                    :page-size="workPageSize"
                    @current-change="workCurrentPageClick">
                  </el-pagination>
                </div>
              </el-col>
            </div>
          </el-col>
          <!--工作情况简报结束-->

          <el-col :span="24">
            <div style="float: right;margin-bottom: 20px">
              <el-button type="primary" @click="returnClick">返回</el-button>
            </div>
          </el-col>
        </el-form>
      </el-col>
    </div>

    <!--查看工作简报对话框-->
    <el-dialog :title="workDialTitle" :visible.sync="workBriefVisible">
      <el-form :model="workTempForm" label-position="top" :rules="workRules" ref="workRuleForm" class="demo-ruleForm">
        <el-col :span="24">
          <el-col :span="12">
            <el-form-item label="设定上报时间：" prop="signTime">
              <el-date-picker
                v-model="workTempForm.signTime"
                type="datetime"
                placeholder="选择日期时间"
                readonly="readonly">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="显示上报情况：">
              <el-button type="primary" size="medium" @click="showUpdateMessage">显示上报情况</el-button>
            </el-form-item>
          </el-col>
        </el-col>

        <el-form-item label="现阶段防御工作部署落实情况：" prop="defenseWork">
          <el-input type="textarea" :autosize="{ minRows: 3}" placeholder="例：已启动预案，施工现场停工，停止路上作业。"
                    v-model="workTempForm.defenseWork"></el-input>
        </el-form-item>
        <el-form-item label="值班人员到位情况：" prop="dutyPeople">
          <el-input type="textarea" :autosize="{ minRows: 3}" placeholder="例：已进岗到位。"
                    v-model="workTempForm.dutyPeople"></el-input>
        </el-form-item>
        <el-form-item label="受损受灾情况：" prop="disasterSituation">
          <el-input type="textarea" :autosize="{ minRows: 3}" placeholder="无相应情况请填写“无”"
                    v-model="workTempForm.disasterSituation"></el-input>
        </el-form-item>
        <el-form-item label="突发事件情况：" prop="emergency">
          <el-input type="textarea" :autosize="{ minRows: 3}" placeholder="无相应情况请填写“无”"
                    v-model="workTempForm.emergency"></el-input>
        </el-form-item>
        <el-form-item label="事件现场应急处置及抢险救灾情况：" prop="emergencyHandle">
          <el-input type="textarea" :autosize="{ minRows: 3}" placeholder="无相应情况请填写“无”"
                    v-model="workTempForm.emergencyHandle"></el-input>
        </el-form-item>
        <el-form-item label="事件应急结束善后处置情况：" prop="emergencyAfter">
          <el-input type="textarea" :autosize="{ minRows: 3}" placeholder="无相应情况请填写“无”"
                    v-model="workTempForm.emergencyAfter"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <div v-if="workUpdateFlag">
          <el-button type="warning" @click="setTimeDelete(workTempForm.jobId)" v-if="workTempForm.setTimeFlag">取消定时</el-button>
          <el-button type="warning" @click="sendDateDialogVisible=true" v-else>定时发布</el-button>
          <el-button type="primary" @click="sendRequest(1)">发布</el-button>
          <el-button type="success" @click="sendRequest(0)">保存</el-button>
          <el-button @click="$refs['workRuleForm'].resetFields();workBriefVisible = false;workUpdateFlag=false">返回
          </el-button>
        </div>
        <div v-else>
          <el-button @click="$refs['workRuleForm'].resetFields();workBriefVisible = false;workUpdateFlag=false">返回</el-button>
        </div>
      </div>
    </el-dialog>
    <!--查看工作简报对话框结束-->

    <!--查看不同级别简报对话框-->
    <el-dialog title="选择简报编辑部门" :visible.sync="deptBriefVisible">
      <el-form :model="deptTempForm">
        <el-form-item label="选择编辑部门：" label-position="top">
          <el-select v-model="deptTempForm.editDept" placeholder="请选择" style="width: 100%">
            <el-option
              v-for="item in deptTempForm.deptOptions"
              :key="item.value"
              :label="item.label"
              :value="item">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary"
                   @click="$router.push({name:'viewWorkBriefWorkflow',params:{emerData:tempEmerData,currentCompany:deptTempForm.editDept}});deptBriefVisible=false">
          确定
        </el-button>
      </div>
    </el-dialog>
    <!--查看不同级别简报对话框结束-->

    <!--设置时间对话框-->
    <el-dialog title="选择自动提交时间" :visible.sync="sendDateDialogVisible">
      <el-date-picker
        v-model="sendDate"
        type="datetime"
        placeholder="选择日期时间">
      </el-date-picker>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="sendRequest(0,true)">确定</el-button>
        <el-button @click="sendDateDialogVisible = false">取消</el-button>
      </div>
    </el-dialog>
    <!--设置时间对话框结束-->

  </div>
</template>

<script>
  import {mapGetters} from 'vuex'
  import DutyTable from '../../../common/smallComponent/dutyTable.vue'
  export default {
    name: "subNewWorkBriefWorkflow",
    data() {
      return {
        activeNames:'1',
        //信息卡数据
        form: {
          emerResponseName: '',
          emerType: '',
          emerFlag: '',
          respLevel: '',
          emerResponseStatus: '',
          signDate: '',

          //工作简报数据
          company: '',//以工位单位进行工作简报编写
          interval: '',
          lastTime: '',
          nextTime: '',
          //简报表格
          workBriefTable: [],
          //物资储备数据
          goodsTable: [],
          //其他文件资料
          fileData: []
        },
        //应急响应和值班表
        responseId:'',
        needResponseFlag:false,
        dutyTableData:{},
        upperDutyTable:[],
        //工作简报对话框
        workCurrentPage: 0,
        workPageSize: 5,
        workTotalItem: 0,
        workBriefVisible: false,
        workTempForm: {
          id: '',
          signTime: '',
          defenseWork: '',
          dutyPeople: '',
          disasterSituation: '',
          emergency: '',
          emergencyHandle: '',
          emergencyAfter: '',
          setTimeFlag:false,
          jobId:'',
        },
        workRules: {
          defenseWork: [{required: true, message: '请选择填入内容', trigger: 'change'}],
          dutyPeople: [{required: true, message: '请选择填入内容', trigger: 'change'}],
        },
        workDialTitle: '',
        workUpdateFlag: false,

        //查看本公司和子公司的工作简报
        deptBriefVisible: false,
        deptTempForm: {
          editDept: '',
          deptOptions: []
        },
        //简报数据状态对应表
        workBriefStatus: [
          {id: 0, name: '未发布', labelType: 'danger'},
          {id: 1, name: '已发布', labelType: 'success'},
        ],
        //-----------------------设置自动发送---------------------------
        sendDate:'',
        autoSubmitJobId:-1,
        sendDateDialogVisible:false,
        //----------------------缓存数据-----------------------------------
        tempEmerData:'',
        currentEmerId: 80,
        startEmerId:'',
        currentPlanId: '',
        currentCompanyId:'',
        currentCompanyName:'',
        currentStatus:'',
        relieveContent:'',
        currentIssuer:{},
        currentInterval: '',
        publicCompanyId:'',//应急响应的发布公司
        //----------------------------状态对应表--------------------------
        statusTable:[
          {id:0,name:'未提交',labelType:'primary'},
          {id:1,name:'待审核',labelType:'warning'},
          {id:2,name:'被退回',labelType:'danger'},

          {id:3,name:'已签发',labelType:'success'},
          {id:4,name:'调整未提交',labelType:'primary'},
          {id:5,name:'调整待审核',labelType:'warning'},
          {id:6,name:'调整被退回',labelType:'danger'},
          {id:7,name:'调整已签发',labelType:'success'},

          {id:8,name:'解除未提交',labelType:'primary'},
          {id:9,name:'解除被退回',labelType:'success'},
          {id:10,name:'解除待审核',labelType:'warning'},

          {id:11,name:'待总结',labelType:'success'},
          {id:12,name:'总结未提交',labelType:'primary'},
          {id:13,name:'总结已提交',labelType:'success'},
          {id:14,name:'已完结',labelType:'info'},
        ],
        //流程数据
        process:{
          processInstanceId:"112501",
          processDefId:"",
          finish:false
        },
        //转发应急
        forwardFlag:false,

        //------------------选择审核人的对话框-----------------------
        selectPersonData:{title:'请选择审核人',isShow:false,defaultPerson:{value:0,label:''}},

      }
    },
    components:{
      DutyTable
    },
    created: function () {
      if (this.$route.params.emergencyId) {
        this.currentEmerId = this.$route.params.emergencyId;
        this.finish=this.$route.params.finish;
        this.forwardFlag=false;
        this.searchEmerById();
      }
    },
    watch: {
      $route(to, from) {
        if (this.$route.name === 'subNewWorkBriefWorkflow') {
          if (this.$route.params.emergencyId) {
            this.currentEmerId = this.$route.params.emergencyId;
            this.dutyTableData.planPublicId=this.currentEmerId;
            this.forwardFlag=false;
            this.searchEmerById();
          }
        }
      }
    },
    methods: {
      //--------------------------------初始化操作-------------------------------
      searchEmerById: function () {
        let params = new URLSearchParams;
        params.append("id", this.currentEmerId);
        this.$http.post('planPublic/find', params).then(function (res) {
          if (res.data.data) {
            this.editEmerForm(res.data.data.list[0]);
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message({
            showClose: true,
            message: '网络错误，请尝试重登录',
            type: 'error'
          });
        }.bind(this));
      },
      editEmerForm: function (val) {
        this.tempEmerData=val;
        this.currentPlanId = val.planId;
        this.form.emerResponseName = val.name;
        this.form.emerType = val.topTypeName + "/" + val.typeName;
        this.form.emerFlag = val.warnSignal;
        this.form.respLevel = val.respLevel;
        this.currentStatus = Number(val.status);
        this.form.emerResponseStatus = this.statusTable[Number(val.status)].name;
        this.form.signDate = this.transferTime(val.signDate, null, true);
        this.publicCompanyId = val.companyId;
        this.currentIssuer = {value: val.signerUserId, label: val.signerUserName};
        this.currentCompanyId = this.$tool.getStorage('LOGIN_USER').companyId;
        this.currentCompanyName = this.form.company = this.$tool.getStorage('LOGIN_USER').companyName;
        this.currentInterval = val.timeInterval;
        this.form.interval = val.timeInterval + " 小时/次";
        this.startEmerId = val.startPlanId ? val.startPlanId : this.currentEmerId;
        if (val.sysNotice) {
          this.relieveContent = val.sysNotice.content;
        }
        //工作简报，应急物资
        this.judgeNeedAutoSubmitBrief();
        this.searchWorkBrief();
        this.searchResponse();

        this.judgeForward();//判断是否已经被转发了
        //应急响应值班表，原来传currentEmerId，现在传startEmerId
        this.dutyTableData={planPublicId:this.startEmerId,companyId:this.$tool.getStorage('LOGIN_USER').companyId,companyName:this.$tool.getStorage('LOGIN_USER').companyName};
        //值班人员列表
        this.$nextTick(function () {
          this.$refs.dutyTable.searchTable();
        })
      },
      searchWorkBrief: function () {
        let params = new URLSearchParams;
        params.append("pageCurrent", 1);
        this.workCurrentPage = 1;
        this.workBriefSendRequest(params);
      },
      searchResponse:function () {
        let params=new URLSearchParams;
        //胖大海说响应第一个之后就不用再响应了
        params.append("emgPlanPulicId",this.currentEmerId);
        params.append("companyId",this.currentCompanyId);
        this.$http.post('planPublicResponse/find', params).then(function (res) {
          this.responseId=res.data.data[0].id;
          this.needResponseFlag=!(res.data.data[0].response);//还没有响应，则提醒响应
          if(this.needResponseFlag){
            this.$notify({title: '提示', message: '点击页面中的响应按钮，即可响应该应急。',});
          }
          this.searchUpperDutyTable();
        }.bind(this)).catch(function (err) {
          console.log(err);
        }.bind(this));
      },
      searchUpperDutyTable:function () {
        let params = new URLSearchParams;
        params.append("planPublicId", this.startEmerId);
        params.append("companyId",  this.publicCompanyId);
        //应急响应值班表，原来传currentEmerId，现在传startEmerId
        this.$http.post('duty/find', params).then(function (res) {
          if (res.data.success) {
            this.upperDutyTable=res.data.data.list;
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
        }.bind(this));
      },
      dutyTimeFormat:function (row) {
        return this.transferTime(row.dutyDate);
      },
      //--------------------------------响应应急------------------------------
      responseInspectClick:function () {
        let params=new URLSearchParams;
        params.append("responseTime",new Date());
        params.append("response",true);
        params.append("id",this.responseId);
        this.$http.post('planPublicResponse/update', params).then(function (res) {
          if(res.data.success){
            this.$message.success('响应成功！');
            this.needResponseFlag=false;
          }else{
            this.$message.error('响应失败');
          }
        }.bind(this)).catch(function (err) {
          this.$message.error('响应失败，请刷新后重试');
          console.log(err);
        }.bind(this));
      },
      //-------------------------------表单事件----------------------------------
      viewBeginMessage: function () {
        this.$router.push({name: 'viewEmergency', params: {emergencyId: this.startEmerId, onlyShow: true}});
      },
      viewAdjustMessage: function () {
        let params=new URLSearchParams;
        params.append("startPlanId",this.startEmerId);
        params.append("history",1);
        this.$http.post('planPublic/find', params).then(function (res) {
          this.adjustArray=[];
          if(this.currentStatus<7){
            this.adjustArray.push({id:this.currentEmerId,name:this.form.emerResponseName,onlyShow:false});
          }else{
            this.adjustArray.push({id:this.currentEmerId,name:this.form.emerResponseName,onlyShow:true});
          }
          if (res.data.success) {
            for(let i=0;i<res.data.data.list.length;i++){
              this.adjustArray.push({id:res.data.data.list[i].id,name:res.data.data.list[i].name,onlyShow:true});
            }
          }
          this.viewAdjustVisible=true;
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message({
            showClose: true,
            message: '网络错误，请尝试重登录',
            type: 'error'
          });
        }.bind(this));
      },
      //---------------------------------转发应急-----------------------------
      //转发应急
      forwardEmergency:function () {
        if(this.tempEmerData.pathId){
          this.$router.push({name:'forwardEmer',params:{emerData:this.tempEmerData}});
        }else{
          this.$http.get('uuid/getUuid').then(function (res) {
            if(res.data.success){
              this.tempEmerData.pathId=res.data.data;
              let params=new URLSearchParams;
              params.append("id",this.tempEmerData.id);
              params.append("planId",this.tempEmerData.planId);
              params.append("pathId",res.data.data);
              this.$http.post('planPublic/update',params).then(function (res) {
                if(res.data.success){
                  this.$router.push({name:'forwardEmer',params:{emerData:this.tempEmerData}});
                }
              }.bind(this)).catch(function (err) {
                console.log(err);
              }.bind(this));
            }
          }.bind(this)).catch(function (err) {
            console.log(err);
            return 0;
          });
        }
      },
      //判断是否已经被转发
      judgeForward:function () {
        this.forwardFlag=false;
        let params=new URLSearchParams;
        params.append("pathId",this.tempEmerData.pathId);
        params.append("companyId",this.$tool.getStorage('LOGIN_USER').companyId);
        this.$http.post('planPublic/find',params).then(function (res) {
          if(res.data.success){
            if(res.data.data.list.length===0){
              this.forwardFlag=true;
            }
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
          return 0;
        });
      },
      //-------------------------------工作简报----------------------------------
      newWorkBrief: function () {
        this.workUpdateFlag = true;
        this.workDialTitle = '新增工作简报';
        let dateTemp=new Date();
        this.workTempForm.signTime = dateTemp;
        this.workTempForm.defenseWork = '';
        this.workTempForm.dutyPeople = '';
        this.workTempForm.disasterSituation = '';
        this.workTempForm.emergency = '';
        this.workTempForm.emergencyHandle = '';
        this.workTempForm.emergencyAfter = '';
        this.workBriefVisible = true;
      },
      viewWorkBrief: function () {
        let params = new URLSearchParams;
        params.append("parentId", this.currentCompanyId);
        params.append("type", 1);
        this.$http.post('dept/find', params).then(function (res) {
          this.deptTempForm.deptOptions = [];
          if (res.data.success) {
            this.deptTempForm.deptOptions.push({value: this.currentCompanyId, label: this.currentCompanyName});
            this.deptTempForm.editDept = {value: this.currentCompanyId, label: this.currentCompanyName};
            for (let i = 0; i < res.data.data.length; i++) {
              this.deptTempForm.deptOptions.push({value: res.data.data[i].id, label: res.data.data[i].name});
            }
            this.deptBriefVisible = true;
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message({
            showClose: true,
            message: '网络错误，请尝试重登录',
            type: 'error'
          });
        }.bind(this));
      },
      workItemUpdate: function (row) {
        this.workDialTitle = '修改工作简报';
        this.workUpdateFlag = true;
        this.workTempForm.id = row.wbId;
        this.workTempForm.defenseWork = row.defenseWork;
        this.workTempForm.dutyPeople = row.dutyPeople;
        this.workTempForm.disasterSituation = row.disasterSituation;
        this.workTempForm.emergency = row.emergency;
        this.workTempForm.emergencyHandle = row.emergencyHandle;
        this.workTempForm.emergencyAfter = row.emergencyAfter;
        if(row.scheduleJobEntity){
          this.workTempForm.setTimeFlag=true;
          this.workTempForm.jobId=row.scheduleJobEntity.jobId;
          this.workTempForm.signTime = row.scheduleJobEntity.jobTime;
        }else{
          this.workTempForm.setTimeFlag=false;
          this.workTempForm.signTime = '';//未发布且未设置时间，则没有发布时间
        }
        this.workBriefVisible = true;
      },
      setTimeDelete:function (jobId) {
        this.$http.post('sys/scheduleBusiness/deleteSendWorkBriefTask?jobId='+jobId).then(function (res) {
          if (res.data.success) {
            this.$message({
              showClose: true,
              message: '取消定时成功',
              type: 'success'
            });
          }
          this.workBriefVisible = false;
          this.searchWorkBrief();
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message({
            showClose: true,
            message: '网络错误，请尝试重登录',
            type: 'error'
          });
        }.bind(this));
      },
      workItemDelete: function (row) {
        this.$confirm('此操作将永久删除该工作简报, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let params = new URLSearchParams;
          params.append("id", row.wbId);
          this.$http.post('workBrief/delete', params).then(function (res) {
            if (res.data.success) {
              this.$message({
                showClose: true,
                message: '删除成功',
                type: 'success'
              });
            }
            this.searchWorkBrief();
          }.bind(this)).catch(function (err) {
            console.log(err);
            this.$message({
              showClose: true,
              message: '网络错误，请尝试重登录',
              type: 'error'
            });
          }.bind(this));
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });
        });
      },
      workItemView: function (row) {
        this.workUpdateFlag = false;
        this.workDialTitle = '查看工作简报';
        let dateTemp = new Date(row.originalTime);
        this.workTempForm.signTime = dateTemp;
        this.workTempForm.defenseWork = row.defenseWork;
        this.workTempForm.dutyPeople = row.dutyPeople;
        this.workTempForm.disasterSituation = row.disasterSituation;
        this.workTempForm.emergency = row.emergency;
        this.workTempForm.emergencyHandle = row.emergencyHandle;
        this.workTempForm.emergencyAfter = row.emergencyAfter;
        this.workBriefVisible = true;
      },
      //对未发布简报的修改和发布
      sendRequest: function (state,setTime=false) {
        this.$refs['workRuleForm'].validate((valid) => {
          if (valid) {
            this.workBriefSetTime=setTime;

            let params = new URLSearchParams;
            var url;
            if(this.workTempForm.id){
              params.append("wbId", this.workTempForm.id);
              url="workBrief/update";
            }else{
              url="workBrief/add"
            }
            params.append("planPublicId", this.startEmerId);
           // params.append("reportTime", this.workTempForm.signTime);
            params.append("defenseWork", this.workTempForm.defenseWork);
            params.append("dutyPeople", this.workTempForm.dutyPeople);
            params.append("disasterSituation", this.workTempForm.disasterSituation);
            params.append("emergency", this.workTempForm.emergency);
            params.append("emergencyHandle", this.workTempForm.emergencyHandle);
            params.append("emergencyAfter", this.workTempForm.emergencyAfter);
            params.append("reportUserId", this.$tool.getStorage('LOGIN_USER').userId);
            params.append("deptId", this.$tool.getStorage('LOGIN_USER').deptId);
            params.append("companyId", this.$tool.getStorage('LOGIN_USER').companyId);
            params.append("status", state);
            this.$http.post(url, params).then(function (res) {
              if (res.data.success) {
                if(this.workBriefSetTime){//需要定时发送
                  if(res.data.data){ this.setTimePublic(res.data.data.wbId);}else{this.setTimePublic(this.workTempForm.id);}
                }else {
                  this.$message({
                    showClose: true,
                    message: '操作成功',
                    type: 'success'
                  });
                  this.searchWorkBrief();
                }
              }
              this.sendDateDialogVisible = false;
              this.workBriefVisible = false;
            }.bind(this)).catch(function (err) {
              console.log(err);
              this.$message({
                showClose: true,
                message: '网络错误，请尝试重登录',
                type: 'error'
              });
            }.bind(this));
          } else {
            console.log('error submit!!');
            return false;
          }
        });
      },
      setTimePublic:function (id) {
        let params=new URLSearchParams;
        params.append("workBriefId",id);
        params.append("sendDate",this.sendDate);
        this.$http.post('sys/scheduleBusiness/sendWorkBriefTask',params).then(function (res) {
          if(res.data.success){
            this.$message({
              showClose: true,
              message: '设置成功',
              type: 'success'
            });
            this.searchWorkBrief();
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message({
            showClose: true,
            message: err,
            type: 'error'
          });
        }.bind(this));
      },
      workCurrentPageClick: function (val) {
        if (val) {
          this.workCurrentPage = val;
          let params = new URLSearchParams;
          params.append("pageCurrent", Number(val));
          this.workBriefSendRequest(params);
        }
      },
      workBriefSendRequest: function (params) {
        params.append("pageSize", 5);
        params.append("companyId", this.$tool.getStorage('LOGIN_USER').companyId);
        params.append("planPublicId", this.startEmerId);
        this.$http.post('workBrief/find', params).then(function (res) {
          if (res.data.success) {
            this.workTotalItem = res.data.data.page.total;
            if (res.data.data.latelyReportTime) {
              this.form.lastTime = this.transferTime(res.data.data.latelyReportTime, null, true);
              this.form.nextTime = this.transferTime(res.data.data.latelyReportTime + 3600 * 1000 * this.currentInterval, null, true);
            }
            let tempWorkBriefList=res.data.data.page.list;
            for (let i = 0; i < tempWorkBriefList.length; i++) {
              tempWorkBriefList[i].originalTime = tempWorkBriefList[i].reportTime;
              tempWorkBriefList[i].reportTime = this.transferTime(tempWorkBriefList[i].reportTime, null, true);
              tempWorkBriefList[i].labelType = this.workBriefStatus[Number(tempWorkBriefList[i].status)].labelType;
              tempWorkBriefList[i].labelName = this.workBriefStatus[Number(tempWorkBriefList[i].status)].name;
            }
            this.form.workBriefTable = tempWorkBriefList;
          } else {
            this.workCurrentPage = 0;
            this.workTotalItem = 0;
            this.form.lastTime = '无';
            this.form.nextTime = '无';
            this.form.workBriefTable = [];
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message({
            showClose: true,
            message: '网络错误，请尝试重登录',
            type: 'error'
          });
        }.bind(this));
      },
      //判断是否已经开启自动提交
      judgeNeedAutoSubmitBrief:function () {
        this.$http.get('sys/schedule/list?params='+this.startEmerId+'-'+this.$tool.getStorage('LOGIN_USER').companyId).then(function (res) {
          if(res.data.data.list.length){
            this.autoSubmitJobId=res.data.data.list[0].jobId;
          }else{
            this.autoSubmitJobId=-1;
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.autoSubmitJobId=-1;
        }.bind(this));
      },
      //自动提交默认内容的工作简报
      autoSubmitBrief:function () {
        this.$confirm('将会按规定上报频率，检查有否已发布工作简报，如没有，则自动发布有默认内容的工作简报', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$http.post('sys/scheduleBusiness/sendWorkBriefAllTask?startPlanPublicId='+this.startEmerId).then(function (res) {
            if(res.data.success){
              this.$message({
                type: 'success',
                message: '设置成功'
              });
              this.autoSubmitJobId=res.data.data.jobId;
            }else{
              this.$message({
                type: 'warning',
                message: '设置失败，可能已被设为自动提交'
              });
            }
          }.bind(this)).catch(function (err) {
            console.log(err);
            this.$message({
              type: 'warning',
              message: '设置失败，可能已被设为自动提交'
            });
          }.bind(this));
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
      },
      //关闭自动提交工作简报功能
      closeAutoSubmitBrief:function () {
        if(this.autoSubmitJobId>0){
          this.$confirm('是否确认关闭自动提交工作简报功能？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            let params=new URLSearchParams;
            params.append("jobIds[]",[this.autoSubmitJobId])
            this.$http.post('sys/schedule/delete',params).then(function (res) {
              if(res.data.success){
                this.$message({
                  type: 'success',
                  message: '关闭成功'
                });
                this.autoSubmitJobId=-1;
              }
            }.bind(this)).catch(function (err) {
              console.log(err);
              this.$message({
                type: 'warning',
                message: '设置失败'
              });
            }.bind(this));
          }).catch(() => {
            this.$message({
              type: 'info',
              message: '已取消'
            });
          });
        }else{
          this.$message({
            type: 'info',
            message: '没有找到自动提交的ID'
          });
        }

      },
      //-------------------------显示下级部门的上报数据------------------------------
      showUpdateMessage:function () {
        let params=new URLSearchParams;
        params.append("planPublicId",this.currentEmerId);
        this.$http.post('workBrief/findLowerLevelWorkBrief',params).then(function (res) {
          if(res.data.success){
            if(res.data.data){
              for(let i=0;i<res.data.data.defenseWork.length;i++){
                this.workTempForm.defenseWork+=res.data.data.defenseWork[i].companyName+'：'+res.data.data.defenseWork[i].date+'，'+this.judgeWrap(res.data.data.defenseWork[i].content);
                this.workTempForm.dutyPeople+=res.data.data.dutyPeople[i].companyName+'：'+res.data.data.dutyPeople[i].date+'，'+this.judgeWrap(res.data.data.dutyPeople[i].content);
                this.workTempForm.disasterSituation+=res.data.data.disasterSituation[i].companyName+'：'+res.data.data.disasterSituation[i].date+'，'+this.judgeWrap(res.data.data.disasterSituation[i].content);
                this.workTempForm.emergency+=res.data.data.emergency[i].companyName+'：'+res.data.data.emergency[i].date+'，'+this.judgeWrap(res.data.data.emergency[i].content);
                this.workTempForm.emergencyHandle+=res.data.data.emergencyHandle[i].companyName+'：'+res.data.data.emergencyHandle[i].date+'，'+this.judgeWrap(res.data.data.emergencyHandle[i].content);
                this.workTempForm.emergencyAfter+=res.data.data.emergencyAfter[i].companyName+'：'+res.data.data.emergencyAfter[i].date+'，'+this.judgeWrap(res.data.data.emergencyAfter[i].content);
              }
            }else{
              this.$message({
                showClose: true,
                message: '暂无上报数据',
                type: 'warning'
              });
            }
          }else{
            this.$message({
              showClose: true,
              message: '暂无上报数据',
              type: 'warning'
            });
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message({
            showClose: true,
            message:err,
            type: 'error'
          });
        }.bind(this));
      },
      judgeWrap:function (content) {
        if(content.charAt(content.length-1)==='\n'||!content.length){
          return content;
        }else {
          return content+'\n';
        }
      },

      //---------------------------------退出---------------------------------
      returnClick: function () {
        this.$router.go(-1);
      },
      dateFormat(row, column) {
        //.replace(/年|月/g, "-").replace(/日/g, " ")
        return new Date(row.uploadTime).Format("yyyy-MM-dd hh:mm").toLocaleString();
      },
    }
  }
</script>

<style scoped>

</style>
