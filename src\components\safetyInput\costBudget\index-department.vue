<template>
  <div id="trainingPlanIndex">
    <div class="background-style">

      <!--搜索区-->
      <div style="padding:20px 0 0px 10px;">
        <el-form label-width="5px">
          <el-row>
            <el-col :span="3">
              <el-form-item >
                <el-input clearable placeholder="项目名称" v-model="form.title"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="3">
              <el-form-item >
                <el-input clearable placeholder="公司/部门" v-model="form.deptName"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="3">
              <el-form-item >
                <el-select
                  clearable
                  v-if="!dialog.form.id"
                  v-model="form.budgetType" placeholder="预算类型">
                  <el-option
                    v-for="item in dialog.otherData.typeList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item >
                <el-date-picker
                  v-model="form.year"
                  type="year"
                  placeholder="选择年">
                </el-date-picker>
              </el-form-item>
            </el-col>

            <el-col :offset="1" :span="2" >
              <el-button style="margin-bottom:0px;" type="primary" @click="searchBtnClickHandle">搜索</el-button>
            </el-col>
            <el-col :span="2">
              <el-button
                v-if="!viewRole"
                @click="addBtnClickHandle"
                type="success" icon="el-icon-plus" style="margin-left: 20px">预算计划</el-button>
            </el-col>
          </el-row>
        </el-form>




      </div>
      <!--表格区-->
      <div style="width: 100%;">
        <div style="padding: 10px 10px 10px 10px">
          <el-table
            border
            :data="tableData.list"
            style="width: 100%">
            <el-table-column
              type="index"
              label="编号"
              width="100"
              align="center"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="title"
              label="名称"
              show-overflow-tooltip
              min-width="150"
              label-class-name="header-style">
              <template slot-scope="scope">
                <router-link :to="{ path : '/safety-input-menu/cost-budget-item', query : { reg : scope.row, date : new Date().getTime() } }">{{scope.row.title}}</router-link>
              </template>
            </el-table-column>
            <el-table-column
              prop="deptName"
              label="公司"
              show-overflow-tooltip
              width="200"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="budgetType"
              label="预算类型"
              width="150"
              label-class-name="header-style">
              <template slot-scope="scope">
                <template v-if="scope.row.budgetType == 1">月度预算</template>
                <template v-if="scope.row.budgetType == 2">季度预算</template>
                <template v-if="scope.row.budgetType == 3">年度预算</template>
              </template>
            </el-table-column>
            <el-table-column
              prop="year"
              :formatter="formatDateTime"
              label="年份"
              width="150"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              label="季/月"
              width="150"
              label-class-name="header-style">
              <template slot-scope="scope">
                <template v-if="scope.row.budgetType == 1">
                  {{scope.row.budgetNo}}月
                </template>
                <template v-if="scope.row.budgetType == 2">
                  第{{scope.row.budgetNo}}季度
                </template>
              </template>
            </el-table-column>
            <el-table-column
              prop="totalBudget"
              label="预算总额（元）"
              width="150"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              fixed="right" label="操作"
              label-class-name="header-style"
              align="center" width="200">
              <template slot-scope="scope">
                <template>
                  <!--<el-button size="mini" type="primary" @click="itemPrintOneClick(scope.row)">单独打印</el-button>-->
                  <el-button  v-if="!viewRole" size="mini" type="primary" @click="itemUpdateClick(scope.row)">修改</el-button>
                  <el-button  v-if="!viewRole" size="mini" type="danger" @click="itemDeleteClick(scope.row)">删除</el-button>
                </template>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div>
          <el-pagination
            background
            layout="prev, pager, next"
            :current-page="tableData.pageNum"
            :page-size="form.pageSize"
            :total="tableData.total"
            @current-change ="disasterPageChangeHandle">
          </el-pagination>
        </div>
      </div>


      <!--对话框-->
      <el-dialog
        title="新增预算"
        :visible.sync="dialog.isShow"
        width="80%"
        :before-close="handleClose">
        <el-form label-width="100px">
          <el-row  class="row">
            <el-col :span="24">
              <el-form-item label="名称：" style="margin: 2px;">
                <span v-if="dialog.form.id">{{dialog.form.title}}</span>
                <el-input
                  disabled
                  v-if="!dialog.form.id"
                  v-model="dialog.form.title"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row  class="row">
            <el-col :span="24">
              <el-form-item label="预算类型：" style="margin: 2px;">
                <span v-if="dialog.form.id">
                  <template v-if="dialog.form.budgetType == 1">月度预算</template>
                  <template v-if="dialog.form.budgetType == 2">季度预算</template>
                  <template v-if="dialog.form.budgetType == 3">年度预算</template>
                </span>
                <el-select
                  @change="titleChange"
                  v-if="!dialog.form.id"
                  v-model="dialog.form.budgetType" placeholder="请选择">
                  <el-option
                    v-for="item in dialog.otherData.typeList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row  class="row">
            <!--年度、季度显示年-->
            <el-col :span="8"  v-if="dialog.form.id && dialog.form.budgetType == '1'">
              <el-form-item label="年：" style="margin: 2px;">
                <span >{{dialog.form.year && $tool.formatDateTime(dialog.form.year).substring(0,4)}}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8"  v-if="dialog.form.budgetType != '1'">
              <el-form-item label="年：" style="margin: 2px;">
                <span v-if="dialog.form.id">{{dialog.form.year && $tool.formatDateTime(dialog.form.year).substring(0,4)}}</span>
                <el-date-picker
                  v-if="!dialog.form.id"
                  v-model="dialog.form.year"
                  @change="titleChange"
                  type="year"
                  placeholder="选择年">
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="8"  v-if="dialog.form.budgetType == '2'">
              <el-form-item label="季度：" style="margin: 2px;">
                <span v-if="dialog.form.id">第{{dialog.form.budgetType}}季度</span>
                <el-select
                  v-if="!dialog.form.id"
                  @change="quarterSelect"
                  v-model="dialog.otherData.quarter" placeholder="请选择">
                  <el-option
                    v-for="item in dialog.otherData.quarterList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8"  v-if="dialog.form.budgetType == '1'">
              <el-form-item label="月：" style="margin: 2px;">
               <!-- <span v-if="dialog.form.id">
                  {{dialog.form.year && $tool.formatDateTime(dialog.form.year).substring(0,4)}}
                  年{{dialog.form.budgetType}}月</span>-->
                <span v-if="dialog.form.id">{{dialog.form.budgetType}}月</span>
                <el-date-picker
                  v-if="!dialog.form.id"
                  @change="monthChange"
                  value-format="yyyy-MM"
                  v-model="dialog.otherData.month"
                  type="month"
                  placeholder="选择月">
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row  class="row">
            <el-col :span="24">
              <el-form-item label="预算单位：" style="margin: 2px;">
                <span>{{dialog.otherData.companyName}}</span>
               <!-- <el-input v-model="dialog.otherData.companyName"></el-input>-->
              </el-form-item>
            </el-col>
          </el-row>
          <el-row  class="row">
            <el-col :span="24">
              <el-form-item label="预算部门：" style="margin: 2px;">
                <span v-if="dialog.form.id">{{dialog.form.deptName}}</span>
                <el-select
                  v-if="!dialog.form.id"
                  multiple
                  @change="deptSubcomNamesSelect"
                  v-model="dialog.otherData.deptSubcomIds" placeholder="请选择">
                  <el-option
                    v-for="item in dialog.otherData.deptSubcomNames"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row  class="row">
            <el-col :span="24">
              <el-form-item label="情况说明：" style="margin: 2px;" >
                <el-input   v-model="dialog.form.remark" type="textarea" :rows="2"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex"  class="row" v-if="dialog.form.id">
            <el-col :span="24">
              <el-form-item label="附件：" style="margin: 2px;">
                <fileUpload  ref="upload" :data="upload"></fileUpload>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button
            type="danger"  size="mini"
            @click="dialogOkBtnClickHandle">确定</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>
<script>
  import fileUpload from '@/components/common/fileUpload'
  export default {
    components: {
      fileUpload
    },
    data() {
      return {
        form : {
          // 项目
          title : '',
          // 部门
          deptName : '',
          // 类型
          budgetType : "",
          // 年份
          year : '',
          // 当前页
          pageCurrent : 1,
          // 页数大小
          pageSize : 10,
        },
        tableData : {},
        // 上传文件
        upload : {
          // 上传参数
          params : {
            contentId: '',
            contentType: 8
          },
          btns : {
            // 上传按钮
            upload : {
              isShow : true,
            },
            // 删除按钮
            delete : {
              isShow : true,
            },
          },
        },
        // 对话框
        dialog : {
          // 是否显示
          isShow : false,
          // 额外数据
          otherData : {
            // 预算类型
            typeList : [
              { value: '1', label: '月度预算' },
              { value: '2', label: '季度预算' },
              { value: '3', label: '年度预算' },
            ],
            // 季度
            quarterList : [
              { value: '1', label: '第一季度' },
              { value: '2', label: '第二季度' },
              { value: '3', label: '第三季度' },
              { value: '4', label: '第四季度' },
            ],
            // 部门列表
            deptSubcomIds : [],
            deptSubcomNames : [],
            // 季度、月
            quarter : '',
            month : '',
            // 公司、公司id
            companyName : '',
            companyId : ''
          },
          form : {
            id : '',
            // 项目
            title : '',
            // 类型
            budgetType : "1",
            // 年份
            year : '',
            // 第几季度，或者第几月
            budgetNo : '',
            // 公司id
            deptId : '',
            // 公司名称
            deptName : '',
            // 部门ID
            deptSubcomIds : [],
            // 部门名称
            deptSubcomNames : [],
            // 情况说明
            remark : '',
          },
        },
        //浏览角色模式
        viewRole : false,
      }
    },
    mounted(){
      this.init();
    },
    watch:{
      // 监听路由
      $route(to,from){
        if(to.name == 'costBudgetIndexDepartment') {
          console.log('test')
          this.init();
        }
      },
    },
    methods:{
      // 对话框-----标题动态改变
      titleChange(){
        let budgetType = this.dialog.form.budgetType;
        let year = this.$tool.formatDateTime(this.dialog.form.year) && this.$tool.formatDateTime(this.dialog.form.year).substring(0, 4);
        let budgetNo = this.dialog.form.budgetNo;
        let str = '';
        switch(budgetType){
          case '1':
            str = year + "年" + budgetNo +"月费用预算";
            break;
          case '2':
            str = year + "年" + budgetNo +"季度费用预算";
            break;
          case '3':
            str = year + "年费用预算";
            break;
        }
        this.dialog.form.title = str;
      },
      // 对话框--季度选择
      quarterSelect(value){
        this.dialog.form.budgetNo = value;
        this.titleChange()
      },
      // 对话框--月选择
      monthChange(dateStr){
        let dateArr = dateStr.split("-")
        if(dateArr){
          let date = new Date();
          date.setFullYear(dateArr[0]);
          this.dialog.form.year = date;
          this.dialog.form.budgetNo = dateArr[1]
          this.titleChange();
        }
      },
      // 通过部门ID获取子公司信息
      getSubCompanyInfo(){
        this.$store.dispatch('deptGetOrgDept',{}).then(function(res){
          if(res.success){
            let deptList = res.data[0].subDept;
            // 遍历部门
            if (deptList){
              deptList.forEach(function(it){

                this.dialog.otherData.deptSubcomNames.push({
                  label : it.name,
                  value : it.id
                })
              }.bind(this))
            }
          } else {
            this.$message({
              type : 'error',
              message : res.message || '错误2'
            })
          }

        }.bind(this))
      },
      // 选择部门
      deptSubcomNamesSelect(data){
        this.dialog.form.deptSubcomIds = data;
        this.dialog.form.deptSubcomNames = [];
        let deptSubcomNames = [];
        // 通过id来着名称
        for( var i = 0; i < this.dialog.otherData.deptSubcomNames.length; i ++ ){
          let item = this.dialog.otherData.deptSubcomNames[i];

          if(data.indexOf(item.value) > -1){
            deptSubcomNames.push(item.label)
          }
        }
        this.dialog.form.deptSubcomNames = deptSubcomNames;
      },
      // 初始化
      init(){
        let user = this.$tool.getStorage("LOGIN_USER");
        this.dialog.otherData.companyName = user.companyName;
        this.dialog.otherData.companyId = user.companyId;
        this.viewRole = this.$tool.judgeViewRole();
        this.getSubCompanyInfo();
        // 搜索
        this.searchBtnClickHandle();
      },
      clear(){
        this.dialog.form = this.$tool.clearObj({}, this.dialog.form);
      },
      // 格式化时间
      formatDateTime(row, column, cellValue){
        let pro = column.property;
        let num = 10;
        // 年份4位 1999
        if(pro === 'year') num = 4;
        let str = this.$tool.formatDateTime(row[pro] || 0);
        return str ? str.substring(0, num) : str;
      },
      // 分页
      disasterPageChangeHandle(page){
        this.form.pageCurrent = page;
        this.searchBtnClickHandle();
      },
      // 搜索按钮
      searchBtnClickHandle(){
//        this.clear();
        // costBudgetPlanListFind
        this.$store.dispatch('costBudgetPlanGetDeptBudget', this.form).then(function(res){
          if(res.success){
            this.tableData = res.data;
          } else {
            this.$message({
              type : 'error',
              message : res.message || '错误'
            })
          }
        }.bind(this));
      },
      // 查看
      itemViewClick(row){
        let name = 'costBudgetAdd';
        let params = {
          id : row.id,
          status : 'view'
        }
        this.$router.push({ name : name, params : params})
      },
      // 单独打印
      itemPrintOneClick(row){
       this.$store.dispatch('getCostBudgetPlanHtmlInfo', {
          year : 2019
        }).then(function(res){
         let myWindow=window.open("".href, "_blank");
         myWindow.document.write(res);
         myWindow.print()
        }.bind(this))
      },
      // 修改
      itemUpdateClick(row){
//        console.log("row = ", row)
        /*let id = row.id;
        this.form.id = id;
        this.searchBtnClickHandle();*/
        this.$tool.cloneObj(this.dialog.form, row);
        console.log("row.title = ", this.dialog.form.title)
        this.dialog.form.title = row.title
        console.log(this.dialog.form,99999999999)
        this.upload.params.contentId = row.id;
        this.dialog.isShow = true;

      },
      // 删除按钮
      itemDeleteClick(row){
        this.$confirm('此操作将永久删除, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(function(){
            this.$store.dispatch('costBudgetPlanDelete', {
              id : row.id
            }).then(function(res){
              if(res.success){
                this.$message({
                  type : 'success',
                  message : '删除成功'
                })
                this.searchBtnClickHandle();
              } else {
                this.$message({
                  type : 'error',
                  message : res.message || '删除失败！！'
                })
              }
            }.bind(this))
          }.bind(this))
      },
      // 新增按钮
      addBtnClickHandle(){
        let form = this.dialog.form;
        this.dialog.form.id = '';
        this.dialog.form.year = new Date();
        this.dialog.isShow = true;
      },
      // 对话框---确定按钮
      dialogOkBtnClickHandle(){
        let form = this.dialog.form;

        form.deptId = this.$tool.getStorage("LOGIN_USER").companyId;
        form.deptName = this.$tool.getStorage("LOGIN_USER").companyName;
        if(form.title == '' || form.year == ''){
          this.$message({
            type : 'error',
            message : '名称、年份不得为空！！'
          })
          return;
        }
        let url = form.id ? 'costBudgetPlanUpdatePlanMain' : 'costBudgetPlanAddOrUpdate';





//        delete this.dialog.form.deptSubcomNames
        this.$store.dispatch(url, this.dialog.form).then(function(res){
          if(res.success){
            this.$message({
              type : 'success',
              message : '操作成功'
            })
            this.handleClose();
            this.searchBtnClickHandle();
            this.dialog.isShow = false;
          } else {
            this.$message({
              type : 'error',
              message :  res.message || '操作失败'
            })
          }
        }.bind(this));
      },
      // 对话框--关闭
      handleClose(){
        this.dialog.form = this.$tool.clearObj({}, this.dialog.form);
        this.dialog.otherData.deptSubcomIds = []
        this.dialog.isShow = false;
      }
    }
  }
</script>
<style>
</style>
