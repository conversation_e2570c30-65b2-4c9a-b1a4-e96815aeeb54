<template>
  <div id="judgeDialog">
    <el-dialog title="提示" :visible.sync="isShow" width="30%" >
      <div style="margin-left: 20px;font-size: medium;letter-spacing: 1px">{{content}}</div>
      <span slot="footer" class="dialog-footer">
          <el-button @click="buttonClick(0)">{{noText}}</el-button>
          <el-button type="primary" @click="buttonClick(1)">{{yesText}}</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
  export default {
    name: 'judgeDialog',
    props:['data'],//data:{content:'',noText:'',yesText:''}是否显示，对话框显示内容,不同意按钮的文字，同意按键的文字
    data() {
      return {
        isShow:false,
        content:'是否需要审核？',
        noText:'不审核',
        yesText:'审核',
      }
    },
    methods:{
      openJudgeDialog:function (content='是否需要审核？',noText='不审核',yesText='审核') {
        this.content=content;
        this.noText=noText;
        this.yesText=yesText;
        this.isShow=true;
      },
      buttonClick:function (val) {
        if(val){
          this.$emit("buttonClick", true);
          this.isShow=false;
        }else{
          this.$emit("buttonClick", false);
          this.isShow=false;
        }
      }
    }

  }
</script>
<style>
</style>
