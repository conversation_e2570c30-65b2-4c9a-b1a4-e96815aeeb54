<template>
  <div class="background-style" style="padding: 10px">
    <el-container>
      <el-main>
        <el-form ref="form" :model="form" label-width="150px">
          <el-row style="margin:0">
            <el-col :span="12">
              <el-form-item label="宣传教育培训名称">
                <span>{{record.trainingName}}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="主持人">
                <span>{{record.host}}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row style="margin:10px 0 0 0">
            <el-col :span="12">
              <el-form-item label="地点">
                <span>{{record.location}}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="时间">
                <span>{{$tool.formatDateTime(record.trainingDate)}}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row style="margin:10px 0 0 0">
            <el-col :span="12">
              <el-form-item label="参加人员">
                <span>{{record.participants}}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="参加人数">
                <span>{{record.participantsCount}}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row style="margin:10px 0 0 0">
            <el-col :span="24">
              <el-form-item label="活动主题">
                <span>{{record.theme}}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row style="margin:10px 0 0 0">
            <el-col :span="24">
              <el-form-item label="主要活动内容记录">
                <span>{{record.trainingRecord}}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex" justify="center">
            <el-button size="small" :span="2" type="primary" @click="$router.back();">返回</el-button>
          </el-row>
        </el-form>
      </el-main>
    </el-container>
  </div>
</template>

<script>




  export default {
    data(){
      return {
        // 活动的内容
        record : {},
      }
    },
    created(){
      this.init();
    },
    watch:{
      $route(to,from){
        let row = to.params && to.params.row && to.params.row.data;
        if(to.name === 'eduDailyInfosItemRecord') {
          if(row){
            this.record = row;
          }
        }
      }
    },
    methods:{
      // 初始化
      init(){
        this.record = this.$route.params.row.data;
      },
    }
  }
</script>

<style>

</style>
