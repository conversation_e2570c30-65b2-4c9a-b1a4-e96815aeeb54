/*
  created by m<PERSON><PERSON><PERSON> on 2018-4-12
  common data in hide danger
*/
import axios from 'axios'
import http from '@/assets/functions/axiosServer'
// 应急救援
export default {
  state: {

  },
  getters: {},
  mutations: {

  },
  actions : {

    /*
     * 费用预算----总计划
     * */


    // 获取人员列表
    userFindSimple({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('user/findSimple', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    //获取当前公司下的所有部门
    /*deptGetOrgDept({ commit },params){
      return new Promise(function(resolve, project){
        http.post('dept/getOrgDept',params).then(function (res) {
          resolve(res.data);
        });
      })
    },
*/

    // 添加或修改----修改里面的数据
    costBudgetPlanAddOrUpdate({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('costBudgetPlan/addOrUpdate', params).then(function (res) {
          resolve(res.data);
        });
      })
    },


    // 修改----修改里面的数据
    costBudgetPlanUpdatePlanItems({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('costBudgetPlan/updatePlanItems', params).then(function (res) {
          resolve(res.data);
        });
      })
    },

    // 添加或修改---修改最外面的数据
    costBudgetPlanUpdatePlanMain({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('costBudgetPlan/updatePlanMain', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 查找----废弃
    costBudgetPlanFind({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('costBudgetPlan/find', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 查找下属所有公司/部门预算
    costBudgetPlanAllSubCompany({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('costBudgetPlan/findAllSub ', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
  // 查找所属公司预算
  costCompanyBudgetPlanFind({ commit }, params){
    return new Promise(function(resolve, project){
      http.post('costBudgetPlan/findCompanyBudgetPlan', params).then(function (res) {
        resolve(res.data);
      });
    })
  },

    // 查找----按照集团查询
    costBudgetPlanGetGroupBudget({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('costBudgetPlan/getGroupBudget', params).then(function (res) {
          //console.log(33333333, res.data)
          resolve(res.data);
        });
      })
    },
    // 查找----按照公司查询
    costBudgetPlanGetCompanyBudget({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('costBudgetPlan/getCompanyBudget', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 查找----本级预算
    costBudgetPlanGetCommonDeptBudget({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('costBudgetPlan/getCommonDeptBudget', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 查找----按照部门查询
    costBudgetPlanGetDeptBudget({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('costBudgetPlan/getDeptBudget', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 查找
    costBudgetPlanListFind({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('costBudgetPlan/listFind', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 单独打印---后台版-
   /* getCostBudgetPlanHtmlInfo({ commit }, params){
      //let url = 'report/edu/eduList/' + params.year;
      let url = 'report/cost/getCostBudgetPlanHtmlInfo/' + params.year;
      return new Promise(function(resolve, project){
        http.get(url).then(function (res) {
          resolve(res.data);
        });
      })
    },*/
    // 查找明细
    costBudgetPlanFindDetail({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('costBudgetPlan/findDetail', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 单个
    costBudgetPlanShow({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('costBudgetPlan/show', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 删除
    costBudgetPlanDelete({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('costBudgetPlan/delete', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    /*
     * 费用预算----明细----costBudgetItem
     * */
    // 添加修改
    costBudgetItemAddOrUpdate({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('costBudgetItem/addOrUpdate', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 添加二级子项目    POST /costBudgetSubItem/addOrUpdate
    costBudgetSubItemAddOrUpdate({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('costBudgetSubItem/addOrUpdate', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 删除二级子项目    POST /costBudgetSubItem/delete
    costBudgetSubItemDelete({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('costBudgetSubItem/delete', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 查找--本部预算
    costBudgetItemFind({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('costBudgetItem/find', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 查找---集团预算---按照子公司 POST /costBudgetPlan/findSubcompanyItems
    costBudgetPlanFindSubcompany({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('costBudgetPlan/findSubcompany', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 查找---集团预算----按照9大类别 POST /costBudgetPlan/findSubcompany
    costBudgetPlanFindSubcompanyItems({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('costBudgetPlan/findSubcompanyItems', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 查找--单个
    costBudgetItemShow({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('costBudgetItem/show', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    /*
     * 费用预算----明细的明细----costBudgetSubItem
     * */
    // 删除---一级分类
    costBudgetItemDelete({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('costBudgetItem/delete', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    /*
     * 费用投入----总体---POST /costAccountReg/addOrUpdate
     * */
    // 添加或修改
    costAccountRegAddOrUpdate({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('costAccountReg/addOrUpdate', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 查找
    costAccountRegFind({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('costAccountReg/find', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 删除
    costAccountRegDelete({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('costAccountReg/delete', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    /*
     * 费用投入----明细---POST /costAccountRecord/addOrUpdate
     * */
    // 添加或修改
    costAccountRecordAddOrUpdate({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('costAccountRecord/addOrUpdate', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 查找----------
    costAccountRecordFind({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('costAccountRecord/find', params).then(function (res) {
          resolve(res.data);
        });
      })
    },

    // 查找----------预算分析---------按月操作安全投入明细
    costAccountRecordFindByTimeCompany({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('costAccountRecord/findByTimeCompany', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // monthSumRecord
    // 查找----------预算分析---------按年查找月份明细
    costAccountRecordMonthSumRecord({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('costAccountRecord/monthSumRecord', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 删除
    costAccountRecordDelete({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('costAccountRecord/delete', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    /*
     * 预算跟踪----左边表格----costBudgetItem
     * */
    // 根据年份来查找预算计划的项目
    costBudgetItemFindByYear({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('costBudgetItem/findByYear', params).then(function (res) {
          resolve(res.data);
        }).catch(function(res){
          project(res);
        });
      })
    },
    // 根据年份、月度、季度等来查找预算计划的项目
    costBudgetItemFindByYearTypeNo({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('costBudgetItem/findByYearTypeNo', params).then(function (res) {
          resolve(res.data);
        }).catch(function(res){
          project(res);
        });
      })
    },
    // 根据planId更新所有项目的投入金额和余额
    costAccountRecordUpdateAllItemAccount({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('costAccountRecord/updateAllItemAccount', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 根据年份来查找预算计划
    costBudgetPlanSearchByYear({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('costBudgetPlan/searchByYear', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 根据年份来查找预算计划----子公司预算
    costBudgetPlanFindTrackSubcompany({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('costBudgetPlan/findTrackSubcompany', params).then(function (res) {
          resolve(res.data);
        });
      })
    },// 根据年份来查找预算计划----子公司预算
    costBudgetPlanFindTrackSubcompanyAna({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('costBudgetPlan/findTrackSubcompanyAna', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    /*
     * 受伤获赔----总体---POST /costInjuryClaim/addOrUpdate
     * */
    // 添加或修改
    costInjuryClaimAddOrUpdate({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('costInjuryClaim/addOrUpdate', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 查找
    costInjuryClaimFind({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('costInjuryClaim/find', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 删除
    costInjuryClaimDelete({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('costInjuryClaim/delete', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    /*
     * 受伤获赔----细节---POST /costInjuryClaimRecord/addOrUpdate
     * */
    // 添加或修改
    costInjuryClaimRecordAddOrUpdate({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('costInjuryClaimRecord/addOrUpdate', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 查找
    costInjuryClaimRecordFind({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('costInjuryClaimRecord/find', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 删除
    costInjuryClaimRecordDelete({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('costInjuryClaimRecord/delete', params).then(function (res) {
          resolve(res.data);
        });
      })
    },





  }
};
