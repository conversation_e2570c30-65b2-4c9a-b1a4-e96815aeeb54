<template>
  <div id="trainingPlanIndex">
    <div class="background-style">
      <!--搜索区-->
      <div class="search-bar">
        <el-row>
          <el-col :span="4">
            <el-input
              v-model="form.name"
              clearable
              placeholder="请输入标题"
            ></el-input>
          </el-col>
          <el-col :offset="1" :span="4">
            <el-input
              v-model="form.companyName"
              clearable
              placeholder="请输入公司名称"
            ></el-input>
          </el-col>
          <el-col :offset="1" :span="4">
            <el-cascader
              v-model="form.type"
              :options="options"
              clearable
              @change="handlefileTypeChange"
              :props="{ checkStrictly: true }"
              :show-all-levels="false"
            ></el-cascader>
          </el-col>
          <el-col :span="2">
            <el-button
              @click="searchBtnClickHandle"
              type="primary"
              icon="el-icon-search"
              v-if="powerBtns.includes('searchBtn')"
              style="margin-left: 20px"
              >搜索</el-button
            >
          </el-col>
          <el-col :span="2">
            <el-button
              @click="addBtnClickHandle"
              v-if="powerBtns.includes('addBtn')"
              type="success"
              style="margin-left: 20px"
              >新增</el-button
            >
          </el-col>
        </el-row>
      </div>
      <!--表格区-->
      <div style="width: 100%">
        <div style="padding: 20px 10px 20px 10px">
          <el-table
            border
            @row-click="rowclick"
            :data="tableData.list"
            style="width: 100%"
          >
            <el-table-column
              type="index"
              label="编号"
              width="50"
              align="center"
              label-class-name="header-style"
            >
            </el-table-column>
            <el-table-column
              prop="name"
              label="名称"
              label-class-name="header-style"
            >
            <template slot-scope="scope">
              <el-link v-if="scope.row.url"
                type="primary"
                :underline="false"
                :href="scope.row.url"
                target="_blank"
                >{{ scope.row.name}}</el-link>
                <span v-else> {{ scope.row.name }} </span>
              </template>
            </el-table-column>
            <el-table-column
              prop="companyName"
              label="公司名称"
              width="220"
              align="center"
              label-class-name="header-style"
            >
            </el-table-column>
            <el-table-column
              prop="typeName"
              label="类型"
              show-overflow-tooltip
              label-class-name="header-style"
            >
            </el-table-column>
            <el-table-column
              prop="fileName"
              label="文件名称"
              show-overflow-tooltip
              width="160"
              label-class-name="header-style"
            >
              <template slot-scope="scope">
                <el-button
                  type="text"
                  size="mini"
                  style="color: #dd6161"
                  @click="previewBtn(scope.row)"
                  >{{ scope.row.fileName }}</el-button
                >
              </template>
            </el-table-column>
            <el-table-column
              prop="fileNumber"
              width="160"
              label="文件编号"
              show-overflow-tooltip
              label-class-name="header-style"
            >
            </el-table-column>
            <el-table-column
              prop="version"
              label="版本版次"
              show-overflow-tooltip
              label-class-name="header-style"
            >
            </el-table-column>

            <el-table-column
              prop="createTime"
              :formatter="formatDateTime"
              label="创建时间"
              label-class-name="header-style"
            >
            </el-table-column>
            <el-table-column
              prop="effectiveTime"
              :formatter="formatDateTime"
              label="生效时间"
              label-class-name="header-style"
            >
            </el-table-column>
            <el-table-column
              fixed="right"
              label="操作"
              label-class-name="header-style"
              align="left"
              width="160"
            >
              <template slot-scope="scope">
                <el-button
                  v-if="powerBtns.includes('editBtn')"
                  size="mini"
                  type="warning"
                  @click="itemEditClick(scope.row)"
                  >修改</el-button
                >
                <el-button
                  v-if="powerBtns.includes('deleteBtn')"
                  size="mini"
                  type="danger"
                  @click="itemDeleteClick(scope.row)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div style="margin-top: 10px">
          <el-pagination
            background
            layout="prev, pager, next"
            :current-page="tableData.pageNum"
            :page-size="form.pageSize"
            :total="tableData.total"
            @current-change="disasterPageChangeHandle"
          >
          </el-pagination>
        </div>
      </div>
      <!--新增对话框-->
      <el-dialog
        title="对话框"
        :visible.sync="dialog.isShow"
        width="70%"
        :before-close="handleClose"
      >
        <el-form label-width="100px">
          <el-row class="row">
            <el-col :span="12">
              <el-form-item label="名称">
                <el-input clearable v-model="dialog.form.name"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="类型">
                <el-cascader
                  v-model="dialog.form.types"
                  :options="options"
                  @change="handlefileTypeChange"
                  :show-all-levels="false"
                ></el-cascader>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row class="row">
            <el-col :span="12">
              <el-form-item label="文件编号">
                <el-input clearable v-model="dialog.form.fileNumber"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="版本版次">
                <el-input clearable v-model="dialog.form.version"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row class="row">
            <el-col :span="6">
              <el-form-item label="生效时间">
                <el-date-picker
                  clearable
                  v-model="dialog.form.effectiveTime"
                  type="date"
                  placeholder="选择日期"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="排序序号">
                <el-input-number
                  clearable
                  v-model="dialog.form.sort"
                ></el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="来源网站">
                <el-input
                  clearable
                  v-model="dialog.form.url"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="文件上传">
                <fileUpload
                  ref="fileUpload"
                  @fileData="fileDataFn"
                  :data="upload"
                ></fileUpload>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="danger" size="mini" @click="dialogOkBtnClickHandle"
            >确定</el-button
          >
        </div>
      </el-dialog>
      <!--预览文件-->
      <el-dialog
        :title="previewDialog.title"
        width="100%"
        top="0vh"
        :center="true"
        :visible.sync="previewDialog.isShow"
      >
        <iframe
          :src="previewDialog.filePath + '#toolbar=0'"
          width="100%"
          height="810"
        ></iframe>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import fileUpload from "@/components/common/fileUploadFileServer";
export default {
  components: {
    fileUpload,
  },
  data() {
    return {
      form: {
        // 标题
        name: "",
        companyName:"",
        // 类型
        type: ['0','1'],
        // 当前页
        pageCurrent: 1,
        // 页数大小
        pageSize: 10,
      },
      tableData: {},
      // 专题类型
      fileType: [],
      options: [
        {
          label: "安全管理文件",
          value: "0",
          children: [
            { label: "制度", value: "1" },
            { label: "操作规程", value: "2 " },
            { label: "应急预案", value: "3" },
            { label: "法律法规", value: "4" },
          ],
        },
        //法律法规制度
        {
          label: "法律法规制度",
          value: "5",
        },
        //事故案例
        {
          label: "事故案例",
          value: "6",
        },
        //安全科普
        {
          label: "安全科普",
          value: "7",
        },
        //经验分享
        {
          label: "经验分享",
          value: "8",
        },
      ],
      // 对话框
      dialog: {
        // 是否显示
        isShow: false,
        form: {
          id: "",
          name: "",
          fileId: "",
          types: [],
          type: "",
          fileNumber: "",
          version: "",
          effectiveTime: "",
          sort: "",
          url: "",
        },
        assist: {
          planList: [],
        },
      },
      upload: {
        tip: "只能上传pdf文件",
        //          limit : 1,
        params: {
          contentId: this.$tool.getStorage("LOGIN_USER").userId,
          contentType: 21,
          //            fId : -1,
        },
        btns: {
          // 上传按钮
          upload: {
            isShow: true,
          },
          // 下载按钮
          download: {
            isShow: false,
          },
          // 删除按钮
          delete: {
            isShow: true,
          },
        },
        uploadUrl: "",
        uploadCookies: true,
        fileData: [],
      },
      // 预览对话框
      previewDialog: {
        // 是否显示
        isShow: false,
        // 标题
        title: "",
        // 文件路径
        filePath: "",
      },

      // 角色 0 员工 1 发布者
      role: 0,
      // 权限按钮
      powerBtns: [],
    };
  },
  created() {
  },
  mounted() {
    this.init();
  },
  watch: {
    "dialog.form.types": {
      handler: function (newVal, oldVal) {
        newVal[0] = newVal[0] + "";
        if (
          newVal[0] == 1 ||
          newVal[0] == 2 ||
          newVal[0] == 3 ||
          newVal[0] == 4
        ) {
          newVal.unshift("0");
        }
      },
      immediate: true, // 设置初始触发
      deep: true, // 设置深度监听
    },
    // dialog
    $route(to, from) {
      if (to.name === "safeFileIndex") {
        this.init();
      }
    },
  },
  methods: {
    handlefileTypeChange() {
      // console.log(this.fileType);
    },
    // 初始化
    init() {
      this.powerBtns = this.$tool.getPowerBtns2URL(
        "manageMenu",
        "/manage-menu/workflow-manage",
        this.$route.path
      );
      // 根据权限按钮设置角色
      //        this.judgeUserRole();
      // 搜索
      this.searchBtnClickHandle();
    },

    judgeUserRole() {
      // 获取权限按钮
      this.powerBtns = this.$tool.getPowerBtns("manageMenu", this.$route.path);
      console.log(1111, this.powerBtns);

      // 公司
      /*if(btns.includes('addBtn')){
          this.role = 4;
        } else {
          this.role = 1;
        }*/
    },
    goUrl(row) {
      console.log(row);
      window.open(row.url);
    },
    // 格式化时间
    formatDateTime(row, column, cellValue) {
      let pro = column.property;
      let num = 10;
      // 年份4位 1999
      if (pro === "createYear") num = 4;
      let str = this.$tool.formatDateTime(row[pro] || 0);
      return str ? str.substring(0, num) : str;
    },
    // 分页
    disasterPageChangeHandle(page) {
      this.form.pageCurrent = page;
      this.searchBtnClickHandle();
    },
    // 搜索按钮
    searchBtnClickHandle() {
      let params = {};
      if (this.form.name) {
        params["name"] = this.form.name;
      }
      if (this.form.companyName) {
        params["companyName"] = this.form.companyName;
      }
      if (this.form.type) {
        if (this.form.type == 0) {
          params["types"] = [1, 2, 3, 4];
        } else {
          params["types"] = this.form.type;
        }
      }
      params["pageCurrent"] = this.form.pageCurrent;
      params["pageSize"] = this.form.pageSize;
      // console.log(params);
      this.$store.dispatch("sysManageFileFind", params).then(
        function (res) {
          if (res.success) {
            this.tableData = res.data;
          } else {
            this.$message({
              type: "error",
              message: res.message || "错误",
            });
          }
        }.bind(this)
      );
    },
    // 预览文件
    previewBtn(row) {
      //        console.log(row);
      this.previewDialog.title = row.fileName;
      this.previewDialog.filePath = row.filePath;
      this.previewDialog.isShow = true;
    },
    // 删除按钮
    itemDeleteClick(row) {
      //        console.log(row);
      this.$confirm("此操作将永久删除, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(
        function () {
          this.$store
            .dispatch("sysManageFileDelete", {
              id: row.id,
            })
            .then(
              function (res) {
                if (res.success) {
                  this.$message({
                    type: "success",
                    message: "删除成功",
                  });
                  this.searchBtnClickHandle();
                } else {
                  this.$message({
                    type: "error",
                    message: res.message || "删除失败！！",
                  });
                }
              }.bind(this)
            );
        }.bind(this)
      );
    },
    rowclick(row) {
      console.log(row);
    },
    // 修改按钮
    itemEditClick(row) {
      // console.log(row);
      // 赋值
      this.dialog.form.id = row.id;
      this.dialog.form.name = row.name;
      this.dialog.form.fileId = row.fileId;
      this.dialog.form.types = row.types;
      this.dialog.form.fileNumber = row.fileNumber;
      this.dialog.form.version = row.version;
      this.dialog.form.effectiveTime = row.effectiveTime;
      this.dialog.form.sort = row.sort;
      this.dialog.form.url = row.url;

      this.$nextTick(
        function () {
          this.$refs.fileUpload.upload.fileData = [
            {
              fileId: row.fileId,
              fileName: row.fileName,
            },
          ];
        }.bind(this)
      );
      this.dialog.isShow = true;
    },
    // 添加按钮
    addBtnClickHandle() {
      this.clear();
      this.dialog.isShow = true;
    },
    // 文件上传
    fileDataFn(data) {
      console.log("父组件获取子组件数据：", data);
      if (data) {
        if (data.length == 1) {
          let type = data[0].fileData.file.type;
          let fileId = data[0].fileId;
          let fileName = data[0].fileName;
          if (type != "application/pdf") {
            this.$message({
              type: "error",
              message: "只能上传PDF格式的文件",
            });
          }
          // 赋值
          this.dialog.form.fileId = fileId;
        } else if (data.length == 0) {
          this.dialog.form.fileId = "";
        } else {
          this.$message({
            type: "error",
            message: "最多只能上传一张PDF格式的文件",
          });
        }
      } else {
        this.dialog.form.fileId = "";
      }
    },
    // 清空数据
    clear() {
      this.dialog.form.id = "";
      this.dialog.form.name = "";
      this.dialog.form.fileId = "";
      this.dialog.form.type = "";
      this.dialog.form.types = [];
      this.dialog.form.fileNumber = "";
      this.dialog.form.version = "";
      this.dialog.form.effectiveTime = "";
      this.dialog.form.sort = "";
      this.dialog.form.url = "";

      this.$nextTick(
        function () {
          this.$refs.fileUpload.upload.fileData = [];
        }.bind(this)
      );
    },
    // 对话框---确定按钮
    dialogOkBtnClickHandle() {
      /*
        *
        * name  : '', 必填
         fileId  : '',必填
         type  : '',必填
         fileNumber  : '',
         version  : '',
         effectiveTime  : '',
         sort  : '',
         id : '',
        *
        * */

      let form = this.dialog.form;
      //        console.log(form);
      //        return;

      if (form.name == "") {
        this.$message({
          type: "error",
          message: "名称不得为空！！",
        });
        return;
      }
      if (form.fileId == "") {
        this.$message({
          type: "error",
          message: "上传文件不得为空！！",
        });
        return;
      }
      if (form.types == "") {
        this.$message({
          type: "error",
          message: "类型不得为空！！",
        });
        return;
      }

      let params = {
        name: form.name,
        fileId: form.fileId,
        type: form.types[form.types.length - 1],
      };
      if (form.id) {
        params["id"] = form.id;
      }
      if (form.fileNumber) {
        params["fileNumber"] = form.fileNumber;
      }
      if (form.version) {
        params["version"] = form.version;
      }
      if (form.effectiveTime) {
        params["effectiveTime"] = form.effectiveTime;
      }
      if (form.sort) {
        params["sort"] = form.sort;
      }
      if (form.url) {
        params["url"] = form.url;
      }

      console.log(params);
      //        return;

      this.$store.dispatch("sysManageFileAddOrUpdate", params).then(
        function (res) {
          if (res.success) {
            this.$message({
              type: "success",
              message: "操作成功",
            });
            this.handleClose();
            this.searchBtnClickHandle();
            this.dialog.isShow = false;
          } else {
            this.$message({
              type: "error",
              message: res.message || "错误",
            });
          }
        }.bind(this)
      );
    },
    // 对话框--关闭
    handleClose() {
      //        this.dialog.form = this.$tool.clearObj({}, this.dialog.form);
      //        this.dialog.assist.planList = [];
      this.dialog.isShow = false;
      this.clear();
    },
  },
};
</script>
<style>
</style>
