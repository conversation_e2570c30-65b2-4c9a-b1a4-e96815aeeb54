<template>
  <div id="">
    <div class="background-style">

      <!--搜索区-->
      <div class="search-bar">
        <div style="padding:10px 10px 0 10px;float: left">
          <el-button
            @click="$router.back();"
             style="margin-left: 20px">返回</el-button>
        </div>
      </div>

      <!--表格区-->
      <div style="width: 100%;">
        <div style="padding: 20px 10px 20px 10px">
          <el-table
            :data="tableData.list"
            border
            style="width: 100%">
            <el-table-column
              type="index"
              label="编号"
              width="100"
              align="center"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="status"
              label="状态"
              width="150"
              label-class-name="header-style">
              <template slot-scope="scope">
                <el-tag size="mini" v-if="scope.row.status === 0">未发布</el-tag>
                <el-tag size="mini" v-if="scope.row.status === 1" type="danger">进行中</el-tag>
                <el-tag size="mini" v-if="scope.row.status === 2" type="success">已完成</el-tag>
                <el-tag size="mini" v-if="scope.row.status === 3" type="warning">作废</el-tag>
              </template>
            </el-table-column>
            <el-table-column
              prop="companyName"
              label="公司名称"
              min-width="300"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="title"
              label="名称"
              min-width="300"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="createYear"
              label="年份"
              :formatter="formatDateTime"
              width="100"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="startTime"
              label="开始时间"
              :formatter="formatDateTime"
              width="150"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="endTime"
              label="结束时间"
              :formatter="formatDateTime"
              width="150"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              fixed="right" label="操作"
              label-class-name="header-style"
              align="center" width="250">
              <template slot-scope="scope">
                  <el-button size="mini" type="success" @click.native="itemViewClick(scope.row)">查看</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div style="margin-top: 10px">
          <el-pagination
            background
            layout="prev, pager, next"
            :current-page="tableData.pageNum"
            :page-size="form.pageSize"
            :total="tableData.total"
            @current-change ="disasterPageChangeHandle">
          </el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
  export default {
    data() {
      return {
        form : {
          // 状态
//          companyId : '',
          companyId : '',
          // 当前页
          pageCurrent : 1,
          // 页数大小
          pageSize : 10,
        },
        tableData : {},
      }
    },
    mounted(){
      this.init();
    },
    watch:{
      $route(to,from){
        if(to.name === 'demandSurveyPublishLower') {
          this.init();
        }
      }
    },
    methods:{
      // 初始化
      init(){

        let user = this.$tool.getStorage('LOGIN_USER');
        this.form.companyId = user.companyId;
        // 搜索
        this.searchBtnClickHandle();
      },
      // 格式化时间
      formatDateTime(row, column, cellValue){
        let pro = column.property;
        let num = 10;
        // 年份4位 1999
        if(pro === 'createYear') num = 4;
        let str = this.$tool.formatDateTime(row[pro] || 0);
        return str ? str.substring(0, num) : str;
      },
      // 分页
      disasterPageChangeHandle(page){
        this.form.pageCurrent = page;
        this.searchBtnClickHandle();
      },
      // 搜索按钮
      searchBtnClickHandle(){
        console.log('form', this.form);
        this.$store.dispatch('eduReqInvFindSubcompany', this.form).then(function(res){
          if(res.success){
            this.tableData = res.data;
          } else {
            this.$message({
              type : 'error',
              message : res.message || '错误'
            })
          }
        }.bind(this));
      },
      // 查看
      itemViewClick(row){
        let name = '';
        let params = {
          id : row.id,
          companyId : row.companyId,
          status : 'view'
        }
        switch(row.status){
          case 0:
          case 1:
          case 2:
            name = 'demandSurveyPublishAdd';
            break;
        }
        console.log('参数',params);
//        return;
        this.$router.push({ name : name, params : params})
      },

    }
  }
</script>
<style>
</style>
