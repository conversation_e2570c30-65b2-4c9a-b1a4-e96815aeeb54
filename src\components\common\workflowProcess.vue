<template>
  <div id="">
    <div style="width: 100%;padding-top: 10px;padding-bottom:10px;float: left;background-color: #f2f2f2">
      <i class="el-icon-upload" style="color:#049ff1;float: left;margin:12px 10px 0 20px"></i>
      <span style="color:#049ff1;width: 200px;float: left;">流程信息</span>
    </div>
    <div style="width: 100%;float:left;padding: 20px">
      <el-col :span="24">
        <el-table
          :data="historicTasks"
          border
          style="width: 100%">
          <el-table-column
            type="index"
            align="center"
            label-class-name="inner-header-style"
            width="50">
          </el-table-column>
          <el-table-column
            prop="name"
            label="环节名称"
            align="center"
            label-class-name="inner-header-style"
            width="320">
          </el-table-column>
          <el-table-column
            prop="userName"
            label="办理人"
            align="center"
            label-class-name="inner-header-style"
            min-width="150">
          </el-table-column>
          <el-table-column
            prop="result"
            label="处理结果"
            align="center"
            label-class-name="inner-header-style"
            min-width="150">
          </el-table-column>
          <el-table-column
            prop="comment"
            label="审批意见"
            align="center"
            label-class-name="inner-header-style"
            min-width="150">
          </el-table-column>
          <el-table-column
            prop="endTime"
            label="办理时间"
            :formatter="formatDateTime"
            align="center"
            label-class-name="inner-header-style"
            min-width="150">
          </el-table-column>
          <el-table-column
            prop="createTime"
            label="创建时间"
            :formatter="formatDateTime"
            align="center"
            label-class-name="inner-header-style"
            min-width="150">
          </el-table-column>
        </el-table>
      </el-col>
      <el-col :span="24">
        <img :src="imageUrl" style="width: 100%;margin: auto;padding-bottom: 20px">
      </el-col>
    </div>

  </div>
</template>

<script>
  export default {
    props: ['data', 'processInstanceId'],
    data() {
      let that = this;
      return {
        form: {
          processInstanceId: "",
          finish: false,
          processDefId: ""
        },
        historicTasks: [],
        imageUrl: '',
      }

    },
    mounted() {
      // 初始化数据
      this.init();
      this.loadWorkflowProcess()
    },
    watch: {
      processInstanceId: function (val) {
        this.init();
        this.loadWorkflowProcess()
      }
    },
    methods: {
      // 初始化数据
      init() {
        // 把父组件传递的值给子组件
        Object.entries(this.data).forEach(function (it) {
          if (this.form.hasOwnProperty(it[0])) {
            this.form[it[0]] = it[1];
          }
        }.bind(this))
        this.imageUrl = this.$http.defaults.baseURL + "/emgFlow/showFlowImg/" + this.form.processInstanceId
//        console.log(this.form,'hhhhhhhhh');
      },
      loadWorkflowProcess() {
        this.$http.get("/emgFlow/getProcessHistoryTask/" + this.form.processInstanceId).then(function (res) {
          if (res.data.success) {
            this.historicTasks = res.data.data;
          }
        }.bind(this)).catch(function (err) {
          console.info(err)
        })
      },
      // 格式化时间
      formatDateTime(row, column, cellValue) {
        let pro = column.property;
        let num = 16;
        let str = this.$tool.formatDateTime(row[pro]) || '';
        return str ? str.substring(0, num) : str;
      },
    }
  }
</script>

<style scoped>

</style>
