<template>
    <div class="background-style" style="padding: 10px">
      <el-container>
        <el-aside width="400px">
          <el-col :span="24">
            <egrid class="egrid"
                   stripe border
                   :data="egrid.data"
                   :columns="egrid.columns"
                   :columns-schema="egrid.columnsSchema"
                   :column-type="egrid.columnType"
                   @current-change="selectionChange">
            </egrid>
          </el-col>
        </el-aside>
        <el-main  v-if="isClickPlanTable" style="padding: 0 0 0 10px">
          <el-col :span="24">
            <egrid class="egrid"
                   stripe border
                   :data="item.data"
                   :columns="item.columns"
                   :columns-schema="item.columnsSchema"
                   :columns-handler="columnsHandler"
                   :column-type="item.columnType">
            </egrid>
          </el-col>
        </el-main>
      </el-container>
    </div>
</template>

<script>
    // 单元格的组件
    var Editor = {
      template:
        `<div>
          <el-button type="primary" size="mini" @click="viewBtnClickHandler">查看</el-button>
          <el-button
            v-if="row.name != 'eduRequirementInvestigation'"
            type="danger" size="mini" @click="downloadBtnClickHandler">下载</el-button>
        </div>`,
      props: ['row', 'col'],
      methods:{
        // 查看按钮
        viewBtnClickHandler(){
          this.$emit('row-view', this.row)
        },
        // 下载按钮
        downloadBtnClickHandler(){
          this.$emit('row-download', this.row)
        }
      }
    }

    export default {
      data(){
        return {
          form : {
            // 当前页
            pageCurrent : 1,
            // 页数大小
            pageSize : 20,
          },
          // 表格---左边计划
          egrid : {
            data : [],
            columns : [
              { label: '名称', prop: 'title' },
              { label: '年份', prop: 'year' },
            ],
            // columnsProps 用于定义所有 columns 公共的属性
            columnsProps: {
              fit : true,
              sortable: true,
              align : 'center',
            },
            columnsSchema : {
              '年份':{
                width : 100,
              }
            },
            columnType : 'index'
          },
          // 表格---右边明细
          item : {
            data : [],
            columns : [
              { label: '台账', prop: 'title' },
              { label: '年份', prop: 'year' },
            ],
            // columnsProps 用于定义所有 columns 公共的属性
            columnsProps: {
              fit : true,
              sortable: true,
              align : 'center',
            },

            columnsSchema : {
              '年份':{
                width : 100,
              }
            },
            // 下载表格对应的字段
            map : {
              keys : [
                'sysUserReports', 'eduCertificates', 'eduEntryTrainings', 'eduDailyInfos',
                'eduPlanList', 'eduReassignments', 'eduRequirementInvestigation'
              ],
              values : [
                '从业人员花名册',
                '安全生产相关负责人及安全生产管理人员安全培训教育登记表',
                '新入职人员安全培训教育登记表',
                '从业人员日常安全培训教育登记表',
                '安全培训教育计划表',
                '转岗、调岗、脱岗人员安全培训教育登记表',
                '安全培训教育需求调查表',
              ],
            }
          },

          // 是否点击了左边的计划
          isClickPlanTable : false,
        }
      },
      mounted(){
        this.init();
      },
      watch:{
        $route(to,from){
          if(to.name === 'accountEduIndex') {
            this.init();
          }
        }
      },
      methods:{
        // 初始化
        init(){
          this.isClickPlanTable = false;
          // 搜索
          this.searchBtnClickHandle();
        },
        // 左边表格数据-----搜索按钮
        searchBtnClickHandle(){
          this.$store.dispatch('reportEduFindYear', this.form).then(function(res){
            if(res.success){
              let list = res.data.list.map(function(it){
                return {
                  title : `${it}年教育培训台账`,
                  year : it
                }
              }.bind(this))
              this.egrid.data = list;
            }
          }.bind(this));
        },
        // 左边表格数据-----单行选择
        selectionChange (row) {
          const loading = this.$loading({
            lock: true,
            text: 'Loading',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });
          this.$store.dispatch('reportEduEduList', {
            year : row.year
          }).then(function(res){
            if(res.success){
              loading.close()
              this.isClickPlanTable = true;
              let list = res.data.titles.map(function(it){
                let filed = this.item.map.keys[this.item.map.values.indexOf(it)];
                return {
                  title : it,
                  year : row.year,
                  name : filed || '',
                  data : res.data[filed] || ''
                }
              }.bind(this))
              console.log(list);
              this.item.data = list;
            }
          }.bind(this));
        },
        // egrid---操作列
        columnsHandler (cols) {
          let that = this;
          return cols.concat({
            label: '操作',
            fixed: 'right',
            width: 200,
            component: Editor,
            listeners: {
              'row-view' (row) {
                let name = '';
                switch(row.name){
                  // 从业人员花名册
                  case 'sysUserReports':
                    name = 'sysUserReports';
                    break;
                  // 需求调查
                  case 'eduRequirementInvestigation':
                    name = 'eduRequirementInvestigation';
                        break;
                  // 培训计划
                  case 'eduPlanList':
                    name = 'eduPlanList';
                    break;
                  // 持证培训
                  case 'eduCertificates':
                    name = 'eduCertificates';
                    break;
                  // 三级培训
                  case 'eduEntryTrainings':
                    name = 'eduEntryTrainings';
                    break;
                  // 转岗培训
                  case 'eduReassignments':
                    name = 'eduReassignments';
                    break;
                  // 日常培训
                  case 'eduDailyInfos':
                    name = 'eduDailyInfosIndex';
                    break;
                }
                that.$router.push({
                  name : name,
                  params : {
                    row : row
                  }
                })
              },
              'row-download'(row){
                console.log(row)
                let setting = {
                  url : '',
                  filename : '',
                }
                switch(row.name){
                  case 'sysUserReports':
                    setting.url = 'report/edu/eduUserListExcel/' + row.year;
                    setting.filename = row.year + "年从业人员花名册.xlsx";
                    break;
                  case 'eduRequirementInvestigation':
                    setting.url = 'report/edu/eduRequirementInvestExcel/' + row.year;
                    setting.filename = row.year + "年安全培训教育需求调查表.xlsx";
                    return;
                    break;
                  case 'eduPlanList':
                    setting.url = 'report/edu/eduPlanExcel/' + row.year;
                    setting.filename = row.year + "年安全培训教育计划表.xlsx";
                    break;
                  case 'eduCertificates':
                    setting.url = 'report/edu/eduCertificateExcel/' + row.year;
                    setting.filename = row.year + "年安全生产相关负责人及安全生产管理人员安全培训教育登记表.xlsx";
                    break;
                  case 'eduEntryTrainings':
                    setting.url = 'report/edu/eduTrainExcel/' + row.year;
                    setting.filename = row.year + "年新入职人员安全培训教育登记表.xlsx";
                    break;
                  case 'eduReassignments':
                    setting.url = 'report/edu/eduReassignmentExcel/' + row.year;
                    setting.filename = row.year + "年转岗、调岗、脱岗人员安全培训教育登记表.xlsx";
                    break;
                  case 'eduDailyInfos':
                    setting.url = 'report/edu/eduDailyInfoExcel/' + row.year;
                    setting.filename = row.year + "年从业人员日常安全培训教育登记表.xlsx";
                    break;
                }
                that.$tool.download(that, setting);
              }
            }
          });
        },
      }
    }
</script>

<style>

</style>
