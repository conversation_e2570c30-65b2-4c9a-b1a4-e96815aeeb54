<template>
  <el-container class="container">
    <el-main>
      <el-collapse>
        <el-collapse-item title="培训发布" name="1">
          <!--发布-->
          <el-form ref="info" label-width="100px" :model="info">
            <el-row type="flex">
              <el-col :span="8">
                <el-form-item label="培训时间">
                  <span>{{ this.$tool.formatDateTime(info.trainingDate) }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="学时">
                  <span>{{ info.trainingHours }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="培训内容">
                  <span>{{ info.courses }}</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row type="flex">
              <el-col :span="8">
                <el-form-item label="参加人员">
                  <span>{{ info.participants }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="组织部门">
                  <span>{{ info.department }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="地点">
                  <span>{{ info.location }}</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
            <!-- 是否打卡 -->
            <el-form-item label="是否打卡">
              <el-radio-group v-model="isPunch">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col v-if="isPunch" :span="12">
            <!-- 打卡位置: -->
            <el-button @click="dialogTableVisible = true">打卡位置:</el-button>
            <span>{{ location.poiname }}</span>
            <span style="margin-left: 20px;">详细地址:{{ location.poiaddress }}</span>
            <el-dialog width="400px" title="打卡位置" :visible.sync="dialogTableVisible" class="ka">
              <iframe class="if" id="mapPage" width="100%" height="600px" frameborder=0 src="https://apis.map.qq.com/tools/locpicker?search=1&type=1&key=MNFBZ-B5DWQ-37N5W-4AIZ6-P7EZQ-O4BDK&referer=myapp">
              </iframe>
            </el-dialog>
          </el-col>
            </el-row>
            <el-row type="flex">
              <el-col :span="24">
                <el-form-item label="预计参加人员">
                  <chooseStaff ref="chooseStaff"></chooseStaff>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row type="flex">
              <el-col :span="8">
                <el-form-item label="通知列表">
                  <ul style="cursor: pointer;color:blue;">
                    <li v-for="o in info.eduDailyNotify" @click="chooseNotify(o)">{{ o.title }}</li>
                  </ul>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row type="flex">
              <el-col :span="8">
                <el-form-item label="通知标题">
                  <el-input v-model="assist.eduDailyNotify.title"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row type="flex">
              <el-col :span="24">
                <el-form-item label="通知内容">
                  <vue-editor v-model="assist.eduDailyNotify.notify"></vue-editor>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-collapse-item>
        <el-collapse-item title="培训执行" name="2">
          <!--执行-->
          <el-form ref="record" label-width="100px" :rules="recordRules" :model="record">
            <el-row type="flex" class="row">
              <el-col :span="8">
                <el-form-item label="培训名称" prop="trainingName">
                  <el-input v-model="record.trainingName"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="主持人" prop="host">
                  <el-input v-model="record.host"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="地点" prop="location">
                  <el-input v-model="record.location"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row type="flex">
              <el-col :span="8">
                <el-form-item label="时间" prop="trainingDate">
                  <el-date-picker v-model="record.trainingDate" type="date">
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="参加人员" prop="participants">
                  <el-input v-model="record.participants">
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="参加人数">
                  <el-input v-model="record.participantsCount">
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row type="flex">
              <el-col :span="24">
                <el-form-item label="实参人员列表" prop="participantsCount">
                  <chooseStaffPage @selectedRows="selectedRowsHandle" :staffData.sync="assist.eduDailyParticipants" ref="chooseStaffPage"></chooseStaffPage>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row type="flex">
              <el-col :span="16">
                <el-form-item label="活动主题" prop="theme">
                  <el-input v-model="record.theme"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row type="flex">
              <el-col :span="24">
                <el-form-item label="活动内容记录" prop="trainingRecord">
                  <el-input v-model="record.trainingRecord" type="textarea" :rows="2"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row type="flex">
              <el-col :span="16">
                <el-form-item label="考试考核">
                  <el-table :data="examineListTable" border>
                    <el-table-column label-class-name="header-style" label="序号" width="100" type="index">
                    </el-table-column>
                    <el-table-column label-class-name="header-style" prop="examineName" label="考核表名称" width="300">
                    </el-table-column>
                    <el-table-column fixed="right" label="操作" label-class-name="header-style" align="center" width="200">
                      <template slot-scope="scope">
                        <el-button size="mini" type="primary" @click="itemUpdateClick(scope.row)">查看</el-button>
                        <el-button size="mini" type="danger" @click="itemDeleteClick(scope.row)">删除</el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row type="flex">
              <el-col :offset="3" :span="4">
                <el-button size="small" type="success" @click="addExamineClickHandle">新增</el-button>
              </el-col>
            </el-row>
            <el-row type="flex" class="row">
              <el-col :span="24">
                <el-form-item>
                  <fileUpload ref="upload" :data="upload"></fileUpload>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row type="flex" justify="center">
              <el-button @click="saveDataBtnClickHandle" size="small" type="success">保存数据</el-button>
              <el-button @click="overBtnClickHandle" size="small" type="danger">结束活动</el-button>
            </el-row>
          </el-form>
        </el-collapse-item>
      </el-collapse>
    </el-main>
    <el-footer>
      <!--考核表对话框-->
      <el-dialog title="考核表" :visible.sync="examineDialog.isShow" width="70%">
        <!--考核-->
        <el-form ref="examineScore" label-width="100px">
          <el-row type="flex" class="row">
            <el-col :span="8">
              <el-form-item label="管理部门">
                <span v-if="examineDialog.status === 'view'">{{ examineList.department }}</span>
                <el-input v-if="examineDialog.status === 'edit'" v-model="examineList.department"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="考核时间">
                <span v-if="examineDialog.status === 'view'">{{ examineList.examineTime }}</span>
                <el-date-picker v-if="examineDialog.status === 'edit'" style="width:175px;" v-model="examineList.examineTime" type="date">
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="考核地点">
                <span v-if="examineDialog.status === 'view'">{{ examineList.examineLocation }}</span>
                <el-input v-if="examineDialog.status === 'edit'" v-model="examineList.examineLocation"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex">
            <el-col :span="8">
              <el-form-item label="考核表名称">
                <span v-if="examineDialog.status === 'view'">{{ examineList.examineName }}</span>
                <el-input v-if="examineDialog.status === 'edit'" v-model="examineList.examineName"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="考核表项目">
                <span v-if="examineDialog.status === 'view'">{{ examineList.examineContent }}</span>
                <el-input v-if="examineDialog.status === 'edit'" v-model="examineList.examineContent"></el-input>
              </el-form-item>
            </el-col>
            <template v-if="examineDialog.status === 'view'">
              <el-col :offset="1" :span="2">
                <el-button type="danger" @click="examineDialog.isMore = true;">批量打分</el-button>
              </el-col>
              <el-col :offset="1" :span="2">
                <el-button type="success" @click="saveScoreBtnClickHandle">保存分数</el-button>
              </el-col>
            </template>
          </el-row>
          <el-row type="flex" v-if="examineDialog.status === 'edit'">
            <el-col :span="24">
              <el-form-item label="考核人员">
                <el-table ref="participantsTable" @selection-change="selectionChangeDialog" :data="examineListParticipants">
                  <el-table-column width="70" type="selection">
                  </el-table-column>
                  <el-table-column prop="username" label="姓名" width="120">
                  </el-table-column>
                  <el-table-column prop="companyName" show-overflow-tooltip label="分数" width="150">
                  </el-table-column>
                </el-table>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex" v-if="examineDialog.status === 'view'">
            <el-col :span="24">
              <el-form-item label="考核人员">
                <el-table ref="participantsTable" :data="examineList.eduDailyExamineScores">
                  <el-table-column width="70" type="selection">
                  </el-table-column>
                  <el-table-column prop="eduUser.username" label="姓名" width="120">
                  </el-table-column>
                  <el-table-column prop="score" show-overflow-tooltip label="分数" align="center" width="300">
                    <template slot-scope="scope">
                      <span v-if="!examineDialog.isMore">{{ scope.row.score }}</span>
                      <el-input-number v-if="examineDialog.isMore" v-model="examineDialog.more.scores[scope.$index]" :min="0" :max="100" :step="5" label="描述文字"></el-input-number>
                    </template>
                  </el-table-column>
                </el-table>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button v-if="examineDialog.status === 'edit'" type="primary" @click="examineOkBtnClickHandle">确 定</el-button>
        </span>
      </el-dialog>
    </el-footer>
  </el-container>
</template>

<script>
import { VueEditor } from 'vue2-editor'
import fileUpload from '@/components/common/fileUpload'
import chooseStaff from '@/components/common/chooseStaff'
import chooseStaffPage from '@/components/common/chooseStaffPage'
export default {
  components: {
    chooseStaff,
    chooseStaffPage,
    VueEditor,
    fileUpload
  },
  data() {
    let that = this;
    return {
      // 打卡位置弹窗状态
      dialogTableVisible: false,
      isPunch: 0,
      //选择的打卡位置
      location: {
        "module": "locationPicker",
        "latlng": {
          "lat": 29.899166,
          "lng": 121.629406
        },
        "poiaddress": "",
        "poiname": "",
        "cityname": ""
      },
      // ###########培训发布#########
      // info表
      info: {
        // ID
        id: '',
        // 培训时间
        trainingDate: '',
        // 时长
        trainingHours: '',
        // 培训内容
        courses: '',
        // 地点
        location: '',
        // 参加人员
        participants: '全体人员',
        // 参与人员列表
        eduDailyParticipants: [],
        // 组织部门
        department: '',
        // 活动通知
        eduDailyNotify: [],
        // 状态 0 未发布    1 已发布    2 进行中    3 待评级    4 已完结
        status: '0',
      },
      // 辅助字段
      assist: {
        // 通知
        eduDailyNotify: {
          title: '',
          notify: '',
        },
        // 参与人员表格
        eduDailyParticipants: [],
      },
      // 参与人员对话框数据
      staffDialog: {
        isShow: false,
      },
      // ###########END#######

      // ###########培训执行#########
      // record表
      record: {
        // 自己的id
        id: '',
        // 主表infoID
        infoId: '',
        // 培训名称
        trainingName: '',
        // 主持人
        host: '',
        // 地点
        location: '',
        // 时间
        trainingDate: '',
        // 参与人员数组
        eduDailyParticipants: [],
        // 参加人员
        participants: '',
        // 参加人数
        participantsCount: 0,
        // 活动主题
        theme: '',
        // 活动内容记录
        trainingRecord: '',
      },
      recordRules: {
        trainingName: [
          { required: true, message: '请输入内容', trigger: 'blur' },
        ],
        host: [
          { required: true, message: '请输入内容', trigger: 'blur' },
        ],
        location: [
          { required: true, message: '请输入内容', trigger: 'blur' },
        ],
        trainingDate: [
          { required: true, message: '请选择日期', trigger: 'blur' },
        ],
        eduDailyParticipants: [
          { required: true, message: '请选择人员', trigger: 'blur' },
        ],
        participants: [
          { required: true, message: '请输入内容', trigger: 'blur' },
        ],
        participantsCount: [
          { required: true, message: '请选择实参人员', trigger: 'change' },
        ],
        theme: [
          { required: true, message: '请输入内容', trigger: 'blur' },
        ],
        trainingRecord: [
          { required: true, message: '请输入内容', trigger: 'blur' },
        ],
      },
      // 考核表
      examineList: {
        // 自己得ID
        id: '',
        // 主表记录表ID
        recordId: '',
        // 管理部门
        department: '',
        // 考核时间
        examineTime: '',
        // 考核地点
        examineLocation: '',
        // 考核表名称
        examineName: '',
        // 考核项目
        examineContent: '',
        // 人员列表
        eduDailyExamineScores: [],
      },
      // 考核表---table
      examineListTable: [],
      // 考核表---参与人员列表
      examineListParticipants: [],
      // 考核表对话框
      examineDialog: {
        // 是否显示
        isShow: false,
        // 状态--edit或者view
        status: 'edit',
        // 是否打分
        isMore: false,
        more: {
          ids: [],
          scores: []
        },
      },
      // 上传文件
      upload: {
        // 上传参数
        params: {
          contentId: '',
          contentType: 7
        },
      },
      // ###########END#######

    }
  },
  watch: {
    $route(to, from) {
      // 如果来至列表页
      if (from.name === 'dailyTrainingIndex' && this.$route.name === 'dailyTrainingProcessHaveInHand') {
        this.init();
      }
    },
  },
  created() {
    this.init();
  },
  mounted() {
    this.init();
    window.addEventListener('message', (event) => {
      // 接收位置信息，用户选择确认位置点后选点组件会触发该事件，回传用户的位置信息
      var loc = event.data;
      if (loc && loc.module == 'locationPicker') {//防止其他应用也会向该页面post信息，需判断module是否为'locationPicker'
        console.log('location', loc);
        // 此处可以将用户的位置信息发送给后端，进行保存
        this.location = loc
      }
      this.dialogTableVisible = false;
    }, false);
  },
  methods: {
    // 初始化
    init() {
      if (this.$route.params.status) {
        this.searchBtnClickHandle();
      } else {
        this.clear();
      }
    },
    // 清空数据
    clear() {
      this.info = this.$tool.clearObj({}, this.info);
      this.assist = this.$tool.clearObj({}, this.assist);
      this.record = this.$tool.clearObj({}, this.record);
      this.examineList = this.$tool.clearObj({}, this.examineList);
      this.examineListTable = [];
      this.examineListParticipants = [];
    },
    // ############培训发布###############
    // 选择通知
    chooseNotify(item) {
      Object.entries(item).forEach(function (it) {
        if (it[1] && this.assist.eduDailyNotify.hasOwnProperty(it[0])) {
          this.assist.eduDailyNotify[it[0]] = it[1];
        }
      }.bind(this));
    },
    // 根据id搜索信息
    searchBtnClickHandle() {
      this.clear();
      let id = this.$route.params.id;
      // 信息表info信息
      // eduDailyInfoFind
      this.$store.dispatch('eduDailyInfoShow', { id: id }).then(function (res) {
        if (res.success) {
          let list = res.data.list[0];
          // 发布培训信息
          Object.entries(list).forEach(function (it) {
            if (it[1] && this.info.hasOwnProperty(it[0])) {
              this.info[it[0]] = it[1];
            }
          }.bind(this));
          // 打卡位置
          this.location.poiaddress = list.poiaddress
          this.location.poiname = list.poiname
          this.location.longitude = list.longitude
          this.location.latitude = list.latitude
          this.isPunch = list.isPunch;
          // 通知
          if (this.info.eduDailyNotify.length > 0) {                   //add by pdn  2020.11.12
            Object.entries(this.info.eduDailyNotify[0]).forEach(function (it) {
              if (it[1] && this.assist.eduDailyNotify.hasOwnProperty(it[0])) {
                this.assist.eduDailyNotify[it[0]] = it[1];
              }
            }.bind(this));
          }
          // 参与人员
          this.record.participantsCount = list.eduDailyParticipants.length;
          this.assist.eduDailyParticipants = list.eduDailyParticipants.map(function (it) {
            return {
              participate: it.participate,
              userId: it.userId,
              companyName: it.eduUser.companyName,
              deptName: it.eduUser.deptName,
              username: it.eduUser.username,
            }
          });

          this.$nextTick(() => {
            // 发布培训的人员列表
            this.$refs['chooseStaff'].changeTableDataHandle(this.assist.eduDailyParticipants);
            // edit 添加和修改的时候，按钮显示；view 查看的时候，按钮隐藏
            this.$refs['chooseStaff'].isShowBtnHandle(true);
            // 执行培训的人员列表
            this.$refs['chooseStaffPage'].isShowSelectionHandle(false);
            this.$refs['chooseStaffPage'].isShowDeleteHandle(false);
            this.$refs['chooseStaffPage'].isShowParticipateHandle(true);
            this.$refs['chooseStaffPage'].isEditDisableHandle(false);
            this.$refs['chooseStaffPage'].setTotalTableDataHandle(this.assist.eduDailyParticipants);
          })

          // 执行培训
          Object.entries(list.eduDailyRecord).forEach(function (it) {
            if (it[1] && this.record.hasOwnProperty(it[0])) {
              this.record[it[0]] = it[1];
            }
          }.bind(this));
          // 培训执行：培训名称、地点、参加人员如果没有填写，从发布里取出来
          if (!this.record.trainingName) this.record.trainingName = this.info.courses;
          if (!this.record.location) this.record.location = this.info.location;
          if (!this.record.participants) this.record.participants = this.info.participants;

          // 记录表record信息
          this.getRecordAndExamineList();
        } else {
          this.$message({
            type: 'error',
            message: res.message || '错误'
          })
        }
      }.bind(this));
    },
    // ############培训发布--END--###############

    // ############培训执行###############
    // 获取执行培训record记录表和考核表examine-list
    getRecordAndExamineList() {
      let id = this.$route.params.id;
      this.$store.dispatch('eduDailyRecordFind', { infoId: id }).then(function (res) {
        if (res.success) {
          if (res.data.list.length) {
            let list = res.data.list[0];
            // 记录表的ID
            this.upload.params.contentId = list.id;

            this.$nextTick(function () {
              this.$refs['upload'].loadFile();
            }.bind(this))
            // 考核表examine-list
            this.$store.dispatch('eduDailyExamineListFind', { recordId: list.id }).then(function (res) {
              if (res.success) {
                this.examineListTable = res.data.list;
              } else {
                this.$message({
                  type: 'error',
                  message: res.message || '错误'
                })
              }
            }.bind(this));
          }
        } else {
          this.$message({
            type: 'error',
            message: res.message || '错误'
          })
        }
      }.bind(this));
    },
    // 执行培训实参人员勾选
    selectedRowsHandle(val) {
      //this.record.participantsCount = val.length;
      let selected = val.map(function (it) {
        return it.userId;
      })
      this.record.eduDailyParticipants = this.info.eduDailyParticipants.map(function (it) {
        let flag = selected.includes(it.userId) ? true : false;
        return {
          participate: flag,
          userId: it.userId,
          eduDailyInfoId: this.info.id,
        }
      }.bind(this))
    },
    // 执行培训---保存数据按钮
    saveDataBtnClickHandle() {
      this.assist.eduDailyParticipants.forEach(item => {
        if (item.participate != true) {
          item.participate = false;
        }
      })
      this.record.eduDailyParticipants = this.assist.eduDailyParticipants;
      this.record.infoId = this.info.id;
      let params = this.$tool.filterObj({}, this.$tool.filterObj({}, this.record));
      this.$store.dispatch('eduDailyRecordAddOrUpdate', params).then(function (res) {
        if (res.success) {
          this.$message({
            type: 'success',
            message: '操作成功'
          })
          this.$router.push({ name: 'dailyTrainingIndex' })
        } else {
          this.$message({
            type: 'error',
            message: res.message || '错误'
          })
        }
      }.bind(this))
    },
    // 执行培训---结束按钮
    overBtnClickHandle() {
      this.record.eduDailyParticipants = this.assist.eduDailyParticipants;
      this.record.infoId = this.info.id;
      let params = this.$tool.filterObj({}, this.$tool.filterObj({}, this.record));

      this.$refs['record'].validate(function (valid) {
        if (valid) {
          if (this.record.participantsCount === 0) {
            this.$message({
              type: 'error',
              message: '请选择实际参加培训的人员！！'
            })
            return;
          }
          this.$store.dispatch('eduDailyRecordAddOrUpdate', params).then(function (res) {
            if (res.success) {
              this.$store.dispatch('eduDailyInfoAddOrUpdate', {
                id: this.record.infoId,
                status: 3
              }).then(function (res) {
                this.$message({
                  type: 'success',
                  message: '操作成功'
                })
                this.$router.push({ name: 'dailyTrainingIndex' })
              }.bind(this));
            } else {
              this.$message({
                type: 'error',
                message: res.message || '错误'
              })
            }
          }.bind(this))
        } else {
          return false;
        }
      }.bind(this));

    },
    // 执行培训----人员列表---勾选框
    // 执行培训--对话框---考核表--初始化
    addExamineClickHandle() {
      // 获取数据
      let record = this.record;
      let examineList = this.examineList;
      examineList.department = this.info.department;
      examineList.recordId = record.id;
      examineList.examineTime = record.trainingDate;
      examineList.examineLocation = record.location;
      examineList.examineName = record.theme + '考核表';
      examineList.examineContent = record.theme;
      // 参加人员---实参人员
      this.examineListParticipants = [];
      this.assist.eduDailyParticipants.forEach(function (it, index) {
        if (it.participate) {
          this.examineListParticipants.push(it);
        }
      }.bind(this));
      this.examineDialog.status = 'edit';
      this.examineDialog.isShow = true;
    },
    // 执行培训---对话框---表格----选中行
    selectionChangeDialog(val) {
      this.examineList.eduDailyExamineScores = val.map(function (it) {
        return {
          userId: it.userId
        };
      })
    },
    // 执行培训--对话框---考核表--确定按钮
    examineOkBtnClickHandle() {
      this.$store.dispatch('eduDailyExamineListAddOrUpdate', this.examineList).then(function (res) {
        if (res.success) {
          this.$message({
            type: 'success',
            message: '操作成功'
          })
          this.examineDialog.isShow = false;
          // 记录表record信息
          this.getRecordAndExamineList();
        } else {
          this.$message({
            type: 'error',
            message: res.message || '错误'
          })
        }
      }.bind(this));
    },
    // 执行培训---对话框---保存分数
    // 批量保存分数
    saveScoreBtnClickHandle() {
      this.$store.dispatch('eduDailyExamineListBatchInputScores', this.examineDialog.more).then(function (res) {
        if (res.success) {
          if (res.success) {
            this.$message({
              type: 'success',
              message: '打分成功'
            })
            this.examineDialog.isMore = false;
            this.itemUpdateClick(this.examineList);
          } else {
            this.$message({
              type: 'error',
              message: res.message || '打分失败！！'
            })
          }
        }
      }.bind(this));
    },
    // 执行培训--考核表---删除按钮
    itemDeleteClick(row) {
      this.$confirm('此操作将永久删除, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(function () {
          this.$store.dispatch('eduDailyExamineListDelete', {
            id: row.id
          }).then(function (res) {
            if (res.success) {
              this.$message({
                type: 'success',
                message: '删除成功'
              })
              // 记录表record信息
              this.getRecordAndExamineList();
            } else {
              this.$message({
                type: 'error',
                message: res.message || '删除失败！！'
              })
            }
          }.bind(this))
        }.bind(this))
    },
    // 执行培训--考核表---查看按钮
    itemUpdateClick(row) {
      this.$store.dispatch('eduDailyExamineListFind', { id: row.id }).then(function (res) {
        if (res.success) {
          let list = res.data.list[0];
          // 将值映射到对话框中
          Object.entries(list).forEach(function (it) {
            if (this.examineList.hasOwnProperty(it[0])) {
              this.examineList[it[0]] = it[1];
            }
          }.bind(this));
          // 获取打分人员的记录的ID
          this.examineDialog.more.ids = list.eduDailyExamineScores.map(function (it) { return it.id; })
          // 分数
          this.examineDialog.more.scores = list.eduDailyExamineScores.map(function (it) { return it.score; })
          this.examineDialog.status = 'view';
          this.examineDialog.isShow = true;
        } else {
          this.$message({
            type: 'error',
            message: res.message || '错误'
          })
        }
      }.bind(this));
    },
    //
    // ############培训执行---END--###############


  }
}
</script>

<style>
.container {
  background: #fff;
  padding: 0px 20px 20px;
}

.title {
  background: rgba(64, 158, 255, .1);
  color: #0f6fc6;
  border: 1px solid rgba(64, 158, 255, .2);
  border-radius: 5px;
}

.row {
  margin-top: 10px;
}
</style>
