<template>
  <div id="viewInvestigation">
    <div class="background-style">
      <el-col :span="16" :offset="4" class="success-background-title">{{titleStr}}</el-col>
      <el-col :span="16" :offset="4">
        <el-form :model="form" ref="ruleForm" label-width="120px" class="demo-ruleForm" label-position="left">
          <el-col :span="24">
            <el-col :span="12">
              <el-form-item label="检查单编号：" prop="checkNum" style="margin: 0">
                {{form.checkNum}}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="发起人：" prop="createUserName" style="margin: 0" label-width="70px">
                {{form.createUserName}}
              </el-form-item>
            </el-col>
          </el-col>
          <el-col :span="24">
            <el-col :span="12">
              <el-form-item label="预计检查日期：" prop="predictInspectDate" style="margin: 0" label-width="110px">
                {{form.predictInspectDate}}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="实际检查日期：" prop="inspectDate" style="margin: 0" label-width="110px">
                {{form.inspectDate}}
              </el-form-item>
            </el-col>
          </el-col>
          <el-col :span="24">
            <el-col :span="12">
              <el-form-item label="检查组组长：" prop="leaderUserName" style="margin: 0">
                {{form.leaderUserName}}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="检查类型：" prop="type" style="margin: 0">
                {{inpectTypeArr[form.type]}}
              </el-form-item>
            </el-col>
          </el-col>
          <el-col :span="24">
            <el-form-item label="检查组成员：" prop="dangerInspectMembers" style="margin: 0">
              <span v-for="item in form.dangerInspectMembers" :key="item.userId">{{item.userName}} , </span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="受检单位：" prop="targetDeptName" style="margin: 0;">
              {{form.targetDeptName}}
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-col :span="12">
              <el-form-item label="受检单位承办人：" prop="targetContractorUserName" style="margin: 0;" label-width="160px">
                {{form.targetContractorUserName}}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="受检单位现场负责人：" prop="targetLiveChargeUserName" style="margin: 0" label-width="160px">
                {{form.targetLiveChargeUserName}}
              </el-form-item>
            </el-col>
          </el-col>
        </el-form>
      </el-col>
      <el-col :span="22" :offset="1">
        <el-table
          v-loading="loading"
          border
          :data="form.dangerInspectListPublicList">
          <el-table-column
            type="index"
            label="序号"
            width="50"
            fixed
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            prop="inspectProject"
            label="检查项目"
            width="150"
            fixed
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            prop="inspectContent"
            min-width="400"
            label="检查标准内容"
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            prop="inspectResult"
            width="300"
            label="检查结果记录"
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            prop="hiddenDangerLevel"
            width="150"
            label="隐患级别"
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            width="200"
            label="隐患照片"
            label-class-name="inner-header-style">
            <template slot-scope="scope">
              <picture-card :picFileList="scope.row.dangerPics"></picture-card>
            </template>
          </el-table-column>
          <el-table-column
            prop="deadline"
            width="150"
            label="整改时间"
            :formatter="changeFormat"
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column fixed="right" label="操作"  label-class-name="inner-header-style" width="90">
            <template slot-scope="scope">
              <el-button size="mini" type="success" @click="itemViewSingleClick(scope.row)">查看</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
      <el-col :span="22" :offset="1">
        <div style="float: right;margin: 20px">
          <el-button @click="$router.go(-1)">返回</el-button>
        </div>
      </el-col>
    </div>

    <el-dialog title="隐患详情" width="94%" :visible.sync="detailVisible">
      <div style="display: inline-block;width: 100%;">
        <view-single-danger :dangerId="showDetailData.dangerId" :inspectSheet="{inspectDate:showDetailData.inspectDate,status:showDetailData.status}" :reformSheet="{inspectId:showDetailData.inspectId,applyUserId:showDetailData.applyUserId}"></view-single-danger>
      </div>
    </el-dialog>

  </div>
</template>
<script>
  import ViewSingleDanger from '../../../common/smallComponent/viewSingleDanger.vue'
  import PictureCard from '../../../common/smallComponent/pictureCard.vue'
  export default {
    name: 'viewInvestigation',
    data() {
      return {
        //标题
        titleStr:'',
//        needCheck:false,
        currentId:'',
//        currentStatus:'',

        form:{
          checkNum:'',
          inspectDate:'',
          targetDeptName:'',
          leaderUserName:'',
          dangerInspectMembers:[],
          targetContractorUserName:'',
          targetLiveChargeUserName:'',
          needFeedback:'',
          dangerInspectListPublicList:[],
        },
        loading:false,
        //检查类型
        inpectTypeArr:['自查','督查','排查'],
        //隐患详情
        detailVisible:false,
        //详情查询数据
        showDetailData:{dangerId:'',inspectDate:'',inspectId:'',applyUserId:'',status:''},

        //选择数据
        needReply:['否','是']
      }
    },
    components:{
      ViewSingleDanger,
      PictureCard,
    },
    created:function () {
      if(this.$route.params.id){
        this.titleStr=this.$route.params.name;
        this.searchDataById(this.$route.params.id);
      }
    },
    watch:{
      $route(to, from){
        if((from.name==='investigation'||from.name==='inspected')&&this.$route.name==='viewInvestigation') {
          if(this.$route.params.id){
            this.titleStr=this.$route.params.name;
            this.searchDataById(this.$route.params.id);
          }
        }
      }
    },
    methods:{
      searchDataById:function (id) {
        //清除之前数据
        this.form.checkNum='';
        this.form.inspectDate='';
        this.form.targetDeptName='';
        this.form.leaderUserName='';
        this.form.targetContractorUserName='';
        this.form.targetLiveChargeUserName='';
        this.form.needFeedback='';
        this.form.dangerInspectMembers.splice(0);
        this.form.dangerInspectListPublicList.splice(0);

        this.currentId=id;
        this.loading=true;
        this.$http.post('danger/inspectPublic/detail', {id:id}).then(function (res) {
          if (res.data.success) {
            let tempForm=res.data.data;
            tempForm.predictInspectDate=this.transferTime(tempForm.predictInspectDate);
            tempForm.inspectDate=this.transferTime(tempForm.inspectDate);
            this.form=tempForm;
            this.loading=false;
          }
        }.bind(this)).catch(function (err) {
          this.$message.error('查找数据失败');
          console.log(err);
        });
      },
      //改时间格式
      changeFormat:function (row) {
        return this.transferTime(row.deadline);
      },

      checkClick:function () {
//        let tempStatus=1;
//        if(this.currentStatus===2){tempStatus=this.form.needFeedback===1?3:4}
        this.$http.post('danger/inspectPublic/update', {id:this.currentId,dangerInspectMembers:this.form.dangerInspectMembers,dangerInspectListPublicList:this.form.dangerInspectListPublicList}).then(function (res) {
          if (res.data.success) {
            this.$message.success('确认查看成功！');
            this.$router.push({name:'investigation'});
          }
        }.bind(this)).catch(function (err) {
          this.$message.error('操作失败！');
          console.log(err);
        });
      },
      //查看单条隐患数据
      itemViewSingleClick:function (row) {
        this.showDetailData.inspectDate=this.form.inspectDate;
        this.showDetailData.dangerId=row.id;
        this.showDetailData.inspectId=this.currentId;
        this.showDetailData.applyUserId=row.applyUserId;
        this.showDetailData.status=this.form.status;
        this.detailVisible=true;
      },

    }
  }
</script>
<style>
</style>
