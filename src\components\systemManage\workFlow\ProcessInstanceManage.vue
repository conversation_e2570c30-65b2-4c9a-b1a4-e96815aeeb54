<template>
  <div id="processInstanceManage" class="background-style">
    <div style="width: 100%;">
      <!--表格区-->
      <el-col :span="16">
        <div style="width: 96%;padding-left: 10px;">
          <div
            style="width: 100%;padding-left:10px;height: 60px;background-color: rgb(236,248,255);margin-bottom: 20px;border-radius: 5px;border-left: 5px solid #50BFFF">
            <h4 style="line-height: 60px;margin-left: 20px">运行中隐患流程</h4>
          </div>
        </div>
        <div style="padding-left: 19px;padding-right: 19px">
          <el-table
            v-loading="loading"
            element-loading-text="同步数据中"
            element-loading-spinner="el-icon-loading"
            element-loading-background="rgba(255, 255, 255, 0.9)"
            :data="dangerProcessInstanceData"
            highlight-current-row
            style="width: 90%;margin-left: 1%"
            stripe
            border>
            <el-table-column type="expand" label-class-name="header-style" >
              <template slot-scope="props">
                <el-table
                  :data="props.row.myTasks"
                  style="width: 100%">
                  <el-table-column
                    type="index"
                    align="center"
                    label-class-name="inner-header-style"
                    width="70">
                  </el-table-column>
                  <el-table-column
                    prop="createTime"
                    label="任务日期"
                    width="180"
                    :formatter="dateFormat"
                    align="center"
                    label-class-name="inner-header-style">
                  </el-table-column>
                  <el-table-column
                    prop="name"
                    label="任务名字"
                    width="180"
                    align="center"
                    label-class-name="inner-header-style">
                  </el-table-column>
                  <el-table-column
                    prop="userName"
                    label="分配人员"
                    min-width="180"
                    align="center"
                    label-class-name="inner-header-style">
                  </el-table-column>
                  <el-table-column fixed="right" label="操作" label-class-name="inner-header-style" align="center" width="160">
                    <template slot-scope="scope">
                      <el-button size="mini" type="danger" @click="choosePeople(scope.row,scope.$index)" style="float: left" >重新分配</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </template>
            </el-table-column>
            <el-table-column
              type="index"
              width="50"
              align="center"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="dangerInspectPublic.name"
              label="隐患检查单"
              width="250"
              align="center"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="username"
              label="发起人"
              width="120"
              align="center"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              label="发起时间"
              min-width="180"
              align="center"
              label-class-name="header-style">
              <template slot-scope="scope">
                    <span>
                      {{($tool.formatDateTime(scope.row.startTime)|| '')
                      .substring(0,16)}}
                    </span>
              </template>
            </el-table-column>

            <!--<el-table-column-->
            <!--label="操作"-->
            <!--align="center"-->
            <!--width="200"-->
            <!--fixed="right"-->
            <!--label-class-name="header-style">-->
            <!--<template slot-scope="scope">-->
            <!--<el-button-->
            <!--size="mini"-->
            <!--type="primary"-->
            <!--&gt;删除</el-button>-->
            <!--<el-button-->
            <!--size="mini"-->
            <!--type="primary"-->
            <!--&gt;查看</el-button>-->
            <!--</template>-->
            <!--</el-table-column>-->
          </el-table>
          <!--分页-->
          <div
            style="left: 20%;margin-top: 40px;transform:translate(-50%,-50%);-webkit-transform:translate(-50%,-50%);position: absolute">
            <el-pagination
              layout="prev, pager, next"
              :total="dangerPage.total"
              @current-change="dangerPageF">
            </el-pagination>
          </div>
        </div>
      </el-col>
    </div>


    <el-dialog
      title="提示"
      :visible.sync="assignDialogVisible"
      width="40%"
      center>
      <div>
        <el-form :inline="true"  class="demo-form-inline">
          <el-form-item label="任务名称">
            <el-input v-model="taskDialog.taskName" ></el-input>
          </el-form-item>
          <el-form-item label="分配人员">
            <el-input v-model="taskDialog.assignUserName" >
            </el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="selectPeopleDialog" >重新选人</el-button>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
    <el-button @click="assignDialogVisible = false">取 消</el-button>
    <el-button type="primary" @click="taskAssign">确 定</el-button>
  </span>
    </el-dialog>

    <!--选择负责人-->
    <search-people-dialog @determineClick="selectPersonClick" :data="selectPersonData" :defaultPersonId="selectPersonData.defaultPerson.value"></search-people-dialog>

  </div>
</template>

<script>
  import SearchPeopleDialog from '@/components/common/smallComponent/searchSingleUser.vue'

    export default {
        name: "processInstanceManage",
      data() {
        return {
          userName:'',
          procdefId:'',
          loading: false,
          dangerProcessInstanceData:[],
          emgPlanProcessInstanceData:[],
          dangerPage:{
            pageSize:10,
            pageCurrent:1,
            total:0
          },
          emgPlanPage:{

          },
          assignDialogVisible:false,
          taskDialog:{
            taskName:"",
            assignUserName:'',
            assignUserId:0,
            taskId:'',
            index:0,
          },
          selectPersonData:{title:'请选择分配人员',isShow:false,defaultPerson:{value:0,label:''}},
        }
      },
      created:function () {
        this.procdefId=this.$route.params.procdefId
        this.imageUrl= this.$http.defaults.baseURL+"/activiti/getProcessPic/"+this.procdefId
        this.loadDangerProcessInstanceData()
      },
      watch:{
        $route(to, from) {

        }
      },
      mounted:function () {

      },
      components : {
        SearchPeopleDialog,
      },
      methods:{
        dateFormat(row, column) {
          //.replace(/年|月/g, "-").replace(/日/g, " ")
          return new Date(row.createTime).Format("yyyy-MM-dd hh:mm").toLocaleString();
        },
        dangerPageF:function (val) {
          this.dangerPage.pageCurrent=val
          this.loadDangerProcessInstanceData()
        },
        loadDangerProcessInstanceData:function(){
          this.$http.get("dangerFlow/getRunningProcessInstance" +
            "?pageSize=10&pageCurrent="+this.dangerPage.pageCurrent).then(function (res) {
              if(res.data.success){
                this.dangerProcessInstanceData=res.data.data.list
                this.dangerPage.total=res.data.data.total
              }
          }.bind(this))

        },
        loadEmgPlanProcessInstanceData:function(){

        },
        choosePeople:function (row,index) {
          this.assignDialogVisible=true
          this.taskDialog.assignUserName=row.userName
          this.taskDialog.taskName=row.name
          this.taskDialog.taskId=row.id
          this.taskDialog.index=index
          this.taskDialog.assignUserId=parseInt(row.userId)
        },
        selectPeopleDialog:function () {
          this.selectPersonData.isShow=true
          this.selectPersonData.defaultPerson.value=this.taskDialog.assignUserId
          this.selectPersonData.defaultPerson.label=this.taskDialog.assignUserName
        },
        selectPersonClick:function (val) {
          this.taskDialog.assignUserName=val.label
          this.taskDialog.assignUserId=val.value
          this.selectPersonData.isShow=false
        },
        taskAssign(){
          this.$http.get("/activiti/assignTask/"
            +this.taskDialog.taskId+"/"+this.taskDialog.assignUserId).then(function (res) {
              if(res.data.success){
                this.assignDialogVisible=false
                this.loadDangerProcessInstanceData()
              }
          }.bind(this)).catch(function (err) {
            console.log(err)
          })
        }
      }
    }
</script>

<style scoped>

</style>
