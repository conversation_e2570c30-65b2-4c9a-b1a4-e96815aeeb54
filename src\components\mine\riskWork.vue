<template>
  <div id="trainingPlanIndex">
    <div class="background-style">
      <!--搜索区-->
      <div style="float: left; margin: 10px">
        <div style="width: 100%">
          任意搜索:
          <el-input v-model="form.keyName" style="width: 200px"></el-input>
          风险等级:
          <el-select v-model="form.riskGrade" placeholder="请选择" clearable>
            <el-option label="重大风险" :value="3"></el-option>
            <el-option label="较大风险" :value="2"></el-option>
            <el-option label="一般风险" :value="1"></el-option>
            <el-option label="低风险" :value="0"></el-option>
          </el-select>
          风险类型：
          <el-select v-model="form.riskType" placeholder="请选择" clearable>
            <el-option label="采掘施工" value="采掘施工"></el-option>
            <el-option label="复绿" value="复绿"></el-option>
            <el-option label="1号线" value="1号线"></el-option>
            <el-option label="1号水洗" value="1号水洗"></el-option>
            <el-option label="2号线" value="2号线"></el-option>
            <el-option label="车队" value="车队"></el-option>
            <el-option
              label="生活区设备设施"
              value="生活区设备设施"
            ></el-option>
          </el-select>
          <el-button
            @click="searchBtnClickHandle"
            type="primary"
            icon="el-icon-search"
            style="margin-left: 20px"
            >搜索</el-button
          >
          <el-button
            @click="addBtnClickHandle"
            type="success"
            style="margin-left: 20px"
            >新增</el-button
          >
        </div>
      </div>
      <!--表格区-->
      <div style="width: 100%">
        <div style="padding: 20px 10px 20px 10px">
          <el-table
            border
            @row-click="rowclick"
            :data="tableData.list"
            style="width: 100%"
          >
            <el-table-column
              prop="id"
              label="序号"
              show-overflow-tooltip
              label-class-name="header-style"
            />

            <el-table-column
              prop="riskType"
              label="风险类型"
              show-overflow-tooltip
              label-class-name="header-style"
            />

            <el-table-column
              prop="workName"
              label="作业名称"
              show-overflow-tooltip
              label-class-name="header-style"
            />

            <el-table-column
              prop="projectName"
              label="项目名称"
              show-overflow-tooltip
              label-class-name="header-style"
            />

            <el-table-column
              prop="riskSource"
              label="危险源"
              show-overflow-tooltip
              label-class-name="header-style"
            />

            <el-table-column
              prop="accidentType"
              label="事故类型"
              show-overflow-tooltip
              label-class-name="header-style"
            />

            <el-table-column
              prop="controlUser"
              label="管控负责人姓名"
              show-overflow-tooltip
              label-class-name="header-style"
            />

            <el-table-column
              prop="riskGrade"
              label="风险等级"
              show-overflow-tooltip
              label-class-name="header-style"
            >
              <!-- 重大风险3，较大风险2，一般风险1，低风险0 -->
              <template slot-scope="scope">
                <span v-if="scope.row.riskGrade === 3"> 重大风险 </span>
                <span v-if="scope.row.riskGrade === 2"> 较大风险 </span>
                <span v-if="scope.row.riskGrade === 1"> 一般风险 </span>
                <span v-if="scope.row.riskGrade === 0"> 低风险 </span>
              </template>
            </el-table-column>

            <el-table-column
              label="操作"
              label-class-name="header-style"
              align="left"
              width="320"
            >
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="primary"
                  @click="itemViewClick(scope.row)"
                  >查看</el-button
                >
                <el-button
                  size="mini"
                  type="warning"
                  @click="itemEditClick(scope.row)"
                  >修改</el-button
                >
                <el-button
                  size="mini"
                  type="danger"
                  @click="itemDeleteClick(scope.row)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div style="margin-top: 10px">
          <el-pagination
            background
            layout="prev, pager, next"
            :current-page="tableData.pageNum"
            :page-size="form.pageSize"
            :total="tableData.total"
            @current-change="disasterPageChangeHandle"
          >
          </el-pagination>
        </div>
      </div>
      <!--新增对话框-->
      <el-dialog
        title="对话框"
        :visible.sync="dialog.isShow"
        width="70%"
        :before-close="handleClose"
      >
        <el-form label-width="120px">
          <el-form-item label="风险类型">
            <el-input
              clearable
              v-model="dialog.form.riskType"
              :readonly="dialog.disabled"
            ></el-input>
          </el-form-item>

          <el-form-item label="作业名称">
            <el-input
              clearable
              v-model="dialog.form.workName"
              :readonly="dialog.disabled"
            ></el-input>
          </el-form-item>

          <el-form-item label="项目名称">
            <el-input
              clearable
              v-model="dialog.form.projectName"
              :readonly="dialog.disabled"
            ></el-input>
          </el-form-item>

          <el-form-item label="危险源">
            <el-input
              clearable
              v-model="dialog.form.riskSource"
              :readonly="dialog.disabled"
            ></el-input>
          </el-form-item>

          <el-form-item label="事故类型">
            <el-input
              clearable
              v-model="dialog.form.accidentType"
              :readonly="dialog.disabled"
            ></el-input>
          </el-form-item>

          <el-form-item label="管控负责人姓名">
            <el-input
              clearable
              v-model="dialog.form.controlUser"
              :readonly="dialog.disabled"
            ></el-input>
          </el-form-item>

          <el-form-item label="风险等级">
            <el-select
              :disabled="dialog.disabled"
              v-model="dialog.form.riskGrade"
              placeholder="请选择"
              clearable
              :readonly="dialog.disabled"
            >
              <el-option label="重大风险" :value="3"></el-option>
              <el-option label="较大风险" :value="2"></el-option>
              <el-option label="一般风险" :value="1"></el-option>
              <el-option label="低风险" :value="0"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="应急处置">
            <el-input
              type="textarea"
              autosize
              clearable
              v-model="dialog.form.emergencyResponse"
              :readonly="dialog.disabled"
            ></el-input>
          </el-form-item>

          <el-form-item label="个体防护">
            <el-input
              type="textarea"
              autosize
              clearable
              v-model="dialog.form.personalProtection"
              :readonly="dialog.disabled"
            ></el-input>
          </el-form-item>

          <el-form-item label="培训教育">
            <el-input
              type="textarea"
              autosize
              clearable
              v-model="dialog.form.trainingEducation"
              :readonly="dialog.disabled"
            ></el-input>
          </el-form-item>

          <el-form-item label="管理控制">
            <el-input
              type="textarea"
              autosize
              clearable
              v-model="dialog.form.managementControl"
              :readonly="dialog.disabled"
            ></el-input>
          </el-form-item>

          <el-form-item label="工程技术">
            <el-input
              type="textarea"
              autosize
              clearable
              v-model="dialog.form.engineeringTechnology"
              :readonly="dialog.disabled"
            ></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button
            v-if="!dialog.disabled"
            type="danger"
            size="mini"
            @click="dialogOkBtnClickHandle(form)"
            >确定</el-button
          >
        </div>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import fileUpload from "@/components/common/fileUploads.vue";
export default {
  components: {
    fileUpload,
  },
  data() {
    return {
      form: {
        riskGrade: null,
        riskType: "",
        keyName: "",
        // 当前页
        pageCurrent: 1,
        // 页数大小
        pageSize: 10,
      },
      tableData: {},
      // 专题类型
      files: [],
      // 对话框
      dialog: {
        // 是否显示
        isShow: false,
        disabled: false,
        form: {
          id: "",
          riskType: "",
          workName: "",
          projectName: "",
          riskSource: "",
          accidentType: "",
          controlUser: "",
          riskGrade: null,
          emergencyResponse: "",
          personalProtection: "",
          trainingEducation: "",
          managementControl: "",
          engineeringTechnology: "",
        },
      },
      // 角色 0 员工 1 发布者
      role: 0,
      // 权限按钮
      powerBtns: [],
    };
  },
  activated() {
    this.init();
  },
  methods: {
    // 初始化
    init() {
      this.searchBtnClickHandle();
    },
    // 分页
    disasterPageChangeHandle(page) {
      this.form.pageCurrent = page;
      this.searchBtnClickHandle();
    },
    // 搜索按钮
    searchBtnClickHandle() {
      let params = this.form;
      this.$http
        .post("/sys/risk/findRiskWork", params)
        .then((res) => {
          if (res.data.success) {
            this.tableData = res.data.data;
          } else {
            this.$message({
              type: "error",
              message: res.data.message || "错误",
            });
          }
        })
        .catch((err) => {
          this.$message({
            type: "error",
            message: err.message || "错误",
          });
        });
    },
    // 删除按钮
    itemDeleteClick(row) {
      //        console.log(row);
      this.$confirm("此操作将永久删除, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(
        function () {
          this.$http
            .post("/sys/risk/delRiskWork", {
              id: row.id,
            })
            .then((res) => {
              if (res.data.success) {
                this.$message({
                  type: "success",
                  message: "删除成功",
                });
                this.searchBtnClickHandle();
              } else {
                this.$message({
                  type: "error",
                  message: res.message || "删除失败！！",
                });
              }
            });
        }.bind(this)
      );
    },
    //修改按钮
    itemEditClick(row) {
      this.dialog.form = JSON.parse(JSON.stringify(row));
      this.dialog.disabled = false;
      this.dialog.isShow = true;
    },
    //查看按钮
    itemViewClick(row) {
      this.dialog.form = JSON.parse(JSON.stringify(row));
      this.dialog.disabled = true;
      this.dialog.isShow = true;
    },
    rowclick(row) {
      console.log(row);
    },
    // 添加按钮
    addBtnClickHandle() {
      this.dialog.form = {
        id: "",
        riskType: "",
        workName: "",
        projectName: "",
        riskSource: "",
        accidentType: "",
        controlUser: "",
        riskGrade: null,
        emergencyResponse: "",
        personalProtection: "",
        trainingEducation: "",
        managementControl: "",
        engineeringTechnology: "",
      };
      this.dialog.disabled = false;
      this.dialog.isShow = true;
    },
    // 对话框---确定按钮
    dialogOkBtnClickHandle() {
      const params = this.dialog.form;
      this.$http.post("/sys/risk/addOrUpdateRiskWork", params).then(
        function (res) {
          if (res.data.success) {
            this.$message({
              type: "success",
              message: "操作成功",
            });
            this.handleClose();
            this.searchBtnClickHandle();
            this.dialog.isShow = false;
          } else {
            this.$message({
              type: "error",
              message: res.data.message || "错误",
            });
          }
        }.bind(this)
      );
    },
    // 对话框--关闭
    handleClose() {
      this.dialog.isShow = false;
    },
  },
};
</script>
<style>
</style>
