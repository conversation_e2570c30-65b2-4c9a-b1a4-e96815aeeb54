<template>
  <div id="trainingPlanIndex">
    <div class="background-style">
      <!--搜索区-->
      <div class="search-bar">
        <el-row>
          <el-col :span="4">
            <el-input
              v-model="form.name"
              clearable
              placeholder="请输入标题"
            ></el-input>
          </el-col>
          <el-col :span="2">
            <el-button
              @click="searchBtnClickHandle"
              type="primary"
              icon="el-icon-search"
              style="margin-left: 20px"
              >搜索</el-button
            >
          </el-col>
          <el-col :span="2">
            <el-button
              @click="addBtnClickHandle"
              type="success"
              style="margin-left: 20px"
              >新增</el-button
            >
          </el-col>
        </el-row>
      </div>
      <!--表格区-->
      <div style="width: 100%">
        <div style="padding: 20px 10px 20px 10px">
          <el-table
            border
            @row-click="rowclick"
            :data="tableData.list"
            style="width: 100%"
          >
            <el-table-column
              type="index"
              label="编号"
              align="center"
              label-class-name="header-style"
            >
            </el-table-column>
            <el-table-column
              prop="userFileName"
              label="文件名称"
              show-overflow-tooltip
              label-class-name="header-style"
            >
              <template slot-scope="scope">
                <el-tag
                v-if="scope.row.userFileName"
                  type="text"
                  size="mini"
                  style="cursor:pointer;"
                  @click="previewBtn(scope.row)"
                  >{{ scope.row.userFileName }}</el-tag
                >
              </template>
            </el-table-column>
            <el-table-column
              prop="remark"
              label="备注"
              show-overflow-tooltip
              label-class-name="header-style"
            />
            <el-table-column
              prop="status"
              label="文件类型"
              show-overflow-tooltip
              width="160"
              label-class-name="header-style"
            />
            <el-table-column
              label="操作"
              label-class-name="header-style"
              align="left"
              width="320"
            >
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="warning"
                  @click="previewBtn(scope.row)"
                  >预览</el-button
                >
                <el-button
                  size="mini"
                  type="warning"
                  @click="previewBtn(scope.row)"
                  >下载</el-button
                >
                <el-button
                  size="mini"
                  type="warning"
                  @click="itemEditClick(scope.row)"
                  >修改</el-button
                >
                <el-button
                  size="mini"
                  type="danger"
                  @click="itemDeleteClick(scope.row)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div style="margin-top: 10px">
          <el-pagination
            background
            layout="prev, pager, next"
            :current-page="tableData.pageNum"
            :page-size="form.pageSize"
            :total="tableData.total"
            @current-change="disasterPageChangeHandle"
          >
          </el-pagination>
        </div>
      </div>
      <!--新增对话框-->
      <el-dialog
        title="对话框"
        :visible.sync="dialog.isShow"
        width="70%"
        :before-close="handleClose"
      >
        <el-form label-width="100px">
          <el-row class="row">
            <el-col :span="12">
              <el-form-item label="文件类型">
                <el-input clearable v-model="dialog.form.status"> </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="备注">
                <el-input clearable v-model="dialog.form.remark"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="文件上传">
                <fileUpload
                  ref="fileUpload"
                  @fileData="fileDataFn"
                  :data="upload"
                ></fileUpload>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button
            type="danger"
            size="mini"
            @click="dialogOkBtnClickHandle(form)"
            >确定</el-button
          >
        </div>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import fileUpload from "@/components/common/fileUploads.vue";
export default {
  components: {
    fileUpload,
  },
  data() {
    return {
      form: {
        // 标题
        name: "",
        // 类型
        type: ["0", "1"],
        // 当前页
        pageCurrent: 1,
        // 页数大小
        pageSize: 10,
      },
      tableData: {},
      // 专题类型
      files: [],
      // 对话框
      dialog: {
        // 是否显示
        isShow: false,
        form: {
          id: "",
          userFileName: "",
          fileId: "",
          types: [],
          status: "",
          remark: "",
        },
        assist: {
          planList: [],
        },
      },
      upload: {
        params: {
          contentId: this.$tool.getStorage("LOGIN_USER").userId,
          contentType: 26,
        },
        btns: {
          // 上传按钮
          upload: {
            isShow: true,
          },
          // 下载按钮
          download: {
            isShow: false,
          },
          // 删除按钮
          delete: {
            isShow: true,
          },
        },
        uploadUrl: "",
        uploadCookies: true,
        fileData: [],
      },

      // 角色 0 员工 1 发布者
      role: 0,
      // 权限按钮
      powerBtns: [],
    };
  },
  activated() {
    this.init();
  },
  methods: {
    // 初始化
    init() {
      this.searchBtnClickHandle();
    },
    // 预览
    previewBtn(row) {
      window.open(row.filePath);
    },
    // 格式化时间
    formatDateTime(row, column, cellValue) {
      let pro = column.property;
      let num = 10;
      // 年份4位 1999
      if (pro === "createYear") num = 4;
      let str = this.$tool.formatDateTime(row[pro] || 0);
      return str ? str.substring(0, num) : str;
    },
    // 分页
    disasterPageChangeHandle(page) {
      this.form.pageCurrent = page;
      this.searchBtnClickHandle();
    },
    // 搜索按钮
    searchBtnClickHandle() {
      let params = {};

      params["pageCurrent"] = this.form.pageCurrent;
      params["pageSize"] = this.form.pageSize;
      this.$http
        .post("/sys/sysMine/findMineFile", params)
        .then((res) => {
          if (res.data.success) {
            this.tableData = res.data.data;
          } else {
            this.$message({
              type: "error",
              message: res.data.message || "错误",
            });
          }
        })
        .catch((err) => {
          this.$message({
            type: "error",
            message: err.message || "错误",
          });
        });
    },
    // 删除按钮
    itemDeleteClick(row) {
      //        console.log(row);
      this.$confirm("此操作将永久删除, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(
        function () {
          this.$http
            .post("/sys/sysMine/delMineFile", {
              id: row.id,
            })
            .then((res) => {
              if (res.data.success) {
                this.$message({
                  type: "success",
                  message: "删除成功",
                });
                this.searchBtnClickHandle();
              } else {
                this.$message({
                  type: "error",
                  message: res.message || "删除失败！！",
                });
              }
            });
        }.bind(this)
      );
    },
    //修改按钮
    itemEditClick(row) {
      this.dialog.form = {
        id: row.id,
        userFileName: row.userFileName,
        fileId: row.fileId,
        status: row.status,
        remark: row.remark,
      };
      this.$nextTick(() => {
        this.$refs.fileUpload.upload.fileData = [
          {
            fileId: row.fileId,
            fileName: row.userFileName,
          },
        ];
      });
      this.dialog.isShow = true;
    },
    rowclick(row) {
      console.log(row);
    },
    // 添加按钮
    addBtnClickHandle() {
      this.dialog.form = {
        id: "",
        userFileName: "",
        fileId: "",
        status: "",
        remark: "",
      };
      this.$nextTick(
        function () {
          this.$refs.fileUpload.upload.fileData = [];
        }.bind(this)
      );
      this.dialog.isShow = true;
    },
    fileDataFn(data) {
      console.log("父组件获取子组件数据：", data);
      this.files = data.map(function (e) {
        console.log(e);
        return {
          id: "",
          eduNewsId: "",
          fileId: e.fileId,
          fileName: e.fileName,
        };
      });
    },
    // 对话框---确定按钮
    dialogOkBtnClickHandle() {
      const params = {
        id: this.dialog.form.id,
        userFileName: this.files&&this.files[this.files.length-1].fileName,
        userFileId: this.files&&this.files[this.files.length-1].fileId,
        status: this.dialog.form.status,
        remark: this.dialog.form.remark,
      };
      this.$http.post("/sys/sysMine/addOrUpdateMineFile", params).then(
        function (res) {
          if (res.data.success) {
            this.$message({
              type: "success",
              message: "操作成功",
            });
            this.handleClose();
            this.searchBtnClickHandle();
            this.dialog.isShow = false;
          } else {
            this.$message({
              type: "error",
              message: res.data.message || "错误",
            });
          }
        }.bind(this)
      );
    },
    // 对话框--关闭
    handleClose() {
      this.dialog.isShow = false;
    },
  },
};
</script>
<style>
</style>
