<template>
  <div id="">
    <el-container class="container">
      <el-main>
        <el-form ref="form" :model="form" label-width="5px">
          <el-row>
            <el-col :span="4">
              <el-form-item >
                <el-input clearable placeholder="视频名称" v-model="form.courseName"></el-input>
              </el-form-item>
            </el-col>
            <el-col :offset="1" :span="1">
              <el-button type="primary" @click="searchBtnClickHandle">搜索</el-button>
            </el-col>
            <el-col :offset="1" :span="1">
              <el-button type="success"
                         icon="el-icon-plus"
                         @click="$router.push({ name : 'safetyEducationVideoAdd' });">新增视频</el-button>
            </el-col>
          </el-row>
          <el-row>
            <el-table
              border
              :data="tableData.list"
              style="width: 100%">
              <el-table-column
                type="index"
                label="编号"
                width="100"
                align="center"
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="courseName"
                label="视频名称"
                min-width="150"
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="courseTime"
                label="学时"
                width="150"
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                fixed="right" label="操作"
                label-class-name="header-style"
                align="left" width="300">
                <template slot-scope="scope">
                  <template >
                    <el-button size="mini" type="primary" @click="itemUpdateClick(scope.row)">修改</el-button>
                    <el-button size="mini" type="danger" @click="itemDeleteClick(scope.row)">删除</el-button>
                    <el-button size="mini" type="warning" @click="itemViewClick(scope.row)">查看学习统计</el-button>
                  </template>
                </template>
              </el-table-column>
            </el-table>
            <el-pagination
              background
              layout="prev, pager, next"
              :current-page="tableData.pageNum"
              :page-size="form.pageSize"
              :total="tableData.total"
              @current-change ="disasterPageChangeHandle">
            </el-pagination>
          </el-row>
        </el-form>
      </el-main>
    </el-container>
    <el-footer>
      <!--查看人员-->
      <el-dialog width="70%" :title="lookNumDialog.title" :visible.sync="lookNumDialog.isShow">
        <el-col :span="6">
          <el-input clearable placeholder="姓名" v-model="lookNumDialog.form.userName"></el-input>
        </el-col>
        <el-col :offset="1" :span="1">
          <el-button type="primary" @click="lookNumDialogChangeHandle(1)">搜索</el-button>
        </el-col>
        <el-table :data="lookNumDialog.tableData.list">
          <el-table-column label="序号" type="index" width="50"></el-table-column>
          <el-table-column property="userName" label="姓名"></el-table-column>
          <el-table-column property="deptName" label="所属部门" show-overflow-tooltip></el-table-column>
          <el-table-column property="createTime" :formatter="formatAllDateTime" label="学习时间" width="200"></el-table-column>
          <el-table-column property="studyStatusName" label="学习状态" width="150"></el-table-column>
          <el-table-column property="courseSchedule" label="学习进度" width="150"></el-table-column>
<!--          <el-table-column property="studyTime"  label="学习用时" width="150"></el-table-column>-->
        </el-table>
        <el-pagination
          background
          layout="prev, pager, next"
          :current-page="lookNumDialog.tableData.pageNum"
          :page-size="lookNumDialog.form.pageSize"
          :total="lookNumDialog.tableData.total"
          @current-change ="lookNumDialogChangeHandle">
        </el-pagination>
      </el-dialog>
    </el-footer>
  </div>
</template>
<script>
  export default {
    name: '',
    data() {
      return {
        // 搜索
        form : {

          // 视频名称
          courseName : '',
          // 类别
//          courseType : '',


          // 当前页
          pageCurrent : 1,
          // 页数大小
          pageSize : 10,
        },
        // 查看人数对话框
        lookNumDialog : {
          // 搜索条件
          form: {
            studyId: '',
            // 当前页
            pageCurrent: 1,
            // 页数大小
            pageSize: 10,
            userName:'',

          },
          // 是否显示
          isShow : false,
          // 标题
          title : '',
          // 表格数据
          tableData : []
        },
        assist:{
          // 类型
          newsType : [
            { value : '党建巡礼', label : '党建巡礼' },
            { value : '廉洁教育', label : '廉洁教育' },
            { value : '理论学习', label : '理论学习' },
            { value : '党务学习', label : '党务学习' },
          ],
        },
        tableData : {},
        // 角色 0 组织者或公司      1 部门        2  班组
        role : 0,
      }
    },
    mounted(){
      this.init();
    },
    watch:{
      $route(to,from){
        if(to.name === 'safetyEducationVideoIndex') {
          this.init();
        }
      }
    },
    methods:{
      // 初始化
      init(){
        this.judgeUserRole();
        // 搜索
        this.searchBtnClickHandle();
      },
      judgeUserRole(){
        // 获取权限按钮
        let btns = this.$tool.getPowerBtns('eduTrainingMenu', this.$route.path);
//        console.log('btns', btns)
        // 公司
        if(btns.includes('addBtn')){
          this.role = 4;
        }
      },
      // 清空数据
      clear(){

      },
      // 格式化时间
      formatDateTime(row, column, cellValue){
        let pro = column.property;
        let num = 10;
        let str = this.$tool.formatDateTime(row[pro]) || '';
        return str ? str.substring(0, num) : str;
      },
      formatAllDateTime(row, column, cellValue){
        let pro = column.property;
        let str = this.$tool.formatDateTime(row[pro]) || '';
        return str ;
      },
      // 分页
      disasterPageChangeHandle(page){
        this.form.pageCurrent = page;
        if(this.isMore){
          this.saveScoreBtnClickHandle();
        } else {
          this.searchBtnClickHandle();
        }
      },
      // 搜索按钮
      searchBtnClickHandle(){
        let params = Object.assign({}, this.form);
       /* if(!params['newsType']){
          params['newsType'] = '';
        }*/
        params = this.$tool.filterObj({}, params)

//        this.$tool.filterObj({},);

        this.$store.dispatch('eduCourseFind', params).then(function(res){
          if(res.success){
            this.tableData = res.data;
          } else {
            this.$message({
              type : 'error',
              message : res.message || '错误'
            })
          }
        }.bind(this));
      },
      // 学习情况汇总
      itemViewClick(row){
        this.tableData=[];
        this.lookNumDialog.form.studyId = row.id;
        this.lookNumDialog.isShow = true;
        this.lookNumDialog.title =  row.courseName;
        this.getEduStudyCondition();
      },
      //  学习情况汇总--对话框---分页
      lookNumDialogChangeHandle(page){
        this.lookNumDialog.form.pageCurrent = page;
        this.getEduStudyCondition();
      },
      //  学习情况汇总人员列表
      getEduStudyCondition(){
        this.$store.dispatch('videoStudyConditionFind', this.lookNumDialog.form).then(function(res){
          if(res.success){
            this.lookNumDialog.tableData = res.data;
          } else {
            this.$message({
              type : 'error',
              message : res.message || '错误'
            })
          }
        }.bind(this))
      },

      // 修改
      itemUpdateClick(row){
        let name = '';
        let params = {
          id : row.id,
          status : 'edit'
        }

        this.$router.push({ name : "safetyEducationVideoAdd", params : params})
      },
      // 删除按钮
      itemDeleteClick(row){
        this.$confirm('此操作将永久删除, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
          .then(function(){
            this.$store.dispatch('eduCourseDelete', {
              id : row.id
            }).then(function(res){
              if(res.success){
                this.$message({
                  type : 'success',
                  message : '删除成功'
                })
                this.searchBtnClickHandle();
              } else {
                this.$message({
                  type : 'error',
                  message : res.message || '删除失败！！'
                })
              }
            }.bind(this))
          }.bind(this))
      },
      // 批量保存分数
      saveScoreBtnClickHandle(){
        this.$store.dispatch('eduEntryTrainingBatchInputScores', this.more).then(function(res){

          if(res.success){
            this.$message({
              type : 'success',
              message : '打分成功'
            })
            this.searchBtnClickHandle();
          } else {
            this.$message({
              type : 'error',
              message : res.message || '打分失败！！'
            })
          }

        }.bind(this));
      },
      // 培训时间
      trainingDateChange(val){
        this.form.startDate = val ? val[0] : '';
        this.form.endDate = val ? val[1] : '';
      },
    }
  }
</script>
<style>
  .container{
    background:#fff;
    padding:0 20px;
  }
  .row{
    margin-top:10px;
  }
</style>
