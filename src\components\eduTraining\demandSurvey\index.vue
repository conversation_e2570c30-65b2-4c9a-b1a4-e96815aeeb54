<template>
    <div id="">

      <el-dialog
        class="gotoDialog"
        title="跳转页面"
        :visible.sync="gotoUpdatePage"
        width="40%">
        <el-button class="btn" type="primary" @click="goto('manage')">管理者</el-button>
        <el-button class="btn" type="success" @click="goto('staff')">员工</el-button>
        <el-button class="btn"  @click="gotoUpdatePage = false">取消</el-button>
      </el-dialog>
    </div>
</template>

<script>
    export default {
      data(){
        return {
          // 权限按钮
          powerBtns : [],
          // 跳转页面对话框
          gotoUpdatePage : false,
        }
      },
      created(){
        this.init();
      },
      watch:{
        $route(to,from){
          if(to.name === 'demandSurveyIndex'){
            this.init();
          }
        }
      },
      methods:{
        init(){
          // 根据权限按钮设置角色
          this.judgeUserRole();
        },
        judgeUserRole(){
          // 获取权限按钮
          this.powerBtns = this.$tool.getPowerBtns('eduTrainingMenu', this.$route.path);
          let name = '';

          // 公司
          if(this.powerBtns.includes('addBtn')){
            this.gotoUpdatePage = true;
          } else {
            this.goto('staff');
          }
        },
        // 跳转页面
        goto(key){
          let name = '';
          switch(key){
            case 'manage':
              name = 'demandSurveyPublishIndex';
              break;
            case 'staff':
              name = 'demandSurveyStaffIndex';
              break;
          }
          this.$router.push({ name : name });
          this.gotoUpdatePage = false;
        }
      }
    }
</script>

<style>
  .gotoDialog {
    margin:0 auto;
  }
  .gotoDialog .btn{
    margin-right:10px;
  }
</style>
