<template>
    <div id="">
      <el-dialog
        :modal-append-to-body="false"
        width="80%"
        :title="dialog.title"
        :before-close="cancelBtnClickHandle"
        :visible.sync="dialog.isShow">
        <el-container>
          <el-aside width="400px">
            <el-tree
              @check="checkHandle"
              show-checkbox
              node-key="id"
              :props="dialog.tree.defaultProps"
              :default-expanded-keys="dialog.treeExpandData"
              :data="dialog.tree.node">
            </el-tree>
          </el-aside>
          <el-main>
            <el-row>
              <el-col :span="6">
                <el-input clearable placeholder="姓名" v-model="dialog.userFind.username"></el-input>
              </el-col>
              <el-col :span="3" :offset="2">
                <el-button style="margin-bottom:5px;" type="primary" @click="searchBtnClickHandle(1)">搜索</el-button>
              </el-col>
              <!--刘杰1204新增 全选按钮 起-->
              <el-col :span="3" :offset="0">
                <el-button style="margin-bottom:5px;" type="primary" @click="selectAllBtn">全部勾选</el-button>
              </el-col>
              <el-col :span="3" :offset="0">
                <el-button style="margin-bottom:5px;" type="primary" @click="selectNoneBtn">全部取消</el-button>
              </el-col>
              <!--刘杰1204新增 终-->

            </el-row>

            <el-table
              ref="multipleTable"
              @selection-change="selectionChange"
              :data="frontPage.tableData"
              border>
              <el-table-column
                width="70"
                type="selection">
              </el-table-column>
              <!--<el-table-column-->
                <!--width="70"-->
                <!--label="序号"-->
                <!--type="index">-->
              <!--</el-table-column>-->
              <el-table-column
              prop="num"
              label="序号"
              width="70">
              </el-table-column>
              <el-table-column
                prop="username"
                label="姓名"
                width="120">
              </el-table-column>
              <el-table-column
                prop="deptName"
                label="部门"
                width="150">
              </el-table-column>
              <el-table-column
                prop="companyName"
                show-overflow-tooltip
                label="公司"
                min-width="150">
              </el-table-column>
            </el-table>
            <el-pagination
              background
              layout="sizes ,prev, pager, next"
              :current-page="frontPage.pageNum"
              :total="frontPage.total"
              :page-size="frontPage.pageSize"
              :page-sizes="[10,20, 50, 100]"
              @current-change ="disasterPageChangeHandle"
              @size-change="handleSizeChange">
            </el-pagination>
          </el-main>
        </el-container>
        <span slot="footer" class="dialog-footer">
          <el-button @click="cancelBtnClickHandle">取 消</el-button>
          <el-button type="primary" @click="okBtnClickHandle">确 定</el-button>
        </span>
      </el-dialog>
    </div>
</template>

<script>
    export default {
      // 对话框是否显示
      props : ['data'],
      data(){
        return {
          dialog : {
            // 根据部门ID、分页数据来获取员工列表
            userFind : {
              deptIds : [],
              // 当前页
              pageCurrent : 1,
              // 页数大小
              pageSize : 10,
              //1129 刘杰 新增username
              //姓名
              username:'',
            },
            // 标题
            title : '请勾选左边部门，搜索并勾选出参与人员',
            // 是否显示
            isShow : false,
            // 部门树
            tree: {
              // 节点
              node : [],
              // 默认属性
              defaultProps : {
                children: 'subDept',
                label: 'name'
              },
            },
            // 员工表
            tableData : [],
            treeExpandData:[]
          },
          // 前端分页
          frontPage : {
            // 当前页
            pageCurrent : 1,
            // 页数大小
            pageSize : 10,
            // 总数
            total : 0,
            // 表格数据
            tableData : [],
          },
          // 当前选中的员工
          currentStaff : [],
        }
      },
      watch:{
        'data.isShow'(from, to){
          this.init();
        }
      },
      computed:{
        'data.isShow'(from, to){
          this.init();
        }
      },
      mounted(){
        this.init();
      },
      methods: {
        init(){
          this.$tool.cloneObj(this.dialog, this.data);
//          console.log(this.data);
          // 参与人员---对话框---获取部门节点树
          if(this.data.nodeTree.length === 0){
            this.participantBtnClickHandle();
          } else {
            this.dialog.tree.node = this.data.nodeTree;
            this.dialog.treeExpandData.push(this.dialog.tree.node[0].id);
          }
        },
        // 设置节点树
        setNodeTree(node){
          this.$nextTick(function(){
            this.dialog.tree.node = node;
            this.dialog.treeExpandData.push(this.dialog.tree.node[0].id);
          }.bind(this));
        },
        // 参与人员---对话框---获取部门节点树
        participantBtnClickHandle(){
          this.$store.dispatch('deptGetOrgDept', {}).then(function(res){
            if(res.success){
              this.dialog.tree.node = res.data;
              this.dialog.treeExpandData.push(this.dialog.tree.node[0].id);
            }
          }.bind(this));
        },
        // 参与人员---对话框---获取员工列表
        checkHandle(data, status){
          this.dialog.userFind.deptIds = status.checkedKeys;
          let userFind = this.dialog.userFind;
          this.searchBtnClickHandle(userFind.pageCurrent);
        },
        // 分页
        disasterPageChangeHandle(page){
          this.searchBtnClickHandle(page)/*
          this.frontPage.pageCurrent = page;
          this.currentChangePage(page)*/;
        },
        handleSizeChange(page){
          this.dialog.userFind.pageSize = page;
          this.searchBtnClickHandle(1)
        },
       /* // 前端分页
        currentChangePage() {
          let from = (this.frontPage.pageCurrent - 1) * this.frontPage.pageSize;
          let to = this.frontPage.pageCurrent * this.frontPage.pageSize;
          this.frontPage.tableData = [];
          for (; from < to; from++) {
            let item = this.dialog.tableData[from];
            if (item) {
              this.frontPage.tableData.push(item);
            }
          }
          this.$nextTick(function(){
            this.toggleSelection();
          }.bind(this));
        },*/
        // 搜索用户信息
        searchBtnClickHandle(pageCurrent){
          let userFind = this.dialog.userFind;
          let params = new URLSearchParams();
          params.append("deptIds", userFind.deptIds);
          params.append("pageCurrent", pageCurrent);
          params.append("pageSize", userFind.pageSize);
          params.append("username", userFind.username);//1129 刘杰 新增 搜索条件加姓名
          this.$store.dispatch('userFind', params).then(function(res){
            if(res.success){
              this.dialog.tableData = res.data.list;
              this.frontPage.total = res.data.total;
              this.frontPage.tableData = res.data.list;
              // this.currentChangePage();
            }
          }.bind(this))
        },
        // 参与人员---对话框---勾选员工
        selectionChange(val){
          // 已经选中的人员
          let userIdArr = this.currentStaff.map(function(it){ return it.userId; })
          // 当前表格显示的人员
          let curTableUserIdArr = this.frontPage.tableData.map(function(it){ return it.userId; })
          if(val.length > 0){
            // 先将 userIdArr 中包含 dialog.dataTable 的剔除
            // 例如已选择的有【1，2，8，9】，当前表格为【1，2，3，4，5】，那么去除之后就是【8，9】
            this.currentStaff = this.currentStaff.filter(function(it){
              return !curTableUserIdArr.includes(it.userId);
            }.bind(this))
            // 再将当前表格选中的添加进去
            val.forEach(function(it){
              if(it){
                this.currentStaff.push(it);
              }
            }.bind(this))
          }
        },
        // 显示已经选中的人员
        toggleSelection() {
          let userIdArr = this.currentStaff.map(function(it){ return it.userId; });
          if (userIdArr.length > 0) {
            this.frontPage.tableData.forEach(function(it, index){
              if(userIdArr.includes(it.userId)){
                this.$refs.multipleTable.toggleRowSelection(this.frontPage.tableData[index]);
              }
            }.bind(this))
          } else {
            this.$refs.multipleTable.clearSelection();
          }
        },

        //刘杰1204 新增 起
        // 全选按钮
        selectAllBtn(){
          if(this.dialog.tableData.length>0){
            let userIdArr = this.currentStaff.map(function(it){ return it.userId; });
            this.dialog.tableData.forEach(function(it){
              if(!userIdArr.includes(it.userId)){
                this.currentStaff.push(it);
              }
            }.bind(this))
            // this.currentStaff=this.dialog.tableData;
            this.toggleSelection();
          }

        },
        //刘杰1204 新增 终

        //刘杰1204 新增 起
        // 全部取消按钮
        selectNoneBtn(){
          if(this.dialog.tableData.length>0){
            this.currentStaff=[];
            this.toggleSelection();
          }
        },
        //刘杰1204 新增 终

        // 确定按钮
        okBtnClickHandle(){
          // 更新父组件
          this.dialog.isShow = false;
          this.$emit('update:data', { isShow : false });
          this.$emit("currentChange", this.currentStaff);
        },
        // 取消按钮
        cancelBtnClickHandle(){
          // 更新父组件
          this.dialog.isShow = false;
          this.$emit('update:data', { isShow : false });
        }

      }
    }
</script>

<style>

</style>
