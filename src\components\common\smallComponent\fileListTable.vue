<template>
  <div id="fileListTable" style="width: 100%">
    <el-table
      :data="fileList"
      border
      style="width: 100%">
      <el-table-column
        type="index"
        align="center"
        label-class-name="inner-header-style"
        width="50">
      </el-table-column>
      <el-table-column
        prop="fileName"
        label="文件名称"
        align="center"
        label-class-name="inner-header-style"
        min-width="320">
      </el-table-column>
      <el-table-column
        prop="uploadTime"
        label="上传时间"
        align="center"
        :formatter="dateFormat"
        label-class-name="inner-header-style"
        width="150">
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        fixed="right"
        width="140"
        label-class-name="inner-header-style">
        <template slot-scope="scope">
          <el-button type="text" size="medium" style="color: #5daf34"
                     @click="downloadClick(scope.row)">下载
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script>
  export default {
    name: 'fileListTable',
    props:['fileList'],
    data() {
      return {}
    },
    methods:{
      downloadClick:function (file) {
        const elink = document.createElement('a'); // 创建a标签
        elink.download = file.fileName ;// 文件名
        elink.style.display = 'none';
        elink.href = this.fileHttp.defaults.baseURL+file.path;
        document.body.appendChild(elink);
        elink.click() ;// 触发点击a标签事件
        document.body.removeChild(elink);
      },
      dateFormat(row, column) {
        //.replace(/年|月/g, "-").replace(/日/g, " ")
        return new Date(row.uploadTime).Format("yyyy-MM-dd hh:mm").toLocaleString();
      },
    }
  }
</script>
<style>
</style>
