<template>
    <div id="">
     <!-- <el-select
        v-model="username"
        @change="userChange"
        filterable
        remote
        reserve-keyword
        placeholder="请输入关键词"
        :remote-method="remoteMethod">
        <el-option
          v-for="item in assist.filterList"
          :key="item.userId"
          :label="item.username"
          :value="item.userId">
        </el-option>
      </el-select>-->

      <el-autocomplete
        clearable
        v-model="username"
        :fetch-suggestions="remoteMethod"
        placeholder="请输入内容"
        @select="userChange"
      ></el-autocomplete>
    </div>
</template>

<script>
    export default {
      props:['data', 'query'],
      data(){
        return {
          // 前端搜索
          username : '',
          // dada的数据--START---后端搜索
          search : {
            // 是否已经培训过了
            entryTrain : false,
            // 公司
            companyId : '',
            // 部门
            deptId : '',
            // 班组
            teamId : '',
            // 当前页
            pageCurrent : 1,
            // 页数大小
            pageSize : 100,
          },
          // dada的数据--END
          assist : {
            // 用户列表--全部的
            staffList : [],
            // 用户列表--筛选过滤后的
            filterList : [],
          },
        }
      },
      watch:{
        // 搜索条件默认值
        'query' : function(from, to){
          this.username = this.query;
        }
      },
      mounted(){
        // 初始化数据
        this.init();
      },
      methods:{
        // 初始化数据
        init(){
          // 把父组件传递的值给子组件
          Object.entries(this.data).forEach(function(it){
            if(this.search.hasOwnProperty(it[0])){
              this.search[it[0]] = it[1];
            }
          }.bind(this));
          this.getUserList();
        },
        // 搜索用户
        getUserList(){
          let params = new URLSearchParams();
          Object.entries(this.search).forEach(function(it){
            if(it[1] || (typeof it[1] == 'boolean')){
              params.set([it[0]], it[1])
            }
          })

          this.$store.dispatch('userFind', params).then(function(res){
            if(res.success){
              this.assist.staffList = res.data.list;
              this.assist.filterList = res.data.list;
            }
          }.bind(this));
        },
        // 用户选择
        userChange(item){
          this.username = item.value;
          this.$emit('userChange', item.value);
        },
        // 员工模糊搜索
        remoteMethod(query, cb) {
          let result = [];
          result = this.assist.staffList.filter(function(it){
            return it.username.indexOf(query) > -1;
          }).map(function(it){
            return {
              value : it.username
            }
          })
          console.log(result)
          cb(result);
        },
      }
    }
</script>

<style>

</style>
