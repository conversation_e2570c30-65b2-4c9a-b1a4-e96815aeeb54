<template>
    <el-container class="container">
      <el-main>
        <el-form ref="form" label-width="150px" :rules="rules" :model="form">
          <el-row type="flex">
            <el-col :span="24">
              <el-form-item  label="基本信息：" class="title">
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex">
            <el-col :span="8">
              <el-form-item class="formItem" label="姓名" prop="eduUser.username">
                <span v-if="pageStatus === 'view'">{{assist.eduUser.username}}</span>
                <userList
                  v-if="pageStatus === 'edit'"
                  :query="assist.eduUser.username"
                  :data="addStaff" @userChange="usernameChange"></userList>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item  class="formItem" label="性别">
                <span>{{assist.eduUser.gender ? '男' : '女'}}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item class="formItem" label="原岗位" prop="oldPost">
                <span v-if="pageStatus === 'view'">{{form.oldPost}}</span>
                <el-input  v-if="pageStatus === 'edit'" v-model="form.oldPost"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex">
            <el-col :span="8">
              <el-form-item class="formItem" label="现岗位" prop="newPost">
                <span v-if="pageStatus === 'view'">{{form.newPost}}</span>
                <el-input  v-if="pageStatus === 'edit'" v-model="form.newPost"></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <template v-if="pageStatus === 'view'">

            <el-row type="flex">
              <el-col :span="24">
                <el-form-item label="公司培训：" class="title">
                </el-form-item>
              </el-col>
            </el-row>
            <el-row type="flex">
              <el-col :span="8">
                <el-form-item class="formItem" label="公司教育者">
                  <span v-if="pageStatus === 'view'">{{assist.eduEntryTrainingCompany.teacher}}</span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item class="formItem" label="公司教育时间">
                  <span v-if="pageStatus === 'view'">{{$tool.formatDateTime(assist.eduEntryTrainingCompany.trainingDate).substring(0,10)}}</span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item class="formItem" label="公司教育学时">
                  <span v-if="pageStatus === 'view'">{{assist.eduEntryTrainingCompany.trainingHours}}</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row type="flex">
              <el-col :span="24">
                <el-form-item class="formItem" label="公司培训内容">
                  <span v-if="pageStatus === 'view'" >{{assist.eduEntryTrainingCompany.courses}}</span>
                </el-form-item>
              </el-col>
            </el-row>
          </template>




          <el-row type="flex">
            <el-col :span="24">
              <el-form-item label="部门转岗培训：" class="title">
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex">
            <el-col :span="8">
              <el-form-item class="formItem" label="部门教育者" prop="teacher">
                <span v-if="pageStatus === 'view'">{{form.teacher}}</span>
                <el-input  v-if="pageStatus === 'edit'" v-model="form.teacher"></el-input>
              <!--  <userList
                  v-if="pageStatus === 'edit'"
                  :query="form.teacher"
                  :data="addStaff" @userChange="teacherChange"></userList>-->
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item class="formItem" label="部门教育时间" prop="trainingDate">
                <span v-if="pageStatus === 'view'">{{$tool.formatDateTime(form.trainingDate).substring(0,10)}}</span>
                <el-date-picker
                  v-if="pageStatus === 'edit'"
                  v-model="form.trainingDate"
                  type="month"
                  placeholder="选择日期">
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item  class="formItem" label="部门教育学时" prop="trainingHours">
                <span v-if="pageStatus === 'view'">{{form.trainingHours}}</span>
                <el-input  v-if="pageStatus === 'edit'" v-model="form.trainingHours"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex">
            <el-col :span="24">
              <el-form-item class="formItem" label="部门培训内容" prop="courses">
                <span v-if="pageStatus === 'view'">{{form.courses}}</span>
                <el-input autosize  v-if="pageStatus === 'edit'" v-model="form.courses" type="textarea" :rows="2"></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row type="flex">
            <el-col :span="24">
              <el-form-item label="班组转岗培训：" class="title">
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex">
            <el-col :span="8">
              <el-form-item class="formItem" label="班组教育者">
                <span v-if="pageStatus === 'view'">{{form.teacherTeam}}</span>
                <el-input  v-if="pageStatus === 'edit'" v-model="form.teacherTeam"></el-input>
             <!--   <userList
                  v-if="pageStatus === 'edit'"
                  :query="form.teacherTeam"
                  :data="addStaff" @userChange="teacherTeamChange"></userList>-->
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item class="formItem" label="班组教育时间">
                <span v-if="pageStatus === 'view'">{{$tool.formatDateTime(form.trainingDateTeam).substring(0,10)}}</span>
                <el-date-picker
                  v-if="pageStatus === 'edit'"
                  v-model="form.trainingDateTeam"
                  type="month"
                  placeholder="选择日期">
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item class="formItem" label="班组教育学时">
                <span v-if="pageStatus === 'view'">{{form.trainingHoursTeam}}</span>
                <el-input  v-if="pageStatus === 'edit'" v-model="form.trainingHoursTeam"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex">
            <el-col :span="24">
              <el-form-item class="formItem" label="班组培训内容">
                <span v-if="pageStatus === 'view'">{{form.coursesTeam}}</span>
                <el-input  autosize  v-if="pageStatus === 'edit'" v-model="form.coursesTeam" type="textarea" :rows="2"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex">
            <el-col :span="8">
              <el-form-item class="formItem" label="考试成绩" prop="score">
                <span v-if="pageStatus === 'view'">{{form.score}}</span>
                <el-input  v-if="pageStatus === 'edit'"  v-model="form.score"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex" class="row" justify="center">
            <el-button size="small" :span="2"
                       v-if="pageStatus === 'edit'"
                       @click="submitBtnClickHandle"
                       type="primary">提交</el-button>
            <el-button size="small" :span="2"  @click="$router.back();">返回</el-button>
          </el-row>
        </el-form>
      </el-main>
    </el-container>
</template>

<script>
    import userList from '@/components/common/userList.vue'
    export default {
      components : {
        userList
      },
      data(){
        return {
          form : {
            // 原岗位
            oldPost : '',
            // 现岗位
            newPost : '',
            // 部门教育时间
            trainingDate : '',
            // 部门教育学时
            trainingHours : '',
            // 部门培训内容
            courses : '',
            // 部门教育者
            teacher : '',
            // 班组教育时间
            trainingDateTeam : '',
            // 班组教育学时
            trainingHoursTeam : '',
            // 班组培训内容
            coursesTeam : '',
            // 班组教育者
            teacherTeam : '',
            // 考试成绩
            score : '',
            // 用户ID
            userId : '',
          },
          assist : {
            // 用户
            eduUser : {
              // 姓名
              username : '',
              // 性别
              gender : false,
            },
            // 查看的时候，有公司培训
            eduEntryTrainingCompany : {},
          },
          rules: {
            oldPost: [
              {required: true, message: '请输入内容', trigger: 'blur'},
            ],
            newPost: [
              {required: true, message: '请输入内容', trigger: 'blur'},
            ],
            trainingDate: [
              {required: true, message: '请选择内容', trigger: 'change'},
            ],
            trainingHours: [
              {required: true, message: '请输入内容', trigger: 'blur'},
            ],
            courses: [
              {required: true, message: '请输入内容', trigger: 'blur'},
            ],
            teacher: [
              {required: true, message: '请输入内容', trigger: 'blur'},
            ],
            score: [
              {required: true, message: '请输入内容', trigger: 'blur'},
            ],
          },
          pageStatus : 'edit',
          // 添加人员
          addStaff : {
            // 公司
            companyId : '',
          },
        }
      },
      watch:{
        $route(to,from){
          // 如果来至列表页
          if(from.name === 'transferPostTrainingIndex'){
            this.init();
          }
        }
      },
      created(){
        this.init();
      },
      mounted(){
        this.init();
      },
      methods: {
        init(){
          let user = this.$tool.getStorage('LOGIN_USER');
          this.addStaff.companyId = user.companyId;
          if(this.$route.params.status){
            this.searchBtnClickHandle();
          } else {
            this.clear();
          }
        },
        // 清空数据
        clear(){
          this.form = this.$tool.clearObj({},this.form);
          this.assist.eduUser = this.$tool.clearObj({},this.assist.eduUser);

          this.pageStatus = 'edit';
        },
        // 部门教育者--选中
        teacherChange(val){
          this.form.teacher = val.username;
        },
        // 班组教育者--选中
        teacherTeamChange(val){
          this.form.teacherTeam = val.username;
        },
        usernameChange(val){
          this.form.userId = val.userId;
          this.assist.eduUser.gender = val.gender;
        },
        // 提交---添加或更新
        submitBtnClickHandle(){
          let id = this.$route.params.id;
          let params = Object.assign({}, this.form);
          if(id) params['id'] = id;
          this.$refs['form'].validate(function(valid) {
            if(valid){
              this.$store.dispatch('eduReassignAddOrUpdate', params).then(function (res) {
                if(res.success){
                  this.$message({
                    type : 'success',
                    message : '操作成功'
                  })
                  this.$router.push({ name : 'transferPostTrainingIndex'})
                } else {
                  this.$message({
                    type : 'error',
                    message : res.message || '操作失败'
                  })
                }
              }.bind(this))
            } else {
              return false;
            }
          }.bind(this))
        },
        // 根据id搜索信息
        searchBtnClickHandle(){
          this.clear();
          let id = this.$route.params.id;
          this.$store.dispatch('eduReassignFind', { id : id }).then(function(res){
            if(res.success){
              let list = res.data.list[0];
              Object.entries(list).forEach(function(it){
                if(this.form.hasOwnProperty(it[0])){
                  this.form[it[0]] = it[1];
                }
              }.bind(this))
              // 查看的时候，公司培训
              this.assist.eduEntryTrainingCompany = list.eduEntryTraining.eduEntryTrainingCompany;
              this.assist.eduUser.username = list.eduUser && list.eduUser.username || '';
              this.assist.eduUser.gender = list.eduUser && list.eduUser.gender || '';
              this.pageStatus = this.$route.params.status || 'edit';

              // 通过userId-->username-->this.addStaff.username
              this.$nextTick(function(){
                this.addStaff.username = this.form.teacher;
              }.bind(this))
            } else {
              this.$message({
                type : 'error',
                message : res.message || '错误'
              })
            }
          }.bind(this));
        },
      }
    }
</script>

<style scoped>
  .container{
    background:#fff;
    padding:0px 20px 20px;
  }
  .row{
    margin-top:10px;
  }
  .title{
    background:rgba(64,158,255,.1);
    color:#0f6fc6;
    border: 1px solid rgba(64,158,255,.2);
    border-radius:5px;
    margin: 5px;
  }
  .formItem{
    margin: 2px;
  }

</style>
