<template>
  <div id="emerResponseWorkflow">
    <div class="background-style">

      <!--应急列表区-->
      <div style="min-width: 900px">
        <div style="width: 100%">
          <div style="width:200px;margin: 10px 0 0 10px;float: left;display: inline-block">
            <el-radio-group v-model="radioResponseType" size="medium" @change="responseTypeChange">
              <el-radio-button  v-for="item in radioResponseButtons" :id="item.value" :label="item.value" :key="item.value">{{item.name}}</el-radio-button>
            </el-radio-group>
          </div>
          <div style="width: 250px;margin:10px 20px 0 0;float: right;display: inline-block;">
            <el-button  v-if="!viewRole" type="success" icon="el-icon-plus" @click="addEmergency">发布应急</el-button>
            <el-button  v-if="!viewRole" type="primary" icon="el-icon-refresh" @click="searchClick">刷新列表</el-button>
          </div>
        </div>
        <!--<div style="float: left;margin: 10px">-->
          <!--<el-cascader-->
            <!--:options="cascaderOptions"-->
            <!--v-model="emerClass"-->
            <!--placeholder="分类选择">-->
          <!--</el-cascader>-->
          <!--<el-select v-model="emerLevel" placeholder="级别选择" style="width: 120px">-->
            <!--<el-option-->
              <!--v-for="item in levelOptions"-->
              <!--:key="item.value"-->
              <!--:label="item.label"-->
              <!--:value="item.label">-->
            <!--</el-option>-->
          <!--</el-select>-->
          <!--<el-select v-model="emerStatus" placeholder="状态选择" style="width: 120px">-->
            <!--<el-option-->
              <!--v-for="item in statusOptions"-->
              <!--:key="item.value"-->
              <!--:label="item.label"-->
              <!--:value="item.value">-->
            <!--</el-option>-->
          <!--</el-select>-->
          <!--<el-input placeholder="请输入预案名称" v-model="searchInput" style="width: 300px;margin-right: 20px">-->
          <!--</el-input>-->
          <!--<el-button icon="el-icon-search" @click="searchClick" class="search-btn">搜 索</el-button>-->
        <!--</div>-->
        <div style="width: 100%;float: left">
          <div style="padding: 10px">
            <el-table
              v-loading="tableLoading"
              :data="tableData"
              border
              highlight-current-row
              @row-dblclick="itemViewClick"
              style="width: 100%">
              <el-table-column
                prop="num"
                label="编号"
                width="60"
                align="center"
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                :label="statusLabel"
                width="150"
                align="center"
                label-class-name="header-style">
                <template slot-scope="scope">
                  <el-tag :type="scope.row.statusColor">{{scope.row.statusName}}</el-tag>
                </template>
              </el-table-column>
              <el-table-column
                prop="name"
                label="应急响应名称"
                width="400"
                show-overflow-tooltip
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="companyName"
                label="发布公司"
                width="250"
                show-overflow-tooltip
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="createDate"
                label="创建日期"
                :formatter="createTimeTransfer"
                width="150"
                align="center"
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="topTypeName"
                label="一级分类"
                width="170"
                align="center"
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="typeName"
                label="二级分类"
                width="170"
                align="center"
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="respLevel"
                label="级别"
                min-width="120"
                align="center"
                label-class-name="header-style">
              </el-table-column>
              <el-table-column fixed="right" label="操作" label-class-name="header-style" width="170">
                <template slot-scope="scope">
                  <div v-if="scope.row.actionFlag===1">
                    <el-button size="mini" type="success" @click="itemViewClick(scope.row)">流程</el-button>
                    <el-button size="mini" type="warning" @click="itemOperateClick(scope.row)">操作</el-button>
                  </div>
                  <div v-else>
                    <el-button size="mini" type="success" @click="itemViewClick(scope.row)">流程</el-button>
                  </div>
                 </template>
              </el-table-column>
            </el-table>
          </div>
          <div >
            <el-pagination
              background
              layout="prev, pager, next"
              :page-size="pageSize"
              :current-page="currentPage"
              :total="totalItem"
              @current-change="currentPageClick">
            </el-pagination>
          </div>
        </div>
      </div>
      <!--应急列表区结束-->

      <!--新增应急预警对话框-->
      <el-dialog title="应急类型选择" :visible.sync="addEmergencyVisible">
        <el-form :model="emerForm" label-position="right">
          <el-form-item label="应急类型:" label-width="120px">
            <el-cascader
              :options="chooseTypeOptions"
              v-model="emerForm.chooseType"
              placeholder="分类选择">
            </el-cascader>
          </el-form-item>
          <el-form-item label="级别:" label-width="120px">
            <el-select v-model="emerForm.chooseLevel" placeholder="级别选择">
              <el-option
                v-for="item in chooseLevelOptions"
                :key="item.value"
                :label="item.label"
                :value="item.label">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="addEmergencyVisible = false">取 消</el-button>
          <el-button type="primary" @click="determineAddEmergency">确 定</el-button>
        </div>
      </el-dialog>
      <!--新增应急预警对话框结束-->

      <!--查看流程对话框-->
      <el-dialog title="查看流程" width="94%" :visible.sync="workflowVisible">
        <div style="display: inline-block;width: 100%;">
          <workflow-process :data="workflowData" :processInstanceId="workflowData.processInstanceId"></workflow-process>
        </div>
      </el-dialog>
      <!--查看流程对话框-->

    </div>
  </div>
</template>
<script>
  import WorkflowProcess from '../../common/workflowProcess.vue'
  export default {
    name: 'emerResponseWorkflow',
    data() {
      return {
        //***************************暂时没有搜索*************************
        //------------------------搜索数据----------------------------
        emerClass:['全部分类'],
        cascaderOptions:[],
        emerLevel:'全部级别',
        levelOptions:[
          {value:'全部级别', label:'全部级别'},
          {value:'应急警报', label:'应急警报'},
          {value:'4级', label:'4级'},
          {value:'3级', label:'3级'},
          {value:'2级', label:'2级'},
          {value:'1级', label:'1级'}
        ],
        emerStatus:'全部状态',
        searchInput:'',
        //*****************************************************************
        //待办和已办
        radioResponseButtons:[{value:'toDoList',name:'我的待办'},{value:'doneList',name:'我的已办'}],
        radioResponseType:'toDoList',
        //------------------------表格数据---------------------------
        tableLoading:false,
        tableData:[],
        currentPage:1,
        totalItem:1,
        pageSize:10,
        //----------------------对话框数据--------------------------------
        addEmergencyVisible:false,
        emerForm:{
          chooseType:[],
          chooseLevel:'1级'
        },
        chooseTypeOptions:[],
        chooseLevelOptions:[
          {value:'应急警报', label:'应急警报'},
          {value:'4级', label:'4级'},
          {value:'3级', label:'3级'},
          {value:'2级', label:'2级'},
          {value:'1级', label:'1级'}
        ],
        //状态表头
        statusLabel:'状态',

        //-----------------------查看流程对话框-------------------------------
        workflowVisible:false,
        workflowData:{
          processInstanceId:'',
          finish:false,
        },
        //浏览角色模式
        viewRole : false,
      }
    },
    computed:{
      statusTable:function () {
        return this.$store.state.emergencyData.statusTable;
      },
    },
    components : {
      'workflow-process' : WorkflowProcess
    },
    mounted:function () {
      this.$store.dispatch('getEmergencyStatusArray');
      this.getPlanType();
      this.searchClick();
      this.init();
    },
    watch:{
      $route(to, from){
        this.init();
        if((from.name==='emergencyFormWorkflow'||from.name==='viewEmergencyWorkflow'||from.name==='editSummaryWorkflow'||from.name==='emerMenuWorkflow'||from.name==='emergencyProcessWorkflow'||from.name==='viewRelieveWorkflow'||from.name==='editSummaryWorkflow'||from.name==='viewSummaryWorkflow')&&this.$route.name==='emerResponseWorkflow'){
          this.searchClick();
        }
      },
    },
    methods: {
      //--------------------------初始化-------------------------------
      init(){
        this.viewRole = this.$tool.judgeViewRole();
      },
      //获取分类
      getPlanType: function () {
        this.$http.get('emgType/getAll/'+this.$tool.getStorage('LOGIN_USER').companyId).then(function (res) {
          this.editPlanTypeArray(res.data.data);
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message({
            showClose: true,
            message: '网络错误，请尝试重登录',
            type: 'error'
          });
        }.bind(this));
      },
//      编写分类
      editPlanTypeArray:function (typeTree) {
        this.cascaderOptions=[];
        this.chooseTypeOptions=[];
        this.cascaderOptions.push({value:'全部分类',label:'全部分类'});

        for(let i=0;i<typeTree.length;i++){
          let tempArray={value:i,label:typeTree[i].typeName,id:typeTree[i].id};
          if(typeTree[i].subTypes.length){
            tempArray.children=[];
            for(let j=0;j<typeTree[i].subTypes.length;j++){
              tempArray.children.push({value:j,label:typeTree[i].subTypes[j].typeName,id:typeTree[i].subTypes[j].id});
            }
          }
          this.cascaderOptions.push(tempArray);
          this.chooseTypeOptions.push(tempArray);
        }
      },
      //--------------------------新增区域响应事件----------------------
      addEmergency:function () {
        this.addEmergencyVisible=true;
      },
      //确定添加
      determineAddEmergency:function () {
        if(this.emerForm.chooseType[0]>=0){
          this.addEmergencyVisible = false;
          this.$router.push({name:'emergencyFormWorkflow',params:{requireType:'newEmergency',emergencyType:this.emerForm.chooseType,emergencyLevel:this.emerForm.chooseLevel}});
        }else{
          this.$message({
            showClose: true,
            message: '请选择应急类型！',
            type: 'warning'
          });
        }
      },

      //--------------------------搜索响应事件--------------------------
      searchClick:function () {
        let params=new URLSearchParams;
        this.currentPage=1;
        params.append("pageCurrent",1);
        this.sendRequest(params);
      },
      currentPageClick:function (val) {
        if(val){
          this.currentPage=val;
          let params=new URLSearchParams;
          params.append("pageCurrent",Number(val));
          this.sendRequest(params);
        }
      },
      //--------------------------表格响应事件--------------------------
      itemViewClick:function (row) {
        this.workflowData.processInstanceId=row.processInstanceId;
        this.workflowVisible=true;
      },
      itemOperateClick:function (row) {
        if(row.operateUrl){
          this.$router.push({name:row.operateUrl,params:{emerData:row}});
        }else{
          this.$message.warning('节点数据获取失败');
        }
      },
      //创建日期改变格式
      createTimeTransfer:function (row) {
        return this.transferTime(row.createDate);
      },

      //--------------------------交互事件------------------------------
      responseTypeChange:function (val) {
        this.searchClick();
      },
      sendRequest:function (params) {
        let requestUrl='';
        if(this.radioResponseType==='toDoList'){
          requestUrl='emgFlow/getMyTask';
          this.statusLabel='状态';
        }else{
          requestUrl='emgFlow/getMyEmgHistoryTask';
          this.statusLabel='操作';
        }
        params.append("pageSize",this.pageSize);
        this.tableLoading=true;
        this.$http.post(requestUrl,params).then(function (res) {
          if(res.data.success){
            if(requestUrl==='emgFlow/getMyTask'){//待办
              if(res.data.data.data.length){
                this.totalItem=res.data.data.page.total;
                this.tableLoading=false;
                this.editTable(res.data.data.data);
              }else {
                this.tableData.splice(0);
                this.tableLoading=false;
              }
            }else{//已办
              if(res.data.data.data.data.length){
                this.totalItem=res.data.data.data.page.total;
                this.tableLoading=false;
                this.editTable(res.data.data.data.data);
              }else {
                this.totalItem=0;
                this.tableData.splice(0);
                this.tableLoading=false;
              }
            }
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message({
            showClose: true,
            message: '网络错误，请尝试重登录',
            type: 'error'
          });
        }.bind(this));
      },
      editTable:function (list) {
        this.tableData=[];
        for(let i=0;i<list.length;i++){
          let tempObj=list[i].emgPlanPublic?list[i].emgPlanPublic:{};
          tempObj.num=(this.currentPage-1)*this.pageSize+i+1;
          tempObj.taskId=list[i].id;
          tempObj.statusName=list[i].name;
          if(this.radioResponseType==='toDoList'){
            if(list[i].extActNode){
              tempObj.statusColor=list[i].extActNode.statusColor;
              tempObj.operateUrl=list[i].extActNode.url;
            }
            tempObj.processInstanceId=list[i].processInstanceId;
            tempObj.operateId=list[i].userId;
            tempObj.actionFlag=1;
          }else{
            tempObj.actionFlag=2;
          }
          this.tableData.push(tempObj);
        }
        this.tableLoading=false;

      },
    }
  }
</script>
<style>
</style>
