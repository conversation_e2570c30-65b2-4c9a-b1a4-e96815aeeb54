<template>
  <div id="dangerTypeManage" >
    <div class="background-style" style="padding: 0 10px">
      <el-col :span="12">
        <el-col :span="24" class="primary-background-title">本公司隐患类型管理</el-col>
        <el-col :span="24">
          <div v-if="addDangerTypeFlag">
            <el-input style="width: 300px" size="small" placeholder="请输入新的隐患类型" v-model="addDangerTypeStr"></el-input>
            <el-button type="success" size="small" @click="addDangerTypeClick">确定</el-button>
            <el-button type="info" size="small" @click="addDangerTypeFlag=false">取消</el-button>
          </div>
          <div v-else><el-button type="success" size="small" @click="addDangerTypeFlag=true">新增隐患类型</el-button></div>
          <el-table :data="tableData" style="margin-top: 10px;">
            <el-table-column type="index" label="序号" width="60" label-class-name="header-style"></el-table-column>
            <el-table-column label="隐患类型" label-class-name="header-style">
              <template slot-scope="scope">
                <div v-if="scope.row.viewFlag">{{scope.row.typeName}}</div>
                <div v-else><el-input size="small" v-model="scope.row.tempTypeName"></el-input></div>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="180" fixed="right" label-class-name="header-style">
              <template slot-scope="scope">
                <div v-if="scope.row.viewFlag">
                  <el-button size="mini" type="primary" @click="scope.row.viewFlag=false;tableData.splice(scope.$index,1,scope.row);">修改</el-button>
                  <el-button size="mini" type="danger" @click="dangerTypeDelete(scope.row)">删除</el-button>
                </div>
                <div v-else>
                  <el-button size="mini" type="success" @click="dangerTypeUpdate(scope.row,scope.$index)">确定</el-button>
                  <el-button size="mini" type="info" @click="scope.row.viewFlag=true;tableData.splice(scope.$index,1,scope.row);">取消</el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-col>
    </div>

  </div>
</template>
<script>
  export default {
    name: 'dangerTypeManage',
    data() {
      return {
        addDangerTypeFlag:false,//新增输入框
        addDangerTypeStr:'',//输入框数据
        tableData:[],
      }
    },
    mounted:function () {
      //获取本公司的隐患类型
      this.searchDangerType();
    },
    watch:{
      $route(to, from){
        if(this.$route.name==='dangerTypeManage') {
          //获取本公司的隐患类型
          this.searchDangerType();
        }
      }
    },
    methods:{
      searchDangerType:function () {
        this.tableData=[];
        this.$http.post('danger/inspectListType/find?companyId='+this.$tool.getStorage('LOGIN_USER').companyId).then(function (res) {
          if (res.data.success) {
            this.tableData=res.data.data;
            this.tableData.forEach(function (item) {
              item.tempTypeName=item.typeName;
              item.viewFlag=true;
            });
          }
        }.bind(this)).catch(function (err) {
          console.log('danger/inspectListType/find:'+err);
        }.bind(this));
      },
      //---------------------------隐患类型管理-------------------------
      addDangerTypeClick:function () {
        if(this.addDangerTypeStr.trim()){
          this.$http.post('danger/inspectListType/add?companyId='+this.$tool.getStorage('LOGIN_USER').companyId+'&typeName='+this.addDangerTypeStr.trim()).then(function (res) {
            if (res.data.success) {
              this.$message.success('添加成功！');
              this.searchDangerType();
              this.addDangerTypeFlag=false;
            }
          }.bind(this)).catch(function (err) {
            this.$message.error('添加隐患类型失败');
            console.log(err);
          }.bind(this));
        }else{
          this.$message.warning('请输入内容');
        }
      },
      dangerTypeDelete:function (row) {
        this.$confirm('将删除该隐患类型, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$http.post('danger/inspectListType/delete?id='+row.id).then(function (res) {
            if (res.data.success) {
              this.$message.success('删除成功！');
              this.searchDangerType();//index有变化，会影响修改的显示，所以直接刷新
            }
          }.bind(this)).catch(function (err) {
            this.$message.error('删除隐患类型失败');
            console.log(err);
          }.bind(this));
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });
        });
      },
      dangerTypeUpdate:function (row,index) {
        this.$http.post('danger/inspectListType/update?id='+row.id+'&typeName='+row.tempTypeName).then(function (res) {
          if (res.data.success) {
            this.$message.success('修改成功！');
            row.typeName=row.tempTypeName;
            row.viewFlag=true;
            this.tableData.splice(index,1,row);
            //this.searchDangerType();
          }
        }.bind(this)).catch(function (err) {
          this.$message.error('修改失败');
          console.log(err);
        }.bind(this));
      }
    }
  }
</script>
<style>
</style>
