<template>
  <div id="tableMange">
    <el-container>
      <el-main>
        <el-row style="margin: 0;padding: 0">
          <!--<el-select-->
            <!--v-model="form.labels"-->
            <!--@change="safeDangerInspectLabelsChangeHandle"-->
            <!--multiple-->
            <!--filterable-->
            <!--remote-->
            <!--reserve-keyword-->
            <!--clearable-->
            <!--collapse-tags-->
            <!--placeholder="请输入标签名后选择,可多选"-->
            <!--style="width: 200px;float: left;margin-left: 10px">-->
            <!--<el-option-->
              <!--v-for="item in labelList"-->
              <!--:key="item.value"-->
              <!--:label="item.label"-->
              <!--:value="item.label">-->
            <!--</el-option>-->
          <!--</el-select>-->
          <el-date-picker
            style="width: 250px;float: left;"
            v-model="createTimeArr"
            type="daterange"
            @change="createTimeChange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期">
          </el-date-picker>
          <el-input v-model="form.dangerInspect.name" clearable placeholder="请输入检查表名称" style="float: left;margin-left: 5px;width: 250px">
            <el-button slot="append" icon="el-icon-search" @click="searchBtnClickHandle"></el-button>
          </el-input>
          <div style="float: left;margin-left: 20px">
            <el-button v-if="!viewRole" type="success" icon="el-icon-plus" @click="$router.push({ name : 'TableManageAdd' });" >新增检查表</el-button>
            <el-button v-if="!viewRole" type="primary" icon="el-icon-upload2" @click="fileList=[];uploadTableVisible=true">导入检查表</el-button>
            <el-button type="primary" icon="el-icon-download" @click="downloadTemplate">导入模板下载</el-button>
            <el-button type="warning"  @click.native="selfCodeViewClick()">员工自查二维码</el-button>

            <!--<el-button @click="copySystemDataClick">复制系统数据</el-button>-->
<!--            <el-switch-->
<!--              v-model="systemData"-->
<!--              @change="reloadData()"-->
<!--              active-text="系统数据"-->
<!--              inactive-text="公司数据">-->
<!--            </el-switch>-->
          </div>

        </el-row>
        <el-row style="margin-top:10px;">
          <el-col :xs="6" :sm="6" :md="6" :lg="6" :xl="5">
            <div style="height: 480px;overflow: scroll;width: 100%">
              <el-tree
                :data="treeArray"
                :props="defaultProps"
                @node-click="checkHandle"
                empty-text="标签加载中"
                highlight-current
                accordion
                style="width: 350px">
              </el-tree>
            </div>
          </el-col>
          <el-col :xs="18" :sm="18" :md="18" :lg="18" :xl="19">
            <el-table
              :data="tableData.list"
              border>
              <el-table-column
                label-class-name="header-style"
                label="序号"
                width="70"
                type="index">
              </el-table-column>
              <el-table-column
                label-class-name="header-style"
                prop="name"
                label="检查表名称"
                show-overflow-tooltip
                width="250">
              </el-table-column>
              <el-table-column
                label-class-name="header-style"
                prop="topTypeName"
                label="分类"
                show-overflow-tooltip
                min-width="200">
                <template slot-scope="scope">
                  <el-tag size="mini" v-for="item in scope.row.safeDangerInspectLabels" :key="item.id" style="margin-right:2px;">{{item.label}}</el-tag>
                </template>
              </el-table-column>
              <el-table-column
                label-class-name="header-style"
                prop="createTime"
                label="创建时间"
                :formatter="formatDateTime"
                width="200">
              </el-table-column>
              <el-table-column
                label-class-name="header-style"
                prop="createUserName"
                label="创建人"
                show-overflow-tooltip
                width="100">
              </el-table-column>
              <el-table-column
                fixed="right" label="操作"
                label-class-name="header-style"
                align="center" width="350">
                <template slot-scope="scope">
                  <el-button size="mini" type="success" @click.native="qrCodeViewClick(scope.row)">二维码</el-button>
                  <el-button size="mini" type="success" @click.native="itemViewClick(scope.row)">查看</el-button>
                  <el-button v-if="!viewRole" size="mini" type="primary" @click="itemUpdateClick(scope.row)">修改</el-button>
                  <el-button v-if="!viewRole" size="mini" type="danger" @click="itemDeleteClick(scope.row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
            <el-pagination
              background
              layout="prev, pager, next"
              :current-page="tableData.pageNum"
              :page-size="form.pageSize"
              :total="tableData.total"
              @current-change ="disasterPageChangeHandle">
            </el-pagination>
          </el-col>
        </el-row>
      </el-main>
    </el-container>
    <!--上传检查表对话框-->
    <el-dialog title="上传检查表" :visible.sync="uploadTableVisible">
      <el-row>选择检查表分类：
        <el-cascader
          :props="uploadTableProp"
          :options="treeArray"
          clearable
          filterable
          :debounce="400"
          change-on-select
          v-model="uploadTableType"
          placeholder="请选择或输入关键字"
          style="width: 100%">
        </el-cascader>
      </el-row>
      <el-row>
        <el-upload
          class="upload-demo"
          ref="upload"
          :action="uploadBaseUrl"
          :with-credentials="cookies"
          :file-list="fileList"
          :auto-upload="false"
          :data="uploadTableParams"
          :before-upload="beforeUpdateClick"
          :on-success="uploadTableSuccess"
          style="width: 400px;margin-top: 10px;">
          <el-button size="small" type="primary">选取检查表</el-button>
        </el-upload>
      </el-row>
      <div slot="footer" class="dialog-footer">
        <el-button type="success" @click="determineUpload">保存</el-button>
        <el-button @click="uploadTableVisible=false">返回</el-button>
      </div>
    </el-dialog>
    <!--上传检查表对话框结束-->
    <!--刘杰 1011 增 起-->
    <!--检查表二维码-->
    <el-dialog   title="检查表二维码" :visible.sync="qrCodeVisible">
      <div  style="height:350px;">
        <img  style="position: absolute;left:50%;top:50%;transform: translate(-50%,-50%);"  :src="qrCode"  />
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button  @click="qrCodeVisible=false">确定</el-button>
      </div>
    </el-dialog>
    <!--刘杰 1011 增 终-->
    <el-dialog   title="自查表二维码" :visible.sync="selfCodeVisible">
      <div  style="height:350px;">
        <img  style="position: absolute;left:50%;top:50%;transform: translate(-50%,-50%);"  :src="selfQrCode"  />
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button  @click="selfCodeVisible=false">确定</el-button>
      </div>
    </el-dialog>

  </div>
</template>
<script>
  export default {
    name: 'tableMange',
    data() {
      return {
        // 搜索显示列表的条件
        form : {
          dangerInspect : {
            // 开始时间
            startDate : '',
            // 结束时间
            endDate : '',
            // 检测表名称
            name : '',
            // 标签
            labels : [],
            companyId: 0,
          },
          // 当前页
          pageCurrent : 1,
          // 页数大小
          pageSize : 10,
        },
        //####辅助字段###
        // 创建时间
        createTimeArr : [],
        // 表格数据
        tableData : {},

        //刘杰1011 增 起
        //二维码
        qrCode: {},
        selfQrCode: {},
        qrCodeVisible: false,
        selfCodeVisible: false,
        //刘杰1011 增 终

        //----------------------------树形标签-------------------------
        defaultProps: {
          children: 'dangerInspectTableLabels',
          label: 'label'
        },
        treeArray:[],
        searchLabelList:[],//搜索标签的路径

        //--------------------------上传检查表----------------------------
        uploadTableVisible:false,
        uploadTableProp:{
          children: 'dangerInspectTableLabels',
          label: 'label',
          value:'id'
        },
        uploadTableParams:{importLabelIds:[],companyId:''},
        uploadTableType:[],
        uploadBaseUrl:'',
        cookies:true,
        fileList:[],
        systemData:false,
        //浏览角色模式
        viewRole : false,
      }
    },
    computed:{
      // 标签
      labelList : function(){
        return this.$store.state.emerHandleModule.labelList
      },
    },
    mounted(){
      this.init();
    },
    watch:{
      $route(to,from){
        if(this.$route.name==='TableManage'){
          this.init();
        }
      }
    },
    methods:{
      // 初始化
      init(){
        // 这里获取的标签不对，但是暂时不需要，所以没改
//        this.$store.dispatch("labelListAction");
        // 搜索
        this.viewRole = this.$tool.judgeViewRole();
        this.searchBtnClickHandle();
        //获取树形标签
        this.getAllLabels();

        //获取上传地址
        this.uploadBaseUrl=this.$http.defaults.baseURL + 'danger/inspect/importDangerInspect';
      },
      // 类别改变处理函数
      safeDangerInspectLabelsChangeHandle(val){
        this.form.labels = val || [];
      },
      // 格式化时间
      formatDateTime(row, column, cellValue){
        return this.$tool.formatDateTime(row.createTime);
      },
      // 创建时间
      createTimeChange(val){
        if(val){
          this.form.dangerInspect.startDate = val[0];
          this.form.dangerInspect.endDate = val[1];
        }
      },
      // 分页
      disasterPageChangeHandle(page){
        this.form.pageCurrent = page;
        this.searchBtnClickHandle();
      },
      // 搜索按钮
      searchBtnClickHandle(){
        // this.form.companyId=this.$tool.getStorage('LOGIN_USER').companyId;
        // this.form.companyId=this.getCompanyId();
        this.form.dangerInspect.companyId=this.getCompanyId();
        this.$store.dispatch('dangerInspectFind', this.form).then(function(res){
          if(res.success){
            this.tableData = res.data;
          }
        }.bind(this));
      },

      //刘杰1011 增 起
      // 查看二维码按钮
      qrCodeViewClick(row){
        this.qrCode = this.$http.defaults.baseURL+'/qrCode/inspectQrCode?codePrefix=safe&inspectId='+row.id;
        this.qrCodeVisible = true;
      },
      // 查看自查二维码按钮
      selfCodeViewClick(){
        this.selfQrCode = this.$http.defaults.baseURL+'/qrCode/inspectQrCode?codePrefix=safe&inspectId='+0;
        this.selfCodeVisible = true;
      },
      //刘杰1011 增 终

      // 查看按钮
      itemViewClick(row){
        this.$router.push({ name : 'TableManageAdd', params : { status : 'view', id : row.id} })
      },
      // 修改按钮
      itemUpdateClick(row){
        this.$router.push({ name : 'TableManageAdd', params : { status : 'edit', id : row.id} })
      },
      // 删除按钮
      itemDeleteClick(row){
        //this.$store.dispatch('dangerInspectDelete')
        this.$confirm('此操作将永久删除, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(function(){
          // 删除---灾后处置
          this.$store.dispatch('dangerInspectDelete', {
            id : row.id,
            // 这里添加了companyId
            companyId: this.$tool.getStorage('LOGIN_USER').companyId
          }).then(function(res){
            if(res.success){
              this.$message({
                type : 'success',
                message : '删除成功'
              })
              this.searchBtnClickHandle();
            } else {
              this.$message({
                type : 'error',
                message : res.message || '删除失败！！'
              })
            }
          }.bind(this))
        }.bind(this))
      },

      //-----------------------------------标签侧边框事件---------------------------------
      getAllLabels:function () {
        // this.$http.get('danger/inspectTableLabel/getLabelStructure?companyId='+this.$tool.getStorage('LOGIN_USER').companyId).then(function (res) {
        this.$http.get('danger/inspectTableLabel/getLabelStructure?companyId='+this.getCompanyId()).then(function (res) {
          if (res.data.success) {
            this.treeArray=res.data.data;
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message({
            showClose: true,
            message: '操作失败',
            type: 'error'
          });
        }.bind(this));
      },
      //标签点击即可响应
      checkHandle:function(obj, node){
        this.form.labels=this.getLabelPath(node);
        this.searchBtnClickHandle();
      },
      //获取完成标签路径
      getLabelPath:function (node) {
        let listTemp=[];
        let nodeTemp=node;
        for(let i=nodeTemp.level;i>=1;i--){
          listTemp.push(nodeTemp.data.label);
          nodeTemp=nodeTemp.parent;
        }
        return listTemp.reverse();
      },

      downloadTemplate:function () {
        const loading = this.$loading({
          lock: true,
          text: 'Loading',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        let params=new URLSearchParams;
        this.$http({ // 用axios发送post请求
          method: 'post',
          url: 'danger/inspect/downloadTemplate', // 请求地址
          data: params, // 参数
          responseType: 'blob' // 表明返回服务器返回的数据类型
        }).then((res) => { // 处理返回的文件流
          loading.close()
          const elink = document.createElement('a') // 创建a标签
          elink.download = '检查表导入模板'+'.xlsx' // 文件名
          elink.style.display = 'none'
          const blob = new Blob([res.data])
          elink.href = URL.createObjectURL(blob)
          document.body.appendChild(elink)
          elink.click() // 触发点击a标签事件
          document.body.removeChild(elink)
        })
      },
      //---------------------------------上传检查表----------------------------
      beforeUpdateClick:function () {
        this.uploadTableParams.importLabelIds=this.uploadTableType;
        // this.uploadTableParams.companyId=this.$tool.getStorage('LOGIN_USER').companyId;
        this.uploadTableParams.companyId=this.getCompanyId();
      },
      determineUpload:function () {
        this.$refs.upload.submit();
      },
      uploadTableSuccess:function (res) {
        if(res.success){
          this.$message.success('上传成功！');
          this.uploadTableVisible=false;
        }else{
          this.$message.warning(res.message);
          this.uploadTableVisible=false;
        }
      },
      //-----------------------------复制系统数据----------------------------------
      copySystemDataClick:function () {
        this.$http.get('/danger/inspect/copyInspectTableByCompany/0'+'/'+this.$tool.getStorage('LOGIN_USER').companyId).then(function (res) {
          this.$message.success('复制成功！');
          this.getClassTree();
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message({
            showClose: true,
            message: '复制系统数据失败！',
            type: 'error'
          });
        }.bind(this));
      },
      getCompanyId:function () {
        if(this.systemData){
          return 0;
        }else{
          return this.$tool.getStorage('LOGIN_USER').companyId;
        }
      },
      reloadData:function () {
        this.searchBtnClickHandle();
        this.getAllLabels()
      }
    }
  }
</script>
<style>
  #tableMange{
    background:#fff;
  }
  .row{
    margin-top:10px;
  }
</style>
