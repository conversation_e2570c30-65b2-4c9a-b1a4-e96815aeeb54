<template>
  <div id="viewSingleDanger">
    <div style="width: 100%;height: 100%;background-color: white">
      <el-col :span="3">
        <el-steps direction="vertical" :active="activeStep" style="margin: 10px auto 0 20px;height: 500px">
          <el-step  v-for="item in stepData" :title="item" :key="item"></el-step>
        </el-steps>
      </el-col>
      <el-col :span="21">
        <el-form ref="form" label-width="120px" style="padding: 10px">
          <el-collapse v-model="activeNames" accordion>
            <el-collapse-item  name="1">
              <template slot="title">
                <div style="font-size: 16px;color: #2d57ae;font-weight: bold;background-color: rgb(236,248,255);padding-left: 20px">隐患信息</div>
              </template>
              <el-col :span="24">
                <el-col :span="12">
                  <el-form-item label="检查项目：" style="margin: 0">
                    {{form.inspectProject}}
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="检查时间：" style="margin: 0">
                    {{form.inspectDate}}
                  </el-form-item>
                </el-col>
              </el-col>
              <el-col :span="24">
                <el-form-item label="检查标准内容：" style="margin: 0">
                  {{form.inspectContent}}
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="检查结果记录：" style="margin: 0">
                  {{form.inspectResult}}
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="隐患照片：" style="margin: 0">
                  <picture-card :picFileList="form.dangerPics"></picture-card>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-col :span="12">
                  <el-form-item label="隐患类型：" style="margin: 0">
                    {{form.dangerType}}
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="隐患级别：" style="margin: 0">
                    {{form.hiddenDangerLevel}}
                  </el-form-item>
                </el-col>
              </el-col>
              <el-col :span="24">
                <el-col :span="12">
                  <el-form-item label="整改期限：" style="margin: 0">
                    {{form.deadline}}
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="整改负责人：" style="margin: 0">
                    {{form.applyUserName}}
                  </el-form-item>
                </el-col>
              </el-col>
              <el-col :span="24">
                <el-form-item label="整改要求：" style="margin: 0">
                  {{form.changeRequire}}
                </el-form-item>
              </el-col>
            </el-collapse-item>
            <el-collapse-item name="2">
              <template slot="title">
                <div style="font-size: 16px;color: #2d57ae;font-weight: bold;background-color: rgb(236,248,255);padding-left: 20px">临时措施</div>
              </template>
              <el-col :span="24">
                <el-form-item label="临时措施时间：" style="margin: 0">
                  {{reformData.tempMeasureTime}}
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="临时措施说明：" style="margin: 0">
                  {{reformData.tempMeasure}}
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="临时措施照片：" style="margin: 0">
                  <picture-card :picFileList="reformData.measurePics"></picture-card>
                </el-form-item>
              </el-col>
            </el-collapse-item>
            <el-collapse-item name="3">
              <template slot="title">
                <div style="font-size: 16px;color: #2d57ae;font-weight: bold;background-color: rgb(236,248,255);padding-left: 20px">隐患整改</div>
              </template>
              <el-col :span="24">
                <el-form-item label="整改完成时间：" style="margin: 0">
                  {{form.changeTime}}
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="整改说明：" style="margin: 0">
                  {{form.changeExplain}}
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="整改照片：" style="margin: 0">
                  <picture-card :picFileList="form.changePics"></picture-card>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="整改回执：" style="margin: 0">
                  {{reformData.reformReply}}
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="整改附件：" style="margin: 0">
                  <file-list :fileList="reformData.reformAttachments"></file-list>
                </el-form-item>
              </el-col>
            </el-collapse-item>
            <el-collapse-item name="4">
              <template slot="title">
                <div style="font-size: 16px;color: #2d57ae;font-weight: bold;background-color: rgb(236,248,255);padding-left: 20px">挂牌督办</div>
              </template>
              <el-col :span="24">
                <el-form-item label="督办完成时间：" style="margin: 0">
                  {{form.superviseReformDate}}
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="督办说明：" style="margin: 0">
                  {{form.superviseReformExplain}}
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="督办完成照片：" style="margin: 0">
                  <picture-card :picFileList="form.supervisePics"></picture-card>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="督办回执：" style="margin: 0">
                  {{reformData.superviseReply}}
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="督办附件：" style="margin: 0">
                  <file-list :fileList="reformData.superviseAttachments"></file-list>
                </el-form-item>
              </el-col>
            </el-collapse-item>
          </el-collapse>
        </el-form>
      </el-col>
    </div>
  </div>
</template>
<script>
  import PictureCard from '../smallComponent/pictureCard.vue'
  import FileList from '../smallComponent/fileList.vue'
  export default {
    name: 'viewSingleDanger',
    props:['dangerId','inspectSheet','reformSheet'],//单条隐患ID，隐患检查单信息（检查日期,检查单状态），整改单信息（检查单ID，隐患负责人ID）
    data() {
      return {
        activeStep:1,
        stepStatusTable01:[
          {status:0,step:1},//就是个占位子的，让status可以直接查询
          {status:1,step:1}, {status:2,step:1}, {status:3,step:2}, {status:4,step:3}, {status:5,step:4}, {status:6,step:5},
          {status:7,step:6}, {status:8,step:6}, {status:9,step:7}, {status:10,step:7}, {status:11,step:7}, {status:12,step:7},
          {status:13,step:7}, {status:14,step:8}, {status:15,step:9},
        ],
        stepStatusTable02:[
          {status:0,step:1},//就是个占位子的，让status可以直接查询
          {status:1,step:1}, {status:2,step:1}, {status:3,step:2}, {status:4,step:3}, {status:5,step:4}, {status:6,step:5},
          {status:7,step:6}, {status:8,step:6}, {status:9,step:7}, {status:10,step:8}, {status:11,step:8}, {status:12,step:9},
          {status:13,step:9}, {status:14,step:9}, {status:15,step:10},
        ],
        activeNames: '1',//默认展开第一个
        //单条隐患数据
        form:{
          dangerPics:[],
          changePics:[],//整改照片
          supervisePics:[]//督办照片
        },
        //整改单信息
        reformData:{
          measurePics:[],
          reformAttachments:[],
          superviseAttachments:[],
        },
        //步骤数据
        stepData:[],
        statusArray01:['发布审核','待检查','检查完成','隐患评估','整改单审核','整改中','整改验收','整改验收审核','结束'],
        statusArray02:['发布审核','待检查','检查完成','隐患评估','整改单审核','整改中','整改验收','挂牌督办','督办验收','结束'],
      }
    },
    mounted:function () {
      this.searchByDangerId(this.dangerId);
      this.searchReformSheet();
    },
    components : {
      PictureCard,
      FileList,
    },
    watch:{
      dangerId:function (val) {
        this.searchByDangerId(val);
        this.searchReformSheet();
      }
    },
    methods:{
      searchByDangerId:function (id) {
        this.activeNames='1';
        if(this.form.dangerPics && this.form.dangerPics.length > 0){
          this.form.dangerPics.splice(0);
        }
        if(this.form.changePics && this.form.changePics.length > 0){
          this.form.changePics.splice(0);
        }if(this.form.supervisePics && this.form.supervisePics.length > 0){
          this.form.supervisePics.splice(0);
        }
        this.$http.post('danger/inspectListPublic/find', {id:id}).then(function (res) {
          if (res.data.success) {
            this.form=res.data.data[0];
            this.form.deadline=this.transferTime(this.form.deadline);
            this.form.changeTime=this.transferTime(this.form.changeTime);
            this.form.superviseTime=this.transferTime(this.form.superviseTime);
            this.form.superviseReformDate=this.transferTime(this.form.superviseReformDate);
            this.form.inspectDate=this.inspectSheet.inspectDate;//检查日期属于检查单信息，不是隐患信息，所以要传进来
          }
        }.bind(this)).catch(function (err) {
          this.$message.error('danger/inspectListPublic/find');
          console.log(err);
        }.bind(this));
      },
      searchReformSheet:function () {//整改单数据
        if(this.reformData.measurePics && this.reformData.measurePics.length > 0){
          this.reformData.measurePics.splice(0);
        }
        if(this.reformData.reformAttachments && this.reformData.reformAttachments.length > 0){
          this.reformData.reformAttachments.splice(0);
        }
        if(this.reformData.superviseAttachments && this.reformData.superviseAttachments.length > 0){
          this.reformData.superviseAttachments.splice(0);
        }
        if(this.reformSheet.applyUserId){
          this.$http.get('danger/reformSheet/find?dangerInspectPublicId='+this.reformSheet.inspectId+'&applyUserId='+this.reformSheet.applyUserId).then(function (res) {
            if (res.data.success) {
              if(res.data.data.length){
                this.reformData=res.data.data[0];
                this.reformData.tempMeasureTime=this.transferTime(this.reformData.tempMeasureTime);
                if(this.reformData.superviseUserId){//如果分配了督办审核人，则进入了督办
                  this.stepData=this.statusArray02;
                  this.activeStep=this.stepStatusTable02[Number(this.inspectSheet.status)].step;
                }else {
                  this.stepData=this.statusArray01;
                  this.activeStep=this.stepStatusTable01[Number(this.inspectSheet.status)].step;
                }
              }else{//没有整改单
                this.reformData={};
                this.stepData=this.statusArray01;
                this.activeStep=this.stepStatusTable01[Number(this.inspectSheet.status)].step;
              }
            }
          }.bind(this)).catch(function (err) {
            this.$message.error('danger/reformSheet/find');
            console.log(err);
          }.bind(this));
        }else{
          this.reformData={};
          this.stepData=this.statusArray01;
          this.activeStep=this.stepStatusTable01[Number(this.inspectSheet.status)].step;
        }

      }

    }

  }
</script>
<style>
</style>
