<template>
  <div id="">
    <el-container class="container">
      <el-main>
        <el-form ref="form" :model="form" label-width="5px">
          <el-row>
            <template v-if="role === 4">
              <el-col :span="3">
                <el-form-item >
                  <el-select v-model="form.eduUser.companyId" @change="companyChange" placeholder="请选择公司" clearable>
                    <el-option
                      v-for="item in assist.companyList"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="3">
                <el-form-item >
                  <el-select  v-model="form.eduUser.deptId" placeholder="请选择部门" clearable>
                    <el-option
                      v-for="item in assist.deptList"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </template>
            <el-col :span="2">
              <el-form-item>
                <el-select v-model="form.status" placeholder="状态" clearable>
                  <el-option
                    v-for="item in assist.status"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="3">
              <el-form-item>
                <el-input clearable placeholder="姓名" v-model="form.eduUser.username"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item >
                <el-date-picker
                  start-placeholder="培训时间范围"
                  end-placeholder=""
                  v-model="assist.entryDate"
                  @change="entryDateChange"
                  style="width: 100%"
                  type="daterange">
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :offset="1" :span="2">
              <el-button type="primary" @click="searchBtnClickHandle">搜索</el-button>
            </el-col>
          </el-row>
          <el-row>
            <!--公司/组织者-->
            <template v-if="role === 4">
              <el-col :offset="1" :span="2">
                <el-button type="success" icon="el-icon-plus" @click="$router.push({ name : 'threeLevelTrainingCompanyAdd' });">三级培训</el-button>
              </el-col>
              <el-col :offset="1" :span="2">
                <el-button type="danger" @click="isMore = true;">批量打分</el-button>
              </el-col>
              <el-col :offset="1" :span="2">
                <el-button type="primary" @click="saveScoreBtnClickHandle">保存分数</el-button>
              </el-col>
            </template>
          </el-row>
          <el-row style="margin-top:20px;">
            <el-table
              :data="tableData.list"
              border>
              <el-table-column
                label-class-name="header-style"
                label="序号"
                align="center"
                width="100"
                type="index">
              </el-table-column>
              <el-table-column
                label-class-name="header-style"
                prop="eduUser.username"
                label="姓名"
                align="center"
                width="100">
              </el-table-column>
              <el-table-column
                label-class-name="header-style"
                prop="score"
                label="考试成绩"
                align="center"
                width="210">
                  <template slot-scope="scope">
                    <span v-if="!isMore || scope.row.status == 1">{{scope.row.score}}</span>
                    <el-input-number
                      v-if="isMore && scope.row.status == 0"
                      v-model="more.scores[scope.$index]"
                      :min="0" :max="100" :step="5" label="描述文字"></el-input-number>
                  </template>
              </el-table-column>
              <el-table-column
                label-class-name="header-style"
                prop="status"
                label="状态"
                align="center"
                width="100">
                <template slot-scope="scope">
                  <el-tag v-if="scope.row.status == 0" type="info">未完成</el-tag>
                  <el-tag v-if="scope.row.status == 1" type="success">已完成</el-tag>
                </template>
              </el-table-column>
              <el-table-column
                label-class-name="header-style"
                prop="eduUser.gender"
                label="性别"
                align="center"
                width="100">
                <template slot-scope="scope">{{scope.row.eduUser.gender ? '男' : '女' || ''}}</template>
              </el-table-column>
              <el-table-column
                label-class-name="header-style"
                prop="eduUser.entryDate"
                :formatter="formatDateTime"
                label="入职时间"
                align="center"
                width="120">
              </el-table-column>
              <el-table-column
                label-class-name="header-style"
                prop="eduUser.deptName"
                label="部门"
                align="center"
                width="150">
              </el-table-column>
              <el-table-column
                label-class-name="header-style"
                :formatter="formatDateTime"
                prop="eduEntryTrainingCompany.trainingDate"
                label="公司培训时间"
                align="center"
                width="120">
              </el-table-column>
              <el-table-column
                label-class-name="header-style"
                prop="eduEntryTrainingCompany.trainingHours"
                label="公司培训学时"
                align="center"
                width="120">
              </el-table-column>
              <el-table-column
                label-class-name="header-style"
                prop="eduEntryTrainingCompany.score"
                label="公司培训成绩"
                align="center"
                width="120">
              </el-table-column>
              <el-table-column
                label-class-name="header-style"
                :formatter="formatDateTime"
                prop="eduEntryTrainingDepartment.trainingDate"
                label="部门培训时间"
                align="center"
                width="120">
              </el-table-column>
              <el-table-column
                label-class-name="header-style"
                prop="eduEntryTrainingDepartment.trainingHours"
                label="部门培训学时"
                align="center"
                width="120">
              </el-table-column>
              <el-table-column
                label-class-name="header-style"
                prop="eduEntryTrainingDepartment.score"
                label="部门培训成绩"
                align="center"
                width="120">
              </el-table-column>
              <el-table-column
                label-class-name="header-style"
                :formatter="formatDateTime"
                prop="eduEntryTrainingTeam.trainingDate"
                label="班组培训时间"
                align="center"
                width="120">
              </el-table-column>
              <el-table-column
                label-class-name="header-style"
                prop="eduEntryTrainingTeam.trainingHours"
                label="班组培训学时"
                align="center"
                width="120">
              </el-table-column>
              <el-table-column
                label-class-name="header-style"
                prop="eduEntryTrainingTeam.score"
                label="部门培训成绩"
                align="center"
                width="120">
              </el-table-column>
              <el-table-column
                fixed="right" label="操作"
                label-class-name="header-style"
                align="left" width="250">
                <template slot-scope="scope">
                  <el-button size="mini" type="success" @click.native="itemViewClick(scope.row)">查看</el-button>
                  <!--非员工-->
                  <el-button
                    v-if="scope.row.status == 0 && role > 1"
                    size="mini" type="primary" @click="itemUpdateClick(scope.row)">修改</el-button>
                  <!--公司/组织者-->
                  <el-button v-if="role === 4" size="mini" type="danger" @click="itemDeleteClick(scope.row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
            <el-pagination
              background
              layout="prev, pager, next"
              :current-page="tableData.pageNum"
              :page-size="form.pageSize"
              :total="tableData.total"
              @current-change ="disasterPageChangeHandle">
            </el-pagination>
          </el-row>
        </el-form>
      </el-main>
      <el-footer>
        <el-dialog
          class="gotoDialog"
          title="跳转页面"
          :visible.sync="gotoUpdatePage"
          width="40%">
            <el-button class="btn" type="primary" @click="goto('company')" v-if="powerBtns.includes('modCBtn')">公司培训</el-button>
            <el-button class="btn" type="success" @click="goto('dept')" v-if="powerBtns.includes('modDBtn')">部门培训</el-button>
            <el-button class="btn" type="danger" @click="goto('team')" v-if="powerBtns.includes('modTBtn')">班组培训</el-button>
            <el-button class="btn"  @click="gotoUpdatePage = false">取消</el-button>
        </el-dialog>
      </el-footer>
    </el-container>
  </div>
</template>
<script>
  export default {
    name: '',
    data() {
      return {
        // 搜索
        form : {
          eduUser : {
            // 姓名
            username : '',
            // 公司id
            companyId : '',
            // 部门id
            deptId : '',
            // 班组id
            teamId : '',
            // 用户id
            userId : '',
          },
          // 入职时间---开始时间
          startDate : '',
          // 入职时间---结束时间
          endDate : '',
          // 状态--公司和组织者一样
          status : '',
          // 当前页
          pageCurrent : 1,
          // 页数大小
          pageSize : 8,
        },
        assist:{
          // 入职时间
          entryDate : '',
          // 状态
          status : [
            { value : '0', label : '未完成' },
            { value : '1', label : '已完成' },
          ],
          // 搜索条件--公司列表和部门列表
          companyList : [],
          deptList : [],
        },
        // 批量
        more : {
          ids : [],
          scores : []
        },
        // 是否批量
        isMore : false,
        tableData : {},
        // 角色 0 组织者或公司      1 部门        2  班组
        role : 0,

        // 权限按钮
        powerBtns : [],
        // 跳转页面对话框
        gotoUpdatePage : false,
        // 跳转到修改页面要传递的参数
        editParams : {},
      }
    },
    mounted(){
      this.init();
    },
    watch:{
      $route(to,from){
        if(to.name === 'threeLevelTrainingIndex') {
          this.init();
        }
      }
    },
    methods:{
      // 初始化
      init(){
        // 根据权限按钮设置角色
        this.judgeUserRole();
        // 搜索
        this.searchBtnClickHandle();
        // 公司部门列表
        this.companyList();
      },
      // 公司部门列表
      companyList(){
        let loginUser = this.$tool.getStorage('LOGIN_USER');
        let params = this.$tool.jsonToForm({
          parentId : loginUser.companyId,
          type : 1,   // 1 公司    0  部门
        })
        let companyList = [];
        this.$store.dispatch('deptFind', params).then(function(res){
          if(res.success){
            let list = res.data;
            companyList = list.map(function(it){
              return {
                id : it.id,
                name : it.name
              }
            }.bind(this))
            companyList.unshift({
              id : loginUser.companyId,
              name : loginUser.companyName
            })
            this.assist.companyList = companyList;
          } else {
            this.$message({
              type : 'error',
              message : res.message || '错误'
            })
          }
        }.bind(this));
      },
      // 公司选择，显示部门
      companyChange(row){
        this.form.eduUser.deptId = '';
        let loginUser = this.$tool.getStorage('LOGIN_USER');
        let params = this.$tool.jsonToForm({
          parentId : row,
          type : 0,   // 1 公司    0  部门
        })
        this.$store.dispatch('deptFind', params).then(function(res){
          if(res.success){
            let list = res.data;
            this.assist.deptList = list.map(function(it){
              return {
                id : it.id,
                name : it.name
              }
            }.bind(this))
          } else {
            this.$message({
              type : 'error',
              message : res.message || '错误'
            })
          }
        }.bind(this));
      },
      judgeUserRole(){
        // 获取权限按钮
        this.powerBtns = this.$tool.getPowerBtns('eduTrainingMenu', this.$route.path);

        // 公司
        if(this.powerBtns.includes('modCBtn')){
          this.role = 4;
        } else if(this.powerBtns.includes('modDBtn')){
          this.role = 3;
        } else if(this.powerBtns.includes('modTBtn')){
          this.role = 2;
        } else {
          this.role = 1;
        }
      },
      // 清空数据
      clear(){
        this.isMore = false;
        this.more.ids = [];
        this.more.scores = [];
      },
      // 格式化时间
      formatDateTime(row, column, cellValue){
        let pro = column.property;
        if(!pro) return '';
        let arr = pro.split('.');
        let time = row[arr[0]][arr[1]];
        if(!time) return '';
        let num = 10;
        let str = this.$tool.formatDateTime(time) || '';
        return str ? str.substring(0, num) : str;
      },
      // 分页
      disasterPageChangeHandle(page){
        this.form.pageCurrent = page;
        if(this.isMore){
          this.saveScoreBtnClickHandle();
        } else {
          this.searchBtnClickHandle();
        }
      },
      // 入职时间
      entryDateChange(val){
        if(val){
          this.form.startDate = val[0];
          this.form.endDate = val[1];
        } else {
          this.form.startDate = '';
          this.form.endDate = '';
        }
      },
      // 搜索按钮
      searchBtnClickHandle(){
        let user = this.$tool.getStorage('LOGIN_USER');
        let eduUser = this.form.eduUser;
        this.clear();
        // 公司
        if (this.role === 4){
//          eduUser.companyId = user.companyId;
        }
        // 部门
        else if(this.role === 3){
          eduUser.deptId = user.deptId;
        }
        // 班组
        else if (this.role === 2){
          eduUser.teamId = user.teamId;
        }
        // 员工
        else{
          eduUser.userId = user.userId;
        }
        let params = this.$tool.filterObj({}, this.$tool.filterObj({}, this.form));

//        console.log(this.role);
//        return;
        let url = this.role == 4 ? 'eduEntryTrainingCompFindDetail' : 'eduEntryTrainingFind';
        this.$store.dispatch(url, params).then(function(res){
          if(res.success){
            this.tableData = res.data;
            let list = res.data.list;
            for(var i = 0; i < list.length; i++){
              if(!list[i].eduUser){
                this.tableData.list[i].eduUser = {};
              }
            }
            res.data.list.forEach(function(it){
              this.more.ids.push(it.id);
              this.more.scores.push(it.score);
            }.bind(this))
          } else {
            this.$message({
              type : 'error',
              message : res.message || '错误'
            })
          }
        }.bind(this));
      },
      // 查看
      itemViewClick(row){
        let name = 'threeLevelTrainingView';
        let params = {
          id : row.id,
          status : 'view'
        }
        this.$router.push({ name : name, params : params})
      },
      // 修改
      itemUpdateClick(row){
        this.editParams = {
          id : row.id,
          status : 'edit'
        }
        this.gotoUpdatePage = true;
        return;
        let name = '';
        switch(this.role){
          // 组织者||公司
          case 4:
                name = 'threeLevelTrainingCompanyEdit';
                break;
          // 部门
          case 3:
                name = 'threeLevelTrainingDepartmentEdit';
                break;
          // 班组
          case 2:
                name = 'threeLevelTrainingTeamEdit';
                break;
        }
        this.$router.push({ name : name, params : params})
      },
      // 删除按钮
      itemDeleteClick(row){
        this.$confirm('此操作将永久删除, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
          .then(function(){
            this.$store.dispatch('eduEntryTrainingDelete', {
              id : row.id,
              userId : row.eduUser.userId
            }).then(function(res){
              if(res.success){
                this.$message({
                  type : 'success',
                  message : '删除成功'
                })
                this.searchBtnClickHandle();
              } else {
                this.$message({
                  type : 'error',
                  message : res.message || '删除失败！！'
                })
              }
            }.bind(this))
          }.bind(this))
      },
      // 批量保存分数
      saveScoreBtnClickHandle(){
        this.$store.dispatch('eduEntryTrainingBatchInputScores', this.more).then(function(res){

          if(res.success){
            this.$message({
              type : 'success',
              message : '打分成功'
            })
            this.searchBtnClickHandle();
          } else {
            this.$message({
              type : 'error',
              message : res.message || '打分失败！！'
            })
          }
        }.bind(this));
      },

      // 跳转页面
      goto(key){
        let name = '';
        let params = this.editParams;
        switch(key){
          case 'company':
            name = 'threeLevelTrainingCompanyEdit';
                break;
          case 'dept':
            name = 'threeLevelTrainingDepartmentEdit';
            break;
          case 'team':
            name = 'threeLevelTrainingTeamEdit';
            break;
        }
        this.$router.push({ name : name, params : params });
        this.gotoUpdatePage = false;
      }

    }
  }
</script>
<style>
  .container{
    background:#fff;
    padding:0 20px;
  }
  .row{
    margin-top:10px;
  }
  .gotoDialog {
    margin:0 auto;
  }
  .gotoDialog .btn{
    margin-right:10px;
  }
</style>
