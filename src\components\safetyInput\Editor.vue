<template>
    <div id="">
      <el-button type="primary" size="mini" @click="editBtnClickHandler">编辑</el-button>
      <el-button type="primary" size="mini" @click="viewBtnClickHandler">查看</el-button>
      <el-button type="primary" size="mini">删除</el-button>
    </div>
</template>

<script>
    export default {
      props: ['row', 'col'],
      methods : {
        // 编辑按钮
        editBtnClickHandler(){
          this.$set(this.row, '_edit', !this.row._edit)
          this.$emit('row-edit', this.row)
        },
        // 查看按钮
        viewBtnClickHandler(){
          this.$set(this.row, '_edit', false)
          this.$emit('row-view', this.row)
        }
      }
    }
</script>

<style>

</style>
