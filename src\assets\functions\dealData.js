export default{
  //返回树形的当前子公司和部门
  editTree:function (val) {
    let arrayTemp=[];
    for(let i=0;i<val.length;i++){
      let itemTemp=new Object;
      itemTemp.id=val[i].id;
      itemTemp.name=val[i].name;
      itemTemp.num=val[i].num;
      itemTemp.parentId=val[i].parentId;
      itemTemp.type=val[i].type;
      itemTemp.selfInspectId=val[i].selfInspectId
      if(val[i].type==0){
        itemTemp.typeName=itemTemp.name+"(部门)"
      }else{
        itemTemp.typeName=itemTemp.name+"(公司)"
      }
      if(val[i].logoFileId){
        itemTemp.logoFileId=val[i].logoFileId
        itemTemp.logoPath=val[i].logoPath
      }else{
        itemTemp.logoFileId=0;
        itemTemp.logoPath='';
      }
      if(val[i].subDept.length){
        itemTemp.subDept=this.editTree(val[i].subDept);
      }
      arrayTemp.push(itemTemp);
    }
    return arrayTemp;
  },
  //返回人员可选，子部门不能选，适用于只能选人员
  editUserDeptTree:function (val,isUser) {
    let arrayTemp=[];
    if(isUser){
      for(let i=0;i<val.length;i++){
        arrayTemp.push({id:val[i].userId,name:val[i].username,disabled: false});
      }
      return arrayTemp;
    }else{
      for(let i=0;i<val.length;i++){
        let itemTemp=new Object;
        itemTemp.id='dept'+val[i].id;
        itemTemp.name=val[i].name;
        if(val[i].subDept.length||val[i].users.length){
          itemTemp.disabled=false;
        }else{
          itemTemp.disabled=true;
        }
        itemTemp.subDept=[];
        if(val[i].subDept.length){
          itemTemp.subDept=this.editUserDeptTree(val[i].subDept,false);
        }
        if(val[i].users.length){
          itemTemp.subDept=itemTemp.subDept.concat(this.editUserDeptTree(val[i].users,true));
        }
        arrayTemp.push(itemTemp);
      }
      return arrayTemp;
    }
  },
  //返回当前公司的所有子部门，去除所有子部门,以树形形式
  editChildrenCompany:function (val) {
    let arrayTemp=[];
    for(let i=0;i<val.length;i++){
      if(val[i].type){//如果为子公司则加入
        let itemTemp=new Object;
        itemTemp.value=val[i].id;
        itemTemp.label=val[i].name;
        if(val[i].subDept.length){
          itemTemp.children=this.editChildrenCompany(val[i].subDept);
          if(itemTemp.children.length){arrayTemp.push(itemTemp);}else{arrayTemp.push({value:val[i].id,label:val[i].name});}
        }else{
          arrayTemp.push({value:val[i].id,label:val[i].name});
        }
      }
    }
    return arrayTemp;
  },
  //base64解码
  decode: function(str) {
    return decodeURIComponent(atob(str).split('').map(function(c) {
      return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
    }).join(''));
  }
}

