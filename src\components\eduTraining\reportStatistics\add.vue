<template>
  <el-container class="container">
    <el-main>
      <!--发布-->
      <el-form ref="info" label-width="100px" :model="info">
        <el-row type="flex">
          <el-col :span="24">
            <el-form-item label="任务名称">
              <el-input v-model="info.title"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex">
          <el-col :span="24">
            <el-form-item label="描述">
              <el-input type="textarea" v-model="info.remark"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="EXCEL上传">
              <fileUpload
                @fileData="fileDataFn"
                ref="uploadComponent"
                :data="upload"
              ></fileUpload>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row class="lineHeight" v-if="info.filePath != ''">
          <el-col :span="24">
            <el-form-item label="文件名">
              <el-button type="text" @click="showPdfDialog">{{
                info.fileName
              }}</el-button>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex">
          <el-col :span="24">
            <chooseStaff
              ref="chooseStaff"
              @selectedRows="selectedRows"
            ></chooseStaff>
          </el-col>
        </el-row>
        <el-row type="flex" class="row" justify="center">
          <el-button
            @click="saveBtnClickHandle({ status: 0 })"
            size="small"
            :span="2"
            type="success"
            >保存</el-button
          >
          <el-button
            @click="saveBtnClickHandle({ status: 1 })"
            size="small"
            :span="2"
            type="success"
            >提交</el-button
          >
          <el-button size="small" :span="2" @click="$router.back()"
            >返回</el-button
          >
        </el-row>
      </el-form>
    </el-main>
  </el-container>
</template>

<script>
import { VueEditor } from "vue2-editor";
import chooseStaff from "@/components/common/chooseStaff";
import fileUpload from "@/components/common/fileUploadXLSX";
export default {
  components: {
    chooseStaff,
    VueEditor,
    fileUpload,
  },
  data() {
    return {
      // info表
      info: {
        // ID
        id: "",
        title: "",
        remark: "",
        fileId: "",
        status: "",
        // 用户id
        userIds: [],
        userList: [],
      },
      // 辅助字段
      assist: {
        time: "", // 时间期限
        // 用户
        userList: [],
        // excel路劲
        excelPath: "",
      },

      upload: {
        params: {
          contentId: this.$tool.getStorage("LOGIN_USER").userId,
          contentType: 26,
        },
        btns: {
          // 上传按钮
          upload: {
            isShow: true,
          },
          // 下载按钮
          download: {
            isShow: false,
          },
          // 删除按钮
          delete: {
            isShow: true,
          },
        },
        uploadUrl: "",
        uploadCookies: true,
        fileData: [],
      },

      viewOrEdit: false,
      // 权限按钮
      powerBtns: [],
      // pdf查看对话框
      dialog: {
        //查看安全风险告知书
        isShow: false,
        pdfUrl: "http://www.safe360.vip/safeFile.pdf",
        title: "查看安全风险告知书",
      },
    };
  },
  watch: {
    $route(to, from) {
      // 如果来至列表页
      if (
        from.name === "ReportStatisticsIndex" &&
        this.$route.name === "ReportStatisticsAdd"
      ) {
        this.init();
      }
    },
  },
  mounted() {
    // this.init();
  },
  methods: {
    // 文章查看
    showPdfDialog() {
      window.open(this.info.filePath);
    },
    // 初始化
    init() {
      if (this.$route.params.status == "edit") {
        this.searchBtnClickHandle();
        //刘杰1130 终
      } else {
        console.log("新增");
        this.clear();
      }
    },
    timeChange(e) {
      if (e) {
        this.info.startTime = e[0];
        this.info.endTime = e[1];
      }
    },

    articleChangeFn(item) {
      this.info.listNewsFile = [];
      for (var i = 0; i < item.length; i++) {
        for (var j = 0; j < this.assist.articleList.length; j++) {
          if (this.assist.articleList[j].value == item[i]) {
            this.info.listNewsFile.push({
              fileId: item[i],
              fileName: this.assist.articleList[j].label,
            });
            break;
          }
        }
      }
    },
    // 人员列表选择组件处理函数
    selectedRows(rows) {
      // 参与人员列表----用户userId列表
      let userIds = rows.map(function (it) {
        return it.userId;
      });
      this.info.userIds = userIds;
    },

    fileDataFn(data) {
      console.log("父组件获取子组件数据：", data);
      this.info.listNewsFile = data.map(function (e) {
        return {
          id: "",
          eduNewsId: "",
          fileId: e.fileId,
          fileName: e.fileName,
        };
      });
      console.log(888, this.info.listNewsFile);
    },

    judgeUserRole() {
      // 获取权限按钮
      let url = "/edu-training-menu/daily-training-index";
      this.powerBtns = this.$tool.getPowerBtns("eduTrainingMenu", url);
    },
    // 清空数据
    clear() {
      this.info.id = "";
      this.info.title = "";
      this.info.remark = "";
      this.info.fileId = "";
      this.info.status = "";
      this.info.userIds = [];
      this.info.filePath = "";
      this.info.listNewsFile = [];
      this.upload.fileData = [];
      this.viewOrEdit = false;
      // 清空人员列表
      if (this.$refs["chooseStaff"].changeTableDataHandle) {
        this.$refs["chooseStaff"].changeTableDataHandle([]);
      }
      //        this.assist = this.$tool.clearObj({}, this.assist);
    },
    // 根据id搜索信息
    searchBtnClickHandle() {
      this.clear();
      let _this = this;
      let id = this.$route.params.id;
      this.viewOrEdit = true;
      let url = "sys/sysManageFile/findReportFormById";

      _this.$http.post(url, { id: id }).then(function (res) {
        if (res.data.success) {
          _this.info = res.data.data;
          // _this.assist.excelPath = res.data.filePath;
          let userList =
            res.data.data.userList.map(
              function (it) {
                return {
                  userId: it.userId,
                  companyId: it.companyId,
                  parentCompanyId: it.parentCompanyId,
                  companyName: it.companyName,
                  deptName: it.deptName,
                  username: it.username,
                };
              }.bind(this)
            ) || [];
          _this.info.userIds = res.data.data.userList.map(it => it.userId); // 新增此行
          // 新增：将原文件信息同步到 listNewsFile
          if (_this.info.fileId) {
            _this.info.listNewsFile = [{
              fileId: _this.info.fileId,
              fileName: _this.info.fileName
            }];
          } else {
            _this.info.listNewsFile = [];
          }
          _this.$refs["chooseStaff"].changeTableDataHandle(userList);
        } else {
          _this.$message({
            showClose: true,
            message: "网络错误,请刷新页面",
            type: "error",
          });
        }
      });
    },
    // 未发布/已发布/进行中【开始按钮】--培训发布--保存按钮
    saveBtnClickHandle(options) {
      let _this = this;

      // 使用浅拷贝避免污染原始数据 (关键修改1)
      let params = { ...this.info };

      // 修正人员选择校验逻辑 (关键修改2)
      if (!params.userIds || params.userIds.length === 0) {
        _this.$message({
          showClose: true,
          message: "请选择参与人员！",
          type: "error",
        });
        return;
      }

      // 文件存在性校验（兼容新旧数据结构）
      if (
        (!params.listNewsFile || params.listNewsFile.length === 0) &&
        !params.fileId
      ) {
        _this.$message.error("请上传文件！");
        return;
      }

      // 统一获取文件ID（兼容新旧数据结构）
      let fileId = "";
      if (params.listNewsFile && params.listNewsFile.length > 0) {
        fileId = params.listNewsFile[params.listNewsFile.length - 1].fileId;
      } else {
        fileId = params.fileId;
      }

      // 确保参数包含最新文件ID (关键修改3)
      params.fileId = fileId;
      params.status = options.status;

      let url = "sys/sysManageFile/addOrUpdateReportForm";

      _this.$http.post(url, params).then((res) => {
        console.log(res);
        if (res.data.success) {
          _this.$message.success("操作成功");
          // 建议成功后跳转回列表页
          _this.$router.back();
        } else {
          _this.$message({
            showClose: true,
            message: res.data.message || "操作失败", // 增加错误消息兜底
            type: "error",
          });
        }
      }).catch(error => { // 增加网络错误处理
        _this.$message.error("网络请求异常");
      });
    },
  },
};
</script>

<style>
.container {
  background: #fff;
  padding: 0px 20px 20px;
}

.title {
  background: rgba(64, 158, 255, 0.1);
  color: #0f6fc6;
  border: 1px solid rgba(64, 158, 255, 0.2);
  border-radius: 5px;
}

.row {
  margin-top: 10px;
}
</style>
