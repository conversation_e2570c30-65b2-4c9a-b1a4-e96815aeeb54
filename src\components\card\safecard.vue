<template>
  <div>
    <div class="option-list">
      <div
        class="option-item"
        v-for="(item, index) in options"
        :key="index"
        :class="{
          active: activeOptionId === item.id,
          'no-margin-right': index === 2,
        }"
        @click="changeOption(item.id)"
      >
        <span>{{ item.label }}</span>
      </div>
    </div>
    <div class="event-list">
      <div class="card-box-img" v-if="this.eventList.length == 0">
        <img src="../image/nodata.png" style="width: 50%" />
      </div>
      <div style="height: 200px">
        <div class="event-item" v-for="(item, index) in eventList" :key="index">
          <el-tooltip :content="item.content" placement="top-start">
            <div class="event-content" @click="toPdf(item.filePath)">
              {{ truncatedString(item.content) }}
            </div>
          </el-tooltip>
          <div style="display: flex; justify-content: center">
            <div v-for="item in 30" :key="item">
              <div class="middle-top-border"></div>
            </div>
          </div>
        </div>
      </div>
      <div class="middle-top-view">
        <span @click="toTheMoon(activeOptionId)" style="cursor: pointer;font-size: 16px;"
          >查看更多</span
        >
        <div class="triangle-right1"></div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  created () {
    this.getData(this.activeOptionId)
  },
  data () {
    return {
      options: [
        // 安全管理文件、法律法规标准、事故案例、安全科普、经验分享
        { id: 0, label: "安全管理文件" },
        { id: 5, label: "法律法规标准" },
        { id: 6, label: "事故案例" },
        { id: 7, label: "安全科普" },
        { id: 8, label: "经验分享" },
      ],
      activeOptionId: 0,
      eventList: [],
    }
  },
  methods: {
    toPdf (filePath) {
      window.open(filePath)
    },
    //查看更多
    toTheMoon (id) {
      this.$router.push({
        name: "safeFileIndex",
        params: { taskFlag: true, componentName: "safeFileIndex", id },
      })
    },
    // 选项卡
    changeOption (id) {
      this.activeOptionId = id
      this.getData(id)
    },
    // 过长的字符串截断
    truncatedString (originalString) {
      if (originalString.length > 30) {
        return originalString.substring(0, 30) + "..."
      }
      return originalString
    },
    // 获取数据
    getData (id) {
      // console.log(id);
      let params = {}
      params["pageSize"] = 4
      if (id === 0) {
        // 将[1, 2, 3, 4]拆分
        params["types"] = [1, 2, 3, 4]
        // params.append("types", [1, 2, 3, 4])
      } else {
        params["types"] = [id]
      }
      this.$store.dispatch("sysManageFileFind", params).then((res) => {
        if (res.success) {
          this.eventList = res.data.list.map((item) => {
            // item["id"] = item.fileType === 1 ? "文件" : "链接";
            item["content"] = item.name
            // item["title"] = item.name;
            // item["time"] = item.createTime;
            return item
          })
        } else {
          this.$message({
            type: "error",
            message: res.message || "错误",
          })
        }
      })
    },
  },
};
</script>
<style scoped>
.option-list {
  margin-top: 10px;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding: 0 20px;
  font-family: "microsoft yahei";
  font-size: 16px;
  color: #666;
}
.option-item {
  box-sizing: border-box;
  height: 30px;
  background: #fff;
  border-radius: 4px;
  padding: 0 16px;
  margin-right: 10px;
  margin-bottom: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  background: #eeeeee;
  border: 1px solid transparent; /* Add a transparent border */
}

.option-item.active {
  background: #fff;
  border-color: #3096fb; /* Change the border color */
  color: #3096fb;
}
.no-margin-right {
  margin-right: 0 !important;
}
.middle-top-border {
  width: 11px;
  height: 1px;
  background-color: #cce3ff;
  margin-left: 1px;
  /* margin-top:14px; */
}
.event-list {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.card-box-img {
  display: flex;
  justify-content: center;
  align-items: center;
}
.event-item {
  display: flex;
  width: 350px;
  height: 50px;
  line-height: 49px;
  color: #666;
  flex-direction: column;
  overflow: hidden;
  /* margin-top:20px; */
}
.event-content {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  cursor: pointer;
}
.middle-top-view {
  color: #3396fb;
  text-align: center;
  width: 390px;
  line-height: 50px;
  font-size: 16px;
}
.triangle-right1 {
  margin-left: 5px;
  width: 0;
  height: 0;
  border-left: 7px solid transparent;
  border-right: 7px solid transparent;
  border-top: 9px solid #1689e9;
  display: inline-block;
}
</style>
