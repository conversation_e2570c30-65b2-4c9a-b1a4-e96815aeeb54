<template>
    <div id="emerHandleLookPlan">
      <el-row class="row">
        <el-col :span="24" class="title">新增应急预案--灾后处理</el-col>
      </el-row>
      <el-form :model="form" :rules="rules" ref="form" label-width="120px" class="demo-ruleForm">
        <el-col :span="24">
          <el-col :span="12">
            <el-form-item label="分类：" prop="topTypeId">
              <el-cascader
                style="width:100%;"
                v-model="planTypeListArr"
                @change="planTypeListChangeHandle"
                :options="planTypeList">
              </el-cascader>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="级别：" prop="eventLevel">
              <el-select
                style="width:100%;"
                v-model="form.eventLevel" placeholder="请选择" @change="eventLevelChangeHandle">
                <el-option
                  v-for="item in ruleForm.select"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-col>
        <!--参考预案-->
        <el-col :span="24">
          <el-col :span="12">
            <el-form-item label="参考预案：">
              <el-select
                style="width:100%;" v-model="referPlan" placeholder="请选择" @change="referPlanListChangeHandle">
                <el-option
                  v-for="item in referPlanList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-col>
        <!--预案名称-->
        <el-col :span="24">
          <el-col :span="12">
            <el-form-item label="预案名称：" prop="name">
              <el-input v-model="form.name"></el-input>
            </el-form-item>
          </el-col>
        </el-col>
        <!--执行清单-->
        <el-col :span="24">
          <el-col :span="24">
            <el-form-item label="执行清单：" prop="emgHandleLists">
              <el-col :span="24">
                <el-button type="primary" size="small" @click="dialog.knowledgePoint.isShow = true">添加知识点</el-button>
              </el-col>
              <el-col :span="24">
                <el-table
                  style="width:700px;"
                  :data="form.emgHandleLists">
                  <el-table-column
                    type="index"
                    width="50">
                  </el-table-column>
                  <el-table-column
                    prop="execContent"
                    label="执行操作"
                    show-overflow-tooltip
                    width="200">
                  </el-table-column>
                  <el-table-column
                    prop="helpInfo"
                    show-overflow-tooltip
                    width="200"
                    label="帮助">
                  </el-table-column>
                  <el-table-column
                    label="操作"
                    width="300"
                    align="center"
                    label-class-name="inner-header-style">
                    <template slot-scope="scope">
                      <el-button type="success" icon="el-icon-arrow-up"  size="mini" @click.native.prevent="toUp(scope.$index, form.emgHandleLists)"></el-button>
                      <el-button type="primary" icon="el-icon-arrow-down"   size="mini" @click.native.prevent="toDown(scope.$index, form.emgHandleLists)"></el-button>
                      <el-button type="danger"  size="mini" @click.native.prevent="emgHandleListsDelHandle(scope.$index, form.emgHandleLists)">删除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </el-col>
            </el-form-item>
          </el-col>
        </el-col>
        <!--注意事项-->
        <el-col :span="24">
          <el-col :span="24">
            <el-form-item label="注意事项：" prop="remark">
              <el-col :span="24">
                <el-button type="primary" size="small" @click="remarkDialog.knowledgePoint.isShow = true">添加知识点</el-button>
              </el-col>
              <el-col :span="24">
                <el-input
                  type="textarea"
                  :rows="6"
                  placeholder="请输入内容"
                  v-model="form.remark">
                </el-input>
              </el-col>
            </el-form-item>
          </el-col>
        </el-col>
        <!--急需物资-->
        <el-col :span="24">
          <el-col :span="24">
            <el-form-item label="应急物资：" prop="emgHandleGoodsList">
              <el-col :span="2">
                <el-button type="primary" size="small" @click="isShowGoodsTypeList = true">添加</el-button>
              </el-col>
              <el-col :span="20" v-if="isShowGoodsTypeList">
                <el-select
                  multiple
                  filterable
                  remote
                  reserve-keyword
                  clearable
                  placeholder="请输入物资名称后选择"
                  :remote-method="goodsTypeListHandle"
                  v-model="goodsTypeListArr"
                  style="width: 400px;">
                  <el-option
                    v-for="item in goodsTypeList"
                    :key="item.value"
                    :label="item.label"
                    :value="item">
                  </el-option>
                </el-select>
                <el-button type="success" plain size="small" @click="goodsTypeListChangeHandle">确定</el-button>
                <el-button type="info" plain size="small" @click="isShowGoodsTypeList = false">取消</el-button>
              </el-col>
              <el-col :span="24">
                <el-table
                  :data="form.emgHandleGoodsList"
                  style="width: 100%">
                  <el-table-column
                    type="index"
                    width="50">
                  </el-table-column>
                  <el-table-column
                    prop="name"
                    label="物资名称"
                    width="180">
                  </el-table-column>
                  <el-table-column
                    label="操作"
                    align="center"
                    label-class-name="inner-header-style">
                    <template slot-scope="scope">
                      <el-button type="danger" size="mini" @click="emgHandleListsDelHandle(scope.$index, form.emgHandleGoodsList)">删除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </el-col>
            </el-form-item>
          </el-col>
        </el-col>

        <el-col :offset="8" :span="12">
          <el-button type="primary" @click="addPlanHandle">确定</el-button>
          <el-button type="primary" @click="returnBtnClickHandle">返回</el-button>
        </el-col>
      </el-form>

      <!-- 添加执行清单对话框 -->
      <knowledge-point-dialog
        @currentChangeHandle="currentChangeHandle"
        @okBtnClickHandle="execOkBtnClickHandle"
        :data="dialog.knowledgePoint">
        <template slot="firstSlot">
          <el-row class="row">
            <el-col :span="3">
              <el-button size="small" type="primary" @click=" dialog.execContent = dialog.selectSingeRow[0].content " icon="el-icon-plus">执行知识点</el-button>
            </el-col>
            <el-col :offset="3" :span="18">
              <el-input v-model="dialog.execContent"></el-input>
            </el-col>
          </el-row>
          <el-row class="row">
            <el-col :span="3">
              <el-button size="small"  type="danger" @click=" dialog.helpInfo = dialog.selectSingeRow[0].content " icon="el-icon-plus">帮助知识点</el-button>
            </el-col>
            <el-col :offset="3" :span="18">
              <el-input v-model="dialog.helpInfo"></el-input>
            </el-col>
          </el-row>
        </template>
      </knowledge-point-dialog>
      <!--对话框结束-->
      <!-- 添加注意事项对话框 -->
      <knowledge-point-dialog
        @okBtnClickHandle="remarkOkBtnClickHandle"
        :data="remarkDialog.knowledgePoint">
      </knowledge-point-dialog>
      <!--对话框结束-->



    </div>
</template>

<script>
    import knowledgePointDialog from '@/components/common/knowledgePointDialog'
    export default {
      components : {
        'knowledge-point-dialog' : knowledgePointDialog
      },
      data(){
        return {

          // 提交的字段
          form : {
            // 添加或者编辑
            //id : 0,
            // 分类Id
            topTypeId : 0,
            typeId : 0,
            // 级别
            eventLevel : '',
            // 名称
            name : '',
            // 注意事项
            remark : '',
            // 执行清单
            emgHandleLists : [
//              { execContent : 11, helpInfo : 11 },
//              { execContent : 22, helpInfo : 22 },
//              { execContent : 33, helpInfo : 33 },
//              { execContent : 44, helpInfo : 44 },
//              { execContent : 55, helpInfo : 55 },
            ],
            // 急需物资
            emgHandleGoodsList : [],
          },

          // 辅助字段
          // 选中类别数组[1,5]
          planTypeListArr : [],
          // 参考预案
          referPlan : '',
          // 选中的 物资类型列表 数组
          goodsTypeListArr : [],
          // 是否显示添加 物资类型列表
          isShowGoodsTypeList : false,
          // 对话框---执行清单
          dialog : {
            // 知识点
            knowledgePoint : {
              isMultiple : false,       // 是否多选
              isShow : false,
            },
            selectSingeRow : [],      // 选中的数据
            //执行操作数据
            execContent : '',
            // 帮助数据
            helpInfo : '',
          },


          // 对话框---注意事项
          remarkDialog : {
            // 知识点
            knowledgePoint : {
              isMultiple : true,       // 是否多选
              isShow : false,
            },
            selectSingeRow : [],      // 选中的数据
          },



          // 表单字段
          ruleForm: {
            select: [
              { value : 1, label : '1级' },
              { value : 2, label : '2级' },
              { value : 3, label : '3级' },
              { value : 4, label : '4级' },
            ],
          },
          value : '',
          // 表单规则
          rules: {
            topTypeId: [
              { required: true, message: '请选择分类', trigger: 'change' },
            ],
            eventLevel: [
              { required: true, message: '请选择级别', trigger: 'change' },

            ],
            name: [
              { required: true, message: '请输入预案名称', trigger: 'change' },
            ],
            emgHandleLists: [
              { required: true, message: '请添加执行清单知识点', trigger: 'change' },
            ],
            remark: [
              { required: true, message: '请添加注意事项知识点', trigger: 'change' },
            ],
            emgHandleGoodsList: [
              { required: true, message: '请添加急需物资知识点', trigger: 'change' },
            ],

          },


        }
      },
      watch:{
        $route(to, from){
          if(this.$route.name==='emerHandleAddPlan'){
            this.init();
          }
        },
      },
      computed:{
        // 分类列表
        planTypeList : function(){
          return this.$store.state.emerHandleModule.planTypeList
        },
        // 参考列表
        referPlanList : function(){
          return this.$store.state.emerHandleModule.referPlanList
        },
        // 物资列表
        goodsTypeList : function(){
          return this.$store.state.emerHandleModule.goodsTypeList
        },
      },
      mounted(){
        this.init();
      },
      methods:{

        init(){

          // 获取分类列表
          this.$store.dispatch("planTypeListAction");

          // 获取修改的数据
          if(this.$route.params.planId){
            this.getInfoHandle();
            return;
          }

          // 获取并且设置上个页面传递的类别、等级信息
          let params = this.$route.params;
          this.form.topTypeId = params.firstClass && params.firstClass.value || '';
          this.form.typeId = params.secondClass && params.secondClass.value | '';
          this.form.eventLevel = params.level || '';
          if(this.form.topTypeId) this.planTypeListArr.push(this.form.topTypeId)
          if(this.form.typeId) this.planTypeListArr.push(this.form.typeId)
          // 改变备案名称
          this.modNameHandle();


        },
        // 类别改变处理函数
        planTypeListChangeHandle(value){
          this.form.topTypeId = value[0];
          this.form.typeId = value[1];
          // 改变备案名称
          this.modNameHandle();
          // 获取参考预案列表
          this.getReferPlanListApi();
        },
        // 级别改变处理函数
        eventLevelChangeHandle(){
          // 改变备案名称
          this.modNameHandle();
          // 获取参考预案接口
          this.getReferPlanListApi();
        },
        // 获取参考预案接口
        getReferPlanListApi(){
          let params = {
            pageCurrent : 1,
            pageSize : 10,
          };
          if(this.form.topTypeId) params['topTypeId'] = this.form.topTypeId;
          if(this.form.typeId) params['typeId'] = this.form.typeId;
          if(this.form.eventLevel) params['eventLevel'] = this.form.eventLevel;

          this.$store.dispatch('referPlanListAction', params)
        },
        // 参考预案---修改---函数
        referPlanListChangeHandle(){
          let that = this;
          let list = this.$store.state.emerHandleModule.referPlanList;
          list = list.filter(function(it){
            return it.label == that.referPlan;
          })[0];
          this.form.emgHandleLists = list.emgHandleLists;
          this.form.remark = list.remark;
          this.form.emgHandleGoodsList = list.emgHandleGoodsList;
        },
        // 修改备案名称--通过类型和等级
        modNameHandle(){
          let typeList = this.$store.state.emerHandleModule.planTypeList;
          if(typeList.length === 0) return;
          if(this.form.topTypeId === '') return;
          let firstTypeLabel = '';
          let secondTypeLabel = '';
          let typeArr = [this.form.topTypeId, this.form.typeId];
          let firstIndex = typeList.findIndex(function(ele, index){
            return ele.value == typeArr[0];
          })
          firstTypeLabel = typeList[firstIndex].label;
          secondTypeLabel = (typeArr[1] && typeList[firstIndex].children.filter(function(ele, index){
              return ele.value == typeArr[1]
            })[0].label) || '';
          this.form.name = secondTypeLabel ? secondTypeLabel + this.form.eventLevel + '预案' : firstTypeLabel + this.form.eventLevel + '预案';

        },
        // 返回按钮点击处理事件函数
        returnBtnClickHandle(){
          this.$router.go(-1);
        },
        // 知识点---事件----单选事件
        currentChangeHandle(val){
          this.dialog.selectSingeRow = val;
        },
        // 执行清单--添加数据
        execOkBtnClickHandle(){
          this.form.emgHandleLists.push({
            execContent : this.dialog.execContent,
            helpInfo : this.dialog.helpInfo,
          });
          this.dialog.execContent = '';
          this.dialog.helpInfo = '';
        },
        // 执行清单---向上移动
        toUp(index, rows){
          if(index === 0) {
            this.$message({
              type : 'warning',
              message : '当前位置为第一位'
            })
            return;
          }
          // 上一个 和 当前个
          let prev = rows[index - 1];
          let current = rows[index];
          // 删除当前
          rows.splice(index - 1, 2, current, prev);
        },
        // 执行清单---向下移动
        toDown(index, rows){
          if(index === rows.length - 1) {
            this.$message({
              type : 'warning',
              message : '当前位置为最后一位'
            })
            return;
          }
          // 上一个 和 当前个
          let next = rows[index + 1];
          let current = rows[index];
          // 删除当前
          rows.splice(index, 2, next, current);
        },
        // 执行清单--删除按钮--处理函数
        emgHandleListsDelHandle(index, rows) {
          rows.splice(index, 1);
        },
        // 注意事项---确定按钮
        remarkOkBtnClickHandle(val){
          let that = this;
          val.forEach(function(it){
            console.log(it.content,9999)
            that.form.remark += `>>>.${it.content}\n`;
          })
        },
        // 物资列表
        goodsTypeListHandle(val){
          let params = {
            pageCurrent : 1,
            pageSize : 20,
            name : val
          }
          this.$store.dispatch("goodsTypeListAction", params);
        },
        // 物资列表选择，并且映射到table中
        goodsTypeListChangeHandle(){
          let list = [];
          let goodsArr = this.form.emgHandleGoodsList.map(function(it){
            return it.name;
          })
          this.goodsTypeListArr.forEach(function(it){
            // 如果没有添加进去
            if(goodsArr.indexOf(it.label) === -1){
              this.form.emgHandleGoodsList.push({
                name : it.label
              })
            }
          }.bind(this));
          this.isShowGoodsTypeList = false;
        },
        // 添加计划---确定按钮
        addPlanHandle(){
          let that = this;
          // 添加还是修改
          if(this.$route.params.planId){
            this.form['id'] = this.$route.params.planId;
            this.$store.dispatch('postDisasterTreatmentUpdateAction', this.form).then(function(res){
              if(res.success){
                this.$message({
                  type : 'success',
                  message : '修改成功'
                })
                delete this.form['id'];
                this.$router.push({ name : 'emerPlan' })
              } else {
                this.$message({
                  type : 'error',
                  message : res.message || '数据有误或者不能为空！！'
                })
                delete this.form['id'];
              }
            }.bind(this));
          } else {
            this.$store.dispatch('postDisasterTreatmentAddAction', this.form).then(function(res){
              if(res.success){
                that.$message({
                  type : 'success',
                  message : '添加成功'
                })
                that.$router.push({ name : 'emerPlan' })
              } else {
                that.$message({
                  type : 'error',
                  message : res.message || '数据有误或者不能为空！！'
                })
              }
            });
          }
        },


        // ########修改页面#########
        // 根据 id 获取数据
        getInfoHandle(){
          let that = this;
          let type = [];
          let params = {
            id : this.$route.params.planId
          }
          this.$store.dispatch('postDisasterTreatmentOneAction', params).then(function(res){
            // 类型
            this.form.topTypeId = this.planTypeListArr[0] = res.topTypeId;
            this.form.typeId = this.planTypeListArr[1] = res.typeId;
            // 等级
            this.form.eventLevel = res.eventLevel;
            // 名称
            this.form.name = res.name;
            // 执行清单
            this.form.emgHandleLists = res.emgHandleLists;
            // 执行清单
            this.form.remark = res.remark;
            // 急需物质
            this.form.emgHandleGoodsList = res.emgHandleGoodsList;
          }.bind(this));




        },
      }
    }
</script>

<style>
  #emerHandleLookPlan{
    background : #fff;
    padding:10px 50px;
    height: 1200px;
  }
  .title{
    text-align: center;
    margin-bottom: 20px;
    margin-top:30px;
    font-size: large;
    letter-spacing: 2px;
    color:#3576AA;
    border-left:5px solid #049ff1;
    border-radius: 5px;
    background-color: rgb(236,248,255);
    height: 50px;
    line-height: 50px;
  }
  .row{
    margin-top:10px;
  }
</style>
