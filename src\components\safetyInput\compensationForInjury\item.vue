<template>
  <div id="trainingPlanIndex">
    <div class="background-style">
      <!--搜索区-->
      <div class="search-bar">
        <div style="padding:10px 10px 0 10px;float: left">
          <el-button
            @click="addBtnClickHandle"
            type="success" icon="el-icon-plus" style="margin-left: 20px">事故索赔记录</el-button>
          <el-button
            @click="$router.back();"
            style="margin-left: 20px">返回</el-button>
        </div>
      </div>
      <!--表格区-->
      <div style="width: 100%;">
        <div style="padding: 20px 10px 20px 10px">

          <el-row style="margin-bottom:10px;">
            {{ ($tool.formatDateTime(reg.year) || '').substring(0,4) }}年度事故获赔表（单位：元）
          </el-row>
          <el-table
            border
            :data="tableData.list"
            style="width: 100%">
            <el-table-column
              type="index"
              label="编号"
              width="100"
              align="center"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="name"
              label="名称"
              min-width="150"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="injuryDescribe"
              label="受伤情况"
              min-width="150"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="disabilityClasses"
              label="伤残等级"
              min-width="150"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="payTime"
              :formatter="formatDateTime"
              label="获赔时间"
              width="150"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="paySum"
              label="获赔金额（元）"
              width="150"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              fixed="right" label="操作"
              label-class-name="header-style"
              align="center" width="200">
              <template slot-scope="scope">
                <template>
                  <el-button size="mini" type="primary" @click="itemUpdateClick(scope.row)">修改</el-button>
                  <el-button size="mini" type="danger" @click="itemDeleteClick(scope.row)">删除</el-button>
                </template>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div style="margin-top: 10px">
          <el-pagination
            background
            layout="prev, pager, next"
            :current-page="tableData.pageNum"
            :page-size="form.pageSize"
            :total="tableData.total"
            @current-change ="disasterPageChangeHandle">
          </el-pagination>
        </div>
      </div>
    </div>
    <!--对话框-->
    <el-dialog
      :visible.sync="dialog.isShow"
      title="明细"
      width="60%"
      :before-close="handleClose">
      <el-form label-width="100px" ref="form" :rules="dialog.rules" :model="dialog.form">
        <el-row  class="row">
          <el-col :span="12">
            <el-form-item label="姓名：" prop="name">
              <el-input v-model="dialog.form.name"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="受伤情况：" prop="injuryDescribe">
              <el-input v-model="dialog.form.injuryDescribe"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row  class="row">
          <el-col :span="12">
            <el-form-item label="伤残等级：" prop="disabilityClasses">
              <el-input v-model="dialog.form.disabilityClasses"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="获赔日期：" prop="payTime">
              <el-date-picker
                style="width:100%;"
                v-model="dialog.form.payTime"
                type="date"
                placeholder="选择日期">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row  class="row">
          <el-col :span="12">
            <el-form-item label="获赔金额：" prop="paySum">
              <el-input-number v-model="dialog.form.paySum"></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row class="row" v-if="dialog.form.id">
          <el-col :span="24">
            <fileUpload :data="dialog.assist.upload"></fileUpload>
          </el-col>
        </el-row>
        <!--<el-row  class="row">-->
          <!--<el-col :span="2">-->
            <!--<el-form-item>-->

            <!--</el-form-item>-->
          <!--</el-col>-->
        <!--</el-row>-->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          type="danger"  size="mini"
          @click="dialogOkBtnClickHandle">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
  import fileUpload from '@/components/common/fileUpload';
  export default {
    components : {
      fileUpload
    },
    data() {
      return {
        form : {
          // 父级ID
          id : '',
          // 当前页
          pageCurrent : 1,
          // 页数大小
          pageSize : 10,
        },
        tableData : [],
        // 对话框
        dialog : {
          // 是否显示
          isShow : false,
          form : {
            // 父级ID
            injuryClaimId : '',
            // 自己的id
            id : '',
            // 姓名
            name : '',
            // 受伤情况
            injuryDescribe : '',
            // 伤残等级
            disabilityClasses : '',
            // 获赔时间
            payTime : '',
            // 获赔金额
            paySum : '',
          },
          rules: {
            name: [
              {required: true, message: '请输入内容', trigger: 'blur'},
            ],
            injuryDescribe: [
              {required: true, message: '请输入内容', trigger: 'blur'},
            ],
            disabilityClasses: [
              {required: true, message: '请输入内容', trigger: 'blur'},
            ],
            payTime: [
              { type: 'date', required: true, message: '请选择日期', trigger: 'blur' }
            ],
            paySum: [
              {required: true, message: '请输入内容', trigger: 'blur'},
            ],
          },
          assist : {
            // 上传文件
            upload : {
              // 上传参数
              params : {
                contentId: '',
                contentType: 10
              },
            },
          },
          // 父级的信息
          reg : '',
        },
        // 父级传递的参数
        reg : '',
      }
    },
    mounted(){
      this.init();
    },
    watch:{
      $route(to,from){
        if(from.name === 'compensationForInjuryIndex') {
          this.init();
        }
      }
    },
    methods:{
      // 初始化
      init(){
        this.reg = this.$route.query.reg;
        this.form.id = this.reg.id;
//        this.dialog.form.accountRegId = this.reg.id;

        // 搜索
        this.searchBtnClickHandle();
      },
      // 格式化时间
      formatDateTime(row, column, cellValue){
        let pro = column.property;
        let num = 10;
        // 年份4位 1999
        if(pro === 'year') num = 4;
        let str = this.$tool.formatDateTime(row[pro] || 0);
        return str ? str.substring(0, num) : str;
      },
      // 分页
      disasterPageChangeHandle(page){
        this.form.pageCurrent = page;
        this.searchBtnClickHandle();
      },
      // 搜索按钮
      searchBtnClickHandle(){
        this.$store.dispatch('costInjuryClaimRecordFind', this.form).then(function(res){
          if(res.success){
            this.tableData = res.data;
          } else {
            this.$message({
              type : 'error',
              message : res.message || '错误'
            })
          }
        }.bind(this));
      },
      // 添加按钮
      addBtnClickHandle(){
        this.dialog.form = this.$tool.clearObj({}, this.dialog.form);
        this.dialog.form.injuryClaimId = this.reg.id;
        this.dialog.isShow = true;
      },
      // 查看按钮
      itemViewClick(row){
        let name = 'costBudgetAdd';
        let params = {
          id : row.id,
          status : 'view'
        }
        this.$router.push({ name : name, params : params})
      },
      // 修改按钮
      itemUpdateClick(row){

        this.$tool.cloneObj(this.dialog.form, row);
        this.dialog.assist.upload.params.contentId = row.id;
        this.dialog.form.payTime = new Date(this.dialog.form.payTime);

        this.dialog.isShow = true;

      },
      // 删除按钮
      itemDeleteClick(row){
        this.$confirm('此操作将永久删除, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
          .then(function(){
            this.$store.dispatch('costInjuryClaimRecordDelete', {
              id : row.id
            }).then(function(res){
              if(res.success){
                this.$message({
                  type : 'success',
                  message : '删除成功'
                })
                this.searchBtnClickHandle();
              } else {
                this.$message({
                  type : 'error',
                  message : res.message || '删除失败！！'
                })
              }
            }.bind(this))
          }.bind(this))
      },
      // 对话框--选择年份
      yearChange(val){
        // 根据年份查找预算费用
        this.$store.dispatch('costBudgetPlanFind', { year : val}).then(function(res){
          if(res.success){
            let list = res.data.list;
            this.dialog.assist.planList = list;
          } else {
            this.$message({
              type : 'error',
              message : res.message || '错误'
            })
          }
        }.bind(this));
      },
      // 对话框---确定按钮
      dialogOkBtnClickHandle(){
        this.$refs['form'].validate(function(valid) {
          if (valid) {
            let params = this.$tool.filterObj({}, this.dialog.form);
            if(this.dialog.form.id) {
              params['id'] = this.dialog.form.id;
            }
            this.$store.dispatch('costInjuryClaimRecordAddOrUpdate', params).then(function(res){
              if(res.success){
                this.$message({
                  type : 'success',
                  message : '操作成功'
                })
                this.searchBtnClickHandle();
                this.dialog.isShow = false;
              }  else {
                this.$message({
                  type : 'error',
                  message : res.message || '错误'
                })
              }
            }.bind(this));
          } else {
            return false;
          }
        }.bind(this));
      },
      // 对话框--关闭
      handleClose(){
        this.dialog.form = this.$tool.clearObj({}, this.dialog.form);
        this.dialog.isShow = false;
      }
    }
  }
</script>
<style>
</style>
