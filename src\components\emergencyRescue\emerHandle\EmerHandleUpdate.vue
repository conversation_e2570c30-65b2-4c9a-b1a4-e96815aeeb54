<template>
    <div id="emerHandleAdd">
      <el-row class="row">
        <el-col :span="24" class="title">事件详情</el-col>
      </el-row>
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="120px" class="demo-ruleForm">
        <el-col :span="24">
          <el-col :span="12">
            <el-form-item label="事件标题" prop="title">
              <el-input v-model="ruleForm.title"></el-input>
            </el-form-item>
          </el-col>
        </el-col>
        <el-col :span="24">
          <el-col :span="12">
            <el-form-item label="事件类型">
              <el-select v-model="ruleForm.type" placeholder="请选择事件类型">
                <el-option label="自然灾害" value="1"></el-option>
                <el-option label="结构设备" value="2"></el-option>
                <el-option label="公共安全" value="3"></el-option>
                <el-option label="其他" value="4"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发生时间" prop="title">
              <el-input v-model="ruleForm.title"></el-input>
            </el-form-item>
          </el-col>
        </el-col>
        <el-col :span="24">
          <el-col :span="12">
            <el-form-item label="事件地点" prop="title">
              <el-input v-model="ruleForm.title"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="桩号" prop="title">
              <el-col :span="6">
                <el-input v-model="ruleForm.title"></el-input>
              </el-col>
              <el-col :span="2">方向</el-col>
              <el-col :span="6">
                <el-input v-model="ruleForm.title"></el-input>
              </el-col>
              <el-col :span="1">K</el-col>
              <el-col :span="6">
                <el-input v-model="ruleForm.title"></el-input>
              </el-col>
              <el-col :span="1">M</el-col>
            </el-form-item>
          </el-col>
        </el-col>
        <el-col :span="24">
          <el-col :span="12">
            <el-form-item label="死亡人数">
              <el-col :span="20">
                <el-input v-model="ruleForm.title"></el-input>
              </el-col>
              <el-col :span="4">人</el-col>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="受伤人数" prop="title">
              <el-col :span="20">
                <el-input v-model="ruleForm.title"></el-input>
              </el-col>
              <el-col :span="4">人</el-col>
            </el-form-item>
          </el-col>
        </el-col>
        <el-col :span="24">
          <el-col :span="12">
            <el-form-item label="经济损失">
              <el-col :span="20">
                <el-input v-model="ruleForm.title"></el-input>
              </el-col>
              <el-col :span="4">元</el-col>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="道路中断类型">
              <el-select v-model="ruleForm.type" placeholder="请选择道路中断类型">
                <el-option label="全幅" value="1"></el-option>
                <el-option label="半幅" value="2"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-col>
        <el-col :span="24">
          <el-col :span="12">
            <el-form-item label="道路中断时间">
              <el-col :span="16">
                <el-input v-model="ruleForm.title"></el-input>
              </el-col>
              <el-col :span="4">
                <el-select v-model="ruleForm.type" placeholder="">
                  <el-option label="天" value="1"></el-option>
                  <el-option label="小时" value="2"></el-option>
                </el-select>
              </el-col>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="道路拥挤长度">
              <el-col :span="16">
                <el-input v-model="ruleForm.title"></el-input>
              </el-col>
              <el-col :span="4">
                <el-select v-model="ruleForm.type" placeholder="">
                  <el-option label="米" value="1"></el-option>
                  <el-option label="公里" value="2"></el-option>
                </el-select>
              </el-col>
            </el-form-item>
          </el-col>
        </el-col>
        <el-col :span="24">
          <el-col :span="12">
            <el-form-item label="危化品是否泄露">
              <el-col :span="16">
                <el-input v-model="ruleForm.title"></el-input>
              </el-col>
              <el-col :span="4">
                <el-select v-model="ruleForm.type" placeholder="">
                  <el-option label="无" value="1"></el-option>
                  <el-option label="是" value="2"></el-option>
                  <el-option label="否" value="2"></el-option>
                </el-select>
              </el-col>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="危化品泄露类型">
              <el-col :span="16">
                <el-input v-model="ruleForm.title"></el-input>
              </el-col>
              <el-col :span="4">
                <el-select v-model="ruleForm.type" placeholder="">
                  <el-option label="无" value="1"></el-option>
                  <el-option label="有毒气体" value="2"></el-option>
                  <el-option label="易燃易爆气体" value="2"></el-option>
                  <el-option label="易燃易爆固体" value="2"></el-option>
                  <el-option label="易燃易爆液体" value="2"></el-option>
                  <el-option label="腐蚀性液体" value="2"></el-option>
                  <el-option label="其他" value="2"></el-option>
                </el-select>
              </el-col>
            </el-form-item>
          </el-col>
        </el-col>
        <el-col :span="24">
          <el-col :span="12">
            <el-form-item label="危化品泄露量" prop="title">
              <el-input v-model="ruleForm.title"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="危化品名" prop="title">
              <el-input v-model="ruleForm.title"></el-input>
            </el-form-item>
          </el-col>
        </el-col>
        <el-col :span="24">
          <el-col :offset="6" :span="12">
            <el-form-item>
              <el-button type="success">变更</el-button>
              <el-button>返回</el-button>
            </el-form-item>
          </el-col>
        </el-col>
      </el-form>
    </div>
</template>

<script>
    export default {
      name : 'emerHandleAdd',
      data() {
        return {
          // 表单字段
          ruleForm: {
            title: '',    // 标题
            type : '',    // 类型
          },

          // 表单规则
          rules: {
            title: [
              { required: true, message: '请输入事件标题', trigger: 'change' },
            ],

          }
        }
      },
      methods: {
        // 下一步按钮点击事件处理函数
        nextBtnClickHandle(){
          this.$router.push({ name : 'emerHandleChoiceEvent' })
        }
      }
    }
</script>

<style>
  #emerHandleAdd{
    background : #fff;
    padding:10px 50px;
    height:700px;
  }
  .title{
    background : #f90;
    text-align:center;
    padding:10px;
    color : #fff;
    font-size:16px;
  }



</style>
