<template>
  <div id="holidayDuty">
    <div class="background-style">
      <!--日期范围和名称搜索-->
      <div style="width: 100%;padding: 10px 10px 0 10px;height: 40px">
        <div style="float: left;display: inline-block;">
          年份：
          <el-date-picker
            v-model="searchDate"
            type="year"
            style="width: 150px"
            @change="searchHolidayDutyTable"
            placeholder="请选择年份">
          </el-date-picker>
        </div>
        <div style="float:right;margin-right: 30px">
          <el-button v-if="powerBtns.includes('myDuty')" type="primary" @click="findMyDutyTable">我的值班</el-button>
          <el-button v-if="powerBtns.includes('vocationManage')" type="warning" @click="openHolidayDialog">节假日管理</el-button>
        </div>

      </div>
      <!--搜索结束-->
      <!--值班单列表-->
      <div style="padding: 10px">
        <el-table
          :data="holidayDutyTable"
          border
          highlight-current-row
          style="width: 100%">
          <el-table-column
            type="index"
            width="100"
            align="center"
            label-class-name="header-style">
          </el-table-column>
          <el-table-column
            prop="name"
            label="节假日类型"
            min-width="350"
            align="center"
            label-class-name="header-style">
          </el-table-column>
          <el-table-column
            prop="dayCount"
            label="值班天数"
            width="100"
            align="center"
            label-class-name="header-style">
          </el-table-column>
          <el-table-column
            prop="year"
            label="年份"
            width="100"
            align="center"
            label-class-name="header-style">
          </el-table-column>
          <el-table-column
            label="操作"
            width="170"
            align="center"
            label-class-name="header-style">
            <template slot-scope="scope">
              <el-button type="success" size="mini" @click="viewHolidayDuty(scope.row)">详情</el-button>
              <el-button v-if="powerBtns.includes('editHolidayDuty')" type="primary" size="mini" @click="editHolidayDuty(scope.row)">编辑</el-button>
            </template>
          </el-table-column>
        </el-table>

      </div>
      <!--值班单列表结束-->
      <!--节假日管理对话框-->
      <el-dialog title="节假日类型管理" :visible.sync="holidayTypeVisible" append-to-body>
        <el-row>
          年份：
          <el-date-picker
            v-model="holidayTypeDate"
            type="year"
            style="width: 150px"
            @change="holidayTypeFind"
            placeholder="请选择年份">
          </el-date-picker>
          <el-button type="primary" style="float: right;margin-right: 20px" size="small" @click="addFlag=true">添加节假日类型</el-button>
        </el-row>

        <el-dialog width="30%" title="添加节假日类型" :visible.sync="addFlag" append-to-body>
          <el-form :model="addHolidayForm" label-width="100px" :rules="addHolidayRule" ref="addHolidayForm">
            <el-form-item label="年份" prop="year">
              <el-date-picker
                v-model="addHolidayForm.year"
                type="year"
                style="width: 200px"
                placeholder="请选择年份">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="类型名称" prop="addHolidayTypeName">
              <el-input style="width: 200px;"  v-model="addHolidayForm.addHolidayTypeName" placeholder="请输入节假日类型名称"></el-input>
            </el-form-item>
            <el-form-item label="拼音缩写" prop="addHolidayTypeCode">
              <el-input style="width: 200px;"  v-model="addHolidayForm.addHolidayTypeCode" placeholder="请输入拼音缩写"></el-input>
            </el-form-item>
            <el-form-item label="值班天数" prop="addHolidayTypeDay">
              <el-input style="width: 200px;"  v-model="addHolidayForm.addHolidayTypeDay" placeholder="请输入天数"></el-input>
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button type="success" size="small" @click="holidayTypeAdd">确认</el-button>
            <el-button type="info" size="small" @click="addFlag=false">取消</el-button>
          </div>
        </el-dialog>

        <el-dialog width="30%" title="修改节假日类型" :visible.sync="editFlag" append-to-body>
          <el-form :model="editHolidayForm" label-width="100px" :rules="editHolidayRule" ref="editHolidayForm">
            <el-form-item label="类型名称" prop="holidayTypeName">
              <el-input style="width: 200px;"  v-model="editHolidayForm.holidayTypeName" placeholder="请输入节假日类型名称"></el-input>
            </el-form-item>
            <el-form-item label="值班天数" prop="holidayTypeDay">
              <el-input style="width: 200px;"  v-model="editHolidayForm.holidayTypeDay" placeholder="请输入天数"></el-input>
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button type="success" size="small" @click="holidayTypeUpdate">确认</el-button>
            <el-button type="info" size="small" @click="editFlag=false">取消</el-button>
          </div>
        </el-dialog>
        <el-row style="margin-top: 5px">
          <el-table :data="holidayTableData">
            <el-table-column type="index" label="序号" width="80"></el-table-column>
            <el-table-column label="节假日类型" width="150" prop="name"></el-table-column>
            <el-table-column label="天数" width="100" prop="dayCount"></el-table-column>
            <el-table-column label="缩写" prop="codeNum" width="100"></el-table-column>
            <el-table-column label="操作" width="170" fixed="right">
              <template slot-scope="scope">
                <el-button size="mini" type="primary" @click="editHolidayForm.id=scope.row.id;editHolidayForm.holidayTypeName=scope.row.name;editHolidayForm.holidayTypeDay=scope.row.dayCount;editFlag=true">修改</el-button>
                <el-button size="mini" type="danger" @click="holidayTypeDelete(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-row>
        <!--<el-row style="margin-top: 5px">-->
          <!--<el-pagination-->
            <!--background-->
            <!--layout="prev, pager, next"-->
            <!--:current-page="holidayTypeCurrentPage"-->
            <!--:total="holidayTypeTotal"-->
            <!--@current-change="holidayTypePageClick">-->
          <!--</el-pagination>-->
        <!--</el-row>-->
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="holidayTypeVisible = false">确 定</el-button>
        </div>
      </el-dialog>
      <!--节假日管理对话框-->

      <!--填写值班记录-->
      <el-dialog title="我的值班" width="70%" :visible.sync="myDutyVisible">
        <el-table
          :data="myDutyTable"
          border
          style="width: 100%">
          <el-table-column
            type="index"
            align="center"
            label-class-name="inner-header-style"
            width="50">
          </el-table-column>
          <el-table-column
            label="节假日"
            prop="dutyCodeName"
            label-class-name="inner-header-style"
            width="150">
          </el-table-column>
          <el-table-column
            label="值班日期"
            prop="dutyDate"
            :formatter="responseTimeFormat"
            label-class-name="inner-header-style"
            width="120">
          </el-table-column>
          <el-table-column
            label="值班记录"
            prop="dutyContent"
            label-class-name="inner-header-style"
            min-width="350">
          </el-table-column>
          <el-table-column
            label="值班地点"
            prop="dutyAddress"
            label-class-name="inner-header-style"
            min-width="350">
          </el-table-column>
          <el-table-column
            label="上报时间"
            prop="writeTime"
            :formatter="writeTimeFormat"
            label-class-name="inner-header-style"
            min-width="350">
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            label-class-name="inner-header-style"
            fixed="right"
            width="80">
            <template slot-scope="scope">
              <el-button type="text" size="small" v-show="!scope.row.hasReport" @click="editDutyForm.rowData=scope.row;editDutyForm.index=scope.$index;editDutyForm.dutyContent=scope.row.dutyContent;editDutyForm.dutyAddress=scope.row.dutyAddress || $tool.getStorage('LOGIN_USER').deptName;editDutyContentVisible=true;">填写记录</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-dialog title="填写值班记录" :visible.sync="editDutyContentVisible" append-to-body>
          <el-form :model="editDutyForm" label-width="100px" ref="editDutyForm">
            <el-form-item label="值班记录" prop="dutyContent">
              <el-input type="textarea" v-model="editDutyForm.dutyContent" placeholder="请输入值班记录"></el-input>
            </el-form-item>
            <el-form-item label="值班地点" prop="dutyContent">
              <el-input type="text" clearable v-model="editDutyForm.dutyAddress" placeholder="请输入值班地点"></el-input>
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button type="success" size="small" @click="editDutyContentClick">确认</el-button>
            <el-button type="info" size="small" @click="editDutyContentVisible=false">取消</el-button>
          </div>
        </el-dialog>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="myDutyVisible = false">确 定</el-button>
        </div>
      </el-dialog>
      <!--填写值班记录结束-->
    </div>
  </div>
</template>
<script>
  export default {
    name: 'holidayDuty',
    data() {
      return {
        //----------------搜索数据-----------------------
        searchDate:'',
        searchYearNumber:'',

        //---------------表格数据------------------------
        holidayDutyTable:[],
        currentPage:0,
        totalItem:0,

        //------------节假日类型管理---------------------
        holidayTypeVisible:false,
        holidayTableData:[],
//        holidayTypeCurrentPage:0,
//        holidayTypeTotal:0,
        addFlag:false,//添加节假日按钮显示
        addHolidayForm:{
          year:'',
          addHolidayTypeName:'',//添加节假日类型的名称
          addHolidayTypeCode:'',
          addHolidayTypeDay:'',
        },
        addHolidayRule:{
          addHolidayTypeName:[{ required: true, message: '请输入节假日类型名称', trigger: 'change'}],
          addHolidayTypeCode:[{ required: true, message: '请输入拼音缩写', trigger: 'change'}],
          addHolidayTypeDay:[{ required: true, message: '请输入天数', trigger: 'change'}],
        },
        editFlag:false,
        editHolidayForm:{
          id:'',
          holidayTypeName:'',
          holidayTypeDay:''
        },
        editHolidayRule:{
          holidayTypeName:[{ required: true, message: '请输入节假日类型名称', trigger: 'change'}],
          holidayTypeDay:[{ required: true, message: '请输入天数', trigger: 'change'}],
        },
        holidayTypeDate:'',//节假日类型年份

        //------------------------填写值班记录-------------------------
        myDutyVisible:false,
        myDutyTable:[],
        editDutyContentVisible:false,
        editDutyForm:{
          index:'',
          rowData:{},
          dutyContent:'',
          dutyAddress : '',
        },

        //权限按钮
        powerBtns : [],

      }
    },
    mounted:function () {
      //初始化权限按钮   add by pdn
      this.powerBtns = this.$tool.getPowerBtns2URL('emerMenu', '/emer-menu/holiday-duty',this.$route.path);
      this.searchDate=new Date();
      this.holidayTypeDate=new Date();
      this.addHolidayForm.year=new Date();
      this.searchHolidayDutyTable();
    },
    watch:{
      $route(to, from) {
        if (this.$route.name === 'holidayDuty'&&from.name === 'editHolidayDuty') {
          this.searchHolidayDutyTable();
        }
      }
    },
    methods:{
      responseTimeFormat:function (row) {
        return this.transferTime(row.dutyDate);
      },
      writeTimeFormat:function (row) {
        return this.transferTime(row.writeTime,null,true);
      },
      searchHolidayDutyTable:function () {
        this.holidayDutyTable=[];
        let params=new URLSearchParams;
        params.append("year",this.searchDate.getFullYear());
        this.$http.post('sysDutyType/find',params).then(function (res) {
          if(res.data.success){
            this.holidayDutyTable=res.data.data;
            let currentDate=new Date();
            let yearNumber=this.searchDate?this.searchDate.getFullYear():currentDate.getFullYear();

            this.holidayDutyTable.forEach(function (item) {
              item.year=yearNumber;
            });
          }
        }.bind(this)).catch(function (err) {
          console.log('sysDutyPublic/find',err);
          this.$message.error('请尝试重登录或检查网络连接');
        }.bind(this));
      },

      editHolidayDuty:function (row) {
        this.$router.push({name:'editHolidayDuty',params:{id:row.id,title:row.name,dutyDayCount:row.dayCount,year:row.year,dutyTypCode:row.codeNum,activeTabName:'edit'}});
      },
      viewHolidayDuty:function (row) {
        this.$router.push({name:'editHolidayDuty',params:{id:row.id,title:row.name,dutyDayCount:row.dayCount,year:row.year,dutyTypCode:row.codeNum,activeTabName:'view'}});
      },

      //-------------------------------------节假日类型管理---------------------------------------------
      openHolidayDialog:function(){
        this.holidayTypeFind();
        this.holidayTypeVisible=true
      },
      holidayTypeFind:function(){
        this.holidayTableData=[];
        let params=new URLSearchParams;
        params.append("year",this.holidayTypeDate.getFullYear());
        this.$http.post('sysDutyType/find',params).then(function (res) {
          if(res.data.success){
            this.holidayTableData=res.data.data;

            this.holidayTableData.forEach(function (item) {
              item.nameTemp=item.name;
              item.dayCountTemp=item.dayCount;
              item.viewFlag=true;
            });
          }
        }.bind(this)).catch(function (err) {
          console.log('sysDutyPublic/find',err);
          this.$message.error('请尝试重登录或检查网络连接');
        }.bind(this));
      },
      holidayTypeAdd:function () {
        this.$refs['addHolidayForm'].validate((valid) => {
          if (valid) {
            let params=new URLSearchParams;
            params.append("year",this.addHolidayForm.year.getFullYear());
            params.append("name",this.addHolidayForm.addHolidayTypeName);
            params.append("codeNum",this.addHolidayForm.addHolidayTypeCode);
            params.append("dayCount",this.addHolidayForm.addHolidayTypeDay);
            this.$http.post('sysDutyType/add',params).then(function (res) {
              if(res.data.success){
                this.$message.success('添加成功！');
                this.holidayTypeFind();
                this.searchHolidayDutyTable();
                this.addFlag=false;
                this.addHolidayForm.year=new Date();
              }
            }.bind(this)).catch(function (err) {
              console.log('sysDutyType/add',err);
              this.$message.error('添加失败');
            }.bind(this));
          } else {
            return false;
          }
        });
      },
      holidayTypeUpdate:function () {
        this.$refs['editHolidayForm'].validate((valid) => {
          if (valid) {
            let params=new URLSearchParams;
            params.append("id",this.editHolidayForm.id);
            params.append("name",this.editHolidayForm.holidayTypeName);
            params.append("dayCount",this.editHolidayForm.holidayTypeDay);
            this.$http.post('sysDutyType/update',params).then(function (res) {
              if(res.data.success){
                this.$message.success('修改成功！');
                this.holidayTypeFind();
                this.searchHolidayDutyTable();
                this.editFlag=false;
              }
            }.bind(this)).catch(function (err) {
              console.log('sysDutyType/update',err);
              this.$message.error('修改失败');
            }.bind(this));
          } else {
            return false;
          }
        });
      },
      holidayTypeDelete:function (row) {
        this.$confirm('此操作将删除节假日类型, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$http.post('sysDutyType/delete?id='+row.id).then(function (res) {
            if(res.data.success){
              this.$message.success('删除成功！');
              this.holidayTypeFind();
              this.searchHolidayDutyTable();
            }
          }.bind(this)).catch(function (err) {
            console.log('sysDutyPublic/delete',err);
            this.$message.error('删除失败');
          }.bind(this));
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });
        });
      },
    //打开填写值班记录对话框
      findMyDutyTable:function () {
        this.myDutyTable=[];
        let params = new URLSearchParams;
        let currentTime=new Date();
        params.append("userId",this.$tool.getStorage('LOGIN_USER').userId);
        params.append("year",currentTime.getFullYear());
        this.$http.post('sysDuty/find', params).then(function (res) {
          if (res.data.success) {
            this.myDutyTable=res.data.data.list;
//            console.log(this.$tool.getStorage('LOGIN_USER').deptName)
//            console.log(res.data.data)
          }
          this.myDutyVisible=true;
        }.bind(this)).catch(function (err) {
          console.log(err);
        }.bind(this));
      },

      //填写值班记录
      editDutyContentClick:function () {
        let params = new URLSearchParams;
        params.append("id",this.editDutyForm.rowData.id);
        params.append("dutyContent",this.editDutyForm.dutyContent);
        params.append("dutyAddress",this.editDutyForm.dutyAddress);
        this.$http.post('sysDuty/update', params).then(function (res) {
          if (res.data.success) {
            this.myDutyTable.splice(this.editDutyForm.index,1,{
              id:this.editDutyForm.rowData.id,
              dutyCodeName:this.editDutyForm.rowData.dutyCodeName,
              dutyDate:this.editDutyForm.rowData.dutyDate,
              dutyContent:this.editDutyForm.dutyContent,
              dutyAddress:this.editDutyForm.dutyAddress
            });
            this.editDutyContentVisible=false;
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
        }.bind(this));
      },

    }
  }
</script>
<style>
</style>
