<template>
  <div id="accountEmer">
    <div class="background-style">
      <!--应急列表区-->
      <div style="width: 100%;">
        <div style="float: left;margin: 10px">
          <el-cascader
            :options="cascaderOptions"
            v-model="emerClass"
            placeholder="分类选择">
          </el-cascader>
          <el-select v-model="emerLevel" placeholder="级别选择" style="width: 120px">
            <el-option
              v-for="item in levelOptions"
              :key="item.value"
              :label="item.label"
              :value="item.label">
            </el-option>
          </el-select>
          <!--<el-select v-model="emerStatus" placeholder="状态选择" style="width: 120px">-->
            <!--<el-option-->
              <!--v-for="item in statusOptions"-->
              <!--:key="item.value"-->
              <!--:label="item.label"-->
              <!--:value="item.value">-->
            <!--</el-option>-->
          <!--</el-select>-->
          <el-input placeholder="请输入预案名称" v-model="searchInput" style="width: 300px;margin-right: 10px">
          </el-input>
          <el-button icon="el-icon-search" @click="searchClick" type="primary">搜 索</el-button>
        </div>
        <div style="width: 100%;float: left">
          <div style="padding: 10px">
            <el-table
              v-loading="tableLoading"
              :data="tableData"
              border
              highlight-current-row
              @row-dblclick="itemViewClick"
              style="width: 100%">
              <el-table-column
                prop="num"
                label="编号"
                width="60"
                align="center"
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                label="状态"
                width="150"
                align="center"
                label-class-name="header-style">
                <template slot-scope="scope">
                  <el-tag :type="scope.row.tagType">{{scope.row.emerStatus}}</el-tag>
                </template>
              </el-table-column>
              <el-table-column
                prop="emerName"
                label="预警名称"
                width="400"
                show-overflow-tooltip
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="createDate"
                label="创建日期"
                width="150"
                align="center"
                show-overflow-tooltip
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="emerTopClass"
                label="一级分类"
                width="170"
                align="center"
                show-overflow-tooltip
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="emerClass"
                label="二级分类"
                width="170"
                align="center"
                show-overflow-tooltip
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="emerLevel"
                label="级别"
                min-width="80"
                align="center"
                show-overflow-tooltip
                label-class-name="header-style">
              </el-table-column>
              <el-table-column fixed="right" label="操作" label-class-name="header-style" width="160">
                <template slot-scope="scope">
                  <el-button size="mini" type="success" @click="itemViewClick(scope.row)">查看</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div>
            <el-pagination
              background
              layout="prev, pager, next"
              :current-page="currentPage"
              :total="totalItem"
              @current-change="currentPageClick">
            </el-pagination>
          </div>
        </div>
      </div>
      <!--应急列表区结束-->

    </div>
  </div>
</template>
<script>
  import {mapGetters} from 'vuex'

  export default {
    name: 'accountEmer',
    data() {
      return {
        //------------------------搜索数据----------------------------
        emerClass: ['全部分类'],
        cascaderOptions: [],
        emerLevel: '全部级别',
        levelOptions: [
          {value: '全部级别', label: '全部级别'},
          {value: '1级', label: '1级'},
          {value: '2级', label: '2级'},
          {value: '3级', label: '3级'},
          {value: '4级', label: '4级'},
          {value: '应急警报', label: '应急警报'}
        ],
        emerStatus: '全部状态',
        statusOptions: [
          {value: '全部状态', label: '全部状态'},

          {value: 0, label: '未提交'},
          {value: 1, label: '待审核'},
          {value: 2, label: '被退回'},

          {value: 3, label: '已签发'},
          {value: 4, label: '调整未提交'},
          {value: 5, label: '调整待审核'},
          {value: 6, label: '调整被退回'},
          {value: 7, label: '调整已签发'},

          {value: 8, label: '解除未提交'},
          {value: 9, label: '解除待审核'},
          {value: 10, label: '解除被退回'},

          {value: 11, label: '已解除'}
        ],
        searchInput: '',
        //------------------------表格数据---------------------------
        tableLoading: false,
        tableData: [],
        currentPage: 1,
        totalItem: 1,

        //----------------------------解除应急的对话框-----------------------
        relieveVisible: false,
        relieveForm: {
          id: '',
          planId: '',
          startId: '',
          relieveTime: '',
          relieveContent: '',
          relieveIssuer: ''
        },
        relieveContentDefault: ['公司各部门、主要控股企业：\n' + '      鉴于____(灾害名称)对宁波的影响逐渐减弱，经研究决定，于',
          '解除_____(类别)_____(级别)应急响应，同时要求各单位：\n' +
          '     1、抓紧做好恢复生产工作，确保正常生产开展。\n' +
          '     2、做好受损、受灾统计、汇总上报工作。\n' +
          '     3、对本次防御工作进行全面梳理，发现不足，总结经验，持续完善。'],
        personLoading: false,
        personOptions: [],
        relieveRules: {
          relieveTime: [{required: true, message: '请选择上报时间', trigger: 'change'}],
          relieveContent: [{required: true, message: '请填写上报内容', trigger: 'change'}],
          relieveIssuer: [{required: true, message: '请选择签发人', trigger: 'change'}],
        },
        //-----------------------状态对照表-----------------------------------
        statusTable: [
          {id: 0, name: '未提交', labelType: 'primary'},
          {id: 1, name: '待审核', labelType: 'warning'},
          {id: 2, name: '被退回', labelType: 'danger'},

          {id: 3, name: '已签发', labelType: 'success'},
          {id: 4, name: '调整未提交', labelType: 'primary'},
          {id: 5, name: '调整待审核', labelType: 'warning'},
          {id: 6, name: '调整被退回', labelType: 'danger'},
          {id: 7, name: '调整已签发', labelType: 'success'},

          {id: 8, name: '解除未提交', labelType: 'primary'},
          {id: 9, name: '解除被退回', labelType: 'success'},
          {id: 10, name: '解除待审核', labelType: 'warning'},

          {id: 11, name: '待总结', labelType: 'success'},
          {id: 12, name: '总结未提交', labelType: 'primary'},
          {id: 13, name: '总结已提交', labelType: 'success'},
          {id: 14, name: '已完结', labelType: 'info'},
        ],
      }
    },
    computed: mapGetters(['getCurrentUser']),
    mounted: function () {
      this.getPlanType();
      this.searchClick();
    },
    watch: {
      $route(to, from) {
        if ((from.name === 'emergencyForm' || from.name === 'viewEmergency' || from.name === 'emergencyProcess' || from.name === 'editSummary' || from.name === 'emerMenu') && this.$route.name === 'emerResponse') {
          this.searchClick();
        }
      },
    },
    methods: {
      //--------------------------初始化-------------------------------
      //获取分类
      getPlanType: function () {
        this.$http.get('emgType/getAll/'+this.$tool.getStorage('LOGIN_USER').companyId).then(function (res) {
          this.editPlanTypeArray(res.data.data);
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message({
            showClose: true,
            message: '网络错误，请尝试重登录',
            type: 'error'
          });
        }.bind(this));
      },
      //编写分类
      editPlanTypeArray: function (typeTree) {
        this.cascaderOptions = [];
        this.chooseTypeOptions = [];
        this.cascaderOptions.push({value: '全部分类', label: '全部分类'});

        for (let i = 0; i < typeTree.length; i++) {
          let tempArray = {value: i, label: typeTree[i].typeName, id: typeTree[i].id};
          if (typeTree[i].subTypes.length) {
            tempArray.children = [];
            for (let j = 0; j < typeTree[i].subTypes.length; j++) {
              tempArray.children.push({
                value: j,
                label: typeTree[i].subTypes[j].typeName,
                id: typeTree[i].subTypes[j].id
              });
            }
          }
          this.cascaderOptions.push(tempArray);
          this.chooseTypeOptions.push(tempArray);
        }
      },

      //--------------------------搜索响应事件--------------------------
      searchClick: function () {
        let params = new URLSearchParams;
        params.append("pageCurrent", 1);
        this.sendRequest(params);
      },
      //--------------------------表格响应事件--------------------------
      itemViewClick: function (row) {
        this.$router.push({name:'accountEmerList',params:{emgPlan:row}});

      },
      currentPageClick: function (val) {
        if (val) {
          this.currentPage = val;
          let params = new URLSearchParams;
          params.append("pageCurrent", Number(val));
          this.sendRequest(params);
        }
      },
      //--------------------------对话框事件----------------------------

      //解除应急


      //--------------------------交互事件------------------------------
      sendRequest: function (params) {
        if (this.emerClass[0] !== '全部分类') {
          if (this.emerClass[1] >= 0) {
            params.append("typeId", this.chooseTypeOptions[this.emerClass[0]].children[this.emerClass[1]].id);
          } else {
            params.append("typeId", this.chooseTypeOptions[this.emerClass[0]].id);
          }
        }
        if (this.emerLevel && this.emerLevel !== '全部级别') {
          params.append("respLevel", this.emerLevel);
        }
        params.append("status", 14);
        if (this.searchInput.trim()) {
          params.append("name", this.searchInput.trim());
        }
        params.append("history", 0);
        params.append("pageSize", 10);
//        params.append("sortField","createDate");
//        params.append("sortOrder","desc");
        this.tableLoading = true;
        this.$http.post('planPublic/find', params).then(function (res) {
          if (res.data.success) {
            if (res.data.data.list.length) {
              this.totalItem = res.data.data.total;
              this.editTable(res.data.data.list);
            } else {
              this.tableData.splice(0);
              this.tableLoading = false;
            }
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message({
            showClose: true,
            message: '网络错误，请尝试重登录',
            type: 'error'
          });
        }.bind(this));
      },
      editTable: function (list) {
        this.tableData = [];
        for (let i = 0; i < list.length; i++) {
          this.tableData.push({
            id: list[i].id,
            num: (this.currentPage - 1) * 10 + i + 1,
            status: list[i].status,
            emerStatus: this.statusTable[list[i].status].name,
            tagType: this.statusTable[list[i].status].labelType,
            emerName: list[i].name,
            createDate: this.transferTime(list[i].createDate),
            emerTopClass: list[i].topTypeName,
            emerClass: list[i].typeName,
            emerLevel: list[i].respLevel,
            planId: list[i].planId,
            startId: list[i].startPlanId,
            deptName: list[i].startPlanId,
            deptId: list[i].deptId,
            companyId: list[i].companyId,
            actionFlag: this.actionFlagJudge(list[i].status)
          });
        }
        this.tableLoading = false;

      },
      actionFlagJudge: function (status) {
        if (status <= 2) {
          return 0;
        } else if (status <= 10) {
          return 1;
        } else if (status <= 12) {
          return 2;
        } else {
          return 3;
        }
      },

    }
  }
</script>
<style>
</style>
