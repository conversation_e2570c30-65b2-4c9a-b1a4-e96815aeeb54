import Vue from 'vue'
import Router from 'vue-router'
import tool from '../components/common/tool'
import Login from '../components/Login.vue'
import Menu from '../components/Menu.vue'
import ManagerView from '../components/ManagerView.vue'
import MapView from '../components/MapView.vue'

//应急救援
import EmerMenu from '../components/emergencyRescue/EmerMenu.vue'
// import EmerGroup from '../components/emergencyRescue/emerGroup/EmerGroup.vue'
// import EmerMaterial from '../components/emergencyRescue/emerMaterial/EmerMaterial.vue'
// import NewEmerMaterial from '../components/emergencyRescue/emerMaterial/emerMaterialActions/NewEmerMaterial.vue'
// import UpdateEmerMaterial from '../components/emergencyRescue/emerMaterial/emerMaterialActions/UpdateEmerMaterial.vue'
// import RecordEmerMaterial from '../components/emergencyRescue/emerMaterial/emerMaterialActions/RecordEmerMaterial.vue'
// import EmerPlan from '../components/emergencyRescue/emerPlan/EmerPlan.vue'
// import NewEmerPlan from '../components/emergencyRescue/emerPlan/planAction/NewEmerPlan.vue'
// import UpdateEmerPlan from '../components/emergencyRescue/emerPlan/planAction/UpdateEmerPlan.vue'
// import ViewEmerPlan from '../components/emergencyRescue/emerPlan/planAction/ViewEmerPlan.vue'
// import EmerResponse from '../components/emergencyRescue/emerResponse/EmerResponse.vue'
// import ViewEmergency from '../components/emergencyRescue/emerResponse/emergencyActions/ViewEmergency.vue'
// import EmergencyProcess from '../components/emergencyRescue/emerResponse/emergencyActions/EmergencyProcess.vue'
// import NewEmerGroup from '../components/emergencyRescue/emerGroup/emerGroupActions/NewEmerGroup.vue'
// import KnowledgeBase from '../components/emergencyRescue/knowledgeBaseManage/KnowledgeBase.vue'
// import TagManage from '../components/emergencyRescue/knowledgeBaseManage/TagManage.vue'
// import NewGroupMember from '../components/emergencyRescue/emerGroup/emerGroupActions/NewGroupMember.vue'
// import ViewWorkBrief from '../components/emergencyRescue/emerResponse/emergencyActions/ViewWorkBrief.vue'
// import EditSummary from '../components/emergencyRescue/emerResponseWorkFlow/recordResponseActions/EditSummary.vue'
// import ViewSummary from '../components/emergencyRescue/emerResponse/emergencyActions/ViewSummary.vue'
// import ViewRelieve from '../components/emergencyRescue/emerResponse/emergencyActions/ViewRelieve.vue'
// import HolidayDuty from '../components/emergencyRescue/holidayDuty/HolidayDuty.vue'
// import EditHolidayDuty from '../components/emergencyRescue/holidayDuty/EditHolidayDuty.vue'
//
// //应急响应工作流形式
// import EmerResponseWorkflow from '../components/emergencyRescue/emerResponseWorkFlow/EmerResponseWorkflow.vue'
// import EditSummaryWorkflow from '../components/emergencyRescue/emerResponseWorkFlow/emergencyActions/EditSummaryWorkflow.vue'
// import EmergencyFormWorkflow from '../components/emergencyRescue/emerResponseWorkFlow/emergencyActions/EmergencyFormWorkflow.vue'
// import EmergencyProcessWorkflow from '../components/emergencyRescue/emerResponseWorkFlow/emergencyActions/EmergencyProcessWorkflow.vue'
// import NewWorkBriefWorkflow from '../components/emergencyRescue/emerResponseWorkFlow/emergencyActions/NewWorkBriefWorkflow.vue'
// import ViewEmergencyWorkflow from '../components/emergencyRescue/emerResponseWorkFlow/emergencyActions/ViewEmergencyWorkflow.vue'
// import ViewRelieveWorkflow from '../components/emergencyRescue/emerResponseWorkFlow/emergencyActions/ViewRelieveWorkflow.vue'
// import ViewSummaryWorkflow from '../components/emergencyRescue/emerResponseWorkFlow/emergencyActions/ViewSummaryWorkflow.vue'
// import ViewWorkBriefWorkflow from '../components/emergencyRescue/emerResponseWorkFlow/emergencyActions/ViewWorkBriefWorkflow.vue'
// import SubNewWorkBriefWorkflow from '../components/emergencyRescue/emerResponseWorkFlow/emergencyActions/SubNewWorkBriefWorkflow.vue'
// import ViewEmergencyProcessWorkflow from '../components/emergencyRescue/emerResponseWorkFlow/emergencyActions/ViewEmergencyProcessWorkflow.vue'
//
// import SuperiorEmerResponse from '../components/emergencyRescue/emerResponseWorkFlow/SuperiorEmerResponse.vue'
// //应急事件
// // START YANG
//
// // 组件测试
// import Test from '../components/common/test.vue'

// ##########应急处理###############
// // 首页
// import EmerHandle from '../components/emergencyRescue/emerHandle/EmerHandle.vue'
// // 查看下级重大事件上报情况
// import EmerHandleReportLower from '../components/emergencyRescue/EmerHandle/reportLower.vue'
// // 添加
// import EmerHandleAdd from '../components/emergencyRescue/emerHandle/EmerHandleAdd.vue'
// // 添加
// import EmerHandleUpdate from '../components/emergencyRescue/emerHandle/EmerHandleUpdate.vue'
// // 修改
// import EmerHandleEdit from '../components/emergencyRescue/emerHandle/EmerHandleEdit.vue'
// // 选择事件
// import EmerHandleChoiceEvent from '../components/emergencyRescue/emerHandle/EmerHandleChoiceEvent.vue'
// // 处理事件
// import EmerHandleDealEvent from '../components/emergencyRescue/emerHandle/EmerHandleDealEvent.vue'
// // 上报重大事件--添加
// import EmerHandleReport from '../components/emergencyRescue/emerHandle/EmerHandleReport.vue'
// // 上报重大事件--查看
// import EmerHandleLookReport from '../components/emergencyRescue/emerHandle/EmerHandleLookReport.vue'
// // ##########应急预案#############
// // 查看预案
// import EmerHandleLookPlan from '../components/emergencyRescue/emerHandle/EmerHandleLookPlan.vue'
// // 添加预案
// import EmerHandleAddPlan from '../components/emergencyRescue/emerHandle/EmerHandleAddPlan.vue'
// // END YANG
//签名页
import EsignPage from '../components/common/esignPage.vue'

//隐患排查
import HideDangerMenu from '../components/hideDanger/HideDangerMenu.vue'
// import FirstPage from '../components/hideDanger/firstPage/FirstPage.vue'
// import InspectionPlan from '../components/hideDanger/inspectionPlan/InspectionPlan.vue'
// import Investigation from '../components/hideDanger/investigation/Investigation.vue'
import TableManage from '../components/hideDanger/tableManage/TableManage.vue'
// import PlanForm from '../components/hideDanger/inspectionPlan/PlanForm.vue'
// import ViewPlanForm from '../components/hideDanger/inspectionPlan/ViewPlanForm.vue'
import TableManageAdd from '../components/hideDanger/tableManage/TableManageAdd.vue'
// import ViewInvestigation from '../components/hideDanger/investigation/investigationAction/ViewInvestigation.vue'
// import TableLabelManage from '../components/hideDanger/tableManage/TableLabelManage.vue'

//隐患排查的流程版
// import HideDangerWorkflow from '../components/hideDanger/dangerWorkflow/HideDangerWorkflow.vue'
// import ChangeFormEditWorkflow from '../components/hideDanger/dangerWorkflow/dangerWorkflowActions/ChangeFormEditWorkflow.vue'
// import ChangeFormUpdateWorkflow from '../components/hideDanger/dangerWorkflow/dangerWorkflowActions/ChangeFormUpdateWorkflow.vue'
// import ChangeFormViewWorkflow from '../components/hideDanger/dangerWorkflow/dangerWorkflowActions/ChangeFormViewWorkflow.vue'
// import InvestigationEditWorkflow from '../components/hideDanger/dangerWorkflow/dangerWorkflowActions/InvestigationEditWorkflow.vue'
// import InvestigationNewWorkflow from '../components/hideDanger/dangerWorkflow/dangerWorkflowActions/InvestigationNewWorkflow.vue'
// import InvestigationUpdateWorkflow from '../components/hideDanger/dangerWorkflow/dangerWorkflowActions/InvestigationUpdateWorkflow.vue'
// import InvestigationViewWorkflow from '../components/hideDanger/dangerWorkflow/dangerWorkflowActions/InvestigationViewWorkflow.vue'
// import SuperviseEditWorkflow from '../components/hideDanger/dangerWorkflow/dangerWorkflowActions/SuperviseEditWorkflow.vue'

/*
 * 教育培训 by YANG 2018-04-17
 * */
// 菜单
import EduTrainingMenu from '@/components/eduTraining/EduTrainingMenu.vue'
// // 需求调研--首页
// import DemandSurveyIndex from '@/components/eduTraining/demandSurvey/index.vue'
// // 需求调研--发布者首页
// import DemandSurveyPublishIndex from '@/components/eduTraining/demandSurvey/publish/index.vue'
// // 需求调研--发布者添加
// import DemandSurveyPublishAdd from '@/components/eduTraining/demandSurvey/publish/add.vue'
// // 需求调研--发布者查看下级需求调查
// import DemandSurveyPublishLower from '@/components/eduTraining/demandSurvey/publish/lower.vue'
// // 需求调研--员工首页
// import DemandSurveyStaffIndex from '@/components/eduTraining/demandSurvey/staff/index.vue'
// // 需求调研--员工添加
// import DemandSurveyStaffAdd from '@/components/eduTraining/demandSurvey/staff/add.vue'
// // 培训计划--首页
// import TrainingPlanIndex from '@/components/eduTraining/trainingPlan/index.vue'
// // 培训计划--添加
// import TrainingPlanAdd from '@/components/eduTraining/trainingPlan/add.vue'
// // 培训计划--查看下级培训计划
// import TrainingPlanLower from '@/components/eduTraining/trainingPlan/lower.vue'
// // 持证培训--首页
// import CertificateTrainingIndex from '@/components/eduTraining/certificateTraining/index.vue'
// // 持证培训--添加
// import CertificateTrainingAdd from '@/components/eduTraining/certificateTraining/add.vue'
// // 三级培训--首页
// import ThreeLevelTrainingIndex from '@/components/eduTraining/threeLevelTraining/index.vue'
// // 三级培训--查看
// import ThreeLevelTrainingView from '@/components/eduTraining/threeLevelTraining/view.vue'
// // 三级培训--公司---添加
// import ThreeLevelTrainingCompanyAdd from '@/components/eduTraining/threeLevelTraining/company/add.vue'
// // 三级培训--公司---编辑
// import ThreeLevelTrainingCompanyEdit from '@/components/eduTraining/threeLevelTraining/company/edit.vue'
// // 三级培训--部门---添加
// import ThreeLevelTrainingDepartmentAdd from '@/components/eduTraining/threeLevelTraining/department/add.vue'
// // 三级培训--部门---编辑
// import ThreeLevelTrainingDepartmentEdit from '@/components/eduTraining/threeLevelTraining/department/edit.vue'
// // 三级培训--班组---添加
// import ThreeLevelTrainingTeamAdd from '@/components/eduTraining/threeLevelTraining/team/add.vue'
// // 三级培训--班组---编辑
// import ThreeLevelTrainingTeamEdit from '@/components/eduTraining/threeLevelTraining/team/edit.vue'
// // 转岗培训--首页
// import TransferPostTrainingIndex  from '@/components/eduTraining/transferPostTraining/index.vue'
// // 转岗培训--添加
// import TransferPostTrainingAdd  from '@/components/eduTraining/transferPostTraining/add.vue'
// // 日常培训--首页
// import DailyTrainingIndex from '@/components/eduTraining/dailyTraining/index.vue'
// // 日常培训--添加
// import DailyTrainingAdd from '@/components/eduTraining/dailyTraining/add.vue'
// // 日常培训--查看
// import DailyTrainingView from '@/components/eduTraining/dailyTraining/view.vue'
// // 五大状态：未发布（0）、已发布（1）、进行中（2）、待评价（3）和已完结（4）
// // 日常培训--进程--未发布（0）
// import DailyTrainingProcessUnpublished from '@/components/eduTraining/dailyTraining/process/unpublished.vue'
// // 日常培训--进程--已发布（1）
// import DailyTrainingProcessPublished from '@/components/eduTraining/dailyTraining/process/published.vue'
// // 日常培训--进程--进行中（2）
// import DailyTrainingProcessHaveInHand from '@/components/eduTraining/dailyTraining/process/haveInHand.vue'
// // 日常培训--进程--待评价（3）
// import DailyTrainingProcessToBeEvaluated from '@/components/eduTraining/dailyTraining/process/toBeEvaluated.vue'
// // 日常培训--进程--已完结（4）
// import DailyTrainingProcessHaveFinished from '@/components/eduTraining/dailyTraining/process/haveFinished.vue'

/*
 * 安全投入 by YANG 2018-04-17
 * */
// 菜单
import safetyInputMenu from '../components/safetyInput/safetyInputMenu.vue'
// 费用预算--首页
import costBudgetIndex from '../components/safetyInput/costBudget/index.vue'
// 费用预算--添加
import costBudgetItem from '../components/safetyInput/costBudget/item.vue'
// 费用投入--首页
import costInputIndex from '../components/safetyInput/costInput/index.vue'
// 费用投入--明细
import costInputItem from '../components/safetyInput/costInput/item.vue'
// 预算跟踪--列表
import budgetaryTrackingIndex from '../components/safetyInput/budgetaryTracking/index.vue'
// 受伤获赔--列表
import compensationForInjuryIndex from '../components/safetyInput/compensationForInjury/index.vue'
// 受伤获赔--明细
import compensationForInjuryItem from '../components/safetyInput/compensationForInjury/item.vue'
/*
*  安全投入 END
* */

//
import ManageMenu from '../components/systemManage/ManageMenu.vue'
// import DepartmentMange from '../components/systemManage/departmentManage/DepartmentMange.vue'
// import EmerPlanManage from '../components/systemManage/emerPlanManage/EmerPlanManage.vue'
// import GroupManage from '../components/systemManage/groupManage/GroupManage.vue'
// import RoleManage from '../components/systemManage/userManage/RoleManage.vue'
// import UserManage from '../components/systemManage/userManage/UserManage.vue'
// import NewRole from '../components/systemManage/userManage/roleAction/NewRole.vue'
// import UpdateRole from '../components/systemManage/userManage/roleAction/UpdateRole.vue'
// import SysLogManage from '../components/systemManage/sysLogManage/SysLogManage.vue'
// import SysNotice from '../components/systemManage/sysNotice/sysNotice.vue'
import powerManageIndex from '../components/systemManage/powerManage/index.vue'
import certificateManageType from '../components/systemManage/certificateManage/type.vue'
import certificateManagePublish from '../components/systemManage/certificateManage/publish.vue'
// import WorkflowManager from '../components/systemManage/workFlow/WorkflowManage.vue'
// import ProcessNode from '../components/systemManage/workFlow/ProcessNode.vue'
import MyNotice from '../components/systemManage/sysNotice/MyNotice.vue'
// import ProcessInstanceManage from '../components/systemManage/workFlow/ProcessInstanceManage.vue'
import TaskNotice from '../components/systemManage/sysNotice/TaskNotice.vue'
// import PostManage from '../components/systemManage/userManage/PostManage.vue'

//台账
import CompanyAccount from '../components/companyAccount/companyAccount.vue'
// import AccountEmer from '../components/companyAccount/emerAccount/EmerResponse.vue'
// import AccountEmerList from '../components/companyAccount/emerAccount/AccountEmerList.vue'
// import ViewEmergencyAccount from '../components/companyAccount/emerAccount/ViewEmergencyAccount.vue'
// import ViewWorkBriefAccount from '../components/companyAccount/emerAccount/ViewWorkBriefAccount.vue'
// import ViewSummaryAccount from '../components/companyAccount/emerAccount/ViewSummaryAccount.vue'
// import AccountDangerIndex from '../components/companyAccount/dangerAccount/index.vue'
// import DangerPlanFormAccount from '../components/companyAccount/dangerAccount/dangerPlanFormAccount.vue'
// import DangerInspectSummary from '../components/companyAccount/dangerAccount/dangerInspectSummary.vue'
// import DangerInspectDetail from '../components/companyAccount/dangerAccount/dangerInspectDetail.vue'
//
// import DangerReformReply from '../components/companyAccount/dangerAccount/dangerReformReply.vue'

/*
 * 台账 by YANG 2018-04-17
 * */

// 安全投入--首页
import accountSafeIndex from '@/components/companyAccount/accountSafe/index.vue'
// 安全投入--明细---预算计划
import accountSafeItemPlan from '@/components/companyAccount/accountSafe/itemPlan.vue'
// 安全投入--明细---费用投入
import accountSafeItemInput from '@/components/companyAccount/accountSafe/itemInput.vue'
// 安全投入--明细---执行
import accountSafeItemImplement from '@/components/companyAccount/accountSafe/itemImplement.vue'
// 安全投入--明细---受伤获赔
import accountSafeItemInjury from '@/components/companyAccount/accountSafe/itemInjury.vue'
// 教育培训--首页
import accountEduIndex from '@/components/companyAccount/accountEdu/index.vue'
// 教育培训--明细--花名册
import sysUserReports from '@/components/companyAccount/accountEdu/sysUserReports.vue'
// 教育培训--明细--需求调查
import eduRequirementInvestigation from '@/components/companyAccount/accountEdu/eduRequirementInvestigation.vue'
// 教育培训--明细--培训计划
import eduPlanList from '@/components/companyAccount/accountEdu/eduPlanList.vue'
// 教育培训--明细--持证培训
import eduCertificates from '@/components/companyAccount/accountEdu/eduCertificates.vue'
// 教育培训--明细--三级培训
import eduEntryTrainings from '@/components/companyAccount/accountEdu/eduEntryTrainings.vue'
// 教育培训--明细--转岗培训
import eduReassignments from '@/components/companyAccount/accountEdu/eduReassignments.vue'
// 教育培训--明细--日常培训--首页
import eduDailyInfosIndex from '@/components/companyAccount/accountEdu/eduDailyInfosIndex.vue'
// 教育培训--明细--日常培训--明细
import eduDailyInfosItem from '@/components/companyAccount/accountEdu/eduDailyInfosItem.vue'
// 教育培训--明细--日常培训--明细--活动记录
import eduDailyInfosItemRecord from '@/components/companyAccount/accountEdu/eduDailyInfosItemRecord.vue'
// 教育培训--明细--日常培训--明细--通知
import eduDailyInfosItemNotify from '@/components/companyAccount/accountEdu/eduDailyInfosItemNotify.vue'
// 教育培训--明细--日常培训--明细--考核表
import eduDailyInfosItemExamList from '@/components/companyAccount/accountEdu/eduDailyInfosItemExamList.vue'





/*
 *  安全投入 END
 * */
Vue.use(Router)

var router = new Router({
  routes: [
    {
      path: '/',
      name: 'login',
      component: Login
    },
    {
      path: '*',
      name: 'unknowPage',
      component: Login
    },
    {
      path: '/menu',
      name: 'menu',
      component: Menu
    },
    {
      path: '/manager-view',
      name: 'managerView',
      component: ManagerView,
    },
    {
      path: '/map-view',
      name: 'mapView',
      component: MapView
    },

    {
      path: '/esign-page',
      name: 'esignPage',
      component: EsignPage
    },
    {
      path: '/emer-menu',
      name: 'emerMenu',
      component: EmerMenu,
      beforeEnter: (to, from, next) => {
        if (tool.getStorage('SAFE_PLATFORM_MENU').subMenu['emerMenu']) {
          next();
        } else {
          next(from.path);//返回原来的页面
        }
      },
      children: [
        {
          path: 'emer-plan-manage',
          name: 'emerPlanManage',
          component: resolve => require(['@/components/systemManage/emerPlanManage/EmerPlanManage.vue'], resolve)
        },
        {
          path: 'emer-group',
          name: 'emerGroup',
          component: resolve => require(['@/components/emergencyRescue/emerGroup/EmerGroup.vue'], resolve)
        }, {
          path: 'new-group-member',
          name: 'newGroupMember',
          component: resolve => require(['@/components/emergencyRescue/emerGroup/emerGroupActions/NewGroupMember.vue'], resolve)
        },
        {
          path: 'new-emer-group',
          name: 'newEmerGroup',
          component: resolve => require(['@/components/emergencyRescue/emerGroup/emerGroupActions/NewEmerGroup.vue'], resolve)
        },
        {
          path: 'emer-material',
          name: 'emerMaterial',
          component: resolve => require(['@/components/emergencyRescue/emerMaterial/EmerMaterial.vue'], resolve)
        },
        {
          path: 'new-emer-material',
          name: 'newEmerMaterial',
          component: resolve => require(['@/components/emergencyRescue/emerMaterial/emerMaterialActions/NewEmerMaterial.vue'], resolve)
        },
        {
          path: 'update-emer-material',
          name: 'updateEmerMaterial',
          component: resolve => require(['@/components/emergencyRescue/emerMaterial/emerMaterialActions/UpdateEmerMaterial.vue'], resolve)
        },
        {
          path: 'record-emer-material',
          name: 'recordEmerMaterial',
          component: resolve => require(['@/components/emergencyRescue/emerMaterial/emerMaterialActions/RecordEmerMaterial.vue'], resolve)
        },
        {
          path: 'emer-plan',
          name: 'emerPlan',
          component: resolve => require(['@/components/emergencyRescue/emerPlan/EmerPlan.vue'], resolve)
        },
        {
          path: 'new-emer-plan',
          name: 'newEmerPlan',
          component: resolve => require(['@/components/emergencyRescue/emerPlan/planAction/NewEmerPlan.vue'], resolve)
        },
        {
          path: 'update-emer-plan',
          name: 'updateEmerPlan',
          component: resolve => require(['@/components/emergencyRescue/emerPlan/planAction/UpdateEmerPlan.vue'], resolve)
        },
        {
          path: 'view-emer-plan',
          name: 'viewEmerPlan',
          component: resolve => require(['@/components/emergencyRescue/emerPlan/planAction/ViewEmerPlan.vue'], resolve)
        },
        {
          path: 'emer-response',
          name: 'emerResponse',
          component: resolve => require(['@/components/emergencyRescue/emerResponse/EmerResponse.vue'], resolve)
        },
        {
          path: 'emergency-process',
          name: 'emergencyProcess',
          component: resolve => require(['@/components/emergencyRescue/emerResponse/emergencyActions/EmergencyProcess.vue'], resolve)
        },
        {
          path: 'view-work-brief',
          name: 'viewWorkBrief',
          component: resolve => require(['@/components/emergencyRescue/emerResponse/emergencyActions/ViewWorkBrief.vue'], resolve)
        },
        {
          path: 'view-emergency',
          name: 'viewEmergency',
          component: resolve => require(['@/components/emergencyRescue/emerResponse/emergencyActions/ViewEmergency.vue'], resolve)
        },
        {
          path: 'edit-summary',
          name: 'editSummary',
          component: resolve => require(['@/components/emergencyRescue/emerResponseWorkFlow/recordResponseActions/EditSummary.vue'], resolve)
        },
        {
          path: 'view-summary',
          name: 'viewSummary',
          component: resolve => require(['@/components/emergencyRescue/emerResponse/emergencyActions/ViewSummary.vue'], resolve)
        },
        {
          path: 'view-relieve',
          name: 'viewRelieve',
          component: resolve => require(['@/components/emergencyRescue/emerResponse/emergencyActions/ViewRelieve.vue'], resolve)
        },
        // 应急处理---yang
        // 事件列表（首页）
        {
          path: 'test',
          name: 'test',
          component: resolve => require(['@/components/common/test.vue'], resolve)
        },
        //##########事件处理##########
        // 事件列表（首页）
        {
          path: 'emer-handle',
          name: 'emerHandle',
          component: resolve => require(['@/components/emergencyRescue/emerHandle/EmerHandle.vue'], resolve)
        },
        // 事件列表（查看下级重大事件上报）
        {
          path: 'emer-handle-report-lower',
          name: 'emerHandleReportLower',
          component: resolve => require(['@/components/emergencyRescue/emerHandle/reportLower.vue'], resolve)
        },
        // 添加事件
        {
          path: 'emer-handle-add',
          name: 'emerHandleAdd',
          component: resolve => require(['@/components/emergencyRescue/emerHandle/EmerHandleAdd.vue'], resolve)
        },
        // 修改事件
        {
          path: 'emer-handle-edit',
          name: 'emerHandleEdit',
          component: resolve => require(['@/components/emergencyRescue/emerHandle/EmerHandleEdit.vue'], resolve)
        },
        // 变更事件
        {
          path: 'emer-handle-update',
          name: 'emerHandleUpdate',
          component: resolve => require(['@/components/emergencyRescue/emerHandle/EmerHandleUpdate.vue'], resolve)
        },
        // 选择事件
        {
          path: 'emer-handle-choice-event',
          name: 'emerHandleChoiceEvent',
          component: resolve => require(['@/components/emergencyRescue/emerHandle/EmerHandleChoiceEvent.vue'], resolve)
        },
        // 处理事件
        {
          path: 'emer-handle-deal-event',
          name: 'emerHandleDealEvent',
          component: resolve => require(['@/components/emergencyRescue/emerHandle/EmerHandleDealEvent.vue'], resolve)
        },
        // 上报重要信息--查看
        {
          path: 'emer-handle-look-report',
          name: 'emerHandleLookReport',
          component: resolve => require(['@/components/emergencyRescue/emerHandle/EmerHandleLookReport.vue'], resolve)
        },
        // 上报重要信息--添加
        {
          path: 'emer-handle-report',
          name: 'emerHandleReport',
          component: resolve => require(['@/components/emergencyRescue/emerHandle/EmerHandleReport.vue'], resolve)
        },
        // 添加预案
        {
          path: 'emer-handle-add-plan',
          name: 'emerHandleAddPlan',
          component: resolve => require(['@/components/emergencyRescue/emerHandle/EmerHandleAddPlan.vue'], resolve)
        },
        // 查看预案
        {
          path: 'emer-handle-look-plan',
          name: 'emerHandleLookPlan',
          component: resolve => require(['@/components/emergencyRescue/emerHandle/EmerHandleLookPlan.vue'], resolve)
        },
        // END YANG
        //应急响应流程版
        {
          path: 'emer-response-workflow',
          name: 'emerResponseWorkflow',
          component: resolve => require(['@/components/emergencyRescue/emerResponseWorkFlow/EmerResponseWorkflow.vue'], resolve)
        },
        {
          path: 'edit-summary-workflow',
          name: 'editSummaryWorkflow',
          component: resolve => require(['@/components/emergencyRescue/emerResponseWorkFlow/emergencyActions/EditSummaryWorkflow.vue'], resolve)
        },
        {
          path: 'emergency-form-workflow',
          name: 'emergencyFormWorkflow',
          component: resolve => require(['@/components/emergencyRescue/emerResponseWorkFlow/emergencyActions/EmergencyFormWorkflow.vue'], resolve)
        },
        {
          path: 'emergency-process-workflow',
          name: 'emergencyProcessWorkflow',
          component: resolve => require(['@/components/emergencyRescue/emerResponseWorkFlow/emergencyActions/EmergencyProcessWorkflow.vue'], resolve)
        },
        {
          path: 'new-work-brief-workflow',
          name: 'newWorkBriefWorkflow',
          component: resolve => require(['@/components/emergencyRescue/emerResponseWorkFlow/emergencyActions/NewWorkBriefWorkflow.vue'], resolve)
        },
        {
          path: 'view-emergency-workflow',
          name: 'viewEmergencyWorkflow',
          component: resolve => require(['@/components/emergencyRescue/emerResponseWorkFlow/emergencyActions/ViewEmergencyWorkflow.vue'], resolve)
        },
        {
          path: 'view-relieve-workflow',
          name: 'viewRelieveWorkflow',
          component: resolve => require(['@/components/emergencyRescue/emerResponseWorkFlow/emergencyActions/ViewRelieveWorkflow.vue'], resolve)
        },
        {
          path: 'view-summary-workflow',
          name: 'viewSummaryWorkflow',
          component: resolve => require(['@/components/emergencyRescue/emerResponseWorkFlow/emergencyActions/ViewSummaryWorkflow.vue'], resolve)
        },
        {
          path: 'view-work-brief-workflow',
          name: 'viewWorkBriefWorkflow',
          component: resolve => require(['@/components/emergencyRescue/emerResponseWorkFlow/emergencyActions/ViewWorkBriefWorkflow.vue'], resolve)
        },
        {
          path: 'view-emergency-process-workflow',
          name: 'viewEmergencyProcessWorkflow',
          component: resolve => require(['@/components/emergencyRescue/emerResponseWorkFlow/emergencyActions/ViewEmergencyProcessWorkflow.vue'], resolve)
        },
        //应急响应流程版结束
        {
          path: 'sub-new-work-brief-workflow',
          name: 'subNewWorkBriefWorkflow',
          component: resolve => require(['@/components/emergencyRescue/emerResponseWorkFlow/emergencyActions/SubNewWorkBriefWorkflow.vue'], resolve)
        },
        {
          path: 'superior-Emer-Response',
          name: 'superiorEmerResponse',
          component: resolve => require(['@/components/emergencyRescue/emerResponseWorkFlow/SuperiorEmerResponse.vue'], resolve)
        },
        {
          path: 'forward-emer',
          name: 'forwardEmer',
          component: resolve => require(['@/components/emergencyRescue/emerResponseWorkFlow/recordResponseActions/forwardEmer.vue'], resolve)
        },
        //节假日值班安排
        {
          path: 'holiday-duty',
          name: 'holidayDuty',
          component: resolve => require(['@/components/emergencyRescue/holidayDuty/HolidayDuty.vue'], resolve)
        },
        {
          path: 'edit-holiday-duty',
          name: 'editHolidayDuty',
          component: resolve => require(['@/components/emergencyRescue/holidayDuty/EditHolidayDuty.vue'], resolve)
        },
        {
          path: 'emer-duty',
          name: 'emerDuty',
          component: resolve => require(['@/components/emergencyRescue/holidayDuty/EmerDuty.vue'], resolve)
        },
        {
          path: 'edit-emer-duty',
          name: 'editEmerDuty',
          component: resolve => require(['@/components/emergencyRescue/holidayDuty/EditEmerDuty.vue'], resolve)
        }
        //节假日值班安排结束
      ]
    },
    {
      path: '/hide-danger-menu',
      name: 'hideDangerMenu',
      component: HideDangerMenu,
      beforeEnter: (to, from, next) => {
        if (tool.getStorage('SAFE_PLATFORM_MENU').subMenu['hideDangerMenu']) {
          next();
        } else {
          next(from.path);//返回原来的页面
        }
      },
      children: [
        {
          path: 'first-page',
          name: 'firstPage',
          component: resolve => require(['@/components/hideDanger/firstPage/FirstPage.vue'], resolve)
        },
        {
          path: 'inspection-plan',
          name: 'inspectionPlan',
          component: resolve => require(['@/components/hideDanger/inspectionPlan/InspectionPlan.vue'], resolve)
        },
        {
          path: 'table-manage',
          name: 'TableManage',
          component: resolve => require(['@/components/hideDanger/tableManage/TableManage.vue'], resolve)
        },
        {
          path: 'plan-form',
          name: 'planForm',
          component: resolve => require(['@/components/hideDanger/inspectionPlan/PlanForm.vue'], resolve)
        },
        {
          path: 'view-plan-form',
          name: 'viewPlanForm',
          component: resolve => require(['@/components/hideDanger/inspectionPlan/ViewPlanForm.vue'], resolve)
        },
        {
          path: 'table-manage-add',
          name: 'TableManageAdd',
          component: resolve => require(['@/components/hideDanger/tableManage/TableManageAdd.vue'], resolve)
        },
        {
          path: 'investigation',
          name: 'investigation',
          component: resolve => require(['@/components/hideDanger/investigation/Investigation.vue'], resolve)
        },
        {
          path: 'view-investigation',
          name: 'viewInvestigation',
          component: resolve => require(['@/components/hideDanger/investigation/investigationAction/ViewInvestigation.vue'], resolve)
        },
        {
          path: 'hide-danger-workflow',
          name: 'hideDangerWorkflow',
          component: resolve => require(['@/components/hideDanger/dangerWorkflow/HideDangerWorkflow.vue'], resolve)
        },
        {
          path: 'change-form-edit-workflow',
          name: 'changeFormEditWorkflow',
          component: resolve => require(['@/components/hideDanger/dangerWorkflow/dangerWorkflowActions/ChangeFormEditWorkflow.vue'], resolve)
        },
        {
          path: 'change-form-update-workflow',
          name: 'changeFormUpdateWorkflow',
          component: resolve => require(['@/components/hideDanger/dangerWorkflow/dangerWorkflowActions/ChangeFormUpdateWorkflow.vue'], resolve)
        },
        {
          path: 'change-form-view-workflow',
          name: 'changeFormViewWorkflow',
          component: resolve => require(['@/components/hideDanger/dangerWorkflow/dangerWorkflowActions/ChangeFormViewWorkflow.vue'], resolve)
        },
        {
          path: 'investigation-edit-workflow',
          name: 'investigationEditWorkflow',
          component: resolve => require(['@/components/hideDanger/dangerWorkflow/dangerWorkflowActions/InvestigationEditWorkflow.vue'], resolve)
        },
        {
          path: 'investigation-new-workflow',
          name: 'investigationNewWorkflow',
          component: resolve => require(['@/components/hideDanger/dangerWorkflow/dangerWorkflowActions/InvestigationNewWorkflow.vue'], resolve)
        },
        {
          path: 'investigation-update-workflow',
          name: 'investigationUpdateWorkflow',
          component: resolve => require(['@/components/hideDanger/dangerWorkflow/dangerWorkflowActions/InvestigationUpdateWorkflow.vue'], resolve)
        },
        {
          path: 'investigation-view-workflow',
          name: 'investigationViewWorkflow',
          component: resolve => require(['@/components/hideDanger/dangerWorkflow/dangerWorkflowActions/InvestigationViewWorkflow.vue'], resolve)
        },
        {
          path: 'supervise-edit-workflow',
          name: 'superviseEditWorkflow',
          component: resolve => require(['@/components/hideDanger/dangerWorkflow/dangerWorkflowActions/SuperviseEditWorkflow.vue'], resolve)
        },
        {
          path: 'table-label-manage',
          name: 'tableLabelManage',
          component: resolve => require(['@/components/hideDanger/tableManage/TableLabelManage.vue'], resolve)
        },
        {
          path: 'danger-type-manage',
          name: 'dangerTypeManage',
          component: resolve => require(['@/components/hideDanger/dangerDataManage/DangerTypeManage.vue'], resolve)
        },
      ]
    },
    {
      path: '/manage-menu',
      name: 'manageMenu',
      component: ManageMenu,
      beforeEnter: (to, from, next) => {
        if (tool.getStorage('SAFE_PLATFORM_MENU').subMenu['manageMenu']) {
          next();
        } else {
          next(from.path);//返回原来的页面
        }
      },
      children: [
        {
          path: 'department-manage',
          name: 'departmentManage',
          component: resolve => require(['@/components/systemManage/departmentManage/DepartmentMange.vue'], resolve)
        },
        {
          path: 'group-manage',
          name: 'groupManage',
          component: resolve => require(['@/components/systemManage/groupManage/GroupManage.vue'], resolve)
        },
        {
          path: 'role-manage',
          name: 'roleManage',
          component: resolve => require(['@/components/systemManage/userManage/RoleManage.vue'], resolve)
        },
        {
          path: 'user-manage',
          name: 'userManage',
          component: resolve => require(['@/components/systemManage/userManage/UserManage.vue'], resolve)
        },
        {
          path: 'new-role',
          name: 'newRole',
          component: resolve => require(['@/components/systemManage/userManage/roleAction/NewRole.vue'], resolve)
        },
        {
          path: 'update-role',
          name: 'updateRole',
          component: resolve => require(['@/components/systemManage/userManage/roleAction/UpdateRole.vue'], resolve)
        },
        {
          path: 'sysLog-manage',
          name: 'sysLogManage',
          component: resolve => require(['@/components/systemManage/sysLogManage/SysLogManage.vue'], resolve)
        },
        {
          path: 'sys-notice',
          name: 'sysNotice',
          component: resolve => require(['@/components/systemManage/sysNotice/sysNotice.vue'], resolve)
        },
        // 通知管理页面

        {
          path: 'workflow-notify-index',
          name: 'workflowNotifyIndex',
          component: resolve => require(['@/components/systemManage/workFlow/notify/index.vue'], resolve)
        },



        // 安全文件页面

        {
          path: 'safe-file-index',
          name: 'safeFileIndex',
          component: resolve => require(['@/components/systemManage/workFlow/safeFile/index.vue'], resolve)
        },


        {
          path: 'workflow-manage',
          name: 'workflowManage',
          component: resolve => require(['@/components/systemManage/workFlow/WorkflowManage.vue'], resolve)
        },
        {
          path: 'process-node',
          name: 'processNode',
          component: resolve => require(['@/components/systemManage/workFlow/ProcessNode.vue'], resolve)
        },
        {
          path: 'process-instance-manage',
          name: 'processInstanceManage',
          component: resolve => require(['@/components/systemManage/workFlow/ProcessInstanceManage.vue'], resolve)
        },
        // 权限管理
        {
          path: 'power-manage-index',
          name: 'powerManageIndex',
          component: resolve => require(['@/components/systemManage/powerManage/index.vue'], resolve)
        },

        // 证书类型
        {
          path: 'certificate-manage-type',
          name: 'certificateManageType',
          component: resolve => require(['@/components/systemManage/certificateManage/type.vue'], resolve)
        },
        // 发证单位
        {
          path: 'certificate-manage-publish',
          name: 'certificateManagePublish',
          component: resolve => require(['@/components/systemManage/certificateManage/publish.vue'], resolve)
        },
        //知识库管理
        {
          path: 'knowledge-base',
          name: 'knowledgeBase',
          component: resolve => require(['@/components/emergencyRescue/knowledgeBaseManage/KnowledgeBase.vue'], resolve)
        },
        {
          path: 'tag-manage',
          name: 'tagManage',
          component: resolve => require(['@/components/emergencyRescue/knowledgeBaseManage/TagManage.vue'], resolve)
        },
        //岗位管理
        {
          path: 'post-manage',
          name: 'postManage',
          component: resolve => require(['@/components/systemManage/userManage/PostManage.vue'], resolve)
        },
        //企业管理-企业信息
        {
          path: 'companyInfo',
          name: 'companyInfo',
          component: resolve => require(['@/components/mine/companyInfo.vue'], resolve)
        },
        //企业管理-矿山信息
        {
          path: 'mineInfo',
          name: 'mineInfo',
          component: resolve => require(['@/components/mine/mineInfo.vue'], resolve)
        },
        //企业管理-矿山信息-查看
        {
          path: 'mineInfoView',
          name: 'mineInfoView',
          component: resolve => require(['@/components/mine/mineInfoView.vue'], resolve),
          meta: {
            keepAlive: false, // 是否缓存
          }
        },
        //企业管理-地图库
        {
          path: 'mapData',
          name: 'mapData',
          component: resolve => require(['@/components/mine/mapData.vue'], resolve)
        },
        //风险清单-设备
        {
          path: 'riskDevice',
          name: 'riskWork',
          component: resolve => require(['@/components/mine/riskDevice.vue'], resolve),
          meta: {
            keepAlive: false, // 是否缓存
          }
        },
        //风险清单-作业
        {
          path: 'riskWork',
          name: 'riskWork',
          component: resolve => require(['@/components/mine/riskWork.vue'], resolve),
          meta: {
            keepAlive: false, // 是否缓存
          }
        },
        //风险四色图-静态
        {
          path: 'riskStatic',
          name: 'riskStatic',
          component: resolve => require(['@/components/mine/riskStatic.vue'], resolve),
          meta: {
            keepAlive: false, // 是否缓存
          }
        },
        //风险四色图-动态
        {
          path: 'riskDynamic',
          name: 'riskDynamic',
          component: resolve => require(['@/components/mine/riskDynamic.vue'], resolve),
          meta: {
            keepAlive: false, // 是否缓存
          }
        },
        //风险四色图-实时
        {
          path: 'riskOnline',
          name: 'riskOnline',
          component: resolve => require(['@/components/mine/riskOnline.vue'], resolve),
          meta: {
            keepAlive: false, // 是否缓存
          }
        },
        //矿山首页
        {
          path: 'minehome',
          name: 'minehome',
          component: resolve => require(['@/components/mine/minehome.vue'], resolve),
          meta: {
            keepAlive: false, // 是否缓存
          }
        },
      ]
    },
    {
      path: '/my-notice',
      name: 'myNotice',
      component: MyNotice,
    },
    {
      path: '/task-notice',
      name: 'taskNotice',
      component: TaskNotice
    },
    //台账
    {
      path: '/company-account',
      name: 'companyAccount',
      component: CompanyAccount,
      beforeEnter: (to, from, next) => {
        if (tool.getStorage('SAFE_PLATFORM_MENU').subMenu['companyAccount']) {
          next();
        } else {
          next(from.path);//返回原来的页面
        }
      },
      children: [
        //应急救援的台账页面
        {
          path: 'account-emer',
          name: 'accountEmer',
          component: resolve => require(['@/components/companyAccount/emerAccount/EmerResponse.vue'], resolve)
        },
        {
          path: 'account-emer-list',
          name: 'accountEmerList',
          component: resolve => require(['@/components/companyAccount/emerAccount/AccountEmerList.vue'], resolve)
        },
        {
          path: 'view-emergency-account',
          name: 'viewEmergencyAccount',
          component: resolve => require(['@/components/companyAccount/emerAccount/ViewEmergencyAccount.vue'], resolve)
        },
        {
          path: 'view-workBrief-account',
          name: 'viewWorkBriefAccount',
          component: resolve => require(['@/components/companyAccount/emerAccount/ViewWorkBriefAccount.vue'], resolve)
        },
        {
          path: 'view-summary-account',
          name: 'viewSummaryAccount',
          component: resolve => require(['@/components/companyAccount/emerAccount/ViewSummaryAccount.vue'], resolve)
        },
        //应急救援的台账页面 end
        //隐患排查的台账页面
        {
          path: 'account-danger-index',
          name: 'accountDangerIndex',
          component: resolve => require(['@/components/companyAccount/dangerAccount/index.vue'], resolve)
        },
        {
          path: 'account-danger-plan',
          name: 'dangerPlanFormAccount',
          component: resolve => require(['@/components/companyAccount/dangerAccount/dangerPlanFormAccount.vue'], resolve)
        },
        {
          path: 'account-danger-summary',
          name: 'dangerInspectSummary',
          component: resolve => require(['@/components/companyAccount/dangerAccount/dangerInspectSummary.vue'], resolve)
        },
        {
          path: 'danger-reform-summary',
          name: 'dangerReformSummary',
          component: resolve => require(['@/components/companyAccount/dangerAccount/dangerReformSummary.vue'], resolve)
        },
        {
          path: 'account-danger-detail',
          name: 'dangerInspectDetail',
          component: resolve => require(['@/components/companyAccount/dangerAccount/dangerInspectDetail.vue'], resolve)
        },
        {
          path: 'danger-reform-reply',
          name: 'dangerReformReply',
          component: resolve => require(['@/components/companyAccount/dangerAccount/dangerReformReply.vue'], resolve)
        },
        //隐患排查的台账页面end


        // 应急物质--首页---开始
        {
          path: 'emg-goods-index',
          name: 'emgGoodsIndex',
          component: resolve => require(['@/components/companyAccount/emgGoods/index.vue'], resolve)
        },
        // 应急物质--首页---END


        // 安全投入----首页
        {
          path: 'account-safe-index',
          name: 'accountSafeIndex',
          component: resolve => require(['@/components/companyAccount/accountSafe/index.vue'], resolve)
        },
        // 安全投入----明细----预算
        {
          path: 'account-safe-item-plan',
          name: 'accountSafeItemPlan',
          component: resolve => require(['@/components/companyAccount/accountSafe/itemPlan.vue'], resolve)
        },
        // 安全投入----明细----投入
        {
          path: 'account-safe-item-input',
          name: 'accountSafeItemInput',
          component: resolve => require(['@/components/companyAccount/accountSafe/itemInput.vue'], resolve)
        },
        // 安全投入----明细----执行
        {
          path: 'account-safe-item-implement',
          name: 'accountSafeItemImplement',
          component: resolve => require(['@/components/companyAccount/accountSafe/itemImplement.vue'], resolve)
        },
        // 安全投入----明细----受伤获赔
        {
          path: 'account-safe-item-injury',
          name: 'accountSafeItemInjury',
          component: resolve => require(['@/components/companyAccount/accountSafe/itemInjury.vue'], resolve)
        },


        // 节假日值班台账
        // 节假日值班----首页
        {
          path: 'holiday-work-index',
          name: 'holidayWorkIndex',
          component: resolve => require(['@/components/companyAccount/holidayWork/index.vue'], resolve)
        },
        // 应急值班台账
        // 应急值班----首页
        {
          path: 'emg-work-index',
          name: 'emgWorkIndex',
          component: resolve => require(['@/components/companyAccount/holidayWork/emgDutyIndex.vue'], resolve)
        },

        // 教育培训----首页
        {
          path: 'account-edu-index',
          name: 'accountEduIndex',
          component: resolve => require(['@/components/companyAccount/accountEdu/index.vue'], resolve)
        },
        // 教育培训----明细---花名册
        {
          path: 'sys-user-reports',
          name: 'sysUserReports',
          component: resolve => require(['@/components/companyAccount/accountEdu/sysUserReports.vue'], resolve)
        },
        // 教育培训----明细---需求调查
        {
          path: 'edu-requirement-investigation',
          name: 'eduRequirementInvestigation',
          component: resolve => require(['@/components/companyAccount/accountEdu/eduRequirementInvestigation.vue'], resolve)
        },
        // 教育培训----明细---培训计划
        {
          path: 'edu-plan-list',
          name: 'eduPlanList',
          component: resolve => require(['@/components/companyAccount/accountEdu/eduPlanList.vue'], resolve)
        },
        // 教育培训----明细---持证培训
        {
          path: 'edu-certificates',
          name: 'eduCertificates',
          component: resolve => require(['@/components/companyAccount/accountEdu/eduCertificates.vue'], resolve)
        },
        // 教育培训----明细---三级培训
        {
          path: 'edu-entry-trainings',
          name: 'eduEntryTrainings',
          component: resolve => require(['@/components/companyAccount/accountEdu/eduEntryTrainings.vue'], resolve)
        },
        // 教育培训----明细---转岗培训
        {
          path: 'edu-reassignments',
          name: 'eduReassignments',
          component: resolve => require(['@/components/companyAccount/accountEdu/eduReassignments.vue'], resolve)
        },
        // 教育培训----明细---日常培训--首页
        {
          path: 'edu-daily-infos-index',
          name: 'eduDailyInfosIndex',
          component: resolve => require(['@/components/companyAccount/accountEdu/eduDailyInfosIndex.vue'], resolve)
        },
        // 教育培训----明细---日常培训--明细
        {
          path: 'edu-daily-infos-item',
          name: 'eduDailyInfosItem',
          component: resolve => require(['@/components/companyAccount/accountEdu/eduDailyInfosItem.vue'], resolve)
        },
        // 教育培训----明细---日常培训--明细--活动记录
        {
          path: 'edu-daily-infos-item-record',
          name: 'eduDailyInfosItemRecord',
          component: resolve => require(['@/components/companyAccount/accountEdu/eduDailyInfosItemRecord.vue'], resolve)
        },
        // 教育培训----明细---日常培训--明细--通知
        {
          path: 'edu-daily-infos-item-notify',
          name: 'eduDailyInfosItemNotify',
          component: resolve => require(['@/components/companyAccount/accountEdu/eduDailyInfosItemNotify.vue'], resolve)
        },
        // 教育培训----明细---日常培训--明细--考核表
        {
          path: 'edu-daily-infos-item-exam-list',
          name: 'eduDailyInfosItemExamList',
          component: resolve => require(['@/components/companyAccount/accountEdu/eduDailyInfosItemExamList.vue'], resolve)
        },
        /*
         * 刘杰 2018-9-18
         * 增加履职报告
         * */
        //履职报告
        {
          path: 'data-report-list',
          name: 'dataReportList',
          component: resolve => require(['@/components/companyAccount/dataReport/dataReportList.vue'], resolve)
        },
        {
          path: 'data-report-detail',
          name: 'dataReportDetail',
          component: resolve => require(['@/components/companyAccount/dataReport/dataReportDetail.vue'], resolve)
        },

        {
          path: 'data-report-detail-edit',
          name: 'dataReportDetailEdit',
          component: resolve => require(['@/components/companyAccount/dataReport/dataReportDetailEdit.vue'], resolve)
        },
        {
          path: 'data-report-detail-subcompany',
          name: 'dataReportDetailSubCompany',
          component: resolve => require(['@/components/companyAccount/dataReport/dataReportDetailSubCompany.vue'], resolve)
        },
        {
          path: 'data-report-system-data',
          name: 'dataReportSystemData',
          component: resolve => require(['@/components/companyAccount/dataReport/dataReportSystemData.vue'], resolve)
        },
        {
          path: 'data-report-templet-list',
          name: 'dataReportTempletList',
          component: resolve => require(['@/components/companyAccount/dataReport/dataReportTempletList.vue'], resolve)
        },
        {
          path: 'data-report-templet-edit',
          name: 'dataReportTempletEdit',
          component: resolve => require(['@/components/companyAccount/dataReport/dataReportTempletEdit.vue'], resolve)
        },
        {
          path: 'data-report-templet-view',
          name: 'dataReportTempletView',
          component: resolve => require(['@/components/companyAccount/dataReport/dataReportTempletView.vue'], resolve)
        },
        //绩效考核台账
        {
          path: 'user-account-kpi',
          name: 'userAccountKPI',
          component: resolve => require(['@/components/companyAccount/accountKPI/userAccountKPI.vue'], resolve)
        },


        // 登录查询
        {
          path: 'login-statistics-index',
          name: 'loginStatisticsIndex',
          component: resolve => require(['@/components/companyAccount/loginStatistics/index.vue'], resolve)
        },


      ]
    },
    /*
    * 教育培训 by YANG 2018-04-17
    * */
    {
      path: '/edu-training-menu',
      name: 'eduTrainingMenu',
      component: EduTrainingMenu,
      beforeEnter: (to, from, next) => {
        if (tool.getStorage('SAFE_PLATFORM_MENU').subMenu['eduTrainingMenu']) {
          next();
        } else {
          next(from.path);//返回原来的页面
        }
      },
      children: [
        // 需求调研----首页
        {
          path: 'demand-survey-index',
          name: 'demandSurveyIndex',
          component: resolve => require(['@/components/eduTraining/demandSurvey/index.vue'], resolve)
        },
        // 需求调研---发布者----首页
        {
          path: 'demand-survey-publish-index',
          name: 'demandSurveyPublishIndex',
          component: resolve => require(['@/components/eduTraining/demandSurvey/publish/index.vue'], resolve)
        },
        // 需求调研---发布者---添加
        {
          path: 'demand-survey-publish-add',
          name: 'demandSurveyPublishAdd',
          component: resolve => require(['@/components/eduTraining/demandSurvey/publish/add.vue'], resolve)
        },
        // 需求调研---发布者---查看下级需求调查
        {
          path: 'demand-survey-publish-lower',
          name: 'demandSurveyPublishLower',
          component: resolve => require(['@/components/eduTraining/demandSurvey/publish/lower.vue'], resolve)
        },
        // 需求调研---员工----首页
        {
          path: 'demand-survey-staff-index',
          name: 'demandSurveyStaffIndex',
          component: resolve => require(['@/components/eduTraining/demandSurvey/staff/index.vue'], resolve)
        },
        // 需求调研---员工---添加
        {
          path: 'demand-survey-staff-add',
          name: 'demandSurveyStaffAdd',
          component: resolve => require(['@/components/eduTraining/demandSurvey/staff/add.vue'], resolve)
        },
        // 培训计划--首页
        {
          path: 'training-plan-index',
          name: 'trainingPlanIndex',
          component: resolve => require(['@/components/eduTraining/trainingPlan/index.vue'], resolve)
        },
        // 培训计划--添加
        {
          path: 'training-plan-add',
          name: 'trainingPlanAdd',
          component: resolve => require(['@/components/eduTraining/trainingPlan/add.vue'], resolve)
        },
        // 培训计划--查看下级培训计划
        {
          path: 'training-plan-lower',
          name: 'trainingPlanLower',
          component: resolve => require(['@/components/eduTraining/trainingPlan/lower.vue'], resolve)
        },
        // 持证培训--首页
        {
          path: 'certificate-training-index',
          name: 'certificateTrainingIndex',
          component: resolve => require(['@/components/eduTraining/certificateTraining/index.vue'], resolve)
        },
        // 持证培训--添加
        {
          path: 'certificate-training-add',
          name: 'certificateTrainingAdd',
          component: resolve => require(['@/components/eduTraining/certificateTraining/add.vue'], resolve)
        },
        // 三级培训--首页
        {
          path: 'three-level-training-index',
          name: 'threeLevelTrainingIndex',
          component: resolve => require(['@/components/eduTraining/threeLevelTraining/index.vue'], resolve)
        },
        // 三级培训--查看
        {
          path: 'three-level-training-view',
          name: 'threeLevelTrainingView',
          component: resolve => require(['@/components/eduTraining/threeLevelTraining/view.vue'], resolve)
        },
        // 三级培训--公司--添加
        {
          path: 'three-level-training-company-add',
          name: 'threeLevelTrainingCompanyAdd',
          component: resolve => require(['@/components/eduTraining/threeLevelTraining/company/add.vue'], resolve)
        },
        // 三级培训--公司--修改
        {
          path: 'three-level-training-company-edit',
          name: 'threeLevelTrainingCompanyEdit',
          component: resolve => require(['@/components/eduTraining/threeLevelTraining/company/edit.vue'], resolve)
        },
        // 三级培训--部门---添加
        {
          path: 'three-level-training-department-add',
          name: 'threeLevelTrainingDepartmentAdd',
          component: resolve => require(['@/components/eduTraining/threeLevelTraining/department/add.vue'], resolve)
        },
        // 三级培训--部门---编辑
        {
          path: 'three-level-training-department-edit',
          name: 'threeLevelTrainingDepartmentEdit',
          component: resolve => require(['@/components/eduTraining/threeLevelTraining/department/edit.vue'], resolve)
        },
        // 三级培训--班组---添加
        {
          path: 'three-level-training-team-add',
          name: 'threeLevelTrainingTeamAdd',
          component: resolve => require(['@/components/eduTraining/threeLevelTraining/team/add.vue'], resolve)
        },
        // 三级培训--班组---编辑
        {
          path: 'three-level-training-team-edit',
          name: 'threeLevelTrainingTeamEdit',
          component: resolve => require(['@/components/eduTraining/threeLevelTraining/team/edit.vue'], resolve)
        },
        // 转岗培训--首页
        {
          path: 'transfer-post-training-index',
          name: 'transferPostTrainingIndex',
          component: resolve => require(['@/components/eduTraining/transferPostTraining/index.vue'], resolve)
        },
        // 转岗培训--添加
        {
          path: 'transfer-post-training-add',
          name: 'transferPostTrainingAdd',
          component: resolve => require(['@/components/eduTraining/transferPostTraining/add.vue'], resolve)
        },
        // 日常培训--首页
        {
          path: 'daily-training-index',
          name: 'dailyTrainingIndex',
          component: resolve => require(['@/components/eduTraining/dailyTraining/index.vue'], resolve)
        },
        // 日常培训--添加
        {
          path: 'daily-training-add',
          name: 'dailyTrainingAdd',
          component: resolve => require(['@/components/eduTraining/dailyTraining/add.vue'], resolve)
        },
        // 日常培训--查看
        {
          path: 'daily-training-view',
          name: 'dailyTrainingView',
          component: resolve => require(['@/components/eduTraining/dailyTraining/view.vue'], resolve)
        },
        // 日常培训--进程---未发布
        {
          path: 'daily-training-process-published',
          name: 'dailyTrainingProcessPublished',
          component: resolve => require(['@/components/eduTraining/dailyTraining/process/published.vue'], resolve)
        },
        // 日常培训--进程---已发布
        {
          path: 'daily-training-process-unpublished',
          name: 'dailyTrainingProcessUnpublished',
          component: resolve => require(['@/components/eduTraining/dailyTraining/process/unpublished.vue'], resolve)
        },
        // 日常培训--进程---进行中
        {
          path: 'daily-training-process-haveInHand',
          name: 'dailyTrainingProcessHaveInHand',
          component: resolve => require(['@/components/eduTraining/dailyTraining/process/haveInHand.vue'], resolve)
        },
        // 日常培训--进程---待评价
        {
          path: 'daily-training-process-toBeEvaluated',
          name: 'dailyTrainingProcessToBeEvaluated',
          component: resolve => require(['@/components/eduTraining/dailyTraining/process/toBeEvaluated.vue'], resolve)
        },
        // 日常培训--进程---已完结
        {
          path: 'daily-training-process-haveFinished',
          name: 'dailyTrainingProcessHaveFinished',
          component: resolve => require(['@/components/eduTraining/dailyTraining/process/haveFinished.vue'], resolve)
        },
        /*
        *  安全教育---党员教育系统
        * */



        // 知识库--列表
        {
          path: 'safety-education-knowledge-index',
          name: 'safetyEducationKnowledgeIndex',
          component: resolve => require(['@/components/eduTraining/safetyEducation/knowledge/index.vue'], resolve)
        },


        // 知识库--新增
        {
          path: 'safety-education-knowledge-add',
          name: 'safetyEducationKnowledgeAdd',
          component: resolve => require(['@/components/eduTraining/safetyEducation/knowledge/add.vue'], resolve)
        },
        // 知识库--查看
        {
          path: 'safety-education-knowledge-view',
          name: 'safetyEducationKnowledgeView',
          component: resolve => require(['@/components/eduTraining/safetyEducation/knowledge/view.vue'], resolve)
        },




        // 文章--列表
        {
          path: 'safety-education-article-index',
          name: 'safetyEducationArticleIndex',
          component: resolve => require(['@/components/eduTraining/safetyEducation/article/index.vue'], resolve)
        },
        // 文章--查看
        /*     {
               path:'safety-education-article-view',
               name:'safetyEducationArticleView',
               component: resolve=>require(['@/components/eduTraining/safetyEducation/article/view.vue'],resolve)
             },*/
        /*  {
            path:'safety-education-study-view',
            name:'safetyEducationStudyView',
            component: resolve=>require(['@/components/eduTraining/safetyEducation/study/view.vue'],resolve)
          },*/
        // 文章-查看
        {
          path: 'safety-education-study-article-view',
          name: 'safetyEducationStudyArticleView',
          component: resolve => require(['@/components/eduTraining/safetyEducation/study/articleView.vue'], resolve)
        },
        // 视频-查看
        {
          path: 'safety-education-study-video-view',
          name: 'safetyEducationStudyVideoView',
          component: resolve => require(['@/components/eduTraining/safetyEducation/study/videoView.vue'], resolve)
        },
        // 文章--添加
        {
          path: 'safety-education-article-add',
          name: 'safetyEducationArticleAdd',
          component: resolve => require(['@/components/eduTraining/safetyEducation/article/add.vue'], resolve)
        },



        // 视频--列表
        {
          path: 'safety-education-video-index',
          name: 'safetyEducationVideoIndex',
          component: resolve => require(['@/components/eduTraining/safetyEducation/video/index.vue'], resolve)
        },
        // 视频--查看
        {
          path: 'safety-education-video-view',
          name: 'safetyEducationVideoView',
          component: resolve => require(['@/components/eduTraining/safetyEducation/video/view.vue'], resolve)
        },
        // 视频--添加
        {
          path: 'safety-education-video-add',
          name: 'safetyEducationVideoAdd',
          component: resolve => require(['@/components/eduTraining/safetyEducation/video/add.vue'], resolve)
        },
        // 积分排行版
        {
          path: 'safety-education-score-sort-index',
          name: 'safetyEducationScoreSortIndex',
          component: resolve => require(['@/components/eduTraining/safetyEducation/scoreSort/index.vue'], resolve)
        },
        {
          path: 'safety-education-study-index',
          name: 'safetyEducationStudyIndex',
          component: resolve => require(['@/components/eduTraining/safetyEducation/study/index.vue'], resolve)
        },
        // 报表统计--首页
        {
          path: 'report-statistics-index',
          name: 'ReportStatisticsIndex',
          component: resolve => require(['@/components/eduTraining/reportStatistics/index.vue'], resolve)
        },
        // 报表统计--新增
        {
          path: 'report-statistics-add',
          name: 'ReportStatisticsAdd',
          component: resolve => require(['@/components/eduTraining/reportStatistics/add.vue'], resolve)
        },
        // 报表统计--汇总查看
        {
          path: 'report-statistics-view',
          name: 'ReportStatisticsView',
          component: resolve => require(['@/components/eduTraining/reportStatistics/view.vue'], resolve)
        },
        // 报表统计--我的待办
        {
          path: 'report-statistics-backlog',
          name: 'ReportStatisticsBacklog',
          component: resolve => require(['@/components/eduTraining/reportStatistics/backlog.vue'], resolve)
        },
        // 报表统计--我的待办--查看
        {
          path: 'report-statistics-backlog-view',
          name: 'ReportStatisticsBacklogView',
          component: resolve => require(['@/components/eduTraining/reportStatistics/backlogView.vue'], resolve)
        },
        /*// 我的学习
        {
          path:'safety-education-study-index',
          name:'safetyEducationStudyIndex',
          component: resolve=>require(['@/components/eduTraining/safetyEducation/study/index.vue'],resolve)
        },*/
      ]
    },
    /*
     * 教育培训 END
     * */
    /*
     * 安全投入 by YANG 2018-05-18
     * */
    {
      path: '/safety-input-menu',
      name: 'safetyInputMenu',
      component: safetyInputMenu,
      children: [
        // 费用预算----首页
        /*{
          path:'cost-budget-index',
          name:'costBudgetIndex',
          component: resolve=>require(['@/components/safetyInput/costBudget/index.vue'],resolve)
        },*/
        // 费用预算----首页---集团
        {
          path: 'cost-budget-index-group',
          name: 'costBudgetIndexGroup',
          component: resolve => require(['@/components/safetyInput/costBudget/index-group.vue'], resolve)
        },
        // 费用预算----首页---本级
        {
          path: 'cost-budget-index-level',
          name: 'costBudgetIndexLevel',
          component: resolve => require(['@/components/safetyInput/costBudget/index-level.vue'], resolve)
        },
        // 费用预算----首页----公司
        {
          path: 'cost-budget-index-company',
          name: 'costBudgetIndexCompany',
          component: resolve => require(['@/components/safetyInput/costBudget/index-company.vue'], resolve)
        },
        // 费用预算----首页---部门
        {
          path: 'cost-budget-index-department',
          name: 'costBudgetIndexDepartment',
          component: resolve => require(['@/components/safetyInput/costBudget/index-department.vue'], resolve)
        },
        // 费用预算----添加
        {
          path: 'cost-budget-item',
          name: 'costBudgetItem',
          component: resolve => require(['@/components/safetyInput/costBudget/item.vue'], resolve)
        },
        // 费用预算----单独打印
        {
          path: 'cost-budget-print-one',
          name: 'costBudgetPrintOne',
          component: resolve => require(['@/components/safetyInput/costBudget/printOne.vue'], resolve)
        },
        // 费用预算----单独打印---后台打印
        {
          path: 'cost-budget-print-one-after',
          name: 'costBudgetPrintOneAfter',
          component: resolve => require(['@/components/safetyInput/costBudget/printOneAfter.vue'], resolve)
        },
        // 费用预算----合并打印
        {
          path: 'cost-budget-print-all',
          name: 'costBudgetPrintAll',
          component: resolve => require(['@/components/safetyInput/costBudget/printAll.vue'], resolve)
        },
        // 费用投入----首页
        {
          path: 'cost-input-index',
          name: 'costInputIndex',
          component: resolve => require(['@/components/safetyInput/costInput/index.vue'], resolve)
        },
        // 费用投入----明细
        {
          path: 'cost-input-item',
          name: 'costInputItem',
          component: resolve => require(['@/components/safetyInput/costInput/item.vue'], resolve)
        },

        // 费用分析----首页
        {
          path: 'cost-analysis-index',
          name: 'costAnalysisIndex',
          component: resolve => require(['@/components/safetyInput/costAnalysis/index.vue'], resolve)
        },
        // 预算跟踪----首页
        {
          path: 'budgetary-tracking-index',
          name: 'budgetaryTrackingIndex',
          component: resolve => require(['@/components/safetyInput/budgetaryTracking/index.vue'], resolve)
        },
        // 受伤获赔----首页
        {
          path: 'compensationForInjury-index',
          name: 'compensationForInjuryIndex',
          component: resolve => require(['@/components/safetyInput/compensationForInjury/index.vue'], resolve)
        },
        // 受伤获赔----明细
        {
          path: 'compensationForInjury-item',
          name: 'compensationForInjuryItem',
          component: resolve => require(['@/components/safetyInput/compensationForInjury/item.vue'], resolve)
        },
      ]
    },
    /*
     * 安全投入 END
     * */

  ]
});

router.beforeEach(({ name }, from, next) => {
  if (name === 'login') {//进入登陆页面
    if (localStorage.LOGIN_USER) {
      localStorage.removeItem('LOGIN_USER');
    }
    if (localStorage.SAFE_PLATFORM_USERNAME) {
      localStorage.removeItem('SAFE_PLATFORM_USERNAME');
    }
    if (localStorage.SAFE_PLATFORM_MENU) {
      localStorage.removeItem('SAFE_PLATFORM_MENU');
    }
    next();
  } else if (name === 'esignPage') {
    next();

  } else {//进入其他页面
    if (localStorage.SAFE_PLATFORM_USERNAME) {//是否登陆过
      next();
    } else {
      next({ name: 'login' });
      if (localStorage.LOGIN_USER) {
        localStorage.removeItem('LOGIN_USER');
      }
      if (localStorage.SAFE_PLATFORM_MENU) {
        localStorage.removeItem('SAFE_PLATFORM_MENU');
      }
    }
  }

});

export default router;
