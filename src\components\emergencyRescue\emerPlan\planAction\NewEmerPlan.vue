<template>
  <div id="newEmerPlan">
    <div class="background-style">
      <el-col :span="16" :offset="4" class="primary-background-title">新增应急预案</el-col>
      <el-col :span="16" :offset="4">
        <el-form :model="form" :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm">
          <el-col :span="24">
            <el-col :span="12">
              <el-form-item label="分类：" prop="classify">
                <el-cascader
                  :options="cascaderOptions"
                  v-model="form.classify"
                  @change="planTypeClick"
                  style="width: 100%"
                  placeholder="请选择">
                </el-cascader>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="级别：" prop="level">
                <el-select v-model="form.level" placeholder="请选择" @change="levelClick" style="width: 100%">
                  <el-option
                    v-for="item in levelOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-col>
          <el-col :span="24">
            <el-form-item label="参考预案：" prop="referPlan">
              <el-select v-model="form.referPlan" placeholder="请选择" @change="referPlanClick" style="width: 100%">
                <el-option
                  v-for="item in planOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="预案名称：" prop="planName">
              <el-input v-model="form.planName" placeholder="请输入预案名称"></el-input>
            </el-form-item>
          </el-col>

          <!--半自编辑部分-->
          <el-col :span="24" style="background-color: rgb(236,248,255);padding: 10px;border-radius: 5px;margin: 10px 0 10px 0">
            <div style="float: left">
              <p style="color: #c0c0c0;margin-left: 20px;letter-spacing: 2px;">请选择参考预案，蓝色区域内容将自动填充</p>
            </div>
            <el-col :span="24">
              <el-col :span="12">
                <el-form-item label="预警信号：" prop="planFlag">
                  <el-select v-model="form.planFlag" placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in planFlagOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.label">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="上报间隔：" prop="interval">
                  <el-input placeholder="请输入时长" v-model="form.interval" type="number"><template slot="append">小时</template></el-input>
                </el-form-item>
              </el-col>
            </el-col>
            <!--<el-col :span="24">-->
              <!--<el-col :span="12">-->
                <!--<el-form-item label="通知对象：" prop="noticedPeople">-->
                  <!--<el-input v-model="form.noticedPeople" placeholder="请点击右侧按钮选择人员" readonly="readonly"></el-input>-->
                <!--</el-form-item>-->
              <!--</el-col>-->
              <!--<el-col :span="12">-->
                <!--<el-button type="primary" size="medium" style="margin-left: 20px;margin-top: 2px" @click="choosePeople">选择人员</el-button>-->
              <!--</el-col>-->
            <!--</el-col>-->

            <!--全自编表单-->
            <el-col :span="24">
              <el-form :model="editForm" :rules="editRules" ref="wholeRuleForm" label-width="20px" class="demo-ruleForm">
                <el-col :span="24">
                  <el-col :span="5">
                    <el-form-item  prop="firstDept">
                      <el-input  type="textarea" :autosize="{ minRows: 1,maxRows: 3}" v-model="editForm.firstDept" placeholder="操作实施者"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="19">
                    <el-form-item  label=":" prop="firstContent">
                      <el-input type="textarea" :autosize="{ minRows: 3}" v-model="editForm.firstContent" placeholder="请输入具体操作内容"></el-input>
                    </el-form-item>
                  </el-col>
                </el-col>
                <el-col :span="24">
                  <el-col :span="5">
                    <el-form-item  prop="secondDept">
                      <el-input type="textarea" :autosize="{ minRows: 1,maxRows: 3}" v-model="editForm.secondDept" placeholder="操作实施者"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="19">
                    <el-form-item  label=":" prop="secondContent">
                      <el-input type="textarea" :autosize="{ minRows: 3}" v-model="editForm.secondContent" placeholder="请输入具体操作内容"></el-input>
                    </el-form-item>
                  </el-col>
                </el-col>
                <el-col :span="24">
                  <el-col :span="5">
                    <el-form-item  prop="thirdDept">
                      <el-input type="textarea" :autosize="{ minRows: 1,maxRows: 3}" v-model="editForm.thirdDept" placeholder="操作实施者"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="19">
                    <el-form-item  label=":" prop="thirdDept01">
                      <el-input v-model="editForm.thirdDept01" placeholder="操作实施者" style="width: 150px;margin-bottom: 10px"></el-input>
                      <el-button style="margin-bottom: 10px" size="small" type="success" @click="openAddDialog('thirdContent01')">添加知识点</el-button>
                      <el-input type="textarea" :autosize="{ minRows: 3}" v-model="editForm.thirdContent01" placeholder="请输入具体操作内容"></el-input>
                    </el-form-item>
                    <el-form-item prop="thirdDept02">
                      <el-input v-model="editForm.thirdDept02" placeholder="操作实施者" style="width: 150px;margin-bottom: 10px"></el-input>
                      <el-button style="margin-bottom: 10px" size="small" type="success" @click="openAddDialog('thirdContent02')">添加知识点</el-button>
                      <el-input type="textarea" :autosize="{ minRows: 3}" v-model="editForm.thirdContent02" placeholder="请输入具体操作内容"></el-input>
                    </el-form-item>
                  </el-col>
                </el-col>
                <el-col :span="24">
                  <el-col :span="5">
                    <el-form-item  prop="fourthDept">
                      <el-input type="textarea" :autosize="{ minRows: 1,maxRows: 3}" v-model="editForm.fourthDept" placeholder="操作实施者"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="19">
                    <el-form-item  label=":" prop="fourthDeptContent">
                      <el-input type="textarea" :autosize="{ minRows: 3}" v-model="editForm.fourthDeptContent" placeholder="请输入具体操作内容"></el-input>
                    </el-form-item>
                  </el-col>
                </el-col>
              </el-form>
            </el-col>
            <!--全自编表单结束-->

            <el-col :span="24">
              <el-form-item label="应急物资：" prop="goodsArray">
                <el-col :span="2" v-if="isShowGoodsTypeList===false">
                  <el-button type="primary" size="small" @click="isShowGoodsTypeList = true">添加</el-button>
                </el-col>
                <el-col :span="24" v-else>
                  <el-select
                    multiple
                    filterable
                    remote
                    reserve-keyword
                    clearable
                    placeholder="请输入物资名称后选择"
                    :loading="goodsLoading"
                    :remote-method="goodsTypeListHandle"
                    v-model="goodsTypeListArr"
                    style="width: 400px;">
                    <el-option
                      v-for="item in goodsOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item">
                    </el-option>
                  </el-select>
                  <el-button type="success" plain size="small" @click="goodsTypeListChangeHandle">确定</el-button>
                  <el-button type="info" plain size="small" @click="isShowGoodsTypeList = false">取消</el-button>
                </el-col>
                <el-col :span="24">
                  <el-table
                    :data="form.goodsArray"
                    style="width: 100%">
                    <el-table-column
                      type="index"
                      width="50">
                    </el-table-column>
                    <el-table-column
                      prop="label"
                      label="物资名称"
                      width="180">
                    </el-table-column>
                    <el-table-column
                      label="操作"
                      align="center"
                      label-class-name="inner-header-style">
                      <template slot-scope="scope">
                        <el-button type="danger" size="mini" @click="emgHandleListsDelHandle(scope.$index)">删除</el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-col>
              </el-form-item>
            </el-col>
          </el-col>
          <el-col :span="24" style="margin-top: 10px">
            <el-form-item label="附件：">
              <div style="width: 100%;float:left">
                <el-upload
                  class="upload-demo"
                  ref="upload"
                  :action="uploadUrl"
                  multiple
                  :with-credentials="cookies"
                  :http-request="ossUploadRequest"
                  :data="fileUploadParams"
                  :file-list="fileList"
                  :auto-upload="false"
                  style="width: 300px;margin-bottom: 10px;">
                  <el-button size="small" type="primary">选取上传附件</el-button>
                </el-upload>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="24" style="margin-top: 10px">
            <el-form-item>
              <el-button style="float: right;margin-left: 20px" @click="returnClick()">返回</el-button>
              <el-button type="primary" style="float: right" @click="saveClick()">保存</el-button>
            </el-form-item>
          </el-col>
        </el-form>
      </el-col>
    </div>

    <!--对话框开始-->
    <el-dialog title="添加知识点" :visible.sync="addKnowledgePoint">
      <el-select
        v-model="searchTag"
        multiple
        filterable
        remote
        reserve-keyword
        clearable
        placeholder="请输入标签名后选择,可多选"
        @change="labelClick"
        :remote-method="remoteTag"
        :loading="tagLoading"
        style="width: 300px">
        <el-option
          v-for="item in tagOptions"
          :key="item.value"
          :label="item.label"
          :value="item.label">
        </el-option>
      </el-select>
      <el-button type="primary" style="margin-left: 20px;margin-top: 5px" @click="searchKnowledgePointClick">搜索</el-button>
      <el-table
        :data="knowledgeTable"
        border
        tooltip-effect="light"
        style="width: 100%;margin-top: 10px"
        @selection-change="handleSelectionChange">
        <el-table-column
          type="selection"
          width="55"
          label-class-name="inner-header-style">
        </el-table-column>
        <el-table-column
          prop="content"
          label="知识点"
          show-overflow-tooltip
          label-class-name="inner-header-style"
          min-width="200">
        </el-table-column>
      </el-table>
      <div style="margin-top: 10px">
        <el-pagination
          background
          layout="prev, pager, next"
          :current-page="currentKnowledgePage"
          :total="totalKnowledgeItem"
          @current-change="currentKnowledgePageClick">
        </el-pagination>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelAddKnowledgePoint">取 消</el-button>
        <el-button type="primary" @click="determineAddKnowledgePoint">确 定</el-button>
      </div>
    </el-dialog>
    <!--对话框结束-->
  </div>
</template>
<script>
  import dealData from '../../../../assets/functions/dealData'
  export default {
    components: {},
    name: 'newEmerPlan',
    data() {
      return {
        //------------------预案分类---------------------
        cascaderOptions: [],
        tempTypeString:[],
        levelOptions:[
          {value:'应急警报', label:'应急警报'},
          {value:'4级', label:'4级'},
          {value:'3级', label:'3级'},
          {value:'2级', label:'2级'},
          {value:'1级', label:'1级'}
        ],
        planOptions:[],

        //------------------表单数据---------------------
        form:{
          classify:[],
          level:'',
          referPlan:'自定义',
          planName:'',
          planFlag:'',
          interval:'',
          noticedPeople:'',
          goodsArray:[],
        },
        editForm:{
          firstDept:'',
          firstContent:'',
          secondDept:'',
          secondContent:'',
          thirdDept:'',
          thirdDept01:'',
          thirdDept02:'',
          thirdContent01:'',
          thirdContent02:'',
          fourthDept:'',
          fourthDeptContent:'',
        },

        addItem:true,
        editItem:false,

        planFlagOptions:[
          {value:'蓝色预警',label:'蓝色预警'},
          {value:'黄色预警',label:'黄色预警'},
          {value:'橙色预警',label:'橙色预警'},
          {value:'红色预警',label:'红色预警'}
        ],
        // 物资类型列表
        isShowGoodsTypeList : false,
        goodsTypeListArr:[],
        goodsLoading:false,
        goodsOptions:[],

        //---------------------表单规则-----------------------
        rules:{
          classify:[{required:true,message:'请选择分类',trigger:'change'}],
          level:[{required:true,message:'请选择等级',trigger:'change'}],
          planFlag:[{required:true,message:'请选择预警信号',trigger:'change'}],
          planName:[{required:true,message:'请输入预案名称',trigger:'change'}],
        },
        editRules:{
          firstDept:[{required:true,message:'请填写机构名称',trigger:'change'}],
          secondDept:[{required:true,message:'请填写机构名称',trigger:'change'}],
          thirdDept:[{required:true,message:'请填写机构名称',trigger:'change'}]
        },

        //-----------------------标签对话框------------------------------
        addKnowledgePoint:false,
        intentStr:'',
        tagLoading:false,

        searchTag:[],
        knowledgeTable:[],
        totalKnowledgeItem:0,
        currentKnowledgePage:1,

        selectedArray:[],

        //上传和下载
        uploadUrl: '',
        cookies: true,
        fileUploadParams: {
          contentId: 0,
          contentType: 0
        },
        fileList:[],
        upFileListLength:'',
      }
    },
    computed:{
      tagOptions:function () {
        return this.$store.state.emergencyData.referLabels;
      }
    },
    created:function () {
      this.getPlanType();
      this.editPlanType();
      this.referPlanClick('自定义');

      this.searchKnowledgePointClick();//让知识点搜索先有数据
    },
    watch:{
      $route(to, from){
        if(from.name==='emerPlan'&&this.$route.name==='newEmerPlan'){
          this.getPlanType();
          this.editPlanType();
          this.referPlanClick('自定义');
        }
      }
    },
    methods:{
      //------------------------初始操作---------------------
      //获取预案所有分类
      getPlanType:function () {

        this.$http.get('emgType/getAll/'+this.$tool.getStorage('LOGIN_USER').companyId).then(function (res) {
          this.editPlanTypeArray(res.data.data);
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message({
            showClose: true,
            message: '网络错误，请尝试重登录',
            type: 'error'
          });
        }.bind(this));
      },
      //编辑预案所有分类
      editPlanTypeArray:function (typeTree) {
        this.cascaderOptions.splice(0);
        for(let i=0;i<typeTree.length;i++){
          let tempArray={value:typeTree[i].id,label:typeTree[i].typeName};
          if(typeTree[i].subTypes.length){
            tempArray.children=[];
            for(let j=0;j<typeTree[i].subTypes.length;j++){
              tempArray.children.push({value:typeTree[i].subTypes[j].id,label:typeTree[i].subTypes[j].typeName});
            }
          }
          this.cascaderOptions.push(tempArray);
        }
      },
      //-----------------------页面数据自动填充--------------
      //填充预案类型
      editPlanType:function () {
        if(this.$route.params.firstClass.value){
          if(this.$route.params.secondClass.value){
            this.form.classify[0]=this.$route.params.firstClass.value;
            this.form.classify[1]=this.$route.params.secondClass.value;
            this.tempTypeString[0]=this.$route.params.firstClass.label;
            this.tempTypeString[1]=this.$route.params.secondClass.label;
          }else{
            this.form.classify[0]=this.$route.params.firstClass.value;
            this.tempTypeString[0]=this.$route.params.firstClass.label;
          }
        }

        if(this.$route.params.level){
          this.form.level=this.$route.params.level;
        }
        this.changePlanName();
        this.searchSimilarPlan();
      },
      //预案类型改变
      planTypeClick:function (val) {
        let tempIndex=this.cascaderOptions.findIndex(function (elem,index) {
          return elem.value===val[0];
        });
        this.tempTypeString[0]=this.cascaderOptions[tempIndex].label;
        if(val[1]){
          let tempItem=this.cascaderOptions[tempIndex].children.find(function (elem,index) {
            return elem.value===val[1];
          });
          this.tempTypeString[1]=tempItem.label;
        }else{
          this.tempTypeString[1]=null;
        }
        this.changePlanName();
        this.searchSimilarPlan();
      },
      //预案等级改变
      levelClick:function () {
        this.changePlanName();
      },
      //填写预案名称
      changePlanName:function () {
        if(this.tempTypeString[1]||this.tempTypeString[0]){
          if(this.form.level){
            this.form.planName=(this.tempTypeString[1]?this.tempTypeString[1]:this.tempTypeString[0])+this.form.level;
          }else{
            this.form.planName=this.tempTypeString[1]?this.tempTypeString[1]:this.tempTypeString[0];
          }
          this.form.planName+='预案';
        }else{
          this.form.planName='';
        }
      },
      //查找参考预案
      searchSimilarPlan:function () {
        this.planOptions.splice(0);
        if(this.form.classify[0]||this.form.classify[1]){
          let params={pageSize:10,pageCurrent:1};
          if(this.form.classify[1]){
            params.typeId=this.form.classify[1];
          }else{
            params.typeId=this.form.classify[0];
          }
          params.companyId=this.$tool.getStorage('LOGIN_USER').companyId;
          this.$http.post('emgPlan/find',params).then(function (res) {
            if(res.data.data.list.length){
              for(let i=0;i<res.data.data.list.length;i++){
                this.planOptions.push({value:res.data.data.list[i].id,label:res.data.data.list[i].name});
              }
            }
          }.bind(this)).catch(function (err) {
            console.log(err);
            this.$message({
              showClose: true,
              message: '网络错误，请尝试重登录',
              type: 'error'
            });
          }.bind(this));
        }
        this.planOptions.push({value:'自定义',label:'自定义'});
      },
      //填写自定义表单
      referPlanClick:function (val) {
        if(val==='自定义'){
          this.clearForm();
          this.form.planFlag='红色预警';
          this.form.interval=12;
          this.form.noticedPeople='全体人员';

          this.editForm.firstDept='应急领导小组';
          this.editForm.secondDept='应急管理办公室';
          this.editForm.thirdDept='应急指挥部';
          this.editForm.thirdDept01='应急启动响应要求';
          this.editForm.thirdDept02='分级响应要求';
          this.editForm.fourthDept='解除应急通知模板';
          this.editForm.fourthDeptContent='';
        }else if(val){
          this.$http.post('emgPlan/find',{id:val,companyId:this.$tool.getStorage('LOGIN_USER').companyId}).then(function (res) {
            if(res.data.data.list.length){
              this.clearForm();
              let tempFormData=res.data.data.list[0];

              this.form.planFlag=tempFormData.warnSignal;
              this.form.interval=tempFormData.timeInterval;
              this.form.noticedPeople='全体人员';

              this.editForm.firstDept=tempFormData.leaderRequireName;
              this.editForm.firstContent=tempFormData.leaderRequire;
              this.editForm.secondDept=tempFormData.officeRequireName;
              this.editForm.secondContent=tempFormData.officeRequire;
              this.editForm.thirdDept=tempFormData.commandRequireName;
              this.editForm.thirdDept01=tempFormData.startupRequireName;
              this.editForm.thirdContent01=tempFormData.startupRequire;
              this.editForm.thirdDept02=tempFormData.levelRequireName;
              this.editForm.thirdContent02=tempFormData.levelRequire;
              this.editForm.fourthDeptContent=tempFormData.relieveTemplate;
              this.form.goodsArray.splice(0);
              for(let i=0;i<tempFormData.emgPlanGoods.length;i++){
                this.form.goodsArray.push({value:tempFormData.emgPlanGoods[i].id,label:tempFormData.emgPlanGoods[i].name});
              }
            }
          }.bind(this)).catch(function (err) {
            console.log(err);
            this.$message({
              showClose: true,
              message: '网络错误，请尝试重登录',
              type: 'error'
            });
          }.bind(this));
        }
      },
      //清空表单中的指定项
      clearForm:function () {
        this.form.planFlag='';
        this.form.interval='';
        this.form.noticedPeople='';
        this.editForm.firstDept='';
        this.editForm.firstContent='';
        this.editForm.secondDept='';
        this.editForm.secondContent='';
        this.editForm.thirdDept='';
        this.editForm.thirdDept01='';
        this.editForm.thirdContent01='';
        this.editForm.thirdDept02='';
        this.editForm.thirdContent02='';
        this.editForm.fourthDeptContent='';
        this.form.goodsArray.splice(0);
      },
      //-----------------------表单操作----------------------
      //物资操作
      goodsTypeListHandle(val){
        let params = new URLSearchParams;
        this.goodsLoading = true;
        params.append("name", val);
        params.append("pageSize", 20);
        params.append("pageCurrent", 1);
        this.$http.post('emgGoodsType/find', params).then(function (res) {
          if (res.data.data.list.length !== 0) {
            this.editGoodsOptions(res.data.data.list);
          }
          this.goodsLoading = false;
        }.bind(this)).catch(function (err) {
          console.log(err);
        });
      },
      editGoodsOptions:function (list) {
        this.goodsOptions.splice(0);
        for(let i=0;i<list.length;i++){
          let temp={value:list[i].id,label:list[i].name};
          this.goodsOptions.push(temp);
        }
      },
      // 物资列表选择，并且映射到table中
      goodsTypeListChangeHandle(){
        let goodsArr = this.form.goodsArray.map(function(it){
          return it.label;
        })
        this.goodsTypeListArr.forEach(function(it){
          // 如果没有添加进去
          if(goodsArr.indexOf(it.label) === -1){
            this.form.goodsArray.push(it)
          }
        }.bind(this));
        this.isShowGoodsTypeList = false;
      },
      emgHandleListsDelHandle:function (index) {
        this.form.goodsArray.splice(index,1);
      },
      //------------------------交互操作-----------------------
      saveClick:function () {
        this.$refs['ruleForm'].validate((valid) => {
          if (valid) {
            this.$refs['wholeRuleForm'].validate((valid) => {
              if (valid) {
                let params=new URLSearchParams;
                if(this.form.classify[1]){
                  params.append("typeId",this.form.classify[1]);
                  params.append("topTypeId",this.form.classify[0]);
                }else{
                  params.append("typeId",this.form.classify[0]);
                  params.append("topTypeId",0);
                }

                params.append("name",this.form.planName);
                params.append("timeInterval",Number(this.form.interval));
                params.append("timeIntervalName",'上报间隔');
                params.append("warnSignal",this.form.planFlag);
                params.append("warnSignalName",'预警信号');
                params.append("respLevel",this.form.level);
                params.append("respLevelName",'级别');

                params.append("leaderRequireName",this.editForm.firstDept);
                params.append("leaderRequire",this.editForm.firstContent);
                params.append("officeRequireName",this.editForm.secondDept);
                params.append("officeRequire",this.editForm.secondContent);
                params.append("commandRequireName",this.editForm.thirdDept);
                params.append("startupRequireName",this.editForm.thirdDept01);
                params.append("startupRequire",this.editForm.thirdContent01);
                params.append("levelRequireName",this.editForm.thirdDept02);
                params.append("levelRequire",this.editForm.thirdContent02);
                params.append("relieveTemplate",this.editForm.fourthDeptContent);
                for(let i=0;i<this.form.goodsArray.length;i++){
                  params.append("emgPlanGoods["+i+"].id",this.form.goodsArray[i].value);
                  params.append("emgPlanGoods["+i+"].name",this.form.goodsArray[i].label);
                }
                params.append("companyId",this.$tool.getStorage('LOGIN_USER').companyId);
                this.$http.post('emgPlan/add',params).then(function (res) {
                  if(res.data.success){
                    this.fileUploadParams.contentId=res.data.data.id;
                    this.upFileListLength=this.$refs.upload.uploadFiles.length;
                    if(this.upFileListLength){
                      this.$refs.upload.submit();
                    }else{
                      this.createSuccessAndOut();
                    }
                  }else{
                    console.log('添加新预案失败');
                  }
                }.bind(this)).catch(function (err) {
                  console.log(err);
                  this.$message({
                    showClose: true,
                    message: '添加新预案失败，可能是网络错误，请尝试重登录',
                    type: 'error'
                  });
                  return false;
                }.bind(this));

              } else {
                console.log('error submit!!');
                return false;
              }
            });
          } else {
            console.log('error submit!!');
            return false;
          }
        });
      },
      returnClick:function () {
        this.$refs['ruleForm'].resetFields();
        this.$refs['wholeRuleForm'].resetFields();
        this.$router.go(-1);
      },
      //----------------------------通过标签查询知识点------------------------
      openAddDialog:function (str) {
        this.addKnowledgePoint=true;
        this.intentStr=str;
      },
      labelClick:function (val) {
        if(this.searchTag.length){
          this.$store.dispatch("getLabels",this.searchTag);
        }else{
          this.$store.dispatch("getLabels",[]);
        }
      },
      remoteTag:function (val) {
        let params = new URLSearchParams;
        if (val !== null&&val!=='') {
          this.tagLoading = true;
          params.append("label", val);
          params.append("pageSize", 10);
          params.append("pageCurrent", 1);
          params.append("companyId",this.$tool.getStorage('LOGIN_USER').companyId);
          this.$http.post('label/find', params).then(function (res) {
            if (res.data.data.list.length !== 0) {
              this.editTag(res.data.data.list);
            }
            this.tagLoading = false;
          }.bind(this)).catch(function (err) {
            console.log(err);
          });
        }else {
          if(this.searchTag.length===0){
            this.$store.dispatch("getLabels",[]);
          }
        }
      },
      editTag:function (list) {
        this.tagOptions.splice(0);
        for(let i=0;i<list.length;i++){
          let temp={value:list[i].label,label:list[i].label};
          this.tagOptions.push(temp);
        }
      },
      searchKnowledgePointClick:function () {
        let params=new URLSearchParams;
        params.append("pageCurrent",1);
        this.currentKnowledgePage=1;
        this.sendKnowSearchRequest(params);
      },
      currentKnowledgePageClick:function (val) {
        if(val){
          let params=new URLSearchParams;
          this.currentKnowledgePage=Number(val);
          params.append("pageCurrent",Number(val));
          this.sendKnowSearchRequest(params);
        }
      },
      sendKnowSearchRequest:function (params) {
        if(this.searchTag.length){
          for(let i=0;i<this.searchTag.length;i++){
            params.append("labels["+i+"]",this.searchTag[i]);
          }
        }
        params.append("companyId",this.$tool.getStorage('LOGIN_USER').companyId);
        this.$http.post('knowledge/find', params).then(function (res) {
          if (res.data.data.list.length !== 0) {
            this.totalKnowledgeItem=res.data.data.total;
            this.knowledgeTable=res.data.data.list;
          }else{
            this.totalKnowledgeItem=0;
            this.knowledgeTable=[];
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
        });
      },
      //多选
      handleSelectionChange:function (val) {
        if(val.length){
          this.selectedArray=val;
        }
      },
      cancelAddKnowledgePoint:function () {
        this.addKnowledgePoint=false;
      },
      determineAddKnowledgePoint:function () {
        if(this.selectedArray.length){
          let tempContent='';
          for(let i=0;i<this.selectedArray.length;i++){
            tempContent+='>>>. '+this.selectedArray[i].content+'\n';
          }
          switch (this.intentStr){
            case 'thirdContent01':
              if(this.editForm.thirdContent01.charAt(this.editForm.thirdContent01.length-1)==='\n'||!this.editForm.thirdContent01.length){
                this.editForm.thirdContent01+=tempContent;
              }else {
                this.editForm.thirdContent01+='\n'+tempContent;
              }
              break;
            case 'thirdContent02':
              if(this.editForm.thirdContent02.charAt(this.editForm.thirdContent02.length-1)==='\n'||!this.editForm.thirdContent02.length){
                this.editForm.thirdContent02+=tempContent;
              }else {
                this.editForm.thirdContent02+='\n'+tempContent;
              }
              break;
            default:
              break;
          }
        }
        this.addKnowledgePoint=false;
      },
      //----------------------------上传文件-------------------------------------

      //直接上传到阿里云上
      ossUploadRequest:function (item) {
        //获取该文件对应的sign
        this.$http.get('sys/oss/sign?contentId='+this.fileUploadParams.contentId+'&contentType='+this.fileUploadParams.contentType+'&realName='+item.file.name).then(function (res) {
          if(res.data){
            let params=new FormData();
            params.append("name",item.file.name);
            params.append("key",res.data.dir + item.file.name);
            params.append("policy",res.data.policy);
            params.append("OSSAccessKeyId",res.data.accessid);
            params.append('success_action_status','200');
            params.append("callback",res.data.callback);
            params.append("signature",res.data.signature);
            params.append("file",item.file);
            this.fileHttp.post('',params,{headers: {'Content-Type': 'multipart/form-data'}}).then(function (res) {
              if(res.data.file){
                let resultStr=dealData.decode(res.data.file);
                let resultJson=JSON.parse(resultStr);
                resultJson.name=resultJson.fileName;
                this.fileList.push(resultJson);
                if(this.fileList.length===this.upFileListLength){
                  this.createSuccessAndOut();
                }
              }else {
                this.$message.error('上传图片失败');
              }
            }.bind(this)).catch(function (err) {
              console.log(err);
              this.$message.error('上传文件失败，可在修改预案处，尝试再次上传');
            }.bind(this));
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message.error('获取唯一标识失败');
        }.bind(this));
      },

      //------------------------成功并退出-----------------------------------------
      //成功并退出
      createSuccessAndOut:function () {
        this.$message({
          showClose: true,
          message: '添加新预案成功',
          type: 'success'
        });
        this.fileList.splice(0);
        this.$router.push({name:'emerPlan'});
      },
    }
  }
</script>
<style>
</style>
