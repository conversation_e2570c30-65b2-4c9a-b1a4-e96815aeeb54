<template>
  <div id="planForm">
    <div class="background-style">
      <el-row style="padding: 0;margin: 0">
        <el-col :span="22" :offset="1" :class="titleClass">{{mainTitle}}</el-col>
      </el-row>
     <el-row style="padding: 0;margin: 0">
       <el-form ref="planForm" label-width="80px" label-position="right">
         <el-col :span="22" :offset="1">
           <div style="width: 100%">
             <div style="width: 16%;display: inline-block;float: left">
               <el-form-item label="年份" label-width="50px">
                 <el-select
                   v-model="planYear"
                   @change="changePlanName"
                   filterable
                   allow-create
                   placeholder="请选择或自定义"
                   style="width:100%;margin-right: 10px">
                   <el-option
                     v-for="item in planYearOption"
                     :key="item"
                     :label="item"
                     :value="item">
                   </el-option>
                 </el-select>
               </el-form-item>
             </div>
             <div style="width: 28%;display: inline-block;float: left">
               <el-form-item label="计划名称">
                 <el-input  v-model="tableName"></el-input>
               </el-form-item>
             </div>
             <div style="width: 28%;display: inline-block;float: left">
               <el-form-item label="文件编号">
                 <el-input  v-model="docNum" placeholder="请填写文件编号"></el-input>
               </el-form-item>
             </div>
             <div style="width: 28%;display: inline-block;float: left">
               <el-form-item label="记录编号">
                 <el-input  v-model="recordNum" placeholder="请填写记录编号"></el-input>
               </el-form-item>
             </div>
           </div>
         </el-col>
       </el-form>

       <el-col :span="22" :offset="1" style="margin-bottom: 10px">
         <div style="width: 300px;float: right">
           <el-button type="primary" icon="el-icon-plus" style="margin-left: 20px" @click="addContent">添加内容</el-button>
           <el-button type="warning" icon="el-icon-edit-outline" @click="referYearVisible=true" v-show="currentType==='add'">往年计划</el-button>
         </div>
       </el-col>
     </el-row>
      <el-row style="padding: 0;margin: 0">
        <el-col :span="22" :offset="1">
          <el-table
            :data="tableData"
            border
            highlight-current-row
            style="width: 100%">
            <el-table-column
              type="index"
              label="编号"
              width="60"
              align="center"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="inspectType"
              label="检查类型"
              width="120"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="inspectDeptName"
              label="检查单位"
              width="250"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="content"
              label="检查内容"
              width="250"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="inspectMemeber"
              label="检查人员"
              width="150"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="inspectDate"
              label="检查日期"
              width="150"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="remark"
              label="备注"
              min-width="150"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column label="操作" label-class-name="header-style" align="center" width="200" fixed="right">
              <template slot-scope="scope">
                <el-button size="mini" type="primary" @click="updateClick(scope.row,scope.$index)">修改</el-button>
                <el-button size="mini" type="danger" @click="deleteClick(scope.row,scope.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col :span="22" :offset="1" style="margin-top: 20px">
          <div style="float: right">
            <el-button type="primary" style="margin-right: 20px" @click="saveClick">保存</el-button>
            <el-button style="margin-right: 20px" @click="returnClick">返回</el-button>
          </div>
        </el-col>
      </el-row>
    </div>

    <!--添加/修改检查内容对话框-->
    <el-dialog :title="checkContentTitle" :visible.sync="checkContentVisible">
      <el-form :model="checkContentForm" label-position="left" :rules="checkContentRules" ref="checkContentForm" class="demo-ruleForm">
        <el-form-item label="检查类型：" prop="inspectType" style="margin: 5px">
          <el-select
            v-model="checkContentForm.inspectType"
            filterable
            allow-create
            style="width: 100%;"
            placeholder="请选择或自定义检查类型">
            <el-option
              v-for="item in checkTypeOption"
              :key="item"
              :label="item"
              :value="item">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="检查单位：" prop="inspectDeptName"  style="margin: 5px">
          <el-input v-model="checkContentForm.inspectDeptName" placeholder="请填写检查单位简称，例：交工、营运、检测、房产"></el-input>
        </el-form-item>
        <el-form-item label="检查内容：" prop="content"  style="margin: 5px">
          <el-input v-model="checkContentForm.content"></el-input>
        </el-form-item>
        <el-form-item label="检查人员：" prop="inspectMemeber"  style="margin: 5px">
          <el-input placeholder="请填写检查人群，例：公司领导" v-model="checkContentForm.inspectMemeber"></el-input>
        </el-form-item>
        <el-form-item label="检查日期：" prop="inspectDate"  style="margin: 5px">
          <el-input placeholder="请填写大致月份，例：7、8月" v-model="checkContentForm.inspectDate"></el-input>
        </el-form-item>
        <el-form-item label="备注：" prop="remark"  style="margin: 5px">
          <el-input v-model="checkContentForm.remark"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="success" @click="determineSave">保存</el-button>
        <el-button @click="$refs['checkContentForm'].resetFields();checkContentVisible = false;">返回</el-button>
      </div>
    </el-dialog>
    <!--检查内容对话框结束-->

    <!--参考往年计划-->
    <el-dialog title="请选择计划年份" :visible.sync="referYearVisible">
      年份：
      <el-select
        v-model="referYear"
        filterable
        allow-create
        placeholder="请选择或自定义"
        style="width: 200px">
        <el-option
          v-for="item in yearOption"
          :key="item"
          :label="item"
          :value="item">
        </el-option>
      </el-select>
      <div slot="footer" class="dialog-footer">
        <el-button type="success" @click="yearChoose">导入</el-button>
        <el-button @click="referYearVisible = false;">返回</el-button>
      </div>
    </el-dialog>
    <!--参考往年计划结束-->
  </div>
</template>
<script>
  export default {
    name: 'planForm',
    data() {
      return {
        mainTitle:'',
        titleClass:'',
        planYear:'',
        planYearOption:[],
        tableName:'',
        docNum:'',
        recordNum:'',
        tableData:[],
        //对话框数据
        checkContentTitle:'',
        checkContentVisible:false,
        checkContentIndex:'',//修改时的序列号
        checkContentId:'',//该行的id
        checkContentForm:{
          inspectType:'',//检查类型
          inspectDeptName:'',//检查单位
          content:'',//检查内容
          inspectMemeber:'',//检查人员
          inspectDate:'',//检查日期
          remark:'',//备注
        },
        checkContentRules:{
        },
        checkTypeOption:['日常检查','专项检查','重点检查','综合检查'],
        //计划年份数据
        referYearVisible:false,
        referYear:'',
        yearOption:[],
        currentYear:'',

        //修改检查计划数据
        currentPlanId:'',
        //缓存数据
        currentType:''
      }
    },
    mounted:function () {
      let tempDate=new Date();
      this.currentYear=tempDate.getFullYear();
      this.planYearOption.push(this.currentYear+2);
      this.planYearOption.push(this.currentYear+1);
      for(let i=this.currentYear;i>2016;i--){
        this.planYearOption.push(i);//计划的年份option
        this.yearOption.push(i);//参考年份option
      }
      this.planYear=this.currentYear;//计划的年份，默认今年
      this.referYear=this.currentYear-1;//参考年份，默认去年
      this.changePlanName();
    },
    created:function () {
      this.tableName='';
      this.docNum='';
      this.recordNum='';
      this.tableData=[];
      if(this.$route.params.operateType==='add'){
        this.currentType='add';
        this.mainTitle='新增检查计划';
        this.titleClass='primary-background-title';
        this.planYear=this.currentYear;//计划的年份，默认今年
        this.referYear=this.currentYear-1;//参考年份，默认去年
        this.changePlanName();
      }else if(this.$route.params.operateType==='update'){
        this.currentType='update';
        this.mainTitle='修改检查计划';
        this.titleClass='warning-background-title';
        this.currentPlanId=this.$route.params.planId;
        this.tableName=this.$route.params.planName;
        this.planYear=this.$route.params.planYear;
        this.docNum=this.$route.params.docNum;
        this.recordNum=this.$route.params.recordNum;
        this.searchTable();
      }else{

      }
    },
    watch:{
      $route(to, from){
        if(from.name==='inspectionPlan'&&this.$route.name==='planForm') {
          this.tableName='';
          this.docNum='';
          this.recordNum='';
          this.tableData=[];
          if(this.$route.params.operateType==='add'){
            this.currentType='add';
            this.mainTitle='新增检查计划';
            this.titleClass='primary-background-title';
            this.planYear=this.currentYear;//计划的年份，默认今年
            this.referYear=this.currentYear-1;//参考年份，默认去年
            this.changePlanName();
          }else if(this.$route.params.operateType==='update'){
            this.currentType='update';
            this.mainTitle='修改检查计划';
            this.titleClass='warning-background-title';
            this.currentPlanId=this.$route.params.planId;
            this.tableName=this.$route.params.planName;
            this.planYear=this.$route.params.planYear;
            this.docNum=this.$route.params.docNum;
            this.recordNum=this.$route.params.recordNum;
            this.searchTable();
          }else{

          }
        }
      }
    },
    methods:{
      //查找对应检查计划
      searchTable:function () {
        this.$http.post('danger/safePlanList/find',{safePlanId:this.currentPlanId}).then(function (res) {
          if(res.data.success){
            this.tableData=res.data.data.list;
          }else{
            console.log('danger/safePlanList/find'+'数据申请失败');
          }
        }.bind(this)).catch(function (err) {
          console.log('检查计划列表查找:'+err);
        }.bind(this));
      },
      //修改计划名称
      changePlanName:function () {
        this.tableName=this.planYear+'年安全检查计划';
      },
      //添加检查内容
      addContent:function () {
        this.checkContentTitle='添加检查内容';
        //内容很多重复就不清空了
//        for(let item in this.checkContentForm){
//          this.checkContentForm[item]='';
//        }
        this.checkContentVisible = true;
      },
      //修改检查内容
      updateClick:function (row,index) {
        this.checkContentTitle='修改检查内容';
        this.checkContentIndex=index;
        //这里的row是table里条目的深拷贝
        for(let item in this.checkContentForm){
          this.checkContentForm[item]=row[item];
        }
        if(this.currentType==='update'){
          this.checkContentId=row.id;
        }
        this.checkContentVisible = true;
      },
      determineSave:function () {
        if(this.currentType==='add'){
          let tempObj={inspectType:this.checkContentForm.inspectType, inspectDeptName:this.checkContentForm.inspectDeptName, content:this.checkContentForm.content, inspectMemeber:this.checkContentForm.inspectMemeber, inspectDate:this.checkContentForm.inspectDate, remark:this.checkContentForm.remark};
          if(this.checkContentTitle==='添加检查内容'){
            tempObj.num=this.tableData.length+1;
            this.tableData.push(tempObj);
            this.checkContentVisible = false;
          }else if(this.checkContentTitle==='修改检查内容'){
            this.tableData.splice(this.checkContentIndex,1,tempObj);
            this.checkContentVisible = false;
          }
        }else if(this.currentType==='update'){//修改状态时直接修改
          let tempObj={safePlanId:this.currentPlanId,inspectType:this.checkContentForm.inspectType, inspectDeptName:this.checkContentForm.inspectDeptName, content:this.checkContentForm.content, inspectMemeber:this.checkContentForm.inspectMemeber, inspectDate:this.checkContentForm.inspectDate, remark:this.checkContentForm.remark};
          if(this.checkContentTitle==='添加检查内容'){
            tempObj.num=this.tableData.length+1;
            this.$http.post('danger/safePlanList/add',tempObj).then(function (res) {
              if(res.data.success){
                this.tableData.push(res.data.data)
                this.checkContentVisible = false;
              }else{
                console.log('danger/safePlanList/add'+'数据申请失败');
              }
            }.bind(this)).catch(function (err) {
              console.log('检查计划列表新增:'+err);
            }.bind(this));
          }else if(this.checkContentTitle==='修改检查内容'){
            tempObj.id=this.checkContentId;
            this.$http.post('danger/safePlanList/update',tempObj).then(function (res) {
              if(res.data.success){
                this.tableData.splice(this.checkContentIndex,1,tempObj);
                this.checkContentVisible = false;
              }else{
                console.log('danger/safePlanList/update'+'数据申请失败');
              }
            }.bind(this)).catch(function (err) {
              console.log('检查计划列表修改:'+err);
            }.bind(this));
          }
        }else {

        }
      },
      //删除检查内容
      deleteClick:function (row,index) {
        this.$confirm('此操作将删除该检查内容, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          if(this.currentType==='add'){
            this.tableData.splice(index,1);
          }else if(this.currentType==='update'){
            this.$http.post('danger/safePlanList/delete',{safePlanId:this.currentPlanId,id:row.id}).then(function (res) {
              if(res.data.success){
                this.tableData.splice(index,1);
                this.checkContentVisible = false;
              }else{
                console.log('danger/safePlanList/delete'+'数据申请失败');
              }
            }.bind(this)).catch(function (err) {
              console.log('检查计划列表删除:'+err);
            }.bind(this));
          }
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });
        });
      },
      //------------------------------往年计划导入--------------------------
      yearChoose:function () {
        this.$http.post('danger/safePlan/find',{year:this.referYear,companyId:this.$tool.getStorage('LOGIN_USER').companyId}).then(function (res) {
          if(res.data.success){
            let tempId=res.data.data.list[0].id;
            this.$http.post('danger/safePlanList/find',{safePlanId:tempId}).then(function (res) {
              if(res.data.success){
                this.tableData=res.data.data.list;
                this.referYearVisible=false;
              }else{
                console.log('danger/safePlanList/find'+'数据申请失败');
              }
            }.bind(this)).catch(function (err) {
              console.log('检查计划列表查找:'+err);
            }.bind(this));
          }else{
            console.log('danger/safePlan/find'+'数据申请失败');
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message({
            showClose: true,
            message: '网络错误，请尝试重登录',
            type: 'error'
          });
        }.bind(this));
      },
      //------------------------------保存和返回----------------------------
      saveClick:function () {
        if(this.currentType==='add'){
          //时间太长了，需要加载
          const loading = this.$loading({
            lock: true,
            text: 'Loading2',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });
          this.$http.post('danger/safePlan/add',{year:this.planYear,name:this.tableName,companyId:this.$tool.getStorage('LOGIN_USER').companyId,docNum:this.docNum,recordNum:this.recordNum}).then(function (res) {
            if(res.data.success){
              for(let i=0;i<this.tableData.length;i++) {
                let params={safePlanId:res.data.data.id,
                  inspectType:this.tableData[i].inspectType,
                  inspectDeptName:this.tableData[i].inspectDeptName,
                  content:this.tableData[i].content,
                  inspectMemeber:this.tableData[i].inspectMemeber,
                  inspectDate:this.tableData[i].inspectDate,
                  remark:this.tableData[i].remark};
                this.$http.post('danger/safePlanList/add',params).then(function (res) {
                  if(res.data.success){
                    if(i===(this.tableData.length-1)){
                      loading.close();
                      this.$message.success('检查计划添加成功');
                      this.returnClick();
                    }
                  }else{
                    loading.close();
                    console.log('danger/safePlanList/add'+'数据申请失败');
                  }
                }.bind(this)).catch(function (err) {
                  loading.close();
                  console.log('检查计划添加表格失败:'+err);
                }.bind(this));
              }
              loading.close()
            }else{
              console.log('danger/safePlan/add'+'数据申请失败');
            }
          }.bind(this)).catch(function (err) {
            loading.close()
            console.log(err);
            this.$message({
              showClose: true,
              message: '添加检查计划失败',
              type: 'error'
            });
          }.bind(this));
        }else if(this.currentType==='update'){
          this.$http.post('danger/safePlan/update',{id:this.currentPlanId,year:this.planYear,name:this.tableName,docNum:this.docNum,recordNum:this.recordNum}).then(function (res) {
            if(res.data.success){
              this.$message.success('检查计划修改成功');
              this.returnClick();
            }else{
              console.log('danger/safePlan/update'+'数据申请失败');
            }
          }.bind(this)).catch(function (err) {
            console.log(err);
            this.$message({
              showClose: true,
              message: '修改检查计划失败',
              type: 'error'
            });
          }.bind(this));
        }
      },
      returnClick:function () {
        this.tableName='';
        this.docNum='';
        this.recordNum='';
        this.tableData=[];
        this.$router.go(-1);
      },
    }
  }
</script>
<style>
</style>
