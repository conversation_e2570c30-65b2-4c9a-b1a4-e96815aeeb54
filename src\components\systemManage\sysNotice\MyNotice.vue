<template>
  <div id="myNotice" style="height: 100%;width: 100%">
    <safeHeader></safeHeader>
    <div style="position:absolute;top:100px;left:0;right:0;bottom: 0;min-width: 1300px;background-color: #f2f2f2;overflow-y: scroll">
      <el-col :span="22" :offset="1" style="background-color: #fff;position: absolute;top:0;bottom: 0;min-height:1000px;padding: 20px">
        <el-button @click="$router.push('/menu')">返回首页</el-button>
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane label="所有通知" name="unRead">
            <div v-for="(notice,index) in tableData" @click="noticeClick(notice,index)"
                 class="task-block-item">
              <el-row style="margin: 3px 0 0 0">
                <el-col :span="18">
                  <i class="el-icon-message" style="margin-right: 10px"></i>
                  <span v-bind:class="{unRead:!notice.hasRead}">{{notice.sysNotice.title}}</span>
                </el-col>
                <el-col :span="6">
                        <span style="color: #8c939d">创建人：{{notice.sysNotice.writeUserName}}
                          | 部门：{{notice.sysNotice.deptName}}</span>
                </el-col>
              </el-row>
              <el-row style="margin: 0">
                <el-col :span="24" style="margin-top: 5px">
                  <i class="el-icon-time" style="margin-right: 10px"></i>
                  <span style="color: #8c939d">{{($tool.formatDateTime(notice.sysNotice.createTime)|| '')
                    .substring(0,16)}}</span>
                </el-col>
              </el-row>
            </div>
            <!--分页-->
            <div
              style="margin-top: 20px;">
              <el-pagination
                layout="prev, pager, next"
                :total="unReadTotal"
                @current-change="currentPage">
              </el-pagination>
            </div>
          </el-tab-pane>
          <!--<el-tab-pane label="历史通知" name="readed">-->
          <!--<div v-for="(notice,index) in readNotice.unReadNoticeData" @click="noticeClick(notice,index)"-->
          <!--class="task-block-item">-->
          <!--<el-row style="margin: 3px 0 0 0">-->
          <!--<el-col :span="18">-->
          <!--<i class="el-icon-message" style="margin-right: 10px"></i>-->
          <!--<span v-bind:class="{unRead:!notice.hasRead}">{{notice.sysNotice.title}}</span>-->
          <!--</el-col>-->
          <!--<el-col :span="6">-->
          <!--<span style="color: #8c939d">创建人：{{notice.sysNotice.writeUserName}}-->
          <!--| 部门：{{notice.sysNotice.deptName}}</span>-->
          <!--</el-col>-->
          <!--</el-row>-->
          <!--<el-row style="margin: 0">-->
          <!--<el-col :span="24" style="margin-top: 5px">-->
          <!--<i class="el-icon-time" style="margin-right: 10px"></i>-->
          <!--<span style="color: #8c939d">{{($tool.formatDateTime(notice.sysNotice.createTime)|| '')-->
          <!--.substring(0,16)}}</span>-->
          <!--<span style="color: #8c939d">阅读时间 :{{($tool.formatDateTime(notice.readTime) || '').substring(0,16)}}</span>-->
          <!--</el-col>-->
          <!--</el-row>-->
          <!--</div>-->
          <!--&lt;!&ndash;分页&ndash;&gt;-->
          <!--<div-->
          <!--style="margin-top: 20px;">-->
          <!--<el-pagination-->
          <!--layout="prev, pager, next"-->
          <!--:total="readNotice.total"-->
          <!--@current-change="readedCurrentPage">-->
          <!--</el-pagination>-->
          <!--</div>-->
          <!--</el-tab-pane>-->
        </el-tabs>
      </el-col>
    </div>

    <el-dialog :title="notice.title"
               center
               :visible.sync="noticeDialogVisible">
      <!--安全生产目标结束-->

      <el-dialog :title="dialog.title"
                 width="100%" top="0vh"
                 :center="true"
                 append-to-body
                 :visible.sync="dialog.isShow">
        <iframe :src="dialog.pdfUrl+'#toolbar=1'"  width="100%" height="810"></iframe>
      </el-dialog>
      <el-col :span="24">
        <span style="color: #8c939d">创建人：{{notice.writeUserName}}
                          | 部门：{{notice.deptName}} | 时间 {{notice.createDate}}</span>
      </el-col>
      <el-col :span="24">
        <el-button
          v-if="notice.filePath"
          type="text" @click="showPdfDialog">{{notice.contentLink}}</el-button>
      </el-col>
      <el-col :span="24" style="margin-top: 20px;margin-bottom: 10px;">
        <vue-editor v-model="notice.content"></vue-editor>
      </el-col>
      <div slot="footer" class="dialog-footer" style="margin-top: 10px;">
        <el-button type="primary" @click="noticeDialogVisible = false">关 闭</el-button>
        <el-button type="primary" @click="noticeDialogVisible = false">查 看</el-button>
      </div>
    </el-dialog>

  </div>
</template>
<script>
  import safeHeader from '@/components/common/header'
  import { VueEditor } from 'vue2-editor'
  export default {
    name: 'myNotice',
    data() {
      return {

        tableData: [],
        pageCurrent:1,
        pages:1,
        unReadTotal:0,
        activeName:'unRead',
        notice:{
          title:'',
          content:'',
          filePath : '',
          contentLikn : '',
          createDate:'',
          writeUserName:'',
          deptName:''
        },
        noticeDialogVisible:false,
        // pdf查看对话框
        dialog:{
          //查看安全风险告知书
          isShow:false,
          pdfUrl:'http://www.safe360.vip/safeFile.pdf',
          title:'查看安全风险告知书',
        },
        //  已读的信息
        readNotice:{
          pageCurrent:1,
          pages:1,
          total:0,
          unReadNoticeData:[]
        }
      }
    },
    components: {
      safeHeader,
      'vue-editor':VueEditor,
    },
    computed: {

    },
    mounted: function () {
      //获取所有用户数据
      this.sendRequest(false, 1,false);
    },
    watch: {
      $route(to, from) {
        if (this.$route.name === 'myNotice') {
          this.sendRequest(false, 1,false);
        }
      }
    },
    methods: {
      //-----------------------------交互事件-------------------------------
      //向服务器发送请求
      sendRequest: function (name, pageCurrent,hasRead) {
//        this.$http.get('/sys/notice/getMyNotice?' + 'pageCurrent=' + pageCurrent+'&hasRead='+hasRead)
        // hasRead 不传了
        this.$http.get('/sys/notice/getMyNotice?' + 'pageCurrent=' + pageCurrent)
          .then(function (res) {
            if (res.data.success) {
              if(hasRead){
                this.readNotice.pageCurrent=res.data.data.pageNum
                this.readNotice.page=res.data.data.pages;
                this.readNotice.total=res.data.data.total;
                this.readNotice.unReadNoticeData=res.data.data.list;
              }else {
                this.pageCurrent = res.data.data.pageNum;
                this.pages = res.data.data.pages;
                this.unReadTotal = res.data.data.total;
                this.tableData = res.data.data.list;
              }
            }
          }.bind(this)).catch(function (err) {
          console.log(err);
        });
        this.$http.get('sys/notice/getUnreadCount').then(function (res) {
          if(res.data.success){
            localStorage.setItem('NOTICE_COUNT', res.data.data);//存储未阅读通知数量
          }
        }.bind(this))
      },
      //-----------------------------对话框事件----------------------------------
      noticeClick:function (row,index) {
        this.noticeDialogVisible=true
        this.notice.title=row.sysNotice.title
        this.notice.content=row.sysNotice.content
        this.notice.contentLink=row.sysNotice.content;
        this.notice.filePath=row.sysNotice.filePath;
        this.notice.writeUserName=row.sysNotice.writeUserName
        this.notice.deptName=row.sysNotice.deptName
        this.notice.createDate=(this.$tool.formatDateTime(row.sysNotice.createTime)|| '').substring(0,16)
        if(!row.hasRead){
          var params=new URLSearchParams();
          params.append("noticeId",row.sysNotice.nId)
          params.append("sysNoticeTargetId",row.id)
          this.$http.post("/sys/notice/readNotice",params).then(function (res) {
            if(res.data.success){
              var tempNotice=row
              tempNotice.hasRead=true;
              localStorage.setItem('NOTICE_COUNT', localStorage.NOTICE_COUNT-1);//存储未阅读通知数量
              this.tableData.splice(index,1,tempNotice)
            }
          }.bind(this)).catch(function (err) {
            this.$message.error("网络错误或者其他："+err)
          }.bind(this))
        }
      },
      // 文章查看
      showPdfDialog:function () {
//        this.dialog.title = this.info.name;
        this.dialog.pdfUrl = this.notice.filePath;
        this.dialog.title = this.notice.contentLink;
        this.dialog.isShow = true
      },
      //-----------------------------表格事件-------------------------------
      //翻页
      currentPage: function (val) {
        this.nowPage = val;
        this.sendRequest(false, this.nowPage,false);
      },
      handleClick(tab, event) {
        console.log(tab.name)
        if(tab.name=='readed'){
          this.sendRequest(false, this.readNotice.pageCurrent,true);
        }else if(tab.name=='unRead'){
          this.sendRequest(false, this.pageCurrent,false);
        }
      },
      readedCurrentPage: function (val) {
        this.nowPage = val;
        this.sendRequest(false, this.nowPage,true);
      },
      returnClick:function () {
        this.planTitle='';
        this.tableContent=[];
        this.$router.go(-1);
      },
    }
  }
</script>
<style>


  /*污染性样式放在默认加载页面中*/
  .el-table__body tr > td {
    padding: 5px !important;
  }

  .el-table__body tr.current-row > td {
    background: rgb(250, 229, 164) !important;
  }
  .unRead{
    color: #4a90e2;
  }
  .task-block-item{border-bottom: 1px solid #ddd;width: 100%;padding-bottom: 10px}
  .task-block-item:hover{background-color: rgb(250,229,164)}
</style>


