<template>
  <el-container class="container" id="safeEducationView">
    <el-main>
      <el-row>
        <el-col :span="24">
          <h1 style="text-align: center;">{{info.name}}</h1>
        </el-col>
      </el-row>
      <el-row class="lineHeight">
        <el-col :span="6"  style="text-align: center;">作者：{{info.createUserName}}</el-col>
        <el-col :span="6" style="text-align: center;">发布时间：{{formatDateTime(info.createTime)}}</el-col>
        <el-col :span="12" style="">出处：{{info.companyName}}</el-col>
      </el-row>
      <el-row class="lineHeight">
        <el-col :span="6"  style="text-align: center;">类型：{{info.typeName}}</el-col>
        <el-col :span="6" style="text-align: center;">共享：{{info.isShare == 1 ? '是' : '否'}}</el-col>
        <el-col :span="12" style="">大小：{{info.size}}</el-col>
      </el-row>
      <el-row  class="lineHeight" >
        <!--<el-col :span="6" style="text-align: center;">部门：{{info.deptName}}</el-col>-->
        <el-col :span="24">
          <el-button
            type="text" @click="showPdfDialog">{{info.filePath}}</el-button>
        </el-col>
      </el-row>
      <el-row class="lineHeight">
        <el-col :span="24">
          <vue-editor v-model="info.remark"></vue-editor>
        </el-col>
      </el-row>
      <el-row type="flex" class="row" justify="center">
        <el-button size="small" :span="2"  @click="$router.back();">返回</el-button>
      </el-row>
    </el-main>


    <!--安全生产目标结束-->

    <el-dialog :title="dialog.title"
               width="100%" top="0vh"
               :center="true"
               :visible.sync="dialog.isShow">
      <iframe :src="dialog.pdfUrl+'#toolbar=0'"  width="100%" height="810"></iframe>
    </el-dialog>

  </el-container>
</template>

<script>

  import _ from 'lodash'
  import { VueEditor } from 'vue2-editor'
  import chooseStaff from '@/components/common/chooseStaff'
  export default {
    components: {
      chooseStaff,
      VueEditor
    },
    data(){
      return {
        // info表
        info : {

        },
        // pdf查看对话框
        dialog:{
          //查看安全风险告知书
          isShow:false,
          pdfUrl:'http://www.safe360.vip/safeFile.pdf',
          title:'查看安全风险告知书',
        },
      }
    },
    watch:{
      $route(to,from){
        // 如果来至列表页
        if(from.name == 'safetyEducationKnowledgeIndex'
          &&this.$route.name=='safetyEducationKnowledgeView'){
          this.searchBtnClickHandle()
        }
      },
    },
    created(){
      this.searchBtnClickHandle();
    },
    methods:{
      formatDateTime(dateStr){
        let num = 10;
        let str = this.$tool.formatDateTime(dateStr) || '';
        return str ? str.substring(0, num) : str;
      },

      // 文章查看
      showPdfDialog:function () {
        this.dialog.title = this.info.name;
        this.dialog.pdfUrl = this.info.filePath;
        this.dialog.isShow=true
      },
      // 根据id搜索信息
      searchBtnClickHandle(){
        let id = this.$route.params.id;
        this.$store.dispatch('eduMediumLibraryGetMediumDetail', { id : id }).then(function(res){
          if(res.success){
            this.info = res.data;
          } else {
            this.$message({
              type : 'error',
              message : res.message || '错误'
            })
          }
        }.bind(this));
      },

    }
  }
</script>

<style>
  .container{
    background:#fff;
    padding:0px 20px 20px;
  }
  #safeEducationView .lineHeight{
    margin-top:20px;
  }
  .title{
    background:rgba(64,158,255,.1);
    color:#0f6fc6;
    border: 1px solid rgba(64,158,255,.2);
    border-radius:5px;
  }
  .row{
    margin-top:10px;
  }
  
</style>
