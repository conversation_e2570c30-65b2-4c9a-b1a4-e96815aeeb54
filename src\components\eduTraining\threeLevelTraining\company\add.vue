<template>
    <el-container class="container">
      <el-main>
        <el-form ref="form" label-width="100px" :rules="rules" :model="form">
          <el-row>
            <el-col :span="24">
              <el-form-item label="公司培训：" class="title">
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex">
            <el-col :offset="2" :span="7">
              <el-form-item  class="formItem" label="培训时间" prop="eduEntryTrainingCompany.trainingDate">
                <el-date-picker
                  v-model="form.eduEntryTrainingCompany.trainingDate"
                  type="date"
                  placeholder="选择日期">
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="7">
              <el-form-item class="formItem" label="学时" prop="eduEntryTrainingCompany.trainingHours">
                <el-input-number :min="1" :max="100" v-model="form.eduEntryTrainingCompany.trainingHours"></el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="7">
              <el-form-item class="formItem" label="教育者" prop="eduEntryTrainingCompany.teacher">
                <el-input v-model="form.eduEntryTrainingCompany.teacher"></el-input>
               <!-- <userList :data="addStaff" @userChange="teacherChange"></userList>-->
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex">
            <el-col :offset="2" :span="22">
              <el-form-item class="formItem" label="培训内容" prop="eduEntryTrainingCompany.courses">
                <el-input v-model="form.eduEntryTrainingCompany.courses" autosize type="textarea" :rows="2"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex">
            <el-col :offset="2" :span="7">
              <el-form-item class="formItem" label="公司培训成绩" prop="eduEntryTrainingCompany.score">
                <el-input-number v-model="form.eduEntryTrainingCompany.score" :min="0" :max="100" :step="5"></el-input-number>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- <el-row type="flex" class="row">
             <el-button
               @click="assist.eduUser.isShow = true;"
               size="small" icon="el-icon-plus" type="primary">人员</el-button>
             <template v-if="assist.eduUser.isShow">
               <userList :data="addStaff" @userChange="userChange"></userList>
               <el-button
                 @click="checkBtnClickHandle"
                 type="success" size="small" icon="el-icon-check"></el-button>
               <el-button type="info" size="small" icon="el-icon-close"
                          @click="assist.eduUser.isShow = false;"></el-button>
             </template>
           </el-row>-->
          <!--<el-row type="flex" class="row">
            <el-form-item label="受训员工" prop="eduUserIds">
              <el-table
                style="width:900px;"
                :data="assist.eduUser.tableData">
                <el-table-column type="selection"  width="50"></el-table-column>
                <el-table-column type="index" label="序号" width="50"></el-table-column>
                <el-table-column property="username" label="姓名" width="80"></el-table-column>
                <el-table-column property="deptName" label="部门" width="150"></el-table-column>
                <el-table-column property="gender" label="性别" width="50">
                  <template slot-scope="scope">{{ scope.row.gender ? '男' : '女'}}</template>
                </el-table-column>
                <el-table-column property="birthday" :formatter="formatDateTime" label="出生年月" width="150"></el-table-column>
                <el-table-column property="degreeOfEducation" label="文化程度" width="100"></el-table-column>
                <el-table-column property="entryDate" :formatter="formatDateTime" label="入职时间" width="150"></el-table-column>
                <el-table-column
                  label="操作"
                  width="100"
                  align="center"
                  fixed="right"
                  label-class-name="inner-header-style">
                  <template slot-scope="scope">
                    <el-button type="danger"  size="mini"
                               @click.native.prevent="emgHandleListsDelHandle(scope.$index, scope.row, assist.eduUser.tableData)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-form-item>
          </el-row>-->
          <chooseStaff
            ref="chooseStaff"
            @selectedRows="selectedRows"></chooseStaff>
          <el-row type="flex" class="row" justify="center">
            <el-button
              @click="submitBtnClickHandle"
              size="small" :span="2" type="primary">提交</el-button>
            <el-button size="small" :span="2" @click="$router.back();">返回</el-button>
          </el-row>
        </el-form>
      </el-main>
    </el-container>
</template>

<script>
    import chooseStaff from '@/components/common/chooseStaff'
    import userList from '@/components/common/userListForInput'
    export default {
      components: {
        chooseStaff,
        userList,
      },
      data(){
        return {
          form : {
            // 公司
            eduEntryTrainingCompany : {
              // 培训时间
              trainingDate : '',
              // 学时
              trainingHours : 0,
              // 教育者
              teacher : '',
              // 培训内容
              courses : '',
              //成绩
              score:'',
            },
            // 部门
            eduEntryTrainingDepartment : {
              trainingId : -1,
            },
            // 班组
            eduEntryTrainingTeam : {
              trainingId : -1,
            },
            // 用户ID
            eduUserIds : [],
          },
          rules: {
            'eduEntryTrainingCompany.trainingDate': [
              {required: true, message: '请选择日期', trigger: 'change'},
            ],
            'eduEntryTrainingCompany.trainingHours': [
              {required: true, message: '请输入内容', trigger: 'blur'},
            ],
            'eduEntryTrainingCompany.teacher': [
              {required: true, message: '请输入内容', trigger: 'blur'},
            ],
            'eduEntryTrainingCompany.courses': [
              {required: true, message: '请输入内容', trigger: 'blur'},
            ],
            eduUserIds: [
              { type: 'array', required: true, message: '请至少添加一个项目', trigger: 'blur' }
            ],
          },
          assist : {
            // 员工数组
            staffList : [],
            // 被选中员工列表
            eduUser : {
              isShow : false,
              // 当前被选中的员工
              selectedData : '',
              // 所有列表
              tableData : [],
            },
          },
          // 添加人员
          addStaff : {
            // 没有参加过培训的人
            entryTrain : false,
            // 公司
            companyId : '',
          },
        }
      },
      watch:{
        $route(to,from){
          // 如果来至列表页
          if(from.name === 'threeLevelTrainingIndex'){
            this.init();
          }
        }
      },
      created(){
        this.init();
      },
      methods: {
        init(){
          this.clear();
          let user = this.$tool.getStorage('LOGIN_USER');
         // this.form.eduEntryTrainingCompany.teacher = user.username;
          this.addStaff.companyId = user.companyId;
        },
        clear(){
//          console.log('clear')
          this.form.eduEntryTrainingCompany = this.$tool.clearObj({}, this.form.eduEntryTrainingCompany);
          this.form.eduUserIds = [];
          this.assist.eduUser.tableData = [];
        },
        // 格式化时间
        formatDateTime(row, column, cellValue){
          let pro = column.property;
          let num = 10;
          // 年份4位 1999
          if(pro === 'createYear') num = 4;
          let str = this.$tool.formatDateTime(row[pro] || 0);
          return str ? str.substring(0, num) : str;
        },
        // 教育者添加
        teacherChange(val){
          this.form.eduEntryTrainingCompany.teacher = val;
        },
        // 添加人员---选择用户
        userChange(val){
          this.assist.eduUser.selectedData = val;
        },
        // 人员---确定按钮
        checkBtnClickHandle(){
          let selectedData = this.assist.eduUser.selectedData;
          // 如果不存在，添加
          if(!this.form.eduUserIds.includes(selectedData.userId)){
            this.form.eduUserIds.push(selectedData.userId);
            this.assist.eduUser.tableData.push(selectedData);
            this.assist.eduUser.isShow = false;
          } else {
            this.$message({
              type : 'error',
              message : '改用户已存在，不能重复添加！！'
            })
          }
        },
        // 人员--删除按钮--处理函数
        emgHandleListsDelHandle(index, item, rows) {
          rows.splice(index, 1);
        },
        // 提交---添加或更新
        submitBtnClickHandle(){
          let params = Object.assign({}, this.form);
          this.$refs['form'].validate(function(valid) {
            if(valid){
              this.$store.dispatch('eduEntryTrainingAdd', params).then(function (res) {
                if(res.success){
                  this.$message({
                    type : 'success',
                    message : '操作成功'
                  })
                  this.$router.push({ name : 'threeLevelTrainingIndex' })
                } else {
                  this.$message({
                    type : 'error',
                    message : res.message || '操作失败'
                  })
                }
              }.bind(this))
            } else {
              return false;
            }
          }.bind(this))
        },
        // 人员列表选择组件处理函数
        selectedRows(rows){
          // 参与人员列表----用户userId列表
          let userIds = rows.map(function(it){
            return it.userId
          })
          this.form.eduUserIds = userIds;
        }


      }
    }
</script>

<style>
  .container{
    background:#fff;
    padding:0px 50px;
  }
  .title{
    background:rgba(64,158,255,.1);
    color:#0f6fc6;
    border: 1px solid rgba(64,158,255,.2);
    border-radius:5px;
    margin:5px;
  }
  .row{
    margin-top:10px;
  }
  .formItem{
    margin:2px;
  }
</style>
