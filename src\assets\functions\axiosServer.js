import axios from "axios";
import { Message } from "element-ui";
import router from "../../router";

const Axios = axios.create({
  // 测试机
    // baseURL:'http://*************:8104/safe/',
    // baseURL:'http://**************:8087/safe/',
  //正式机
  // baseURL:'https://www.safe360.vip/safe/',
  baseURL:'/safe/',
  // 戴--检测00
  // baseURL:'http://**************:8087/safe',
  // baseURL:'http://************:8087/safe/',
  // baseURL:'http://localhost:8087/safe/',
  // Ubuntu测试机
  // baseURL:'http://**********:8087/safe/',
  // baseURL:'http://***************:8087/safe/',
  timeout: 1200000,
  withCredentials: true, // 是否允许带cookie这些
});
//
//返回状态判断(添加响应拦截器)
// Axios.interceptors.response.use(
//   res => {
//     //对响应数据做些事
//     if (!res.data.success) {
//     //   if(res.data.code===100){
//     //     if(localStorage.LOGIN_USER){
//     //       localStorage.removeItem('LOGIN_USER');
//     //     }
//     //     if(localStorage.SAFE_PLATFORM_USERNAME){
//     //       localStorage.removeItem('SAFE_PLATFORM_USERNAME');
//     //     }
//     //     router.push({path: '/'});
//     //     Message({
//     //       showClose: true,
//     //       message: '请重新登陆！',
//     //       type: "error"
//     //     });
//     //     return Promise.reject(res.data.message);
//     //   }else if(res.data.code===101){
//     //     Message({
//     //       showClose: true,
//     //       message: '功能权限限制！',
//     //       type: "error"
//     //     });
//     //     res.data.data=null;
//     //     return res;
//     //   }else{
//     //
//     //   }
//       Message({
//         showClose: true,
//         message: res.data.message,
//         type: "error"
//       });
//       return Promise.reject(res.data.message);
//     }
//     return res;
//   },
//   error => {
//     // 如果没有获取过用户信息，直接跳转到登陆界面
//     if (!window.localStorage.getItem("LOGIN_USER")) {
//       router.push({
//         path: "/"
//       });
//     } else {
//       switch (error.response.status) {
//         case 400:
//           error.message = '错误请求'
//           break;
//         case 401:
//           error.message = '未授权，请重新登录'
//           break;
//         case 403:
//           error.message = '拒绝访问'
//           break;
//         case 404:
//           error.message = '请求错误,未找到该资源'
//           break;
//         case 405:
//           error.message = '请求方法未允许'
//           break;
//         case 408:
//           error.message = '请求超时'
//           break;
//         case 500:
//           error.message = '服务器端出错'
//           break;
//         case 501:
//           error.message = '网络未实现'
//           break;
//         case 502:
//           error.message = '网络错误'
//           break;
//         case 503:
//           error.message = '服务不可用'
//           break;
//         case 504:
//           error.message = '网络超时'
//           break;
//         case 505:
//           error.message = 'http版本不支持该请求'
//           break;
//         default:
//           error.message = `连接错误${error.response.status}`
//       }
//       Message({
//         showClose: true,
//         message: error.message,
//         type: "error"
//       });
//       return Promise.reject(error)
//     }
//   }
// );

export default Axios
