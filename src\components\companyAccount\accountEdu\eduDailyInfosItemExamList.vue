<template>
  <div class="background-style" style="padding: 10px">
    <el-container>
      <el-main>
        <el-form ref="form" :model="form" label-width="150px">
          <el-row style="margin:0">
            <el-col :span="24">
              <el-form-item label="考核项目">
                <span>{{record.examineName}}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row style="margin:10px 0 0 0">
            <el-col :span="12">
              <el-form-item label="考核地点">
                <span>{{record.examineLocation}}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="考核时间">
                <span>{{$tool.formatDateTime(record.examineTime)}}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row style="margin:10px 0 0 0">
            <el-col :span="24">
              <egrid class="egrid"
                     stripe border
                     maxHeight="400"
                     :data="egrid.data"
                     :columns="egrid.columns"
                     :columns-handler="columnsHandler"
                     :column-type="egrid.columnType">
              </egrid>
            </el-col>
          </el-row>
          <el-row type="flex" class="row" justify="center">
            <el-button size="small" :span="2" type="primary" @click="$router.back();">返回</el-button>
          </el-row>
        </el-form>
      </el-main>
    </el-container>
  </div>
</template>

<script>




  export default {
    data(){
      return {
        // 活动的内容
        record : {},
        // 表格
        egrid : {
          data : [],
          columns : [
            { label: '姓名', prop: 'username' },
            { label: '考核成绩', prop: 'score' },
          ],
          // columnsProps 用于定义所有 columns 公共的属性
          columnsProps: {
            fit : true,
            sortable: true,
            align : 'center',
          },
          columnsSchema : {

          },
          columnType : 'index'
        }
      }
    },
    created(){
      this.init();
    },
    watch:{
      $route(to,from){
        let row = to.params && to.params.row && to.params.row.data;
        if(to.name === 'eduDailyInfosItemExamList') {
          if(row){
            this.record = row;
            this.searchBtnClickHandle();
          }
        }
      }
    },
    methods:{
      // 初始化
      init(){
        this.record = this.$route.params.row.data;
        this.searchBtnClickHandle();
      },
      searchBtnClickHandle(){
        this.record.eduDailyExamineScores.forEach(function(it){
          this.egrid.data.push({
            username : it.eduUser.username,
            score : it.score
          })
        }.bind(this));
      }
    }
  }
</script>

<style>

</style>
