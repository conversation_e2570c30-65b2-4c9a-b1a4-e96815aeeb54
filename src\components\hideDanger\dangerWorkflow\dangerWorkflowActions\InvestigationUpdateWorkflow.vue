<template>
  <div id="investigationUpdateWorkflow">
    <div class="background-style">
      <el-col :span="16" :offset="4" class="warning-background-title">修改隐患排查</el-col>
      <el-col :span="16" :offset="4" class="card-shadow-style">
        <div style="width: 100%;padding-top: 10px;padding-bottom:10px;float: left;background-color: #f2f2f2">
          <i class="el-icon-document" style="color:#049ff1;float: left;margin:3px 10px 0 20px"></i>
          <span style="color:#049ff1;width: 200px;float: left;">基本信息</span>
          <div style="float: right;margin-right: 20px;display: inline-block">
            <el-button type="primary" size="small" @click="openUpdateDialog">修改信息</el-button>
          </div>
        </div>
        <div style="width: 100%;float:left;">
          <el-form :model="form" ref="infoForm" label-width="100px" class="demo-ruleForm">
            <el-col :span="24">
              <el-col :span="12">
                <el-form-item label="检查单编号：" prop="checkNum" style="margin: 0">
                  {{form.checkNum}}
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="预计检查日期：" prop="predictInspectDate" style="margin: 0" label-width="110px">
                  {{form.showDate}}
                </el-form-item>
              </el-col>
            </el-col>
            <el-col :span="24">
              <el-col :span="12"  v-if="dangerTypeName!='自查'">
                <el-form-item label="检查组组长：" prop="leaderUserName" style="margin: 0">
                  {{form.leaderUserName}}
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="检查类型：" style="margin: 0" label-width="90px">
                  {{dangerTypeName}}
                </el-form-item>
              </el-col>
            </el-col>
            <div v-if="dangerTypeName!='自查'">
              <el-col :span="24">
                <el-col :span="12">
                  <el-form-item label="受检单位：" prop="targetDeptName" style="margin: 0" label-width="90px">
                    {{form.targetDeptName}}
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="检查单位：" prop="publicDeptName" style="margin: 0;" label-width="90px">
                    {{form.publicDeptName}}
                  </el-form-item>
                </el-col>
              </el-col>
              <el-col :span="24">
                <el-form-item label="检查组成员：" prop="dangerInspectMembers" style="margin: 0">
                  <span v-for="item in form.dangerInspectMembers" :key="item.value">{{item.label}} , </span>
                </el-form-item>
              </el-col>
            </div>
            <el-col :span="24">
              <el-form-item label="检查单名称：" prop="name" style="margin: 0">
                {{form.name}}
              </el-form-item>
            </el-col>
          </el-form>
        </div>
      </el-col>
      <el-col :span="22" :offset="1">
        <el-table
          border
          :data="dangerInspectListPublicList">
          <el-table-column
            type="index"
            label="序号"
            width="50"
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            prop="inspectProject"
            label="检查项目"
            show-overflow-tooltip
            width="150"
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            prop="inspectContent"
            min-width="400"
            show-overflow-tooltip
            label="检查标准内容"
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            label="操作"
            width="200"
            align="center"
            fixed="right"
            label-class-name="inner-header-style">
            <template slot-scope="scope">
              <el-button size="mini" type="warning" @click="itemUpdateClick(scope.row,scope.$index)">修改</el-button>
              <el-button size="mini" type="danger" @click="itemDeleteClick(scope.row,scope.$index)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div style="width: 100%;height: 40px;background-color: rgb(236,248,255);border-bottom: 1px solid #f2f2f2;">
          <div style="width: 100px;margin: auto">
            <el-button type="text" icon="el-icon-plus" @click="knowledgePoint.setting.isShow = true;">添加检查项目</el-button>
          </div>
        </div>
      </el-col>
      <el-col :span="22" :offset="1">
        <div style="float: right;margin: 20px">
          <el-button type="primary" @click="submitClick">提交</el-button>
          <el-button type="success" @click="saveClick">保存</el-button>
          <el-button @click="$router.push({name:'hideDangerWorkflow'})">返回</el-button>
        </div>
      </el-col>
    </div>

    <!-- 添加注意事项对话框 -->
    <knowledge-point-dialog
      @currentChangeHandle="currentChangeHandle"
      @okBtnClickHandle="execOkBtnClickHandle"
      :data="knowledgePoint.setting">
      <template slot="firstSlot">
        <el-row class="row">
          <el-col :span="3">
            <el-button @click="addCheckObject" size="small" type="primary" icon="el-icon-plus">检查项目</el-button>
          </el-col>
          <el-col :offset="3" :span="18">
            <el-input v-model="knowledgePoint.inspectProject"></el-input>
          </el-col>
        </el-row>
        <el-row class="row">
          <el-col :span="3">
            <el-button @click="addCheckContent" size="small"  type="danger" icon="el-icon-plus">检查标准内容</el-button>
          </el-col>
          <el-col :offset="3" :span="18">
            <el-input v-model="knowledgePoint.inspectContent"></el-input>
          </el-col>
        </el-row>
      </template>
    </knowledge-point-dialog>
    <!--对话框结束-->

    <!-- 修改基本信息对话框 -->
    <el-dialog title="修改基本信息" :visible.sync="baseInfoVisible">
      <el-form :model="updateForm" label-position="left" :rules="rules" ref="updateForm" class="demo-ruleForm">
        <el-form-item label="检查单编号：" prop="checkNum">
          <el-input v-model="updateForm.checkNum" style="width: 320px"></el-input>
        </el-form-item>
        <el-form-item label="检查单名称：" prop="name">
          <el-input v-model="updateForm.name" style="width: 310px"></el-input>
        </el-form-item>
        <el-form-item label="预计检查日期：" prop="predictInspectDate">
          <el-date-picker
            v-model="updateForm.predictInspectDate"
            type="date"
            placeholder="选择日期"
            style="width: 330px">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="修改受检单位：" prop="targetDeptId" v-show="dangerTypeName==='检查'">
          <el-cascader
            change-on-select
            :options="unitOptions"
            placeholder="请选择" style="width: 320px"
            v-model="targetCompanyArray"
            @change="handlePickCompany">
          </el-cascader>
        </el-form-item>
        <el-form-item label="检查组组长：" prop="leaderUserId">
          <el-select
            v-model="updateForm.leaderUserId"
            filterable
            remote
            reserve-keyword
            clearable
            placeholder="请输入姓名后选择"
            :remote-method="remotePerson"
            :loading="personLoading"
            style="width: 300px">
            <el-option
              v-for="item in personOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="检查组成员：" prop="dangerInspectMembers">
          <el-select
            v-model="updateForm.dangerInspectMembers"
            multiple
            filterable
            remote
            reserve-keyword
            clearable
            placeholder="请输入姓名后选择"
            :remote-method="remotePerson"
            :loading="personLoading"
            style="width: 100%">
            <el-option
              v-for="item in personOptions"
              :key="item.value"
              :label="item.label"
              :value="item">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="warning" @click="updateBaseInfo">修改</el-button>
        <el-button @click="baseInfoVisible = false;">返回</el-button>
      </div>
    </el-dialog>
    <!--对话框结束-->

    <!-- 修改条目-->
    <el-dialog title="修改内容" :visible.sync="updateInspectListVisible">
      <el-form :model="itemForm" label-position="top" ref="itemForm" class="demo-ruleForm">
        <el-form-item label="检查项目：" prop="inspectProject">
          <el-input v-model="itemForm.inspectProject"></el-input>
        </el-form-item>
        <el-form-item label="检查标准内容：" prop="inspectContent">
          <el-input v-model="itemForm.inspectContent"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="warning" @click="determineUpdateItem">修改</el-button>
        <el-button @click="updateInspectListVisible = false;">返回</el-button>
      </div>
    </el-dialog>
    <!--修改条目结束-->

    <!--选择审核人-->
    <search-people-dialog @determineClick="selectPersonClick" :data="selectPersonData" :defaultPersonId="selectPersonData.defaultPerson.value"></search-people-dialog>

  </div>
</template>
<script>
  import knowledgePointDialog from '@/components/common/knowledgePointDialog'
  import SearchPeopleDialog from '../../../common/smallComponent/searchSinglePeople.vue'
  export default {
    name: 'investigationUpdateWorkflow',
    data() {
      return {
        //当前investigation的id
        currentId:'',
        //检查单数据
        form:{
          //检查单编号
          checkNum:'',
          //检查日期
          predictInspectDate:'',
          showDate:'',
          //受检单位
          targetDeptId:'',
          //检查组组长
          leaderUserId:'',
          //检查组成员
          dangerInspectMembers:[],
          //检查单名称
          name:'' ,
        },
        dangerTypeName:'',//当前检查类型
        //检查内容
        dangerInspectListPublicList:[],

        //修改表内容
        baseInfoVisible:false,
        updateForm:{
          id:'',
          //检查单编号
          checkNum:'',
          //检查日期
          predictInspectDate:'',
          //受检单位
          targetDeptId:'',
          //检查组组长
          leaderUserId:'',
          //检查组成员
          dangerInspectMembers:[],
          //检查单名称
          name:'' ,
        },
        rules:{
          targetDeptId:[{ required: true, message: '请选择受检单位', trigger: 'change' }],
          leaderUserId:[{ required: true, message: '请选择人员', trigger: 'change' }],
          name: [{ required: true, message: '请输入检查单名称', trigger: 'change' }],
        },

        //修改条目对话框内容
        updateInspectListVisible:false,
        itemForm:{
          id:'',
          inspectProject:'',
          inspectContent:''
        },
        targetCompanyArray:[],

        //人员查询数据
        personLoading:false,

        // 对话框---添加知识点
        knowledgePoint : {
          // 知识点
          setting : {
            isMultiple : false,       // 是否多选
            isShow : false,         // 对话框是否显示
          },
          selectSingeRow : [],      // 选中的数据//执行操作数据
          // 检测项目
          inspectProject : '',
          // 检测标准内容
          inspectContent : '',
        },
        // 表格被选中的数据
        tableDataSelections : [],
        //------------------选择审核人的对话框-----------------------
        needPersonDialog:true,
        selectPersonData:{title:'请选择审核人',isShow:false,defaultPerson:{value:0,label:''}},
        //流程任务ID
        taskId:'',

      }
    },
    components : {
      'knowledge-point-dialog' : knowledgePointDialog,
      'search-people-dialog' : SearchPeopleDialog,
    },
    computed:{
      unitOptions:function () {//当前公司的子公司
        if(this.$store.state.hideDangerData.targetDept.length){
          return this.$store.state.hideDangerData.targetDept[0].children;
        }else{
          return [];
        }
      },
      personOptions:function () {
        return this.$store.state.sysManageData.personByJson;
      },
    },
    mounted:function () {
      this.$store.dispatch('getTargetDept');
      if(this.$route.params.dangerData){
        this.taskId=this.$route.params.dangerData.taskId;
        this.dangerTypeName=this.$route.params.dangerData.typeName;
        this.initUpdate(this.$route.params.dangerData.id);
      }
    },
    watch:{
      $route(to, from){
        if(from.name==='hideDangerWorkflow'&&this.$route.name==='investigationUpdateWorkflow'||from.name==='taskNotice') {
          this.$store.dispatch('getTargetDept');
          if(this.$route.params.dangerData){
            this.taskId=this.$route.params.dangerData.taskId;
            this.dangerTypeName=this.$route.params.dangerData.typeName;
            this.initUpdate(this.$route.params.dangerData.id);
          }
        }
      }
    },
    methods: {
      //修改初始化
      initUpdate:function (id) {
        this.clearData();
        this.currentId=id;
        this.$http.post('danger/inspectPublic/detail', {id:id}).then(function (res) {
          if (res.data.success) {
            this.form=res.data.data;
            this.dangerInspectListPublicList=this.form.dangerInspectListPublicList;
            this.form.showDate=this.transferTime(this.form.predictInspectDate);

            let memberTempArray=res.data.data.dangerInspectMembers;
            this.form.dangerInspectMembers=[];
            this.personOptions.splice(0);
            memberTempArray.forEach(function (item) {
              this.form.dangerInspectMembers.push({value:item.userId,label:item.userName});
              this.personOptions.push({value:item.userId,label:item.userName});
            }.bind(this));
            let index=this.personOptions.findIndex(function (item) {
              return item.value===this.form.leaderUserId;
            }.bind(this));
            if(index<0){//列表里没有改人员，就加入
              this.personOptions.push({value:this.form.leaderUserId,label:this.form.leaderUserName});
            }
          }
        }.bind(this)).catch(function (err) {
          console.log('danger/inspectPublic/detail');
          console.log(err);
        });
      },
      //修改后重新查找信息
      searchBaseInfo:function () {
        this.form.checkNum='';
        this.form.predictInspectDate='';
        this.form.showDate='';
        this.form.targetDeptId='';
        this.form.leaderUserId='';
        this.form.name='';
        this.personOptions.splice(0);
        this.form.dangerInspectMembers.splice(0);
        this.$http.post('danger/inspectPublic/detail', {id:this.currentId}).then(function (res) {
          if (res.data.success) {
            this.form=res.data.data;
            this.form.showDate=this.transferTime(this.form.predictInspectDate);

            let memberTempArray=res.data.data.dangerInspectMembers;
            this.form.dangerInspectMembers=[];
            memberTempArray.forEach(function (item) {
              this.form.dangerInspectMembers.push({value:item.userId,label:item.userName});
              this.personOptions.push({value:item.userId,label:item.userName});
            }.bind(this));
            let index=this.personOptions.findIndex(function (item) {
              return item.value===this.form.leaderUserId;
            }.bind(this));
            if(index<0){//列表里没有改人员，就加入
              this.personOptions.push({value:this.form.leaderUserId,label:this.form.leaderUserName});
            }
          }
        }.bind(this)).catch(function (err) {
          console.log('danger/inspectPublic/detail');
          console.log(err);
        });
      },
      //清除之前数据
      clearData:function () {
        this.form.checkNum='';
        this.form.predictInspectDate='';
        this.form.showDate='';
        this.form.targetDeptId='';
        this.form.leaderUserId='';
        this.form.name='';
        this.personOptions.splice(0);
        this.unitOptions.splice(0);
        this.form.dangerInspectMembers.splice(0);
        this.dangerInspectListPublicList.splice(0);
      },
      //查找人员
      remotePerson:function (val) {
        this.$store.dispatch('getPersonByJson',{name:val,companyId:this.$tool.getStorage('LOGIN_USER').companyId});
      },
      //打开修改对话框
      openUpdateDialog:function () {
        this.updateForm.id=this.form.id;
        this.updateForm.checkNum=this.form.checkNum;
        this.updateForm.name=this.form.name;
        this.updateForm.predictInspectDate=this.form.predictInspectDate;
        this.updateForm.targetDeptId=this.form.targetDeptId;
        this.updateForm.leaderUserId=this.form.leaderUserId;
        this.updateForm.dangerInspectMembers.splice(0);
        this.updateForm.dangerInspectMembers=this.form.dangerInspectMembers.slice(0);
        this.baseInfoVisible = true;
      },
      //修改基本信息
      updateBaseInfo:function () {
        this.updateForm.dangerInspectMembers.forEach(function (item) {
          item.userId=item.value;
        });
        this.$http.post('danger/inspectPublic/update',  this.updateForm).then(function (res) {
          if (res.data.success) {
            this.$message.success('修改成功！');
            this.searchBaseInfo();
            this.baseInfoVisible = false;
          }
        }.bind(this)).catch(function (err) {
          console.log('danger/inspectPublic/update');
          console.log(err);
        });
      },
      //保存
      saveClick:function () {
        this.$message.success('已保存！');
        this.$router.push({name:'hideDangerWorkflow'});
      },
      //提交
      submitClick:function () {
        this.selectPersonData.defaultPerson={value:this.form.examineUserId,label:this.form.examineUsername};
        this.selectPersonData.isShow=true;
      },
      selectPersonClick(val){
        if(val){
          let flowParams=new URLSearchParams;
          flowParams.append("taskId",this.taskId);
          flowParams.append("check",true);
          flowParams.append("applyUserId",val);
          this.doTaskClick(flowParams);
        }else{
          this.$message.warning('请选择审核人');
        }
      },
      doTaskClick:function (flowParams) {
        this.$http.post('emgFlow/doTask',flowParams).then(function (res) {
          if(res.data.success) {
            if(this.selectPersonData.isShow){this.selectPersonData.isShow=false;}
            this.$message.success('提交成功！');
            this.$router.push({name:'hideDangerWorkflow'});
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message.error('流程执行失败')
        }.bind(this));
      },


      //-------------------------------------检查内容的修改和删除-------------------------------
      //修改条目
      itemUpdateClick:function (row,index) {
        this.itemForm.inspectProject=row.inspectProject;
        this.itemForm.inspectContent=row.inspectContent;
        this.itemForm.id=row.id;
        this.itemForm.index=index;
        this.updateInspectListVisible = true;
      },
      determineUpdateItem:function () {
        this.$http.post('/danger/inspectListPublic/update', this.itemForm).then(function (res) {
          if (res.data.success) {
            this.$message.success('修改成功！');
            this.dangerInspectListPublicList.splice(this.itemForm.index,1,{id:this.itemForm.id,inspectProject:this.itemForm.inspectProject,inspectContent:this.itemForm.inspectContent});
            this.updateInspectListVisible = false;
          }
        }.bind(this)).catch(function (err) {
          console.log('danger/inspectListPublic/update');
          console.log(err);
        });
      },
      //删除条目
      itemDeleteClick:function (row,index) {
        this.$confirm('删除该条内容, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$http.post('/danger/inspectListPublic/delete', {id:row.id}).then(function (res) {
            if (res.data.success) {
              this.$message.success('删除成功！');
              this.dangerInspectListPublicList.splice(index,1);
            }
          }.bind(this)).catch(function (err) {
            console.log('/danger/inspectListPublic/delete');
            console.log(err);
          });

        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });
        });
      },

      //--------------------------添加检查内容对话框------------------------
      // 知识点---事件----单选事件
      currentChangeHandle(val){
        this.knowledgePoint.selectSingeRow = val;
      },
      // 知识点确定--添加数据
      execOkBtnClickHandle(){
        if(this.knowledgePoint.inspectProject&&this.knowledgePoint.inspectContent){
          let tempObj={inspectPublicId:this.currentId,inspectProject : this.knowledgePoint.inspectProject, inspectContent : this.knowledgePoint.inspectContent};
          this.$http.post('/danger/inspectListPublic/add', tempObj).then(function (res) {
            if (res.data.success) {
              this.dangerInspectListPublicList.push(tempObj);
            }
          }.bind(this)).catch(function (err) {
            console.log('/danger/inspectListPublic/add');
            console.log(err);
          });

        }else{
          this.$message.warning('内容不完整，不能添加');
        }
        this.knowledgePoint.inspectProject = '';
        this.knowledgePoint.inspectContent = '';
      },
      addCheckObject:function () {
        if(this.knowledgePoint.selectSingeRow.length){
          this.knowledgePoint.inspectProject = this.knowledgePoint.selectSingeRow[0].content;
        }else{
          this.$message.warning('请选择知识点');
        }
      },
      addCheckContent:function () {
        if(this.knowledgePoint.selectSingeRow.length){
          this.knowledgePoint.inspectContent = this.knowledgePoint.selectSingeRow[0].content;
        }else{
          this.$message.warning('请选择知识点');
        }
      },

      //选择受检单位
      handlePickCompany:function () {
        this.updateForm.targetDeptId=this.targetCompanyArray[this.targetCompanyArray.length-1];
      }
    },
  }
</script>
<style>
</style>
