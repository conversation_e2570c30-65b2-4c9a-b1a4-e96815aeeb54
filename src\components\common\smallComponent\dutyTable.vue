<template>
  <div id="dutyTable">
    <el-table
      :data="dutyTable"
      border
      style="width: 100%">
      <el-table-column
        prop="dutyDate"
        label="值班日期"
        align="center"
        :formatter="dutyDateFormat"
        label-class-name="inner-header-style"
        width="120">
      </el-table-column>
      <el-table-column
        prop="name"
        label="值班人"
        align="center"
        label-class-name="inner-header-style"
        width="120">
      </el-table-column>
      <el-table-column
        prop="company"
        label="公司"
        label-class-name="inner-header-style"
        min-width="300">
      </el-table-column>
      <el-table-column
        prop="phone"
        label="手机长号"
        align="center"
        label-class-name="inner-header-style"
        width="130">
      </el-table-column>
      <el-table-column
        prop="shortPhone"
        label="手机短号"
        align="center"
        label-class-name="inner-header-style"
        width="120">
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        label-class-name="inner-header-style"
        fixed="right"
        width="90">
        <template slot-scope="scope">
          <el-button type="danger" size="mini" @click="deleteClick(scope.row,scope.$index)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div style="width: 100%;height: 40px;background-color: white;padding: 5px 0 5px 0;border-bottom: 1px solid #EBEEF5;border-left: 1px solid #EBEEF5;border-right: 1px solid #EBEEF5;">
      <div style="width: 100px;margin:auto">
        <el-button type="text" size="medium" icon="el-icon-plus" @click="addPersonClick">添加人员</el-button>
      </div>
    </div>

    <!--新增值班人员对话框-->
    <el-dialog title="添加值班人员" :visible.sync="addPerson">
      <el-form :model="personForm" :rules="personRules" ref="personForm" label-position="right" class="demo-ruleForm">
        <el-form-item label="值班日期:" label-width="120px" prop="dutyDate" style="margin-bottom: 10px">
          <el-date-picker
            v-model="personForm.dutyDate"
            type="date"
            placeholder="选择日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="姓名:" label-width="120px" prop="name" style="margin-bottom: 10px">
          <el-select
            v-model="personForm.name"
            filterable
            remote
            reserve-keyword
            clearable
            placeholder="请输入姓名后选择"
            @change="handlePersonClick"
            :remote-method="remotePersonDuty"
            :loading="personLoading"
            style="width: 220px">
            <el-option
              v-for="item in personOptions"
              :key="item.value"
              :label="item.label"
              :value="item">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="手机长号:" label-width="120px" prop="phoneNumber" style="margin-bottom: 10px">
          <el-input v-model="personForm.phoneNumber" style="width: 400px"></el-input>
        </el-form-item>
        <el-form-item label="手机短号:" label-width="120px" prop="shortNumber" style="margin-bottom: 10px">
          <el-input v-model="personForm.shortNumber" style="width: 400px"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelAddPerson">取 消</el-button>
        <el-button type="primary" @click="determineAddPerson">确 定</el-button>
      </div>
    </el-dialog>
    <!--新增值班人员对话框结束-->
  </div>
</template>
<script>
  export default {
    name: 'dutyTable',
    props:['dutyData'],//包含内容：planPublicId, companyId, companyName
    data() {
      return {
        addPerson:false,//对话框flag
        dutyTable:[],
        personForm:{
          name:'',
          dutyDate:'',
          phoneNumber:'',
          shortNumber:''
        },
        personRules:{
          dutyDate:[
            { required: true, message: '请选择值班日期', trigger: 'change' }
          ],
          name: [
            { required: true, message: '请选择人员', trigger: 'change' }
          ],
        },
        personLoading:false,
        personOptions:[],
      }
    },
    methods:{
      //搜索表格内容
      searchTable:function () {
        let params = new URLSearchParams;
        params.append("planPublicId", this.dutyData.planPublicId);
        params.append("companyId", this.dutyData.companyId);
        this.$http.post('duty/find', params).then(function (res) {
          if (res.data.success) {
            this.dutyTable=res.data.data.list;
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
        }.bind(this));
      },
      //-------------------------------表格功能------------------------------
      //删除人员
      deleteClick:function (row,index) {
        this.$confirm('此操作将删除该记录, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let params = new URLSearchParams;
          params.append("id", row.duId);
          this.$http.post('duty/delete', params).then(function (res) {
            if (res.data.success) {
              this.dutyTable.splice(index, 1);
            }
          }.bind(this)).catch(function (err) {
            console.log(err);
          }.bind(this));
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });
        });

      },
      //值班日期转化
      dutyDateFormat:function (row) {
        return this.transferTime(row.dutyDate);
      },
      //-------------------------------对话框中的人员选择-----------------------
      //搜索人员
      remotePersonDuty:function (val) {
        this.personLoading = true;
        let urlStr='user/find?username='+val;
        if(this.dutyData.companyId){//只能搜索本公司的人员
          urlStr+='&companyId='+this.dutyData.companyId;
        }
        this.$http.get(urlStr).then(function (res) {
          if(res.data.success){
            this.personOptions=[];
            for (let i = 0; i < res.data.data.list.length; i++) {
              this.personOptions.push({value:res.data.data.list[i].userId,label:res.data.data.list[i].username,phone:res.data.data.list[i].mobile,shortPhone:res.data.data.list[i].shortPhone});
            }
            this.personLoading = false;
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
        });
      },
      handlePersonClick:function (val) {
        this.personForm.phoneNumber=val.phone;
        this.personForm.shortNumber=val.shortPhone;
      },
      //-----------------------------添加人员---------------------------------------
      //新加人员
      addPersonClick:function () {
        this.dialogTitle='';
        this.addPerson=true;
      },
      cancelAddPerson:function () {
        this.addPerson = false;
      },
      determineAddPerson:function () {
        this.$refs['personForm'].validate((valid) => {
          if (valid) {
            let params = new URLSearchParams;
            params.append("planPublicId", this.dutyData.planPublicId);
            params.append("companyId", this.dutyData.companyId);
            params.append("dutyDate", this.personForm.dutyDate);
            params.append("name",this.personForm.name.label);
            params.append("userId", this.personForm.name.value);
            params.append("phone", this.personForm.phoneNumber);
            params.append("shortPhone",this.personForm.shortNumber);
            this.$http.post('duty/add', params).then(function (res) {
              if (res.data.success) {
                this.dutyTable.push({
                  dutyDate:this.personForm.dutyDate,
                  name:this.personForm.name.label,
                  phone:this.personForm.phoneNumber,
                  shortPhone:this.personForm.shortNumber,
                  company:this.dutyData.companyName
                });
                this.$refs['personForm'].resetFields();
                this.addPerson = false;
              }else{
                this.addPerson = false;
              }
            }.bind(this)).catch(function (err) {
              console.log(err);
            }.bind(this));
          } else {
            console.log('error submit!!');
            return false;
          }
        });
      },
    }
  }
</script>
<style>
</style>
