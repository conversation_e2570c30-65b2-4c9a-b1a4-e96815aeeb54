/**
 * Created by 小炮子子 on 2018-04-03.
 */
export default {
  // 输入时间戳，格式化日期
  formatDateTime(inputTime) {
    if(!inputTime) return;
    var date = new Date(inputTime);
    var y = date.getFullYear();
    var m = date.getMonth() + 1;
    m = m < 10 ? ('0' + m) : m;
    var d = date.getDate();
    d = d < 10 ? ('0' + d) : d;
    var h = date.getHours();
    h = h < 10 ? ('0' + h) : h;
    var minute = date.getMinutes();
    var second = date.getSeconds();
    minute = minute < 10 ? ('0' + minute) : minute;
    second = second < 10 ? ('0' + second) : second;
    return y + '-' + m + '-' + d+' '+h+':'+minute+':'+second;
  },
  // 本地 localStorage 存储
  getStorage(key) {
    if(localStorage.getItem(key)){
      return JSON.parse(localStorage.getItem(key)); // 取数据 => 如果本地有key 则执行，没有则引用data的数据
    }
    return false;
  },
  setStorage(key, value){
    localStorage.setItem(key, JSON.stringify(value));
  },
  // 过滤对象，如果属性值为空，则不要
  filterObj(to, from){
    //console.log(from,9999999)
    Object.entries(from).forEach(function(it){
      switch(typeof it[1]){
        case 'string':
        case 'number':
        case 'boolean':
        case 'undefined':
        case 'null':
              if(it[1]) to[it[0]] = it[1];
              break;
        case 'object':
              // Date
              if(it[1] instanceof Date){
                //console.log(it);
                to[it[0]] = it[1];
              }
              // Array
              else if (it[1] instanceof Array){
                to[it[0]] = it[1];
              }
              // JSON
              else {
                if(JSON.stringify(it[1]) !== '{}') {
                  this.filterObj(to[it[0]] = {}, it[1]);
                }
              }
              break;
      }
    }.bind(this));
    return to || {};
  },
  // 清空数据
  clearObj(to, from){
    // 如果from不是对象，那么返回 {}
    if(typeof from !== 'object') return {};
    Object.entries(from).forEach(function(it) {
      let typeOf = typeof it[1];
      switch (typeOf) {
        // string number boolean undefined null object function
        case 'string':
          to[it[0]] = '';
          break;
        case 'number':
          to[it[0]] = 0;
          break;
        case 'boolean':
          to[it[0]] = false;
          break;
        // 数组和对象
        case 'object':
          // Date
          if(it[1] instanceof Date){
            to[it[0]] = '';
          }
          // Array
          else if (it[1] instanceof Array){
            to[it[0]] = [];
          }
          // JSON
          else {
            to[it[0]] = {};
            this.clearObj(to[it[0]], it[1])
          }
          break;
      }
    }.bind(this));
    return to;
  },
  // 深度拷贝
  deepClone(obj1,obj2){
    //最初的时候给它一个初始值=它自己或者是一个json
    var obj2=obj2||{};
    for(var name in obj1){
      //先判断一下obj[name]是不是一个对象
      if(typeof obj1[name] === "object"){
        //我们让要复制的对象的name项=数组或者是json
        obj2[name]= Array.isArray(obj1[name]) ? [] : {};
        //然后来无限调用函数自己 递归思想
        this.deepClone(obj1[name],obj2[name]);
      }else{
        //如果不是对象，直接等于即可，不会发生引用。
        obj2[name]=obj1[name];
      }
    }
    //然后在把复制好的对象给return出去
    return obj2;
  },
  judgeViewRole(){
    let viewRole = false;
    let user = this.getStorage('LOGIN_USER');
    let roles = user.roles.map(function(it){ return it.id; });
    if(roles.includes(45) && roles.length ==1){
      viewRole = true
    }else{
      viewRole = false
    }
    return viewRole;
  },
  // 判断用户信息
  judgeUserRole(){
    let user = this.getStorage('LOGIN_USER');
    let roles = user.roles.map(function(it){ return it.role; });
    let role = 0;
    if(roles.includes('员工')){
      role = 1;
    }
    else if(roles.includes('班组')){
      role = 2;
    }
    else if(roles.includes('部门')){
      role = 3;
    }
    else if(roles.includes('组织者/公司')){
      role = 4;
    }
    return role;
  },
  // 复制对象---把obj2复制给obj1
  cloneObj(obj1, obj2){
    // 遍历obj2
    Object.entries(obj2).forEach(function(it){
      // 如果obj1存在改属性
      if(it[1] && obj1.hasOwnProperty(it[0])){
        //console.log("type = ", it[0], ", value = ", it[1])
        // 如果属性值为对象，递归；否则赋值
        if(typeof it[1] === 'object'){
          if(Array.isArray(it[1])){
            obj1[it[0]] = it[1];
          } else {
            this.cloneObj(obj1[it[0]], it[1]);
          }
        } else {
          obj1[it[0]] = it[1];
        }
      }
    }.bind(this))
  },
  // json--->form data形式
  jsonToForm(data){
    let params = new URLSearchParams();
    Object.entries(data).forEach(function(it){
      params.append(it[0], it[1])
    })
    return params;
  },

  // 打印文件
  print(context, setting){
    // context 参数  上下文环境
    // setting 参数   url和filename
    context.$http({ // 用axios发送post请求
      method: 'get',
      url: setting.url, // 请求地址
      responseType: 'blob' // 表明返回服务器返回的数据类型
    }).then(function(res){ // 处理返回的文件流
      //  loading.close()
      const content = res
      const elink = document.createElement('a') // 创建a标签
      elink.download =  setting.filename // 文件名
      elink.style.display = 'none'
      const blob = new Blob([res.data])
      elink.href = URL.createObjectURL(blob)
      document.body.appendChild(elink)
      elink.click() // 触发点击a标签事件
      document.body.removeChild(elink)
    })
  },
  // 下载文件
  download(context, setting){

    // context 参数  上下文环境
    // setting 参数   url和filename
    context.$http({ // 用axios发送post请求
      method: 'get',
      url: setting.url, // 请求地址
      responseType: 'blob' // 表明返回服务器返回的数据类型
    }).then(function(res){ // 处理返回的文件流

      console.log(111,res)
      if(res.success == false){
        context.$message({
          type : 'error',
          message : res.message || '错误'
        })
        return;
      }
      //  loading.close()
      const content = res
      const elink = document.createElement('a') // 创建a标签
      elink.download =  setting.filename // 文件名
      elink.style.display = 'none'
      const blob = new Blob([res.data])
      elink.href = URL.createObjectURL(blob)
      document.body.appendChild(elink)
      elink.click() // 触发点击a标签事件
      document.body.removeChild(elink)
    })
  },
  // 获取权限按钮---根据模块名称来获取
  /*
  * 两层目录
  * 根据模块名称来获取按钮——只使用于两层的路径，例如教育培训---持证计划
  * 参数：moduleName -- 模块的名称，比如教育培训 eduTrainingMenu
  *       path -- 菜单的路径，比如三级教育的URL  edu-training-menu/demand-survey-staff-index
  * */
  getPowerBtns(moduleName, path){
    // 找到对应的模块，例如当前的是教育培训模块
    // 找到对应的菜单的路径，例如当前点击的是教育培训---三级教育
    // 通过对比路径，来找到当前点击菜单下的按钮
    let module = this.getStorage('SAFE_PLATFORM_MENU')['subMenu'][moduleName].find(function(it){
      return it.url === path;
    }.bind(this));
    let list = [];
    if(module){
      list = (module['list'] || []).map(function(it){
        return it.name;
      }.bind(this)) || [];
    }
    return list;
  },



  // 获取权限按钮---根据模块名称来获取
  /*
   * 三层目录
   * 根据模块名称来获取按钮——只使用于三层的路径，例如 系统管理-工作流-安全文件
   * 参数：moduleName -- 模块的名称，系统管理 manageMenu
   *       path_1st -- 菜单的一层路径，系统管理-工作流  /manage-menu/workflow-manage
   *       path_2nd -- 菜单的二层路径，系统管理-工作流-安全文件  /manage-menu/safe-file-index
   * */
  getPowerBtns2URL(moduleName, path_1st, path_2nd){
    // 找到对应的模块，例如当前的是教育培训模块
    // 找到对应的菜单的路径，例如当前点击的是教育培训---三级教育
    // 通过对比路径，来找到当前点击菜单下的按钮
    let module_1st = this.getStorage('SAFE_PLATFORM_MENU')['subMenu'][moduleName].find(function(it){
      return it.url === path_1st;
    }.bind(this));
    //console.log(module_1st);
    // 再寻找二层路径
    let module_2nd = [];
    if(module_1st && module_1st.list && module_1st.list.length > 0){
      module_2nd = module_1st.list.find(function(it){
        return it.url == path_2nd;
      }.bind(this))
    }

    let list = [];
    if(module_2nd.list && module_2nd.list.length > 0){

      //console.log(module_2nd.list);
      list = module_2nd['list'].map(function(it){
          return it.name;
        }.bind(this)) || [];
    }
    //console.log(list)
    return list;
  },




  // 获取URL对应的参数
  getQueryString(name) {
    let value = '';
    let url = window.location.href;
    console.log("地址：", url)
    let search = url.substr(url.indexOf("?") + 1)
    let paramArr = search.split("&")
    for( var i=0; i < paramArr.length; i++){
      let param = paramArr[i].split("=");
      if(param[0] == name){
        value = param[1]
        break;
      }
    }
    return value;
  },
  // 获取登录人的信息
  getLoginer(){
    return this.getStorage("LOGIN_USER");
  },

  getPercent(num, total) {
    /// <summary>
    /// 求百分比
    /// </summary>
    /// <param name="num">当前数</param>
    /// <param name="total">总数</param>
    num = parseFloat(num);
    total = parseFloat(total);
    if (isNaN(num) || isNaN(total)) {
      return "-";
    }
    return total <= 0 ? "0%" : (Math.round(num / total * 10000) / 100.00)+"%";
  },
  getNowTime() {
    var now = new Date();
    var year = now.getFullYear(); //得到年份
    var month = now.getMonth(); //得到月份
    var date = now.getDate(); //得到日期
    var hour =" 00:00:00"; //默认时分秒 如果传给后台的格式为年月日时分秒，就需要加这个，如若不需要，此行可忽略
    month = month + 1;
    month = month.toString().padStart(2, "0");
    date = date.toString().padStart(2, "0");
    var defaultDate = `${year}-${month}-${date}${hour}`;
    console.log(defaultDate)
    return defaultDate;
  },
  isLeapYear(year) {
    var cond1 = year % 4 == 0;  //条件1：年份必须要能被4整除
    var cond2 = year % 100 != 0;  //条件2：年份不能是整百数
    var cond3 = year % 400 ==0;  //条件3：年份是400的倍数
    //当条件1和条件2同时成立时，就肯定是闰年，所以条件1和条件2之间为“与”的关系。
    //如果条件1和条件2不能同时成立，但如果条件3能成立，则仍然是闰年。所以条件3与前2项为“或”的关系。
    //所以得出判断闰年的表达式：
    var cond = cond1 && cond2 || cond3;
    if(cond) {
      return true;
    } else {
      return false;
    }
},


}

