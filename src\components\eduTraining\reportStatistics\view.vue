<template>
  <el-container class="container">
    <el-main>
      <!--发布-->
      <el-form ref="info" label-width="100px" :model="info">
        <el-row type="flex">
          <el-col :span="24">
            <el-form-item label="任务名称">
              <el-input v-model="info.title"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex">
          <el-col :span="24">
            <el-form-item label="描述">
              <el-input type="textarea" v-model="info.remark"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="文件:">
              <el-button type="text" @click="showPdfDialog">{{
                info.fileName
              }}</el-button>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex">
          <el-col :span="24">
            <chooseStaff
              ref="chooseStaff"
              @selectedRows="selectedRows"
            ></chooseStaff>
          </el-col>
        </el-row>
        <el-row type="flex" class="row" justify="center">
          <!-- <el-button
            @click="saveBtnClickHandle({ status: 0 })"
            size="small"
            :span="2"
            type="success"
            >保存</el-button
          >
          <el-button
            @click="saveBtnClickHandle({ status: 1 })"
            size="small"
            :span="2"
            type="success"
            >提交</el-button
          > -->
          <el-button size="small" :span="2" @click="$router.back()"
            >返回</el-button
          >
        </el-row>
      </el-form>
    </el-main>
    <el-dialog
      :title="dialog.title"
      width="100%"
      top="0vh"
      :center="true"
      :visible.sync="dialog.isShow"
    >
      <iframe
        :src="dialog.pdfUrl + '#toolbar=0'"
        width="100%"
        height="810"
      ></iframe>
    </el-dialog>
  </el-container>
</template>

<script>
import { VueEditor } from "vue2-editor";
import chooseStaff from "./chooseStaff";
import fileUpload from "@/components/common/fileUploadXLSX";
export default {
  components: {
    chooseStaff,
    VueEditor,
    fileUpload,
  },
  data() {
    return {
      // info表
      info: {
        // ID
        id: "",
        title: "",
        remark: "",
        fileId: "",
        status: "",
        // 用户id
        userIds: [],
      },
      // 辅助字段
      assist: {
        time: "", // 时间期限
        // 用户
        userList: [],
        // excel路劲
        excelPath: "",
      },

      upload: {
        params: {
          contentId: this.$tool.getStorage("LOGIN_USER").userId,
          contentType: 26,
        },
        btns: {
          // 上传按钮
          upload: {
            isShow: true,
          },
          // 下载按钮
          download: {
            isShow: false,
          },
          // 删除按钮
          delete: {
            isShow: true,
          },
        },
        uploadUrl: "",
        uploadCookies: true,
        fileData: [],
      },

      viewOrEdit: false,
      // 权限按钮
      powerBtns: [],
      // pdf查看对话框
      dialog: {
        //查看安全风险告知书
        isShow: false,
        pdfUrl: "http://www.safe360.vip/safeFile.pdf",
        title: "查看安全风险告知书",
      },
    };
  },
  watch: {
    $route(to, from) {
      // 如果来至列表页
      if (
        from.name === "ReportStatisticsIndex" &&
        this.$route.name === "ReportStatisticsAdd"
      ) {
        this.init();
      }
    },
  },
  activated() {
    this.init();
  },
  mounted() {
    this.init();
  },
  methods: {
    // 文章查看
    showPdfDialog() {
      // this.dialog.title = this.info.title;
      window.open(this.info.filePath);
      // this.dialog.pdfUrl = this.info.filePath;
      // this.dialog.isShow = true;
    },
    // 初始化
    init() {
      this.searchBtnClickHandle();
    },
    timeChange(e) {
      // console.log(e);
      if (e) {
        this.info.startTime = e[0];
        this.info.endTime = e[1];
      }
    },

    articleChangeFn(item) {
      //        console.log(111,item);
      //        console.log(222,this.assist.articleList);
      this.info.listNewsFile = [];
      for (var i = 0; i < item.length; i++) {
        for (var j = 0; j < this.assist.articleList.length; j++) {
          if (this.assist.articleList[j].value == item[i]) {
            this.info.listNewsFile.push({
              fileId: item[i],
              fileName: this.assist.articleList[j].label,
            });
            break;
          }
        }
      }
      //        console.log(111,this.info.listNewsFile)
    },
    // 人员列表选择组件处理函数
    selectedRows(rows) {
      // 参与人员列表----用户userId列表
      let userIds = rows.map(function (it) {
        return it.userId;
      });
      this.info.userIds = userIds;
    },

    fileDataFn(data) {
      console.log("父组件获取子组件数据：", data);
      this.info.listNewsFile = data.map(function (e) {
        return {
          id: "",
          eduNewsId: "",
          fileId: e.fileId,
          fileName: e.fileName,
        };
      });
      console.log(888, this.info.listNewsFile);
    },

    judgeUserRole() {
      // 获取权限按钮
      let url = "/edu-training-menu/daily-training-index";
      this.powerBtns = this.$tool.getPowerBtns("eduTrainingMenu", url);
    },
    // 清空数据
    clear() {
      this.info.id = "";
      this.info.title = "";
      this.info.remark = "";
      this.info.fileId = "";
      this.info.status = "";
      this.info.userIds = [];

      this.upload.fileData = [];
      this.viewOrEdit = false;
      // 清空人员列表
      if (this.$refs["chooseStaff"].changeTableDataHandle) {
        this.$refs["chooseStaff"].changeTableDataHandle([]);
      }
      //        this.assist = this.$tool.clearObj({}, this.assist);
    },
    // 根据id搜索信息
    searchBtnClickHandle() {
      this.clear();
      let _this = this;
      let id = this.$route.params.id;
      this.viewOrEdit = true;
      let url = "sys/sysManageFile/findReportFormById";

      _this.$http.post(url, { id: id }).then(function (res) {
        if (res.data.success) {
          _this.info = res.data.data;
          // _this.assist.excelPath = res.data.filePath;
          let userList =
            res.data.data.userList.map(
              function (it) {
                return {
                  userId: it.userId,
                  companyId: it.companyId,
                  parentCompanyId: it.parentCompanyId,
                  companyName: it.companyName,
                  deptName: it.deptName,
                  username: it.username,
                  fileName: it.fileName,
                  filePath: it.filePath,
                  userStatus: it.userStatus,
                };
              }.bind(this)
            ) || [];
          _this.$refs["chooseStaff"].changeTableDataHandle(userList);
          //同步文件
          // _this.$refs['uploadComponent'].loadFile();
        } else {
          // _this.$message({
          //   showClose: true,
          //   message: "2失败！",
          //   type: "error",
          // });
        }
      });
    },
    // 未发布/已发布/进行中【开始按钮】--培训发布--保存按钮
    saveBtnClickHandle(options) {
      let _this = this;
      let params = this.info;
      if (params["userIds"].length == 0) {
        _this.$message({
          showClose: true,
          message: "请选择参与人员！",
          type: "error",
        });
        return;
      }
      if (!this.info.listNewsFile || this.info.listNewsFile.length == 0) {
        return this.$message({
          showClose: true,
          message: "请上传文件！",
          type: "error",
        });
      }
      params["fileId"] =
        this.info.listNewsFile[this.info.listNewsFile.length - 1].fileId;

      params["status"] = options.status;

      let url = "sys/sysManageFile/addOrUpdateReportForm";

      _this.$http.post(url, params).then((res) => {
        console.log(res);
        if (res.data.success) {
          _this.$message.success("操作成功");
        } else {
          _this.$message({
            showClose: true,
            message: "操作2失败！",
            type: "error",
          });
        }
      });
    },
  },
};
</script>

<style>
.container {
  background: #fff;
  padding: 0px 20px 20px;
}

.title {
  background: rgba(64, 158, 255, 0.1);
  color: #0f6fc6;
  border: 1px solid rgba(64, 158, 255, 0.2);
  border-radius: 5px;
}

.row {
  margin-top: 10px;
}
</style>
