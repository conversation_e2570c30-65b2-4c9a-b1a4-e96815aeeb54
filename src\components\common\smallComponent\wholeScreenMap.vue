<template>
  <div id="mapView" style="width: 100%;height: 100%;">
    <div id="wholeScreenMap" style="position: absolute;top: 0;left: 0;bottom: 0;right: 0;padding: 0;margin: 0"></div>
    <div id="infoCard" style="position: fixed;top: 80px;right: 110px;width: 300px;min-height: 30px;z-index: 4;border-radius: 10px;padding: 10px;">
      <el-cascader
        v-model="searchArr"
        :options="dotOptions"
        placeholder="请输入站点名称或所在线路"
        expand-trigger="hover"
        filterable
        clearable
        :props="dotProps"
        @change="selectClick"
        style="width: 100%"
        :show-all-levels="false"
      ></el-cascader>
    </div>
  </div>
</template>
<script>
  import bmap from 'echarts/extension/bmap/bmap'
  //地图对象，标记对象，
  let mapObj;
  export default {
    name: 'mapView',
    data () {
      return {
        markers:[],

        //数据集
        tableData:[],
        bigDotData:[],
        // bigDotData:[
        //   {nodeName:'南岸服务区',positionName:'杭州湾跨海大桥',pointName:'南岸服务区',headPerson:'邬贤明',safePerson:'陈旭军',personNum:'293',longitude:121.190051,latitude:30.330287,description:'成立于2008年5月，位于G15沈海高速杭州湾跨海大桥段南岸，桩号G1413+800，主要承担服务区经营保障服务职能。'},
        //   {nodeName:'北岸服务区',positionName:'杭州湾跨海大桥',pointName:'北岸服务区',headPerson:'吕德平',safePerson:'郑元斌',personNum:'316',longitude:121.038615,latitude:30.595908,description:'成立于2008年5月，位于G15沈海高速杭州湾跨海大桥段北岸，桩号K1380，主要承担服务区经营保障服务职能。'},
        //   {nodeName:'慈城服务区',positionName:'杭州湾跨海大桥南岸连接线',pointName:'慈城服务区',headPerson:'张科',safePerson:'严鹏',personNum:'252',longitude:121.429545,latitude:30.023622,description:'于2008年5月份开业，位于沈海高速公路K1465处，分东西两区，占地面积67165平方米，总建筑面积6408.8平方米，主要承担服务区经营保障服务职能。'},
        //   {nodeName:'镇海服务区',positionName:'宁波绕城高速公路（东段）',pointName:'镇海服务区',headPerson:'张衠瑜',safePerson:'冯宗广',personNum:'212',longitude:121.616195,latitude:29.997894,description:'成立于2012年9月，位于宁波绕城高速东段公路K17处，分南北两区，区占地面积约87333平方米，总建筑面积约6000多平方米，主要承担服务区经营保障服务职能。 '}
        // ],

        //控件数据
        searchArr:[],
        dotOptions:[],
        dotProps:{
          value:'id',
          label:'positionName',
          children:'sysPositionNodeROS'
        }
      }
    },
    mounted:function () {//组件加载完后调用
      this.getSiteList();
      this.getSiteTree();
    },
    watch:{

    },
    methods:{
      //获取说有站点信息，在地图上描点
      getSiteList:function(){
        this.$http.post('sysPositionNode/getNodeList').then(function (res) {
          if (res.data.success) {
            //获取所有站点数据
            this.tableData.splice(0);
            this.tableData=res.data.data;

            //获取大点数据--开始
            let params=new URLSearchParams;
            params.append("searchValue",'服务区');
            this.$http.post('sysPositionNode/getNodeList',params).then(function (res) {
              if (res.data.success) {
                this.bigDotData.splice(0);
                this.bigDotData=res.data.data;

                //绘制地图和点--开始
                let option = {
                  title: {
                    text: '',
                    left: 'center'
                  },
                  tooltip: {
                    trigger: 'item',
                    formatter: function (params) {
                      return this.findContentOfDot(params.seriesName,params.name,params.value);
                    }.bind(this),
                  },
                  bmap: {
                    center: [121.425066,30.159903],
                    zoom: 10,
                    roam: true,
                    mapStyle: {
                      styleJson: [
                        {
                          'featureType': 'land',     //调整土地颜色
                          'elementType': 'geometry',
                          'stylers': {
                            'color': '#081734'
                          }
                        },
                        {
                          'featureType': 'building',   //调整建筑物颜色
                          'elementType': 'geometry',
                          'stylers': {
                            'color': '#04406F'
                          }
                        },
                        {
                          'featureType': 'building',   //调整建筑物标签是否可视
                          'elementType': 'labels',
                          'stylers': {
                            'visibility': 'off'
                          }
                        },
                        {
                          'featureType': 'highway',     //调整高速道路颜色
                          'elementType': 'geometry',
                          'stylers': {
                            'color': '#015B99'
                          }
                        },
                        {
                          'featureType': 'highway',    //调整高速名字是否可视
                          'elementType': 'labels',
                          'stylers': {
                            'visibility': 'off'
                          }
                        },
                        {
                          'featureType': 'arterial',   //调整一些干道颜色
                          'elementType': 'geometry',
                          'stylers': {
                            'color':'#003051'
                          }
                        },
                        {
                          'featureType': 'arterial',
                          'elementType': 'labels',
                          'stylers': {
                            'visibility': 'off'
                          }
                        },
                        {
                          'featureType': 'green',
                          'elementType': 'geometry',
                          'stylers': {
                            'visibility': 'off'
                          }
                        },
                        {
                          'featureType': 'water',
                          'elementType': 'geometry',
                          'stylers': {
                            'color': '#044161'
                          }
                        },
                        {
                          'featureType': 'subway',    //调整地铁颜色
                          'elementType': 'geometry.stroke',
                          'stylers': {
                            'color': '#003051'
                          }
                        },
                        {
                          'featureType': 'subway',
                          'elementType': 'labels',
                          'stylers': {
                            'visibility': 'off'
                          }
                        },
                        {
                          'featureType': 'railway',
                          'elementType': 'geometry',
                          'stylers': {
                            'visibility': 'off'
                          }
                        },
                        {
                          'featureType': 'railway',
                          'elementType': 'labels',
                          'stylers': {
                            'visibility': 'off'
                          }
                        },
                        {
                          'featureType': 'all',     //调整所有的标签的边缘颜色
                          'elementType': 'labels.text.stroke',
                          'stylers': {
                            'color': '#313131'
                          }
                        },
                        {
                          'featureType': 'all',     //调整所有标签的填充颜色
                          'elementType': 'labels.text.fill',
                          'stylers': {
                            'color': '#FFFFFF'
                          }
                        },
                        {
                          'featureType': 'manmade',
                          'elementType': 'geometry',
                          'stylers': {
                            'visibility': 'off'
                          }
                        },
                        {
                          'featureType': 'manmade',
                          'elementType': 'labels',
                          'stylers': {
                            'visibility': 'off'
                          }
                        },
                        {
                          'featureType': 'local',
                          'elementType': 'geometry',
                          'stylers': {
                            'visibility': 'off'
                          }
                        },
                        {
                          'featureType': 'local',
                          'elementType': 'labels',
                          'stylers': {
                            'visibility': 'off'
                          }
                        },
                        {
                          'featureType': 'subway',
                          'elementType': 'geometry',
                          'stylers': {
                            'lightness': -65
                          }
                        },
                        {
                          'featureType': 'railway',
                          'elementType': 'all',
                          'stylers': {
                            'lightness': -40
                          }
                        },
                        {
                          'featureType': 'boundary',
                          'elementType': 'geometry',
                          'stylers': {
                            'color': '#8b8787',
                            'weight': '1',
                            'lightness': -29
                          }
                        }]
                    }
                  },
                  series: [{
                    name: '站点',
                    type: 'scatter',
                    coordinateSystem: 'bmap',
                    data: this.convertData(this.tableData),
                    symbolSize: 10,
                    label: {
                      normal: {
                        formatter: '{b}',
                        position: 'right',
                        show: false
                      },
                      emphasis: {
                        show: true
                      }
                    },
                    itemStyle: {
                      normal: {
                        color: '#f4e925',
                        shadowBlur: 10,
                        shadowColor: '#333'
                      }
                    }
                  },
                    {
                      name: '服务区',
                      type: 'effectScatter',
                      coordinateSystem: 'bmap',
                      data: this.convertData(this.bigDotData),
                      symbolSize: 23,
                      showEffectOn: 'render',
                      rippleEffect: {
                        brushType: 'stroke'
                      },
                      hoverAnimation: true,
                      label: {
                        normal: {
                          formatter: '{b}',
                          position: 'right',
                          show: true
                        }
                      },
                      itemStyle: {
                        normal: {
                          color: '#f4e925',
                          shadowBlur: 10,
                          shadowColor: '#333'
                        }
                      },
                      zlevel: 1
                    }
                  ]
                };

                var myChart = this.echarts.init(document.getElementById('wholeScreenMap'));
                myChart.setOption(option);

                mapObj = myChart.getModel().getComponent('bmap').getBMap();
                mapObj.addControl(new BMap.MapTypeControl());
                //绘制地图和点--结束

              }
            }.bind(this)).catch(function (err) {
              console.log(err);
            });
            //获取大点数据--结束

          }
        }.bind(this)).catch(function (err) {
          console.log(err);
        });
      },
      //获取查询框的树形数据
      getSiteTree:function(){
        this.$http.post('sysPositionNode/getNodeTree').then(function (res) {
          if (res.data.success) {
            this.dotOptions.splice(0);
            this.dotOptions=res.data.data;
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
        });
      },
      //加载标记点
      loadMaker:function () {
        for (let i=0;i<this.tableData.length;i++){
          let marker = new BMap.Marker(new BMap.Point(this.tableData[i].longitude,this.tableData[i].latitude),{title:this.tableData[i].nodeName});        // 创建标注
          mapObj.addOverlay(marker);// 将创建的点标记添加到已有的地图实例：
          marker.addEventListener("click",function(e){
            this.openInfo(this.tableData[i])}.bind(this)
          );
        }
      },



      //-------------------------echart函数-开始---------------------------
      //图表数据处理
      convertData : function(data) {
        let list=[];
        for(let i=0;i<data.length;i++){
          list.push({
            name:data[i].nodeName,
            value:[data[i].longitude,data[i].latitude,i]//添加index进到值中,是为了方便找到该项的内容，在formatter里显示
          })
        }
        return list;
      },
      //查找服务区并返回详细信息
      findContentOfDot:function (series,name,val) {
        let str=String(val);
        let arr=str.split(",");
        let content='';
        if(series==='服务区'){
          content=this.bigDotData[arr[2]];
        }else {
          content=this.tableData[arr[2]];
        }
        let info = [];
        info.push(name);
        info.push("所在地：    "+content.positionName);
        info.push("负责人：    "+content.headPerson);
        info.push("安全负责人："+content.safePerson);
        //info.push("从业人数：  "+content.personNum);
        return info.join("<br/>");
      },
      //-------------------------echart函数--结束--------------------------

      //-------------------------搜索框函数和响应窗口-开始---------------------
      //搜索框函数
      selectClick:function (list) {
        if(list.length>1){
          let params=new URLSearchParams;
          params.append("id",list[1]);
          this.$http.post('sysPositionNode/getNodeDetail',params).then(function (res) {
            if (res.data.success) {
              mapObj.closeInfoWindow();
              mapObj.centerAndZoom(new BMap.Point(121.425066,30.159903),10);
              this.openInfo(res.data.data);
            }
          }.bind(this)).catch(function (err) {
            console.log(err);
          });
        }else{
          mapObj.closeInfoWindow();
          mapObj.centerAndZoom(new BMap.Point(121.425066,30.159903),10);
        }
      },
      //显示信息窗体
      openInfo:function (row) {
        let point = new BMap.Point(row.longitude,row.latitude);
        //构建信息窗体中显示的内容
        let info = [];
        info.push("<div style=\"width: 300px;font-size: 14px\"><div style=\"padding:0 5px 5px 4px;color: #0168B7\"><b>"+row.nodeName+"</b></div><div style=\"line-height: 20px\"> "+"负责人：    "+row.headPerson);
        info.push("负责人电话："+row.headPersonPhone);
        info.push("安全负责人："+row.safePerson);
        info.push("安全员电话："+row.safePersonPhone);
        //info.push("从业人数：  "+row.personNum);
        //info.push("部门概况：  "+row.description);
        info.push("</div></div>");
        let infoWindow = new BMap.InfoWindow(info.join("<br/>"),{offset:new BMap.Size(1,1)});  // 创建信息窗口对象
        mapObj.centerAndZoom(point,12);
        mapObj.openInfoWindow(infoWindow,point);//开启信息窗口
      },
      //-------------------------搜索框函数和响应窗口-结束---------------------
    }

  }

</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>

</style>
