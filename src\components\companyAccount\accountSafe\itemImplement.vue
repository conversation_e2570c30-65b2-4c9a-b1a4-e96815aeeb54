<template>
  <div>
    <el-container style="background:#fff;">
      <el-main>
        <el-row style="margin:0">
          <el-col :span="6">
            <el-button type="primary" size="mini" @click="$router.back()">返回</el-button>
          </el-col>
        </el-row>
        <el-row style="margin:10px 0 0 0">
          <el-col :span="12">
            <el-table
              border
              show-summary
              highlight-current-row
              @current-change="handleCurrentChange"
              :data="mainTableData.list"
              style="width: 95%">
              <el-table-column
                prop="item"
                label="预算计划项目名称"
                min-width="150">
              </el-table-column>
              <el-table-column
                prop="itemTotalBudget"
                label="费用预算（元）">
              </el-table-column>
              <el-table-column
                prop="accountSum"
                label="预算执行（元）">
              </el-table-column>
              <!--<el-table-column
                label="预算余额（元）">
                <template slot-scope="scope">
                  {{scope.row.itemTotalBudget - scope.row.itemCurrentBudget}}
                </template>-->
              <!-- </el-table-column> -->
              <el-table-column
                prop="accountRemain"
                label="预算余额（元）">
              </el-table-column>
            </el-table>
            <el-pagination
              background
              layout="prev, pager, next"
              :current-page="mainTableData.pageNum"
              :page-size="form.pageSize"
              :total="mainTableData.total"
              @current-change ="disasterPageChangeHandle">
            </el-pagination>
          </el-col>
          <el-col :span="12">
            <el-table
              border
              show-summary
              :data="detailTableData">
              <el-table-column
                prop="item"
                label="项目明细"
                min-width="150">
              </el-table-column>
              <el-table-column
                prop="itemBudget"
                label="费用预算（元）">
              </el-table-column>
              <el-table-column
                prop="accountSum"
                label="预算执行（元）">
              </el-table-column>
              <el-table-column
                label="预算余额（元）"
                prop="accountRemain">
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>
      </el-main>
    </el-container>
  </div>
</template>
<script>
  export default {
    data() {
      return {
        form : {
          // 年份
          year : '',
          // 当前页
          pageCurrent : 1,
          // 页数大小
          pageSize : 10,
        },
        // 主表----左边
        mainTableData : [],
        // 次表----右边
        detailTableData : [],
      }
    },
    created(){
      this.init();
    },
    watch:{
      $route(to,from){
        let year = to.params && to.params.row && to.params.row.year;
        if(to.name === 'accountSafeItemImplement') {
          if(year){
            let date = new Date();
            date.setFullYear(year);
            this.form.year = date;
            this.searchBtnClickHandle();
          }
        }
      }
    },
    methods:{
      // 初始化
      init(){
        let date = new Date();
        date.setFullYear(this.$route.params.row.year);
        this.form.year = date;
        // 搜索
        this.searchBtnClickHandle();
      },
      // 清空数据
      clear(){
        this.mainTableData = [];
        this.detailTableData = [];
      },
      // 分页
      disasterPageChangeHandle(page){
        this.form.pageCurrent = page;
        this.searchBtnClickHandle();
      },
      // 搜索按钮
      searchBtnClickHandle(){
        // 根据年份获取项目
        this.$store.dispatch('costBudgetItemFindByYear', this.form).then(function(res){
          if(res.success){
            this.mainTableData = res.data;
          } else {
            this.clear();
          }
        }.bind(this));
      },
      // 左边表格---单选
      handleCurrentChange(val){
        let id = val.id;
        // 项目明细
        this.$store.dispatch('costBudgetItemShow', { id : id }).then(function(res){
          if(res.success){
            let list = res.data.costBudgetSubItems;
            if(list.length == 0){
              this.$message({
                type : 'warning',
                message : '该项目暂时没有明细！'
              })
              return;
            }
            this.detailTableData = list;
          }
        }.bind(this));
      }

    }
  }
</script>
<style>
</style>
