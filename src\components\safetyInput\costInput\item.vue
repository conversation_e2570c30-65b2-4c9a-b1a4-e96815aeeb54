<template>
  <div id="trainingPlanIndex">
    <div class="background-style">
      <!--搜索区-->
      <div class="search-bar">
        <div style="padding:10px 10px 0 10px;float: left">
          <el-button
            v-if="!viewRole"
            @click="addBtnClickHandle"
            type="success" icon="el-icon-plus" style="margin-left: 20px">费用记录</el-button>
          <el-button
            @click="$router.back();"
            style="margin-left: 20px">返回</el-button>
        </div>
      </div>
      <!--表格区-->
      <div style="width: 100%;">
        <div style="padding: 20px 10px 20px 10px">
          <el-row style="margin-bottom:10px;">
            {{ ($tool.formatDateTime(reg.year) || '').substring(0,4) }}年度费用投入表（单位：元）
          </el-row>
          <el-table
            border
            show-summary
            :summary-method="getSummaries"
            :data="tableData.list"
            style="width: 100%">
            <el-table-column
              type="index"
              label="编号"
              width="100"
              align="center"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="item"
              label="项目"
              align="center"
              min-width="150"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="accountTime"
              align="center"
              :formatter="formatDateTime"
              label="发生时间"
              width="150"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="accountNum"
              align="center"
              label="费用金额（元）"
              width="150"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="department"
              label="经办部门"
              align="center"
              width="150"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="remark"
              label="备注"
              align="center"
              width="150"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              fixed="right" label="操作"
              label-class-name="header-style"
              align="center" width="200">
              <template slot-scope="scope">
                <template>
                  <el-button v-if="!viewRole" size="mini" type="primary" @click="itemUpdateClick(scope.row)">修改</el-button>
                  <el-button v-if="!viewRole"   size="mini" type="danger" @click="itemDeleteClick(scope.row)">删除</el-button>
                </template>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div style="margin-top: 10px">
          <el-pagination
            background
            layout="prev, pager, next"
            :current-page="tableData.pageNum"
            :page-size="form.pageSize"
            :total="tableData.total"
            @current-change ="disasterPageChangeHandle">
          </el-pagination>
        </div>
      </div>
    </div>
    <!--对话框-->
    <el-dialog
      :visible.sync="dialog.isShow"
      title="费用投入明细"
      width="70%"
      :before-close="handleClose">
      <el-form label-width="150px" ref="form" :rules="dialog.rules" :model="dialog.form">
        <el-row  class="dialogrow" >
          <el-col :span="12">
            <el-form-item label="项目：" style="margin: 0px" prop="item">
              <el-input v-model="dialog.form.item"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发生时间：" style="margin: 0px" prop="accountTime">
              <el-date-picker
                style="width:100%;"
                v-model="dialog.form.accountTime"
                type="date"
                placeholder="选择日期">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row  class="dialogrow">
          <el-col :span="12">
            <el-form-item label="金额（元）：" style="margin: 0px" prop="accountNum">
              <el-input-number v-model="dialog.form.accountNum" style="width:100%;"></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="经办部门：" style="margin: 0px">
              <!-- <span>{{dialog.form.department}}</span>-->
              <!--

                  v-model="dialog.assist.deptSubcomIds"


              -->
              <el-select
                v-model="dialog.form.deptId"
                @change="deptSubcomNamesSelect"
                placeholder="请选择">
                <el-option
                  v-for="item in dialog.assist.deptSubcomNames"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>

            </el-form-item>
          </el-col>
        </el-row>
        <el-row  class="dialogrow" >
          <el-col :span="12">
            <el-form-item label="预算项目一级" style="margin: 0px">
              <el-select
                :disabled="dialog.form.id != ''"
                @change="budgetPlanChange"
                v-model="dialog.assist.budgetPlanId"
                placeholder="请选择">
                <el-option
                  v-for="item in dialog.assist.planList"
                  :key="item.id"
                  :label="item.item"
                  :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="预算内余额（元）：" style="margin: 0px">
              <!--<span :style="{ color : isOverran(dialog.assist.selectedFirstItem.itemTotalBudget - dialog.assist.selectedFirstItem.accountSum) }">{{(dialog.assist.selectedFirstItem.itemTotalBudget - dialog.assist.selectedFirstItem.accountSum) || 0}}</span>-->
              <span :style="{ color : isOverran(dialog.assist.accountRemain) }">{{dialog.assist.accountRemain || 0}}</span>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="总预算金额（元）：" style="margin: 0px">
              <span>{{dialog.assist.itemTotalBudget || 0}}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row  class="dialogrow">
          <el-col :span="12">
            <el-form-item label="预算项目二级"  prop="budgetItemId" style="margin: 0px">
              <el-select @change="budgetItemChange" v-model="dialog.form.budgetItemId" placeholder="请选择">
                <el-option
                  v-for="item in dialog.assist.itemList"
                  :key="item.id"
                  :label="item.item"
                  :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="预算内余额（元）：" style="margin: 0px">
              <span :style="{ color : isOverran(dialog.assist.selectedSecondItem.accountRemain) }" >{{(dialog.assist.selectedSecondItem.accountRemain) || 0}}</span>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="总预算金额（元）：" style="margin: 0px">
              <span>{{dialog.assist.selectedSecondItem.itemBudget || 0}}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row  class="dialogrow">
          <el-col :span="24">
            <el-form-item label="备注：" style="margin: 0px">
              <el-input v-model="dialog.form.remark" type="textarea" :rows="2"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row class="dialogrow" v-if="dialog.form.id">
          <el-col :span="24">
            <fileUpload :data="dialog.assist.upload"></fileUpload>
          </el-col>
        </el-row>
        <!--<el-row  class="row">-->
        <!--<el-col :span="2">-->
        <!--<el-form-item>-->

        <!--</el-form-item>-->
        <!--</el-col>-->
        <!--</el-row>-->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          type="danger"  size="mini"
          @click="dialogOkBtnClickHandle">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
  import fileUpload from '@/components/common/fileUpload';
  export default {
    components : {
      fileUpload
    },
    data() {
      return {
        form : {
          // 父级ID
          accountRegId : '',
          // 当前页
          pageCurrent : 1,
          // 页数大小
          pageSize : 10,
        },
        tableData : [],
        // 对话框
        dialog : {
          // 是否显示
          isShow : false,
          form : {
            // 父级ID
            accountRegId : '',
            // 自己的id
            id : '',
            // 项目
            item : '',
            // 发生时间
            accountTime : '',
            // 发生金额
            accountNum : '',
            accountRemain : '',
            itemTotalBudget : '',
            // 经办部门
            department : '',
            // 经办部门ID
            deptId : '',
            // 备注
            remark : '',
            // 预算项目--二级
            budgetItemId : '',
          },
          rules: {
            item: [
              {required: true, message: '请输入内容', trigger: 'blur'},
            ],
            accountTime: [
              { type: 'date', required: true, message: '请选择日期', trigger: 'blur' }
            ],
            accountNum: [
              {required: true, message: '请输入内容', trigger: 'blur'},
            ],
            budgetItemId: [
              {  required: true, message: '请选择', trigger: 'change' }
            ],
          },
          assist : {
            // 上传文件
            upload : {
              // 上传参数
              params : {
                contentId: '',
                contentType: 11
              },
            },
            // 预算费用项目列表---一级分类
            planList : [],
            // 被选中的一级分类
            selectedFirstItem : {},
            // 预算内余额--一级
            accountRemain : 0,
            // 总预算金额--二级
            itemTotalBudget : 0,
            // 被选中的一级分类的id----预算费用的id
            budgetPlanId : '',
            // 预算费用项目列表---二级级分类
            itemList : [],
            // 被选中的一级分类
            selectedSecondItem : {},
            // 部门
            deptSubcomNames : [],
          },
          // 父级的信息---费用投入
          reg : '',
        },
        // 父级传递的参数
        reg : '',
        //浏览角色模式
        viewRole : false,
      }
    },
    mounted(){
      this.init();
    },
    watch:{
      $route(to,from){
        if(from.name === 'costInputIndex') {
          this.init();
        }
      },
      'dialog.isShow'(to, from){
        // 关闭对话框的时候
        if(!to){
          this.clear();
          console.log(this.dialog.form)
        }
      }
    },
    methods:{
      // 通过部门ID获取子公司信息
      getSubCompanyInfo(){
        this.$store.dispatch('deptGetOrgDept',{}).then(function(res){
          if(res.success){
            let deptList = res.data[0].subDept;
            // 遍历部门
            if (deptList){
              deptList.forEach(function(it){

                this.dialog.assist.deptSubcomNames.push({
                  label : it.name,
                  value : it.id
                })
              }.bind(this))
            }
          } else {
            this.$message({
              type : 'error',
              message : res.message || '错误2'
            })
          }

        }.bind(this))
      },
      // 选择部门
      deptSubcomNamesSelect(data){
        console.log("data = ", data)
        this.dialog.form.deptId = data;

        // 通过id来着名称
        for( var i = 0; i < this.dialog.assist.deptSubcomNames.length; i ++ ){
          let item = this.dialog.assist.deptSubcomNames[i];
          if(data == item.value){
            this.dialog.form.department = item.label;
            break;
          }
        }
        console.log("form = ", this.dialog.form)
      },
      // 清空数据
      clear(){
        // this.dialog.form.accountRegId = '';
        this.dialog.form.id = '';
        this.dialog.form.item = '';
        this.dialog.form.accountTime = '';
        this.dialog.form.accountNum = '';
        this.dialog.form.deptId = '';
        this.dialog.form.department = '';
        this.dialog.form.budgetItemId = '';
        this.dialog.form.remark = '';
        this.dialog.form.accountRemain = '';
        this.dialog.form.itemTotalBudget = '';





//        this.dialog.assist.planList = [];
        this.dialog.assist.selectedFirstItem = {};
        this.dialog.assist.accountRemain = 0;
        this.dialog.assist.itemTotalBudget = 0;
        this.dialog.assist.budgetPlanId  = 0;
        this.dialog.assist.itemList = [];
        this.dialog.assist.selectedSecondItem = [];
//        this.dialog.assist.deptSubcomNames = [];
      },
      // 是否超过预算，超出预算变红
      isOverran(account){
        // 返回颜色，红色代表超出预算，黑色正常；
        if(account < 0 ) {
          return 'red';
        }else {
          return 'black';
        }
      },
      // 初始化
      init(){
        this.viewRole = this.$tool.judgeViewRole();
        this.reg = this.$route.query.reg;
        this.dialog.form.accountRegId = this.reg.id;
        this.form.accountRegId = this.reg.id;
        if(this.reg.budgetPlanId){
          // 预算项目分类
          this.getBudgetItemList();
        }
        this.dialog.form.department = this.$tool.getStorage("LOGIN_USER").deptName;
        // 搜索
        this.searchBtnClickHandle();
        // 获取部门信息
        this.getSubCompanyInfo();
      },
      // 对话框---预算项目一级---列表
      getBudgetItemList(){
        let params = {
          budgetPlanId : this.reg.budgetPlanId
        }
        this.$store.dispatch('costBudgetItemFind', params).then(function(res){
          if(res.success){
            this.dialog.assist.planList = res.data.list.map(function(it){
              return {
                id : it.id,
                item : it.item
              }
            }.bind(this))

          } else {
            this.$message({
              type : 'error',
              message : res.message || '错误'
            })
          }
        }.bind(this));
      },
      // 对话--预算项目一级--选择
      budgetPlanChange(val){
//        console.log("val = ", val)
        if(val){
          this.getSubItemList(val);
        }
        // 根据ID找到对应的整条数据
        this.dialog.assist.selectedFirstItem = this.dialog.assist.planList.find(function(it){
          return it.id == val;
        })
      },
      // 获取预算计划子项列表
      getSubItemList(itemId){
        let params = {
          // 计划ID
          id : itemId
        }
        this.$store.dispatch('costBudgetItemShow', params).then(function(res){
          if(res.success){
            console.log('二级', res.data);
            let list = res.data.costBudgetSubItems;
            this.dialog.assist.itemList = list;
            this.dialog.assist.accountRemain = res.data.accountRemain;
            this.dialog.assist.itemTotalBudget = res.data.itemTotalBudget;

          } else {
            this.$message({
              type : 'error',
              message : res.message || '错误'
            })
          }
        }.bind(this));
      },
      // 对话框--预算项目--二级--选择
      budgetItemChange(val){
        // 根据ID找到对应的整条数据
        this.dialog.assist.selectedSecondItem = this.dialog.assist.itemList.find(function(it){
          return it.id == val;
        })
        console.log('二级选择：',this.dialog.assist.selectedSecondItem)
      },
      // 格式化时间
      formatDateTime(row, column, cellValue){
        let pro = column.property;
        let num = 10;
        // 年份4位 1999
        if(pro === 'year') num = 4;
        let str = this.$tool.formatDateTime(row[pro] || 0);
        return str ? str.substring(0, num) : str;
      },
      // 分页
      disasterPageChangeHandle(page){
        this.form.pageCurrent = page;
        this.searchBtnClickHandle();
      },
      // 搜索按钮
      searchBtnClickHandle(){
        this.$store.dispatch('costAccountRecordFind', this.form).then(function(res){
          if(res.success){
            this.tableData = res.data;
          } else {
            this.$message({
              type : 'error',
              message : res.message || '错误'
            })
          }
        }.bind(this));
      },
      // 添加按钮
      addBtnClickHandle(){
        this.dialog.isShow = true;
        this.clear();
      },
      // 查看按钮
      itemViewClick(row){
        let name = 'costBudgetAdd';
        let params = {
          id : row.id,
          status : 'view'
        }
        this.$router.push({ name : name, params : params})
      },
      // 修改按钮
      itemUpdateClick(row){
//        console.log("row = ", row)

        this.getSubItemList(row.costBudgetItem.id);
        this.$tool.cloneObj(this.dialog.form, row);
        this.dialog.assist.budgetPlanId = row.costBudgetItem.id
        this.dialog.assist.selectedSecondItem = row.costBudgetSubItem
//        selectedSecondItem
        this.dialog.form.budgetItemId = row.budgetItemId

        this.dialog.form.accountRemain = row.accountRemain
        this.dialog.form.itemTotalBudget = row.itemTotalBudget
//        this.dialog.assist.budgetPlanId = 2848
//        console.log("type is ", typeof this.dialog.assist.budgetPlanId)
        this.dialog.assist.upload.params.contentId = row.id;
        this.dialog.form.accountTime = new Date(this.dialog.form.accountTime);

        this.dialog.isShow = true;
//        console.log(3333, this.dialog.assist)

      },
      // 删除按钮
      itemDeleteClick(row){
        this.$confirm('此操作将永久删除, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
          .then(function(){
            this.$store.dispatch('costAccountRecordDelete', {
              id : row.id
            }).then(function(res){
              if(res.success){
                this.$message({
                  type : 'success',
                  message : '删除成功'
                })
                this.searchBtnClickHandle();
              } else {
                this.$message({
                  type : 'error',
                  message : res.message || '删除失败！！'
                })
              }
            }.bind(this))
          }.bind(this))
      },
      // 对话框---确定按钮
      dialogOkBtnClickHandle(){
        this.$refs['form'].validate(function(valid) {
          if (valid) {
            let params = this.$tool.filterObj({}, this.dialog.form);
            if(this.dialog.form.id) {
              params['id'] = this.dialog.form.id;
            }
            params['accountRegId'] = this.reg.id;
            this.$store.dispatch('costAccountRecordAddOrUpdate', params).then(function(res){
              if(res.success){
                this.$message({
                  type : 'success',
                  message : '操作成功'
                })
                this.searchBtnClickHandle();
                this.$refs['form'].resetFields();
                this.clear();
                this.dialog.isShow = false;

              } else {
                this.$message({
                  type : 'error',
                  message : res.message || '错误'
                })
              }
            }.bind(this));
          } else {
            return false;
          }
        }.bind(this));
      },
      // 是否超过预算，超出预算变红
      isOverran(account){
        // 返回颜色，红色代表超出预算，黑色正常；
        if(account < 0 ) {
          return 'red';
        }else {
          return 'black';
        }
      },
      // 对话框--关闭
      handleClose(){
        //刘杰1120 删除 原因：新增费用记录按钮打开的对话框，未确认直接关闭，会导致下次点开按钮打开的对话框缺少关联信息，无法保存录入，需重新刷新才行
//        this.dialog.form = this.$tool.clearObj({}, this.dialog.form);
        this.dialog.isShow = false;
      },
      // 合计
      // 房间号的合计去掉
      getSummaries (param) {
        const { columns, data } = param
        const sums = []
        columns.forEach((column, index) => {
          if (index === 0) {
          sums[index] = '总计'
        } else if (index === 3) {
          const values = data.map(item => Number(item[column.property]))
          if (!values.every(value => isNaN(value))) {
            sums[index] = values.reduce((prev, curr) => {
                const value = Number(curr)
                if (!isNaN(value)) {
                  return prev + curr
            } else {
              return  prev
            }
          }, 0)
            sums[index] = sums[index].toFixed(2)
          } else {
            sums[index] = 'N/A'
          }
        } else {
          sums[index] = '--'
        }
      })
        return sums
      }
    }
  }
</script>
<style>
  .dialogrow{
    margin: 4px;
  }

</style>
