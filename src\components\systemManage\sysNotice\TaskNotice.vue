<template>
  <div id="taskNotice">
    <safeHeader></safeHeader>
    <div
      style="
        position: absolute;
        top: 70px;
        left: 0;
        right: 0;
        bottom: 0;
        min-width: 1300px;
        background-color: #f2f2f2;
        overflow: scroll;
      "
    >
      <el-col
        :span="22"
        :offset="1"
        style="
          background-color: #fff;
          position: absolute;
          top: 0;
          bottom: 0;
          min-height: 650px;
          padding: 20px;
        "
      >
        <el-button @click="$router.push('/menu')">返回首页</el-button>
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane label="隐患排查任务" name="dangerTask">
            <div
              v-for="notice in dangerTask"
              @click="noticeClick(notice)"
              class="task-block-item"
            >
              <el-row style="margin: 3px 0 0 0">
                <el-col :span="13">
                  <i class="el-icon-message" style="margin-right: 10px"></i>
                  <span style="color: #4a90e2">{{ notice.name }}</span>
                </el-col>
                <el-col :span="11">
                  <span style="color: #8c939d"
                    >创建人：{{ notice.createUserName }} | 部门：{{
                      notice.publicDeptName
                    }}</span
                  >
                </el-col>
              </el-row>
              <el-row style="margin: 0">
                <el-col :span="24" style="margin-top: 5px">
                  <i class="el-icon-time" style="margin-right: 10px"></i>
                  <span style="color: #8c939d">{{
                    ($tool.formatDateTime(notice.createTime) || "").substring(
                      0,
                      16
                    )
                  }}</span>
                </el-col>
              </el-row>
            </div>
            <!--分页-->
            <div style="margin-top: 20px">
              <el-pagination
                layout="prev, pager, next"
                :total="dangerTotal"
                @current-change="currentDangerPage"
              >
              </el-pagination>
            </div>
          </el-tab-pane>
          <el-tab-pane label="应急值班任务" name="emerTask">
            <div
              v-for="(taskItem, index) in taskList"
              @click="taskClick(taskItem)"
              class="task-block-item"
              :key="index"
            >
              <el-row style="margin: 3px 0 0 0">
                <el-col :span="13">
                  <i class="el-icon-message" style="margin-right: 10px"></i>
                  <span style="color: #4a90e2">{{ taskItem.name }}</span>
                </el-col>
                <el-col :span="11">
                  <span style="color: #8c939d"
                    >创建人：{{ taskItem.username }} | 部门：{{
                      taskItem.companyName
                    }}</span
                  >
                </el-col>
              </el-row>
              <el-row style="margin: 0">
                <el-col :span="24" style="margin-top: 5px">
                  <i class="el-icon-time" style="margin-right: 10px"></i>
                  <span style="color: #8c939d">{{
                    ($tool.formatDateTime(taskItem.createTime) || "").substring(
                      0,
                      16
                    )
                  }}</span>
                </el-col>
              </el-row>
            </div>
            <!--分页-->
            <div style="margin-top: 20px">
              <el-pagination
                layout="prev, pager, next"
                :total="emerTotal"
                @current-change="currentEmerPage"
              >
              </el-pagination>
            </div>
          </el-tab-pane>
          <el-tab-pane label="节假日值班" name="demandTask">
            <div
              v-for="(taskItem, index) in taskListHolidays"
              @click="taskClick(taskItem)"
              class="task-block-item"
              :key="index"
            >
              <el-row style="margin: 3px 0 0 0">
                <el-col :span="13">
                  <i class="el-icon-message" style="margin-right: 10px"></i>
                  <span style="color: #4a90e2">{{ taskItem.name }}</span>
                </el-col>
                <el-col :span="11">
                  <span style="color: #8c939d"
                    >创建人：{{ taskItem.username }} | 部门：{{
                      taskItem.companyName
                    }}</span
                  >
                </el-col>
              </el-row>
              <el-row style="margin: 0">
                <el-col :span="24" style="margin-top: 5px">
                  <i class="el-icon-time" style="margin-right: 10px"></i>
                  <span style="color: #8c939d">{{
                    ($tool.formatDateTime(taskItem.createTime) || "").substring(
                      0,
                      16
                    )
                  }}</span>
                </el-col>
              </el-row>
            </div>
            <!--分页-->
            <div style="margin-top: 20px">
              <el-pagination
                layout="prev, pager, next"
                :total="emerTotal"
                @current-change="currentEmerPage"
              >
              </el-pagination>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-col>
    </div>
  </div>
</template>
<script>
import safeHeader from '@/components/common/header'
export default {
  name: 'taskNotice',
  data () {
    return {
      activeName: 'dangerTask',
      dangerTask: [],
      dangerTotal: 0,
      emerTask: [],
      emerTotal: 0,
      demandTask: [],
      demandTotal: 0,
      taskList: [],
      taskListHolidays: [],
      //检查类型
      inpectTypeArr: ['自查', '检查', '排查'],
    }
  },
  components: {
    safeHeader
  },
  mounted: function () {
    this.searchDangerTask(1)
  },
  watch: {
    $route (to, from) {
      if (from.name === 'menu') {
        this.activeName = 'dangerTask'
        this.searchDangerTask(1)
      }
    }
  },
  methods: {
    handleClick (tab) {
      if (this.activeName === 'dangerTask') {
        this.searchDangerTask(1)
      } else if (this.activeName === 'emerTask') {
        // this.searchEmerTask(1)
        this.searchHolidays()
      } else {
        // this.searchDemandTask() 
        this.searchHolidays()
      }
    },
    currentDangerPage: function (val) {
      if (val) {
        this.searchDangerTask(Number(val))
      }
    },

    searchDangerTask: function (pageCurrent) {
      this.$http.get('dangerFlow/getUndoTask?pageCurrent=' + pageCurrent).then(function (res) {
        if (res.data.success) {
          this.dangerTotal = res.data.data.page.total
          let list = res.data.data.data
          this.dangerTask = []
          for (let i = 0; i < list.length; i++) {
            let tempObj = list[i].dangerInspectPublic ? list[i].dangerInspectPublic : {}
            tempObj.taskId = list[i].id
            tempObj.statusName = list[i].name
            if (list[i].extActNode) {
              tempObj.statusColor = list[i].extActNode.statusColor
              tempObj.operateUrl = list[i].extActNode.url
              let strTOJson = JSON.parse(list[i].extActNode.config)
              tempObj.nodeData = strTOJson.types[tempObj.type]
              tempObj.currentStatus = list[i].extActNode.status
            }
            tempObj.processInstanceId = list[i].processInstanceId
            tempObj.operateId = list[i].userId
            tempObj.subProcessStartUserId = list[i].subProcessStartUserId
            tempObj.hasReassign = list[i].hasReassign
            if (tempObj.hasReassign) {
              tempObj.fromUserId = list[i].extActTaskReassign.fromUserId
              tempObj.ressignNodeId = list[i].extActTaskReassign.id
            }

            tempObj.typeName = this.inpectTypeArr[tempObj.type]

            this.dangerTask.push(tempObj)
          }
        }
      }.bind(this)).catch(function (err) {
        console.log(err)
      })
    },
    currentEmerPage: function (val) {
      if (val) {
        this.searchEmerTask(Number(val))
      }
    },
    taskClick: function (rowData) {
      if (rowData.taskType === "【隐患整改】") {
        if (rowData.operateUrl) {
          this.$router.push({
            name: rowData.operateUrl,
            params: {
              dangerData: rowData,
              taskFlag: true,
              componentName: "hideDangerWorkflow",
            },
          })
        } else {
          this.$message.warning("节点数据获取失败")
        }
      } else if (rowData.taskType === "【应急值班】") {
        this.$router.push({
          name: "emerDuty",
          params: {
            taskFlag: true,
            componentName: "emerDuty",
          }
        })
      } else {
        //需求类型
        this.$router.push({
          name: "holidayDuty",
          params: {
            taskFlag: true,
            componentName: "holidayDuty",
          }
        })
      }
    },
    //时间戳转化
    formattedtime: function (params) {
      const timestamp = params
      const date = new Date(timestamp)
      const year = date.getFullYear()
      const month = date.getMonth() + 1
      const day = date.getDate()
      const formattedDate = `${year}-${month < 10 ? "0" + month : month}-${day < 10 ? "0" + day : day
        }`
      return formattedDate
    },
    searchHolidays () {
      this.$http.post("/sysDuty/findUndoEmgAndDuty").then((res) => {
        // this.holidaysTaskNum= res.data.data.page.total;
        this.taskList = []
        this.taskListHolidays = []
        const list = res.data.data.list
        this.emerTotal = res.data.data.total
        list.forEach((i, index) => {
          let j = {} // 在每次循环开始时创建一个新对象
          j.taskId = i.id
          j.taskType = i.bizType == 1 ? "【节假日值班】" : "【应急值班】"
          j.name = i.dutyCodeName
          j.createTime = this.formattedtime(i.dutyDate)
          j.username = i.username
          j.companyName = i.companyName
          j.companyName = i.companyName
          if (i.bizType == 1) {
            this.taskListHolidays.push(j)
          } else {
            this.taskList.push(j)
          }
          // if (index < 6) {
          //   this.taskList.push(j)
          //   this.taskListHolidays.push(j)
          // }
        })
      })
    },
    searchEmerTask: function (pageCurrent) {
      this.$http.post('emgFlow/getMyTask?pageCurrent=' + pageCurrent).then(function (res) {
        if (res.data.success) {
          this.emerTotal = res.data.data.page.total
          let list = res.data.data.data
          this.emerTask = []
          for (let i = 0; i < list.length; i++) {
            let tempObj = list[i].emgPlanPublic ? list[i].emgPlanPublic : {}
            tempObj.taskId = list[i].id
            tempObj.statusName = list[i].name
            if (list[i].extActNode) {
              tempObj.statusColor = list[i].extActNode.statusColor
              tempObj.operateUrl = list[i].extActNode.url
            }
            tempObj.processInstanceId = list[i].processInstanceId
            tempObj.operateId = list[i].userId

            this.emerTask.push(tempObj)
          }
        }
      }.bind(this)).catch(function (err) {
        console.log(err)
      })
    },
    searchDemandTask: function () {
      this.demandTask = []
      let params = {}
      params['pageCurrent'] = 1
      params['pageSize'] = 5
      params['userId'] = this.$tool.getStorage('LOGIN_USER').userId
      params['status'] = 0
      this.$store.dispatch('eduReqUserRltFind', params).then(function (res) {
        if (res.success) {
          this.demandTotal = res.total
          let list = res.data.list
          for (let i = 0; i < list.length; i++) {
            let tempObj = list[i]
            tempObj.taskId = list[i].id
            tempObj.operateUrl = "demandSurveyStaffAdd"
            // tempObj.operateUrl="demandSurveyStaffIndex";
            tempObj.operateId = list[i].id
            tempObj.name = list[i].eduRequirementInvestigation.title
            tempObj.taskType = '【需求】'
            tempObj.companyName = list[i].department
            tempObj.createDate = list[i].eduRequirementInvestigation.createDate

            this.demandTask.push(tempObj)
          }
        } else {
          this.emreTaskNum = 0
        }
      }.bind(this)).catch(function (err) {
        console.log(err)
      })
    },
    noticeClick: function (rowData) {
      if (this.activeName === 'dangerTask') {
        if (rowData.operateUrl) {
          this.$router.push({ name: rowData.operateUrl, params: { dangerData: rowData, taskFlag: true, componentName: 'hideDangerWorkflow' } })
        } else {
          this.$message.warning('节点数据获取失败')
        }
      } else if (this.activeName === 'emerTask') {
        if (rowData.operateUrl) {
          this.$router.push({ name: rowData.operateUrl, params: { emerData: rowData, taskFlag: true } })
        } else {
          this.$message.warning('节点数据获取失败')
        }
      } else if (this.activeName === 'demandTask') {
        if (rowData.operateUrl) {
          this.$router.push({ name: rowData.operateUrl, params: { status: 'edit', id: rowData.operateId, taskFlag: true, componentName: 'demandSurveyStaffAdd' } })
        } else {
          this.$message.warning('节点数据获取失败')
        }
      }

    },
  }
}
</script>
<style>
.task-block-item {
  border-bottom: 1px solid #ddd;
  width: 100%;
  padding-bottom: 10px;
}
.task-block-item:hover {
  background-color: rgb(250, 229, 164);
}
</style>
