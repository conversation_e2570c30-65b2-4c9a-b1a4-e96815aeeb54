<template>
  <div id="forwardEmer">
    <div class="background-style">
      <el-col :span="16" :offset="4" class="success-background-title">转发应急响应</el-col>
      <el-col :span="16" :offset="4">
        <el-form :model="form" :rules="rules" ref="ruleForm" label-width="140px" class="demo-ruleForm">
          <el-col :span="24">
            <el-col :span="12">
              <el-form-item label="分类：" prop="classify">
                <el-cascader
                  :options="cascaderOptions"
                  v-model="form.classify"
                  @change="typeClick"
                  style="width: 100%"
                  placeholder="请选择">
                </el-cascader>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="上报间隔：" prop="timeInterval">
                <el-input v-model="form.timeInterval"> <template slot="append">小时</template></el-input>
              </el-form-item>
            </el-col>
          </el-col>
          <el-col :span="24">
            <el-col :span="12">
              <el-form-item label="级别：" prop="level">
                <el-select v-model="form.level" placeholder="请选择" style="width: 100%">
                  <el-option
                    v-for="item in levelOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="预警信号：" prop="emergencyFlag">
                <el-select v-model="form.emergencyFlag" placeholder="请选择" style="width: 100%">
                  <el-option
                    v-for="item in emergencyFlagOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.label">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-col>
          <el-col :span="24">
            <el-form-item label="名称：" prop="emergencyName">
              <el-input v-model="form.emergencyName" placeholder="请输入应急响应名称"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="应急启动信息：" prop="beginMessage">
              <el-input type="textarea" :autosize="{ minRows: 3}" v-model="form.beginMessage"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="预警情况：" prop="emerSituation">
              <el-input type="textarea" :autosize="{ minRows: 3}" v-model="form.emerSituation" placeholder="例：受梅雨带影响，我市昨天夜里到今天已普遍出现大到暴雨，预计今天夜里到明天大到暴雨仍将持续，请注意防范。"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="参考预案：" prop="referPlan">
              <el-select v-model="form.referPlan" placeholder="请选择" @change="referPlanChange" style="width: 100%">
                <el-option
                  v-for="item in planOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="应急响应要求：" prop="emerRequire">
              <el-button style="margin-bottom: 10px" size="small" type="success" @click="openAddDialog">添加知识点</el-button>
              <el-input type="textarea" :autosize="{ minRows: 3}" v-model="form.emerRequire" placeholder="选择参考预案，该项可自动填充，如没有对应预案，请自定义应急响应要求"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="本公司值班安排：" prop="dutyTable">
              <el-table
                :data="form.dutyTable"
                border
                style="width: 100%">
                <el-table-column
                  type="index"
                  align="center"
                  label-class-name="inner-header-style"
                  width="50">
                </el-table-column>
                <el-table-column
                  prop="dutyDate"
                  label="值班日期"
                  align="center"
                  :formatter="currentDutyDateFormat"
                  label-class-name="inner-header-style"
                  width="120">
                </el-table-column>
                <el-table-column
                  prop="name"
                  label="值班人"
                  align="center"
                  label-class-name="inner-header-style"
                  width="120">
                </el-table-column>
                <el-table-column
                  prop="phone"
                  label="手机长号"
                  align="center"
                  label-class-name="inner-header-style"
                  width="130">
                </el-table-column>
                <el-table-column
                  prop="shortPhone"
                  label="手机短号"
                  align="center"
                  label-class-name="inner-header-style"
                  min-width="120">
                </el-table-column>
                <el-table-column
                  label="操作"
                  align="center"
                  label-class-name="inner-header-style"
                  fixed="right"
                  width="120">
                  <template slot-scope="scope">
                    <el-button type="danger" size="mini" @click="deleteClick(scope.$index)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
              <div style="width: 100%;height: 40px;background-color: white;padding: 5px 0 5px 0;border-bottom: 1px solid #EBEEF5;border-left: 1px solid #EBEEF5;border-right: 1px solid #EBEEF5;">
                <div style="width: 100px;margin:auto">
                  <el-button type="text" size="medium" icon="el-icon-plus" @click="addPersonClick">添加人员</el-button>
                </div>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="通知人员选择：" label-position="top">
              <choose-staff ref="chooseStaff" @selectedRows="selectedRows"></choose-staff>
            </el-form-item>
          </el-col>
          <el-col :span="24" style="margin-top: 10px" >
            <el-form-item>
              <el-button style="float: right;margin-left: 20px" @click="returnClick()">返回</el-button>
              <el-button type="primary" style="float: right" @click="submitClick()">提交</el-button>
            </el-form-item>
          </el-col>
        </el-form>
      </el-col>
    </div>

    <!--新增值班人员对话框-->
    <el-dialog title="添加值班人员" :visible.sync="addPerson">
      <el-form :model="personForm" :rules="personRules" ref="personForm" label-position="right" class="demo-ruleForm">
        <el-form-item label="姓名:" label-width="120px" prop="name">
          <el-select
            v-model="personForm.name"
            filterable
            remote
            reserve-keyword
            clearable
            placeholder="请输入姓名后选择"
            @change="handlePersonClick"
            :remote-method="remotePersonDuty"
            :loading="personLoading"
            style="width: 220px">
            <el-option
              v-for="item in personDutyOptions"
              :key="item.value"
              :label="item.label"
              :value="item">
            </el-option>
          </el-select>
          <!--<el-input v-model="personForm.name" style="width: 400px" placeholder="请输入值班人员姓名"></el-input>-->
        </el-form-item>
        <el-form-item label="值班日期:" label-width="120px" prop="dutyDate">
          <el-date-picker
            v-model="personForm.dutyDate"
            type="date"
            placeholder="选择日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="手机长号:" label-width="120px" prop="phoneNumber">
          <el-input v-model="personForm.phoneNumber" style="width: 400px"></el-input>
        </el-form-item>
        <el-form-item label="手机短号:" label-width="120px" prop="shortNumber">
          <el-input v-model="personForm.shortNumber" style="width: 400px"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelAddPerson">取 消</el-button>
        <el-button type="primary" @click="determineAddPerson">确 定</el-button>
      </div>
    </el-dialog>
    <!--新增值班人员对话框结束-->

    <!--添加知识点对话框开始-->
    <el-dialog title="添加知识点" :visible.sync="addKnowledgePoint">
      <el-select
        v-model="searchTag"
        multiple
        filterable
        remote
        reserve-keyword
        clearable
        placeholder="请输入标签名后选择,可多选"
        @change="labelClick"
        :remote-method="remoteTag"
        :loading="tagLoading"
        style="width: 300px">
        <el-option
          v-for="item in tagOptions"
          :key="item.value"
          :label="item.label"
          :value="item.label">
        </el-option>
      </el-select>
      <el-button type="primary" style="margin-left: 20px" @click="searchKnowledgePointClick">搜索</el-button>
      <el-table
        :data="knowledgeTable"
        border
        tooltip-effect="light"
        style="width: 100%;margin-top: 10px"
        @selection-change="handleSelectionChange">
        <el-table-column
          type="selection"
          width="55"
          label-class-name="inner-header-style">
        </el-table-column>
        <el-table-column
          prop="content"
          label="知识点"
          show-overflow-tooltip
          label-class-name="inner-header-style"
          min-width="200">
        </el-table-column>
      </el-table>
      <div style="margin-top: 10px">
        <el-pagination
          background
          layout="prev, pager, next"
          :current-page="currentKnowledgePage"
          :total="totalKnowledgeItem"
          @current-change="currentKnowledgePageClick">
        </el-pagination>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelAddKnowledgePoint">取 消</el-button>
        <el-button type="primary" @click="determineAddKnowledgePoint">确 定</el-button>
      </div>
    </el-dialog>
    <!--添加知识点对话框结束-->

    <search-people-dialog @determineClick="selectPersonClick" :data="selectPersonData" :defaultPersonId="selectPersonId"></search-people-dialog>

  </div>
</template>
<script>
  import chooseStaff from '@/components/common/chooseStaff'
  import SearchPeopleDialog from '../../../common/smallComponent/searchSinglePeople.vue'
  export default {
    name: 'forwardEmer',
    data() {
      return {
        //-----------------------初始化数据-------------------------------------
        cascaderOptions:[],
        planOptions:[],
        levelOptions:[
          {value:'应急警报', label:'应急警报'},
          {value:'4级', label:'4级'},
          {value:'3级', label:'3级'},
          {value:'2级', label:'2级'},
          {value:'1级', label:'1级'}
        ],
        emergencyFlagOptions:[
          {value:'蓝色预警',label:'蓝色预警'},
          {value:'黄色预警',label:'黄色预警'},
          {value:'橙色预警',label:'橙色预警'},
          {value:'红色预警',label:'红色预警'}
        ],
        //-----------------------表单数据---------------------------------------
        form:{
          classify:[],
          level:'',
          emergencyName:'',
          emergencyFlag:'',
          issuer:'',
          beginMessage:'',
          emerSituation:'',
          referPlan:'',
          emerRequire:'',
          dutyTable:[],
          examine:'',
          noticeUserIds:[],
          timeInterval:'',
          pathId:'',
        },
        rules:{
          classify:[{ required: true, message: '请选择分类', trigger: 'change' }],
          emergencyName:[{ required: true, message: '请输入应急响应名称', trigger: 'change' }],
          timeInterval:[{ required: true, message: '请输入上报间隔时间，数字格式', trigger: 'change' }],
        },
        //------------------------人员选择----------------------------
        personLoading:false,
        personOptions:[],
        personDutyOptions:[],
        addPerson:false,
        personForm:{
          name:'',
          dutyDate:'',
          dutyDateTemp:'',
          phoneNumber:'',
          shortNumber:''
        },
        personRules:{
          name:[{ required: true, message: '请选择人员', trigger: 'change' }],
          dutyDate:[{ required: true, message: '请选择日期', trigger: 'change' }],
        },

        //-------------------------缓存数据-----------------------------
        tempDate:'',//当日的时间戳
        shortEmerName:'',//没有日期的应急名称
        currentEmerId:'',//查看和修改当前应急的ID
        startPlanPublicId:'',//启动应急的ID
        startPlanName:'',//启动应急的名称
        currentEmerStatus:0,//当前应急的状态
        currentEmerReferPlanId:'',//当前应急的参考预案的id
        taskId:'',//当前任务ID
        tempResponseData:{},//当前应急响应对象
        signUser:{},//审核人的参考项
        doTaskLoading:'',//走流程时的缓冲图

        //-----------------------对话框------------------------------
        addKnowledgePoint:false,
        intentStr:'',
        searchTag:[],
        tagLoading:false,

        knowledgeTable:[],
        totalKnowledgeItem:10,
        currentKnowledgePage:1,
        selectedArray:[],
        //--------------------通知人员显示-------------------------
        noticePersonTable:[],
        //------------------选择审核人的对话框-----------------------
        selectPersonData:{title:'请选择审核人',isShow:false,defaultPerson:{value:0,label:''}},
        selectPersonId:0,

      }
    },
    components : {
      'search-people-dialog' : SearchPeopleDialog,
      'choose-staff':chooseStaff
    },
    computed:{
      tagOptions:function () {
        return this.$store.state.emergencyData.referLabels;
      },
    },
    created:function () {
      this.getPlanType();
      this.searchKnowledgePointClick();//让知识点搜索先有数据
    },
    mounted:function () {
      let dateTemp=new Date();
      this.tempDate=dateTemp.getTime();
    },
    watch:{
      $route(to, from){
        if((from.name==='subNewWorkBriefWorkflow')&&this.$route.name==='forwardEmer'){
          this.getPlanType();
        }
      },
    },
    methods:{
      //--------------------------初始化-------------------------------
      //获取分类
      getPlanType: function () {
        this.$http.get('emgType/getAll/'+this.$tool.getStorage('LOGIN_USER').companyId).then(function (res) {
          this.editPlanTypeArray(res.data.data);
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message({
            showClose: true,
            message: '网络错误，请尝试重登录',
            type: 'error'
          });
        }.bind(this));
      },
      //编写分类,以及一切初始化工作
      editPlanTypeArray:function (typeTree) {
        this.cascaderOptions=[];
        for(let i=0;i<typeTree.length;i++){
          let tempArray={value:i,label:typeTree[i].typeName,id:typeTree[i].id};
          if(typeTree[i].subTypes.length){
            tempArray.children=[];
            for(let j=0;j<typeTree[i].subTypes.length;j++){
              tempArray.children.push({value:j,label:typeTree[i].subTypes[j].typeName,id:typeTree[i].subTypes[j].id});
            }
          }
          this.cascaderOptions.push(tempArray);
        }

        //该部分必须在分类获取之后进行，所以放这里了，反正每次进来都要执行这里
        if(this.$route.params.emerData){
          this.editEmerForm(this.$route.params.emerData);
        }
      },
      editEmerForm:function (val) {
        this.tempResponseData=val;
        this.currentEmerId=val.id;
        this.signUser={value:val.signerUserId,label:val.signerUserName};
        this.taskId=val.taskId;
        this.form.level=val.respLevel;
        this.form.emergencyName=val.name;
        this.form.emergencyFlag=val.warnSignal;
        this.personOptions=[];
        this.personOptions.push({value:val.signerUserId,label:val.signerUserName});
        this.form.issuer={value:val.signerUserId,label:val.signerUserName};
        this.form.beginMessage=val.startInfo;
        this.form.emerSituation=val.warnSituation;
        this.form.emerRequire=val.startupRequire;
        this.currentEmerReferPlanId=val.planId;
        this.currentEmerStatus=val.status;
        this.startPlanPublicId=val.startPlanId?val.startPlanId:this.currentEmerId;
        this.startPlanName=val.name;
        this.form.examine=val.auditOpinion;
        this.form.pathId=val.pathId;
        this.form.timeInterval=val.timeInterval;

        this.form.noticeUserIds=[];//通知人员选择

        this.findCurrentCompanyDutyTable();
      },
      //--------------------------初始化结束-------------------------------


      //--------------------------表单响应事件-------------------------
      //获取参考预案列表
      searchSimilarPlan:function () {
        this.planOptions=[];
        if(this.form.classify[0]>=0){
          let params={pageSize:10,pageCurrent:1};
          if(this.form.level){
            params.respLevel=this.form.level;
          }
          if(this.form.classify[1]>=0){
            params.typeId=this.cascaderOptions[this.form.classify[0]].children[this.form.classify[1]].id;
          }else{
            params.typeId=this.cascaderOptions[this.form.classify[0]].id;
          }
          params.companyId=this.$tool.getStorage('LOGIN_USER').companyId;
          this.$http.post('emgPlan/find',params).then(function (res) {
            if(res.data.data.size){
              for(let i=0;i<res.data.data.list.length;i++){
                this.planOptions.push({value:res.data.data.list[i].id,label:res.data.data.list[i].name});
              }
            }
          }.bind(this)).catch(function (err) {
            console.log(err);
            this.$message({
              showClose: true,
              message: '网络错误，请尝试重登录',
              type: 'error'
            });
          }.bind(this));
        }
      },
      referPlanChange:function () {
        if(this.form.referPlan){
          this.$http.post('emgPlan/find',{id:this.form.referPlan}).then(function (res) {
            if(res.data.data.list.length){
              this.form.emergencyFlag=res.data.data.list[0].warnSignal;
              this.form.emerRequire+=res.data.data.list[0].startupRequireName+'\n';
              this.form.emerRequire+=res.data.data.list[0].startupRequire+'\n\n';
              this.form.emerRequire+=res.data.data.list[0].levelRequireName+'\n';
              this.form.emerRequire+=res.data.data.list[0].levelRequire+'\n';
            }
          }.bind(this)).catch(function (err) {
            console.log(err);
            this.$message({
              showClose: true,
              message: '网络错误，请尝试重登录',
              type: 'error'
            });
          }.bind(this));
        }
      },

      //选择通知人员
      choosePeople:function () {
        this.asideFlag=true;
        if(this.form.noticeUserIds.length===0){//如果还未选择人员则重置
          if(this.$refs['chooseStaff'].changeTableDataHandle){
            this.$refs['chooseStaff'].changeTableDataHandle([]);
          }
        }else{
          let userArray=this.form.noticeUserIds.map(function (item) {
            return {userId:item}
          });
          this.$refs['chooseStaff'].changeTableDataHandle(userArray);
        }
        if(this.$refs['chooseStaff'].isShowBtnHandle){
          this.$refs['chooseStaff'].isShowBtnHandle(true);
        }
        if(this.$refs['chooseStaff'].isShowJoinHandle){
          this.$refs['chooseStaff'].isShowJoinHandle(true);
        }
      },
      //通知人员选择
      selectedRows(rows){
        // 参与人员id数组
        this.form.noticeUserIds = rows.map(function(it) {
          return it.userId;
        });
      },

      //-------------------------页面为查看时，数据的填充--------------

      findCurrentCompanyDutyTable:function () {
        let params = new URLSearchParams;
        //应急响应值班表，原来传currentEmerId，现在传startEmerId
        params.append("planPublicId", this.startPlanPublicId);
        params.append("companyId",  this.$tool.getStorage('LOGIN_USER').companyId);
        this.$http.post('duty/find', params).then(function (res) {
          if (res.data.success) {
            this.form.dutyTable=res.data.data.list;
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
        }.bind(this));
      },
      currentDutyDateFormat:function (row) {
        return this.transferTime(row.dutyDate);
      },
      //-------------------------类型变化函数---------------------------
      typeClick:function () {
        this.searchSimilarPlan();
      },
      //--------------------------远程人员查询-------------------------------
      remotePerson:function (val) {
        this.personLoading = true;
        this.$http.get('user/find?username='+val).then(function (res) {
          if(res.data.success){
            this.personOptions=[];
            for (let i = 0; i < res.data.data.list.length; i++) {
              this.personOptions.push({value:res.data.data.list[i].userId,label:res.data.data.list[i].username});
            }
            this.personLoading = false;
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
        });
      },
      editPerson:function (val) {
        let temp;
        this.personDutyOptions=[];
        for (let i = 0; i < val.length; i++) {
          temp = new Object;
          temp.value = val[i].userId;
          temp.label = val[i].username;
          temp.phone = val[i].mobile;
          temp.shortPhone=val[i].shortPhone;
          this.personDutyOptions.push(temp);
        }
      },
      remotePersonDuty:function (val) {
        this.personLoading = true;
        this.$http.get('user/find?username='+val+'&companyId='+this.$tool.getStorage('LOGIN_USER').companyId).then(function (res) {
          if(res.data.success){
            this.editPerson(res.data.data.list);
            this.personLoading = false;
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
        });
      },
      handlePersonClick:function (val) {
        if(typeof (val)==='string'){
          this.$message({
            showClose: true,
            message: '此人员的手机信息需自行填写！',
            type: 'warning'
          });
        }else{
          this.personForm.phoneNumber=val.phone;
          this.personForm.shortNumber=val.shortPhone;
        }
      },
      //---------------------------人员表格事件----------------------------
      deleteClick:function (index) {
        this.form.dutyTable.splice(index,1);
      },
      addPersonClick:function () {
        this.addPerson=true;
      },
      determineAddPerson:function () {
        this.$refs['personForm'].validate((valid) => {
          if (valid) {
            if(typeof (this.personForm.name)==='string'){
              this.form.dutyTable.push({
                dutyDate:this.personForm.dutyDate,
                name:this.personForm.name,
                userId:null,
                phone:this.personForm.phoneNumber,
                shortPhone:this.personForm.shortNumber
              });
            }else{
              this.form.dutyTable.push({
                dutyDate:this.personForm.dutyDate,
                name:this.personForm.name.label,
                userId:this.personForm.name.value,
                phone:this.personForm.phoneNumber,
                shortPhone:this.personForm.shortNumber
              });
            }

            this.$refs['personForm'].resetFields();
            this.addPerson = false;
          } else {
            console.log('error submit!!');
            return false;
          }
        });
      },
      cancelAddPerson:function () {
        this.$refs['personForm'].resetFields();
        this.addPerson = false;
      },
      //---------------------------提交和返回-----------------------------
      //提交
      submitClick:function () {
        this.$refs['ruleForm'].validate((valid) => {//表单是否完整
          if (valid) {
            if(this.form.noticeUserIds.length){//通知人员是否选择
              this.selectPersonData.isShow=true;
            }else{
              this.$confirm('您没有选择通知人员，若不选择人员则默认和原通知人员相同, 是否返回选择?', '提示', {
                confirmButtonText: '是',
                cancelButtonText: '否',
                type: 'warning'
              }).then(() => {
                this.asideFlag=true;
              }).catch(() => {
                this.selectPersonData.isShow=true;
              });
            }
          } else {
            console.log('error submit!!');
            return false;
          }
        });
      },
      selectPersonClick:function (val) {
        if(val){
          this.selectPersonData.isShow=false;
          this.doTaskLoading=this.$loading({
            lock: true,
            text: 'Loading',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });
          //新建或修改
          let params=new URLSearchParams;
          params.append("signerUserId",val);
          params.append("applyUserId",val);
          params.append("status",1);
          params.append("history",0);
          params.append("pathId",this.form.pathId);
          this.sendRequire(params,'/emgFlow/startPlanPublic','提交成功');
        }else {
          this.$message.warning('请选择审核人');
        }
      },

      //----------------------------填写表单-------------------------
      sendRequire:function (params,urlStr,successStr) {
        if(this.form.classify[1]>=0){//这里存的是index
          params.append("typeId",this.cascaderOptions[this.form.classify[0]].children[this.form.classify[1]].id);
          params.append("topTypeId",this.cascaderOptions[this.form.classify[0]].id);
        }else{
          params.append("typeId",this.cascaderOptions[this.form.classify[0]].id);
          params.append("topTypeId",0);
        }
        params.append("respLevel",this.form.level);
        params.append("name",this.form.emergencyName);
        params.append("warnSignal",this.form.emergencyFlag);
        params.append("startInfo",this.form.beginMessage);
        params.append("warnSituation",this.form.emerSituation);
        params.append("noticeUserIds",this.form.noticeUserIds);
        params.append("timeInterval",Number(this.form.timeInterval));

        params.append("planId",this.form.referPlan);

        params.append("startupRequire",this.form.emerRequire);

        params.append("deptId",this.$tool.getStorage('LOGIN_USER').deptId);
        params.append("companyId",this.$tool.getStorage('LOGIN_USER').companyId);

        for(let i=0;i<this.form.dutyTable.length;i++){
          if(typeof (this.form.dutyTable[i].dutyDate)==='number'){
            let tempDate=new Date();
            tempDate.setTime(this.form.dutyTable[i].dutyDate);
            this.form.dutyTable[i].dutyDate=tempDate;
          }
          if(this.form.dutyTable[i].dutyPersonId){
            params.append("emgDuties["+i+"].userId",this.form.dutyTable[i].userId);
            params.append("emgDuties["+i+"].dutyDate",this.form.dutyTable[i].dutyDate);
            params.append("emgDuties["+i+"].userName",this.form.dutyTable[i].name);
            params.append("emgDuties["+i+"].phone",this.form.dutyTable[i].phone);
            params.append("emgDuties["+i+"].shortPhone",this.form.dutyTable[i].shortPhone);
          }else{
            params.append("emgDuties["+i+"].dutyDate",this.form.dutyTable[i].dutyDate);
            params.append("emgDuties["+i+"].name",this.form.dutyTable[i].name);
            params.append("emgDuties["+i+"].phone",this.form.dutyTable[i].phone);
            params.append("emgDuties["+i+"].shortPhone",this.form.dutyTable[i].shortPhone);
          }
        }

        this.$http.post(urlStr,params).then(function (res) {
          if(res.data.success){
            this.$message({
              showClose: true,
              message: successStr,
              type: 'success'
            });
            this.doTaskLoading.close();
            this.cascaderOptions=[];
            this.planOptions=[];
            this.personOptions=[];
            this.$router.push({name:'superiorEmerResponse'});
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message({
            showClose: true,
            message: '网络错误，请尝试重登录',
            type: 'error'
          });
        }.bind(this));
      },
      //----------------------------通过标签查询知识点------------------------
      openAddDialog:function (str) {
        this.addKnowledgePoint=true;
        this.intentStr=str;
      },
      labelClick:function (val) {
        if(this.searchTag.length){
          this.$store.dispatch("getLabels",this.searchTag);
        }else{
          this.$store.dispatch("getLabels",[]);
        }
      },
      remoteTag:function (val) {
        let params = new URLSearchParams;
        if (val !== null&&val!=='') {
          this.tagLoading = true;
          params.append("label", val);
          params.append("pageSize", 10);
          params.append("pageCurrent", 1);
          params.append("companyId",this.$tool.getStorage('LOGIN_USER').companyId);
          this.$http.post('label/find', params).then(function (res) {
            if (res.data.data.list.length !== 0) {
              this.editTag(res.data.data.list);
            }
            this.tagLoading = false;
          }.bind(this)).catch(function (err) {
            console.log(err);
          });
        }else {
          if(this.searchTag.length===0){
            this.$store.dispatch("getLabels",[]);
          }
        }

      },
      editTag:function (list) {
        this.tagOptions.splice(0);
        for(let i=0;i<list.length;i++){
          let temp={value:list[i].id,label:list[i].label};
          this.tagOptions.push(temp);
        }
      },
      searchKnowledgePointClick:function () {
        let params=new URLSearchParams;
        params.append("pageCurrent",1);
        this.currentKnowledgePage=1;
        this.sendKnowSearchRequest(params);
      },
      currentKnowledgePageClick:function (val) {
        if(val){
          let params=new URLSearchParams;
          this.currentKnowledgePage=Number(val);
          params.append("pageCurrent",Number(val));
          this.sendKnowSearchRequest(params);
        }
      },
      sendKnowSearchRequest:function (params) {
        if(this.searchTag.length){
          for(let i=0;i<this.searchTag.length;i++){
            params.append("labels["+i+"]",this.searchTag[i]);
          }
        }
        params.append("companyId",this.$tool.getStorage('LOGIN_USER').companyId);
        this.$http.post('knowledge/find', params).then(function (res) {
          if (res.data.data.list.length !== 0) {
            this.totalKnowledgeItem=res.data.data.total;
            this.knowledgeTable=res.data.data.list;
          }else{
            this.totalKnowledgeItem=0;
            this.knowledgeTable=[];
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
        });
      },
      //多选
      handleSelectionChange:function (val) {
        if(val.length){
          this.selectedArray=val;
        }
      },
      cancelAddKnowledgePoint:function () {
        this.addKnowledgePoint=false;
      },
      determineAddKnowledgePoint:function () {
        if(this.selectedArray.length){
          let tempContent='';
          for(let i=0;i<this.selectedArray.length;i++){
            tempContent+='>>>. '+this.selectedArray[i].content+'\n';
          }
          if(this.form.emerRequire.charAt(this.form.emerRequire.length-1)==='\n'||!this.form.emerRequire.length){
            this.form.emerRequire+=tempContent;
          }else {
            this.form.emerRequire+='\n'+tempContent;
          }
        }
        this.addKnowledgePoint=false;
      },

      //返回
      returnClick:function () {
        this.cascaderOptions=[];
        this.planOptions=[];
        this.personOptions=[];
        this.$router.go(-1);
      },

    }
  }
</script>
<style>
</style>
