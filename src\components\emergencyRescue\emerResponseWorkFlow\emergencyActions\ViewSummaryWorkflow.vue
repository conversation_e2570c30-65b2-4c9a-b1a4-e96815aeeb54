<template>
  <div id="viewSummary">
    <div class="background-style">
      <el-col :span="16" :offset="4" class="success-background-title">
        {{form.name}}
      </el-col>
      <el-col :span="16" :offset="4">
        <el-form :model="form"  label-width="120px">
          <el-col :span="24">
            <el-col :span="12">
              <el-form-item label="编制公司：">
                {{form.dept}}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="记录：">
                {{form.type}}
              </el-form-item>
            </el-col>
          </el-col>
        </el-form>
      </el-col>
      <el-col :span="20" :offset="2">
        <table class="simple-table">
          <tr><td v-for="item01 in tableHead01">{{item01.title}}</td></tr>
          <tr><td v-for="item01 in tableHead01">{{tableContent[item01.index]}}</td></tr>
          <tr><td v-for="item02 in tableHead02">{{item02.title}}</td></tr>
          <tr><td v-for="item02 in tableHead02">{{tableContent[item02.index]}}</td></tr>
        </table>
        <el-col :span="24" style="margin-top: 20px" v-if="isSigner">
          <el-form :model="form" ref="singleForm" label-width="100px" class="demo-ruleForm">
            <el-form-item label="审核意见：" prop="examine">
              <el-dropdown @command="editExamine">
                <el-button type="primary" size="small">
                  审核参考<i class="el-icon-arrow-down el-icon--right"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item v-for="item in selectOptions" :key="item.id" :command="item.content">{{item.name}}</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
              <el-input type="textarea" :autosize="{ minRows: 3}" v-model="form.examine"></el-input>
            </el-form-item>
            <el-form-item label="查看应急：" >
              <el-button type="success" size="small" @click="viewResponseProcess">查看应急响应</el-button>
            </el-form-item>
          </el-form>
        </el-col>
        <div style="margin-top: 10px;margin-right: 20px;float: right">
          <div v-if="isSigner">
            <el-button type="primary" @click="signClick(1)">通过</el-button>
            <el-button type="danger" @click="returnBackClick">退回</el-button>
            <el-button type="primary" plain @click="$router.push({name:'emerResponseWorkflow'})">返回</el-button>
          </div>
          <div v-else>
            <el-button type="primary"plain @click="$router.push({name:'emerResponseWorkflow'})">返回</el-button>
          </div>
        </div>
      </el-col>

    </div>
  </div>
</template>
<script>
  export default {
    name: 'viewSummary',
    data() {
      return {
        form:{
          name:'',
          dept:'',
          type:'应急工作总结评估表',
          examine:''
        },
        tempResponseData:{},//当前应急响应对象
        //参考审核数据
        selectOptions:[
          {id:'examine01',name:'同意签发',content:'经审核，同意完结该应急响应。'},
          {id:'examine02',name:'退回修改',content:'经审核，该总结有待修改，意见如下：'}
        ],
        isSigner:false,//是否为审核人
        emerId:'',
        companyId:'',
        taskId:'',
        tableContent:{},
        tableHead01:[
          {title:'应急响应起止时间',index:'timeRange'},
          {title:'应急响应类型等级',index:'responseLevel'},
          {title:'各主要控股企业应急响应情况',index:'responseSituation'},
          {title:'应急效果评估',index:'effectEvaluation'},
        ],
        tableHead02:[
          {title:'受损单位',index:'damagedUnit'},
          {title:'人员伤亡情况',index:'deathSituation'},
          {title:'财产损失情况',index:'econLossSituation'},
          {title:'经济损失预估',index:'econLossEstimate'},
        ],
      }
    },
    created:function () {
      if(this.$route.params.emerData){
        this.tempResponseData=this.$route.params.emerData;
        this.form.name=this.$route.params.emerData.name;
        this.emerId=this.$route.params.emerData.id;
        if(this.$route.params.currentCompany){//如果传了特定的公司，则查特定公司的简报
          this.companyId=this.$route.params.currentCompany.value;
          this.form.dept=this.$route.params.currentCompany.label;
        }else{
          this.companyId=this.$route.params.emerData.companyId;
          this.form.dept=this.$route.params.emerData.companyName;
        }
        this.taskId=this.$route.params.emerData.taskId;
        let status=this.$route.params.emerData.status;
        this.isSigner=false;
        if(status===13){
          if(Number(this.$route.params.emerData.operateId)===this.$tool.getStorage('LOGIN_USER').userId){
            this.isSigner=true;
          }
        }
        this.searchSum();
      }
    },
    watch:{
      $route(to, from) {
        if (( from.name === 'emergencyProcessWorkflow'||from.name === 'emerResponsesWorkflow'||from.name==='taskNotice') && this.$route.name === 'viewSummaryWorkflow') {
          if(this.$route.params.emerData){
            this.tempResponseData=this.$route.params.emerData;
            this.form.name=this.$route.params.emerData.name;
            this.emerId=this.$route.params.emerData.id;
            if(this.$route.params.currentCompany){//如果传了特定的公司，则查特定公司的总结
              this.companyId=this.$route.params.currentCompany.value;
              this.form.dept=this.$route.params.currentCompany.label;
            }else{
              this.companyId=this.$route.params.emerData.companyId;
              this.form.dept=this.$route.params.emerData.companyName;
            }
            this.taskId=this.$route.params.emerData.taskId;
            let status=this.$route.params.emerData.status;
            this.isSigner=false;
            if(status===13){
              if(this.$route.params.emerData.operateId===this.$tool.getStorage('LOGIN_USER').userId){
                this.isSigner=true;
              }
            }
            this.searchSum();
          }
        }
      }
    },
    methods:{
      //填写审核意见
      editExamine:function (content) {
        this.form.examine=content;
      },
      //查看应急响应的记录
      viewResponseProcess:function () {
        this.$router.push({name:'viewEmergencyProcessWorkflow',params:{emerData:this.tempResponseData}});
      },
      //查找总结
      searchSum:function () {
        let params=new URLSearchParams;
        params.append("companyId",this.companyId);
        params.append("planPublicId",this.emerId)
        this.$http.post('emgSummary/find', params).then(function (res) {
          if (res.data.success) {
            if(res.data.data.list.length){
              this.tableContent=res.data.data.list[0];
            }
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message({
            showClose: true,
            message: '网络错误，请尝试重登录',
            type: 'error'
          });
        }.bind(this));
      },
      returnBackClick:function () {
        this.$confirm('此操作将退回该总结, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
         this.signClick(0);
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消退回'
          });
        });
      },
      signClick:function (status) {
        let  doTaskLoading=this.$loading({
          lock: true,
          text: 'Loading',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        let flowParams=new URLSearchParams;
        flowParams.append("taskId",this.taskId);
        flowParams.append("comment",this.form.examine);
        flowParams.append("result",status);
        this.$http.post('emgFlow/doTask',flowParams).then(function (res) {
          if(res.data.success){
            if(status){
              this.$message.success('总结审核通过');
            }else{
              this.$message.success('总结退回成功');
            }
            doTaskLoading.close();
            this.$router.push({name:'emerResponseWorkflow'});
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message.error('操作失败')
        }.bind(this));
      },

    }
  }
</script>
<style scoped>
  .simple-table tr th, .simple-table tr td{width: 25%;}
</style>
