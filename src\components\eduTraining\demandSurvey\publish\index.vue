<template>
  <div id="">
    <div class="background-style">

      <!--搜索区-->
      <div class="search-bar">
        <div style="padding:10px 10px 0 10px;float: left">
          <!--年份：-->
          <el-date-picker
            clearable
            v-model="form.createYear"
            type="year"
            placeholder="年份">
          </el-date-picker>
          <!--状态：-->
          <el-select clearable v-model="form.status" placeholder="状态">
            <el-option
              v-for="item in statusSelect"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
          <el-button
            @click="searchBtnClickHandle"
            type="primary" icon="el-icon-search" style="margin-left: 20px">搜索</el-button>
          <el-button
            @click="$router.push({ name : 'demandSurveyPublishAdd' });"
            type="success" icon="el-icon-plus" style="margin-left: 20px">需求调查</el-button>
          <el-button
            v-if="powerBtns.includes('lowerBtn')"
            @click="$router.push({ name : 'demandSurveyPublishLower' });"
            type="error" style="margin-left: 20px">下级调查</el-button>
        </div>
      </div>

      <!--表格区-->
      <div style="width: 100%;">
        <div style="padding: 20px 10px 20px 10px">
          <el-table
            :data="tableData.list"
            border
            style="width: 100%">
            <el-table-column
              type="index"
              label="编号"
              width="100"
              align="center"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="status"
              label="状态"
              width="150"
              label-class-name="header-style">
              <template slot-scope="scope">
                <el-tag size="mini" v-if="scope.row.status === 0">未发布</el-tag>
                <el-tag size="mini" v-if="scope.row.status === 1" type="danger">进行中</el-tag>
                <el-tag size="mini" v-if="scope.row.status === 2" type="success">已完成</el-tag>
                <el-tag size="mini" v-if="scope.row.status === 3" type="warning">作废</el-tag>
              </template>
            </el-table-column>
            <el-table-column
              prop="title"
              label="名称"
              min-width="300"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="createYear"
              label="年份"
              :formatter="formatDateTime"
              width="100"
              label-class-name="header-style">
            </el-table-column>

            <el-table-column
              prop="companyName"
              label="公司名称"
              min-width="300"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="startTime"
              label="开始时间"
              :formatter="formatDateTime"
              width="150"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="endTime"
              label="结束时间"
              :formatter="formatDateTime"
              width="150"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              fixed="right" label="操作"
              label-class-name="header-style"
              align="left" width="200">
              <template  slot-scope="scope">
                <!--未发布-->
                <template  v-if="scope.row.status == 0">
                  <el-button size="mini" type="primary" @click="itemUpdateClick(scope.row)">修改</el-button>
                  <el-button size="mini" type="danger" @click="itemDeleteClick(scope.row)">删除</el-button>
                </template>
                <!--进行中-->
                <template  v-if="scope.row.status >= 1">
                  <el-button size="mini" type="success" @click.native="itemViewClick(scope.row)">查看</el-button>
                  <el-button size="mini" type="danger" @click="itemVoidClick(scope.row)">作废</el-button>
                </template>
                <!--已完成-->
<!--                <template v-if="scope.row.status == 2">-->
<!--                  <el-button size="mini" type="warning" @click.native="restartBtnClick(scope.row)">重启</el-button>-->
<!--                </template>-->
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div style="margin-top: 10px">
          <el-pagination
            background
            layout="prev, pager, next"
            :current-page="tableData.pageNum"
            :page-size="form.pageSize"
            :total="tableData.total"
            @current-change ="disasterPageChangeHandle">
          </el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
  export default {
    data() {
      return {
        form : {
          // 年份
          createYear : '',
          // 状态
          status : '',
          // 当前页
          pageCurrent : 1,
          // 页数大小
          pageSize : 10,
        },
        // 辅助字段--状态
        statusSelect:[
          { value : 0, label : '未发布' },
          { value : 1, label : '进行中' },
          { value : 2, label : '已完成' },
        ],
        tableData : {},
        // 权限按钮
        powerBtns : [],
      }
    },
    mounted(){
      this.init();
    },
    watch:{
      $route(to,from){
        if(to.name === 'demandSurveyPublishIndex') {
          this.init();
        }
      }
    },
    methods:{
      // 初始化
      init(){
        // 显示按钮
        let url = '/edu-training-menu/demand-survey-index';
        this.powerBtns = this.$tool.getPowerBtns('eduTrainingMenu', url);
        console.log('按钮',this.powerBtns);
        // 搜索
        this.searchBtnClickHandle();
      },
      // 格式化时间
      formatDateTime(row, column, cellValue){
        let pro = column.property;
        let num = 10;
        // 年份4位 1999
        if(pro === 'createYear') num = 4;
        let str = this.$tool.formatDateTime(row[pro] || 0);
        return str ? str.substring(0, num) : str;
      },
      // 分页
      disasterPageChangeHandle(page){
        this.form.pageCurrent = page;
        this.searchBtnClickHandle();
      },
      // 搜索按钮
      searchBtnClickHandle(){
        this.$store.dispatch('eduReqInvManagerFindSimple', this.form).then(function(res){
          if(res.success){
            this.tableData = res.data;
          } else {
            this.$message({
              type : 'error',
              message : res.message || '错误'
            })
          }
        }.bind(this));
      },
      // 查看
      itemViewClick(row){
        let name = '';
        let params = {
          id : row.id,
          status : 'view'
        }
        switch(row.status){
          case 0:
          case 1:
          case 2:
                name = 'demandSurveyPublishAdd';
                break;
        }
        this.$router.push({ name : name, params : params})
      },
      // 修改
      itemUpdateClick(row){
        let name = '';
        let params = {
          id : row.id,
          status : 'edit'
        }
        switch(row.status){
          case 0:
          case 1:
            name = 'demandSurveyPublishAdd';
            break;
          case 2:
            break;
        }
        this.$router.push({ name : name, params : params})
      },
      // 删除按钮
      itemDeleteClick(row){
        this.$confirm('此操作将永久删除, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(function(){
          // 删除---灾后处置
          this.$store.dispatch('eduReqInvDelete', {
            id : row.id
          }).then(function(res){
            if(res.success){
              this.$message({
                type : 'success',
                message : '删除成功'
              })
              this.searchBtnClickHandle();
            } else {
              this.$message({
                type : 'error',
                message : res.message || '删除失败！！'
              })
            }
          }.bind(this))
        }.bind(this))
      },
      // 作废
      itemVoidClick(row){
        let params = {
          id : row.id,
          status : 3
        };
        this.$confirm('此操作将作废, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
          .then(function(){
            // 删除---灾后处置
            this.$store.dispatch('eduReqInvSave', params).then(function(res){
              if(res.success){
                this.$message({
                  type : 'success',
                  message : '作废成功'
                })
                this.searchBtnClickHandle();
              } else {
                this.$message({
                  type : 'error',
                  message : res.message || '作废失败！！'
                })
              }
            }.bind(this))
          }.bind(this))
      },
      // 重启
      restartBtnClick(row){
        let params = {
          id : row.id,
        };
        this.$confirm('此操作将状态改为进行中, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
          .then(function(){
            // 删除---灾后处置
            this.$store.dispatch('eduReqInvRestart', params).then(function(res){
              if(res.success){
                this.$message({
                  type : 'success',
                  message : '修改状态成功'
                })
                this.searchBtnClickHandle();
              } else {
                this.$message({
                  type : 'error',
                  message : res.message || '作废失败！！'
                })
              }
            }.bind(this))
          }.bind(this))
      }

    }
  }
</script>
<style>
</style>
