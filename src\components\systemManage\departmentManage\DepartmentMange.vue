<template>
  <div id="departmentManage" class="background-style">
    <el-row>
      <el-col :span="13">
        <!--公司管理区-->
        <div style="padding-top: 20px;padding-left: 20px">
          <div style="width: 100%;height: 60px;background-color: rgb(236,248,255);margin-bottom: 20px;border-radius: 5px;border-left: 5px solid #50BFFF">
            <h4 style="line-height: 60px;margin-left: 20px">公司结构管理</h4>
          </div>
          <div style="width: 100%;display: block;height: 60px">
<!--            <el-button type="primary" plain style="float: right;display: inline-block" @click="addDepartment">添加一级公司</el-button>-->
            <el-button type="primary" plain style="float: right;display: inline-block;margin-right: 10px;" @click="addTeamDialog">添加班组</el-button>
            <el-button type="primary" plain style="float: right;display: inline-block;margin-right: 10px;" @click="moveUp" >上移</el-button>
            <el-button type="primary" plain style="float: right;display: inline-block" @click="moveDown">下移</el-button>
          </div>
          <div>
            <el-tree
              :data="treeArray"
              :props="defaultProps"
              highlight-current
              node-key="id"
              :expand-on-click-node="false"
              :render-content="renderContent"
              :default-expanded-keys="treeExpandData"
              @node-click="treeNodeClick"
              style="width: 100%;">
            </el-tree>
          </div>
        </div>
      </el-col>
      <el-col :span="11">
        <!--公司或者部门对应的版主管理-->
        <div style="padding-top: 20px;padding-left: 20px">
          <div style="width: 95%;height: 60px;background-color: rgb(236,248,255);margin-bottom: 20px;border-radius: 5px;border-left: 5px solid #50BFFF">
            <h4 style="line-height: 60px;margin-left: 20px">班组管理</h4>
          </div>
          <div style="width: 95%">
            <el-table
              :data="teamData"
              border
              style="width: 100%"
            >
              <el-table-column
               prop="name"
               label="班组名"
               width="150"
               label-class-name="header-style"
              >
              </el-table-column>
              <el-table-column
                prop="deptName"
                label="部门"
                min-width="300"
                label-class-name="header-style"
              ></el-table-column>
              <el-table-column
                label="操作"
                align="center"
                width="170"
                fixed="right"
                label-class-name="header-style"
              >
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="primary"
                    @click="editTeamDialog(scope.row)">修改</el-button>
                  <el-button
                    size="mini"
                    type="danger"
                    @click="deleteTeam(scope.row,scope.$index)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </el-col>
    </el-row>


    <!--对话框-->
    <el-dialog :title="dialogTitle" :visible.sync="dialogFormVisible">
      <el-form :model="dialogForm" label-position="left">
        <el-form-item label="名  称" label-width="120">
          <el-input v-model="dialogForm.name" style="width: 400px"></el-input>
        </el-form-item>
        <el-form-item label="类  型" label-width="120">
          <el-select v-model="dialogForm.type" placeholder="请选择">
            <el-option v-for="item in dialogDepartmentType" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
          <el-tooltip placement="top">
            <div slot="content">公司下面可以创建公司和部门<br/>部门下面只能创建部门</div>
<!--            <el-button>？</el-button>-->
            <i class="el-icon-warning"></i>
          </el-tooltip>

        </el-form-item>
        <el-form-item label="自查检查表" label-width="120">
          <el-input v-model="dialogForm.selfInspectTableName" style="width: 200px"></el-input>
          <el-button type="primary" @click="searchReferTableClick(1);chooseTableVisible=true;">选择自查表</el-button>
        </el-form-item>
        <el-form-item v-show="isCompany" label="公司logo" label-width="80">
          <el-upload
            class="avatar-uploader"
            :action='upload.uploadUrl'
            :show-file-list="false"
            :with-credentials="upload.uploadCookies"
            :http-request="ossUploadRequest"
            :data="upload.params"
            :before-upload="beforeAvatarUpload">
            <img v-if="deptLogoUrl" :src="deptLogoUrl" class="avatar">
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false;dialogForm.selfInspectTableName='';dialogForm.selfInspectId=0">取 消</el-button>
        <el-button type="primary" @click="dialogDetermine">确 定</el-button>
      </div>
    </el-dialog>
    <!--班组添加编辑对话框-->
    <el-dialog :title="teamDialogTitle" :visible.sync="teamDialogVisible">
      <el-form :model="dialogTeamForm" label-position="left">
        <el-form-item label="班组名称" label-width="80">
          <el-input v-model="dialogTeamForm.name" style="width: 400px"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="teamDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="addTeam">确 定</el-button>
      </div>
    </el-dialog>


    <!--选择参考检查表-->
    <el-dialog title="选择检查表" :visible.sync="chooseTableVisible">
      <div style="">
        <span style="float: left;margin-left: 10px;line-height: 40px;display: inline-block">分类：</span>
        <el-cascader
          :props="tableProp"
          :options="tableTagOption"
          clearable
          filterable
          :debounce="400"
          change-on-select
          v-model="chooseTable.tableTag"
          placeholder="请选择或输入关键字"
          style="width: 70%;float: left;display: inline-block">
        </el-cascader>
        <el-button type="primary" style="float: left;margin-left: 20px;display: inline-block" @click="searchReferTableClick(1)">搜索</el-button>
      </div>

      <el-table
        :data="referTableData"
        border
        tooltip-effect="light"
        highlight-current-row
        @row-click="referTableClick"
        style="width: 100%;margin-top: 20px;float: left">
        <el-table-column
          type="index"
          width="55"
          label-class-name="inner-header-style">
        </el-table-column>
        <el-table-column
          prop="name"
          label="检查表"
          show-overflow-tooltip
          label-class-name="inner-header-style">
        </el-table-column>
      </el-table>
      <div style="margin-top: 5px;float: left;width: 100%;margin-bottom: 10px">
        <el-pagination
          background
          layout="prev, pager, next"
          :current-page="tableCurrentPage"
          :total="tableTotalItem"
          @current-change="tablePageClick">
        </el-pagination>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="chooseTableVisible=false">取 消</el-button>
        <el-button type="primary" @click="determineReferTable">确 定</el-button>
      </div>
    </el-dialog>
    <!--参考检查表结束-->
  </div>
</template>
<script>
  import dealData from '../../../assets/functions/dealData'
  export default {
    name: 'departmentManage',
    data() {
      return {
        dialogTitle:'',
        dialogFormVisible:false,
        dialogForm:{
          name:'',
          type:'1',
          selfInspectId:0,
          selfInspectTableName:''
        },
        dialogDepartmentType:[
          {value:'1',label:'公司'},
          {value:'0',label:'部门'}
        ],
        tempParentId:'',
        tempId:'',
        requestType:'',
        treeId:10,
        defaultProps: {
          children: 'subDept',
          label: 'typeName'
        },
        move:{
          moveId:0,
          num:0,
          parentId:0,
          name:'',
          type:0,
        },
        /*班组信息*/
        teamData:[],
        teamForm:{
          id:0,
          name:'',
          num:0,
          delFlag:0,
          deptId:0
        },
        teamDialogVisible:false,
        teamDialogTitle:'添加班组',
        dialogTeamForm:{
          name:''
        },
        teamUpdate:false,
        deptLogoUrl:'',
        isCompany:false,
        upload:{
          params:{
            contentId:'',
            contentType:16
          },
          uploadUrl:'',
          uploadCookies:true,
        },
        loading:false,
        //----------------------选择检查表对话框----------------------------
        chooseTableVisible:false,
        chooseTable:{
          tableTag:[],
          tagLoading:false
        },
        tableProp:{
          children: 'dangerInspectTableLabels',
          label: 'label',
          value:'label'
        },
        referTableData:[],
        currentReferTable:'',
        tableCurrentPage:0,
        tableTotalItem:0,
        treeExpandData:[]
      }
    },
    computed:{
      treeArray:function(){
        if(this.$store.state.sysManageData.deptTree[0]){
          this.treeExpandData.push(this.$store.state.sysManageData.deptTree[0].id)
        }else{
        }

        return this.$store.state.sysManageData.deptTree;
      },
      tableTagOption:function () {
        return this.$store.state.hideDangerData.tableTreeLabels;
      },
    },
    mounted:function () {
      this.$store.dispatch("getAllDept");
      //检查表的标签，树形的
      this.$store.dispatch("getTableTreeLabels",this.$tool.getStorage('LOGIN_USER').companyId);
    },
    watch:{
    },
    methods: {
      moveUp:function () {
        if(this.move.moveId!=0){
          var params=new URLSearchParams();
          params.append("id",this.move.moveId)
          params.append("num",this.move.num)
          params.append("parentId",this.move.parentId)
          params.append("type",this.move.type)
          this.$http.post("/dept/mvUpDept",params).then(function (res) {
            if(res.data.success){
              this.$store.dispatch("getAllDept");
              this.move.moveId=0
              this.$message.success("上移成功")
            }
          }.bind(this)).catch(function (err) {

          })
        }else{
          this.$message.error("请先选中公司或者部门")
        }
      },
      moveDown:function () {
        if(this.move.moveId!=0){
          var params=new URLSearchParams();
          params.append("id",this.move.moveId)
          params.append("num",this.move.num)
          params.append("parentId",this.move.parentId)
          params.append("type",this.move.type)
          this.$http.post("/dept/mvDownDept",params).then(function (res) {
            if(res.data.success){
              this.$store.dispatch("getAllDept");
              this.move.moveId=0
              this.$message.success("下移成功")
            }
          }.bind(this)).catch(function (err) {

          })
        }else{
          this.$message.error("请先选中公司或者部门")
        }
      },
      dialogDetermine:function () {
        //console.log(this.dialogForm.name);
        if(this.dialogForm.name.trim()){
          let params= new URLSearchParams;
          params.append("name",this.dialogForm.name.trim());
          params.append("type",Number(this.dialogForm.type));
          params.append("selfInspectId",this.dialogForm.selfInspectId);
          if(this.requestType==='add'){
            params.append("parentId",this.tempParentId);
          }else{
            params.append("id",this.tempId);
          }
          this.$http.post('dept/addOrUpdate',params).then(function (res) {
            if(res.data.success){
              this.$message({
                showClose: true,
                message: '新增成功',
                type: 'success'
              });
              // this.$store.dispatch("getAllDept");
              this.$store.dispatch("getAllDept");
              this.dialogForm.name='';
              this.dialogForm.type='1';
              this.dialogFormVisible=false;
              this.dialogForm.selfInspectTableName='';
              this.dialogForm.selfInspectId=0
            }else{
              this.$message({
                showClose: true,
                message: res.data.message,
                type: 'warning'
              });
            }
          }.bind(this)).catch(function (err) {
            this.dialogForm.name='';
            this.dialogForm.type='1';
            this.dialogFormVisible=false;
            this.dialogForm.selfInspectTableName='';
            this.dialogForm.selfInspectId=0
            console.log(err);
            this.$message({
              showClose: true,
              message: '网络错误，请尝试重登录',
              type: 'error'
            });
          }.bind(this));

        }else{
          this.dialogFormVisible=false;
          this.$message({
            type: 'warning',
            message: '部门名不能为空'
          });
        }
      },
      addDepartment:function () {
        this.dialogTitle="新增";
        this.dialogForm.name='';
        this.dialogForm.type='1';
        this.tempParentId=0;
        this.requestType='add';
        this.dialogFormVisible=true;
      },
      append(data) {
        this.dialogTitle="新增";
        this.dialogForm.name='';
        this.dialogForm.type='1';
        this.tempParentId=data.id;
        this.requestType='add';
        this.dialogFormVisible=true;
        this.dialogForm.selfInspectTableName='';
        this.dialogForm.selfInspectId=0
      },
      edit(data){
        //console.log(data);
        this.dialogTitle="修改";
        this.dialogForm.name=data.name;
        this.dialogForm.type=String(data.type);
        this.dialogForm.selfInspectId=data.selfInspectId
        this.tempId=data.id;
        this.requestType='edit';
        this.dialogFormVisible=true;
        if(this.dialogForm.selfInspectId!=0 && this.dialogForm.selfInspectId != null){
          this.getInspectTableName(this.dialogForm.selfInspectId)
        }else{
          this.dialogForm.selfInspectTableName='';
          this.dialogForm.selfInspectId=0
        }
      },
      remove(node, data) {
        if(data.type ==1){
          this.$confirm('此操作将永久删除该公司及其下属所有部门, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            let params=new URLSearchParams;
            params.append("deptId",data.id);
            this.$http.post('dept/deleteCompany',params).then(function (res) {
              if(res.data.success){
                this.$message({
                  showClose: true,
                  message: '删除成功',
                  type: 'success'
                });
                this.$store.dispatch("getAllDept");
              }else{
                this.$message({
                  showClose: true,
                  message: res.data.message,
                  type: 'warning'
                });
              }
            }.bind(this));
          }).catch(() => {
            this.$message({
              type: 'info',
              message: '已取消删除'
            });
          });
        }else{
          this.$confirm('此操作将永久删除该部门, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            let params=new URLSearchParams;
            params.append("deptId",data.id);
            this.$http.post('dept/delete',params).then(function (res) {
              if(res.data.success){
                this.$message({
                  showClose: true,
                  message: '删除成功',
                  type: 'success'
                });
                this.$store.dispatch("getAllDept");
              }else{
                this.$message({
                  showClose: true,
                  message: res.data.message,
                  type: 'warning'
                });
              }
            }.bind(this));
          }).catch(() => {
            this.$message({
              type: 'info',
              message: '已取消删除'
            });
          });
        }


      },
      treeNodeClick:function (row) {
        this.move.moveId=row.id
        this.move.num=row.num
        this.move.parentId=row.parentId
        this.move.name=row.name
        this.move.type=row.type
        this.upload.params.contentId=row.id
        if(row.type==1){
          this.isCompany=true
        }
        if(row.logoFileId==0){
          this.deptLogoUrl=''
        }else{
          this.deptLogoUrl=row.logoPath
        }
        this.loadTeam(row.id)
      },
      /*班组开始*/
      loadTeam:function (id) {
        var params=new URLSearchParams()
        params.append("deptId",id)
        params.append("delFlag",0)
        this.$http.post("/team/find",params).then(function (res) {
          if(res.data.success){
            this.teamData=res.data.data
          }
        }.bind(this)).catch(function (res) {

        })
      },
      addTeamDialog:function () {
        if(this.move.moveId!=0){
          this.teamDialogVisible=true
          this.teamDialogTitle="添加班组"
          this.teamUpdate=false
        }else{
          this.$message.error("请先选中部门")
        }
      },
      addTeam:function () {
        var params=new URLSearchParams()
        params.append("name",this.dialogTeamForm.name)
        params.append("deptId",this.move.moveId)
        if(this.teamUpdate){
          params.append("id",this.teamForm.id)
          this.$http.post("/team/update",params).then(function (res) {
            this.loadTeam(this.move.moveId)
            this.teamDialogVisible=false
          }.bind(this)).catch(function (err) {
            this.$message.error(err)
          })
        }else {
          params.append("deptName",this.move.name)
          this.$http.post("/team/add",params).then(function (res) {
            if(res.data.success){
              this.teamData.splice(this.teamData.length,0,res.data.data)
              this.teamDialogVisible=false
            }else{
              this.$message.error("创建班组失败")
            }
          }.bind(this)).catch(function (err) {
            this.$message.error(err)
          })
        }
      },
      editTeamDialog:function (row) {
        this.teamDialogVisible=true
        this.dialogTeamForm.name=row.name
        this.teamUpdate=true
        this.teamForm.id=row.id
        this.teamDialogTitle="编辑班组"
      },
      deleteTeam:function (row,index) {
        this.$confirm('确认删除班组么, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          var params=new URLSearchParams()
          params.append("id",row.id)
          this.$http.post("/team/delete",params).then(function (res) {
            if(res.data.success){
              this.teamData.splice(index,1)
              this.$message.success("删除成功")
            }
          }.bind(this)).catch(function (err) {

          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });
        });
      },
      renderContent(h, { node, data, store }) {
        if(data.type==0){
          return (
            <span style="flex: 1; display: flex; align-items: center; justify-content: space-between; font-size: 14px; padding-right: 8px;">
            <span>
            <span>{node.label}</span>
          </span>
          <span>
          <el-button style="font-size: 14px;" type="text" on-click={ () => this.append(data) }>添加子部门</el-button>
          <el-button style="font-size: 14px;color: #67C23A" type="text" on-click={ () => this.edit(data) }>编辑</el-button>
          <el-button style="font-size: 14px;color: #E60228" type="text" on-click={ () => this.remove(node, data) }>删除</el-button>
          </span>
          </span>);
        }else{
          return (
            <span style="flex: 1; display: flex; align-items: center; justify-content: space-between; font-size: 14px; padding-right: 8px;">
            <span>
            <span>{node.label}</span>
          </span>
          <span>
          <el-button style="font-size: 14px;" type="text" on-click={ () => this.append(data) }>添加子部门</el-button>
          <el-button style="font-size: 14px;" type="text" on-click={ () => this.initCompanyData(data) }>初始数据</el-button>
          <el-button style="font-size: 14px;color: #67C23A" type="text" on-click={ () => this.edit(data) }>编辑</el-button>
          <el-button style="font-size: 14px;color: #E60228" type="text" on-click={ () => this.remove(node, data) }>删除</el-button>
          </span>
          </span>);
        }

      },
      //签名直传公司logo
      ossUploadRequest:function (item) {
        //获取该文件对应的sign
        this.$http.get('sys/oss/sign?contentId='+this.upload.params.contentId+'&contentType='+this.upload.params.contentType+'&realName='+item.file.name).then(function (res) {
          if(res.data){
            let params=new FormData();
            params.append("name",item.file.name);
            params.append("key",res.data.dir + item.file.name);
            params.append("policy",res.data.policy);
            params.append("OSSAccessKeyId",res.data.accessid);
            params.append('success_action_status','200');
            params.append("callback",res.data.callback);
            params.append("signature",res.data.signature);
            params.append("file",item.file);
            this.fileHttp.post('',params,{headers: {'Content-Type': 'multipart/form-data'}}).then(function (res) {
              if(res.data.file){
                let resultStr=dealData.decode(res.data.file);
                let resultJson=JSON.parse(resultStr);
                this.deptLogoUrl=this.fileHttp.defaults.baseURL+resultJson.path;
                this.$message.success('上传成功');
                this.$http.post("/dept/addOrUpdate?id="+this.upload.params.contentId+'&logoFileId='+resultJson.fId).then(function (res) {
                }.bind(this)).catch(function (err) {
                  this.$store.dispatch("getAllDept");
                })
              }else{
                this.$message.error('上传图片失败');
              }

            }.bind(this))
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message.error('获取唯一标识失败');
        }.bind(this));
      },
      beforeAvatarUpload(file) {
        const isJPG = file.type === 'image/jpeg';
        const isLt2M = file.size / 1024 / 1024 < 2;

        if (!isJPG) {
          this.$message.error('上传头像图片只能是 JPG 格式!');
        }
        if (!isLt2M) {
          this.$message.error('上传头像图片大小不能超过 2MB!');
        }
        return isJPG && isLt2M;
      },
      initCompanyData:function(data){
        const loading = this.$loading({
          lock: true,
          text: '数据初始化中',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        this.$http.get("/init/initCompanyData/0/"
          +data.id).then(function (res) {
            loading.close()
            if(res.success){
              this.$message.success("初始化成功")
            }else{
              this.$message.error("该公司已经初始化数据，或者有其他错误")
            }
        }.bind(this)).catch(function (err) {

        })
      },
      //---------------------------------选择参考检查表------------------------------------
      //-PS-这里的标签都是用来选参考检查表
      //搜索参考检查表
      searchReferTableClick:function (page) {
        this.tableCurrentPage=page;
        let params={pageCurrent:page,pageSize:10
          ,labels:this.chooseTable.tableTag
          ,dangerInspect:{companyId:this.$tool.getStorage('LOGIN_USER').companyId}};
        this.referTableData=[];
        this.$http.post('danger/inspect/find', params).then(function (res) {
          if (res.data.success) {
            this.tableTotalItem=res.data.data.total;
            this.referTableData=res.data.data.list;
          }
          this.chooseTable.tagLoading = false
        }.bind(this)).catch(function (err) {
          console.log(err);
        });
      },
      //翻页
      tablePageClick:function (val) {
        if(val){
          this.searchReferTableClick(val);
        }
      },
      //查看参考检查表
      viewReferTable:function (row) {

      },
      //选中某一行
      referTableClick:function (row) {
        if(row){
          this.dialogForm.selfInspectTableName=row.name
          this.dialogForm.selfInspectId=row.id
          this.currentReferTable=row;
        }else {
          this.currentReferTable='';
        }
      },
      //确定参考检查表
      determineReferTable:function () {
        if(this.currentReferTable){
          // this.$router.push({name:'investigationNewWorkflow',params:{referPlanId:this.currentReferTable.id,referName:this.currentReferTable.name,investigationType:this.investigationType,typeName:this.inpectTypeArr[this.investigationType]}})
        }else{
          this.$message.warning('请选择检查表!');
        }
        this.chooseTableVisible=false;
      },

      getInspectTableName:function (inspectId) {
        let params={dangerInspect:{id:inspectId}};
        this.$http.post("/danger/inspect/find",params).then(function (res) {
          if(res.data.success){
            if(res.data.data.size>0){
              this.dialogForm.selfInspectTableName=res.data.data.list[0].name
            }
          }else{
            this.$message.error("获取检查表名字失败")
          }
        }.bind(this)).catch(function (err) {
          console.log(err)
        })
      },
    }
  }
</script>
<style>
  .el-table__body tr.current-row>td {
    background: rgb(255,243,185)!important;
  }
  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #0f6fc6;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
  }
  .avatar {
    width: 178px;
    height: 178px;
    display: block;
  }
</style>
