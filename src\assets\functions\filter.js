exports.install = function (Vue, options) {
  Vue.prototype.transferTime = function (time=null,format=null,withTime=false) {
    if(format){//有规定格式的时间
      if (time) {
        let oDate = new Date();
        oDate.setTime(time);

        let y = oDate.getFullYear();
        let m = oDate.getMonth() + 1;
        let d = oDate.getDate();

        format=format.replace('y',y);
        format=format.replace('m',m);

        if(withTime){//需要带上时分秒
          format=format.replace('d',d);
          return format+' '+oDate.getHours()+':'+oDate.getMinutes()+':'+oDate.getSeconds();
        }else{
          return format.replace('d',d);
        }

      }else{
        return ''
      }
    }else{//默认格式的时间
      if (time) {
        let oDate = new Date();
        oDate.setTime(time);

        let y = oDate.getFullYear();
        let m = oDate.getMonth() + 1;
        let d = oDate.getDate();

        if(withTime){
          return y + '-' + m + '-' + d+' '+oDate.getHours()+':'+oDate.getMinutes()+':'+oDate.getSeconds();
        }else{
          return y + '-' + m + '-' + d;
        }

      }else{
        return ''
      }
    }

  };


  Vue.prototype.$formatDate = function (time=null,format=null,withTime=false) {
    if(format){//有规定格式的时间
      if (time) {
        let oDate = new Date();
        oDate.setTime(time);

        let y = oDate.getFullYear();
        let m = oDate.getMonth() + 1;
        let d = oDate.getDate();

        format=format.replace('y',y);
        format=format.replace('m',m);

        if(withTime){//需要带上时分秒
          format=format.replace('d',d);
          return format+' '+oDate.getHours()+':'+oDate.getMinutes()+':'+oDate.getSeconds();
        }else{
          return format.replace('d',d);
        }

      }else{
        return ''
      }
    }else{//默认格式的时间
      if (time) {
        let oDate = new Date();
        oDate.setTime(time);

        let y = oDate.getFullYear();
        let m = oDate.getMonth() + 1;
        let d = oDate.getDate();

        if(withTime){
          return y + '-' + m + '-' + d+' '+oDate.getHours()+':'+oDate.getMinutes()+':'+oDate.getSeconds();
        }else{
          return y + '-' + m + '-' + d;
        }

      }else{
        return ''
      }
    }

  };

  //在对象数组里查找，符合要求的对象，返回index
  Vue.prototype.findIndexOfJsonArray = function (arr,prop,val){//对象数组，需查找的属性，该属性的值
    for(let i=0;i<arr.length;i++){
      if(arr[i][prop]===val){
        return i;
      }
    }
    return -1;
  };

  //时间补零
  Vue.prototype.timeAddZero = function (val) {
    if(val<10){
      return '0'+val;
    }else{
      return val;
    }
  };
};
