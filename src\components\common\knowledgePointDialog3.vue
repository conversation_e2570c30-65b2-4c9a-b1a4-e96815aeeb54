<template>
  <div id="knowledgePointDialog">
    <!--对话框开始-->
    <el-dialog title="添加知识点" :visible.sync="isShow" width="80%">
      <el-container>
        <el-main >
          <el-row  class="row">
            <el-form ref="form" label-width="100px"  >
              <el-tabs value="first">
                <el-tab-pane label="联动查询" name="first" >
                  <el-col :span="18">
                    <el-form-item label="选择分类：">
                      <el-cascader
                        style="width:400px;"
                        @change="handleChange"
                        :props="label.cascaderProp"
                        :options="label.result">
                      </el-cascader>
                    </el-form-item>
                  </el-col>
                 <!-- <el-col :offset="1" :span="8">
                    <el-form-item label="项目名称：">
                      {{ label.selectedItem.value }}
                    </el-form-item>
                  </el-col>
                  <el-col :offset="1" :span="2">
                    <el-button type="success" @click="searchBtnClickHandle">搜索</el-button>
                  </el-col>-->
                </el-tab-pane>
                <el-tab-pane label="标签查询" name="second">
                  <el-col :span="18">
                    <el-select
                      multiple
                      v-model="selectedSearchValArr"
                      filterable
                      remote
                      reserve-keyword
                      clearable
                      placeholder="请输入标签名后选择,可多选"
                      style="width: 500px">
                      <el-option
                        v-for="item in labelList"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                      </el-option>
                    </el-select>
                  </el-col>
                  <el-col :span="6">
                    <el-button type="success"  @click="searchBtn2ClickHandle" size="small" icon="el-icon-search">搜索</el-button>
                  </el-col>
                </el-tab-pane>
                <el-tab-pane label="关键字查询" name="thrid">
                  <el-col :span="6">
                    <el-input v-model="keyword"></el-input>
                  </el-col>
                  <el-col :offset="1" :span="6">
                    <el-button type="success"  @click="searchBtn3ClickHandle" size="small" icon="el-icon-search">搜索</el-button>
                  </el-col>
                </el-tab-pane>
              </el-tabs>
            </el-form>
          </el-row>
          <el-row class="row">
            <el-col :span="24">
              <el-transfer
                v-model="transfer.selectedData"
                :data="transfer.data"
                @change="changeHandle"
                :props="transfer.props">
              </el-transfer>
            </el-col>
          </el-row>
        </el-main>
      </el-container>
         <span slot="footer" class="dialog-footer">
          <el-button @click="isShow = false">取 消</el-button>
          <el-button type="primary" @click="okBtnClickHandle">确 定</el-button>
        </span>
    </el-dialog>
  </div>
</template>

<script>
  export default {
    data(){
      return {
        // 是否显示对话框
        isShow : false,
        // 联动搜索
        label : {
          // 搜索条件---标签查询的条件
          search : {
//            parent : 0,
            pageSize : 10000,
            companyId: 0,
          },
          // 级联选择器配置属性
          cascaderProp : {
            label : 'label',
            value : 'label',
            children : 'safeLabels'
          },
          // 搜索结果---联动的
          result : [],
          // 选中的值
          selectedItem : {
            value : '',
            id : '',
          }
        },
        // 标签搜索的数据
        labelList : [],
        // 关键字搜索
        keyword : '',
        // 穿梭框
        transfer : {
          // 总体的数据
          data : [],
          // 选中的值，右边的值
          selectedData : [],
          // 属性配置
          props : {
            key : 'id',
            label : 'content',
          },
        },
        // 穿梭框选中组成的表格
        tableData : [],

        // 标签搜索选择字段
        selectedSearchValArr : [],


      }
    },
    computed:{
      // 知识点
      knowledgeList : function(){
        return this.$store.state.emerHandleModule.knowledgeList;
      }
    },
    watch:{
      'isShow'(from, to){
        this.clear();
      }
    },
    mounted(){
      this.init();
    },
    methods: {
      clear(){
        this.label.selectedItem = this.$tool.clearObj({}, this.label.selectedItem);
        this.transfer.data = [];
        this.transfer.selectedData = [];
        this.tableData = [];
      },
      init(){
        this.clear();
        this.searchLabelList();
        this.searchCascaderList();
      },
      // 搜索知识点标签
      searchLabelList(){
        let params = {
          companyId : this.$tool.getStorage('LOGIN_USER').companyId,
          pageSize : 50
        }
        this.$store.dispatch('findLabelStr', this.$tool.jsonToForm(params)).then(function(res){
          if(res.success){
            this.labelList = res.data.list.map(function(it){
              return {
                label : it,
                value : it
              }
            }.bind(this));
          }
        }.bind(this));
      },
      // 搜索联动
      searchCascaderList(){
        this.label.search.companyId = this.$tool.getStorage('LOGIN_USER').companyId;
        let params = this.$tool.jsonToForm(this.label.search);
        this.$store.dispatch('getLabelConstruct', params).then(function(res){
          if(res.success){
            this.label.result = res.data;
          }
        }.bind(this));
      },
      // 标签动态加载次级选项
      handleItemChange(val){
        // 找到对应的parent
        this.label.search.companyId=this.$tool.getStorage('LOGIN_USER').companyId;
        this.label.search.parent = val[val.length - 1];
        let params = this.$tool.jsonToForm(this.label.search);
        // 通过parent找到子类
        this.$store.dispatch('labelFind', params).then(function(res){
          if(res.success){
            // 找到对应的id,把值放到children数组中去
            let list = res.data.list;
            if(list.length > 0){
              let parent = list[0].parent;
              // 如果list为空，则说明父级没有子集，那么就删除父级的children
              this.lookItemByParent(this.label.result, parent, list);
            }
          }
        }.bind(this));
      },
      /*
       * 从data里面找到parent相等的子项的index
       * data 是指父数据
       * parent 是指要从父数据中查找的parent
       * addData 是指查找之后的children要添加的children
       * */
      lookItemByParent(data, parent, addData){
        let index = -1;
        for(let i = 0; i < data.length; i++){
          // 判断是否有children。有，则继续下一层，没有则开始寻找
          if(data[i].children && data[i].children.length > 0){
            this.lookItemByParent(data[i].children, parent, addData);
          } else {
            if(data[i].value === parent){
              index = i;
              break;
            }
          }
        }
        // 找到对应的item的index
        if(index > -1){
          if(addData.length > 0){
            addData.forEach(function(it){
              let item = {
                label : it.label,
                value : it.id,
              }
              if(it.hasSon){
                item['children'] = [];
              }
              data[index].children.push(item);
            }.bind(this))
          }
        }
      },
      // 选择对应的标签
      handleChange(val){
        let params = this.$tool.jsonToForm({
          labels : val,
          companyId:this.$tool.getStorage('LOGIN_USER').companyId
        });

        this.$store.dispatch('knowledgelFind', params).then(function(res) {
          if (res.success) {
            this.transfer.data = res.data.list
          }
        }.bind(this));
      },
      // 通过标签搜索知识点----联动搜索
      searchBtnClickHandle(){
        let label = this.label.selectedItem.value;
        if(!label){
          this.$message({
            type : 'warning',
            message : '请选择分类再搜索！'
          })
          return;
        }
        // 先清空数据
        let params = this.$tool.jsonToForm({
          labels : [this.label.selectedItem.value],
          pageSize : 1000,
          companyId:this.$tool.getStorage('LOGIN_USER').companyId
        });
        this.$store.dispatch('knowledgelFind', params).then(function(res) {
          if (res.success) {
            this.transfer.data = res.data.list;
          } else {
            this.transfer.data = [];
          }
        }.bind(this));
      },
      // 通过标签搜索知识点----标签搜索
      searchBtn2ClickHandle(){
        let params = {
          pageCurrent : 1,
          labels : [],
          companyId:this.$tool.getStorage('LOGIN_USER').companyId
        }
        params.labels = this.selectedSearchValArr.map(function(it){
          return it;
        })
        // 获取知识点列表
        this.$store.dispatch("knowledgeList", params).then(function(res){
          if (res.success) {
            this.transfer.data = res.data.list;
          } else {
            this.transfer.data = [];
          }
        }.bind(this))
      },
      // 通过关键字搜索知识点----关键字搜索
      searchBtn3ClickHandle(){
        let user = this.$tool.getStorage('LOGIN_USER');
        let params = {
          content : this.keyword,
          companyId : user.companyId
        }
        // 获取知识点列表
        this.$store.dispatch("knowledgeList", params).then(function(res){
          if (res.success) {
            this.transfer.data = res.data.list;
          } else {
            this.transfer.data = [];
          }
        }.bind(this))
      },
      // 右侧列表元素变化时触发
      changeHandle(val){
        if(val.length < 1) {
          this.tableData = [];
          return;
        }
        this.tableData = this.transfer.data.filter(function(it){
          return val.includes(it.id)
        })
      },
      // 确定按钮
      okBtnClickHandle(){
        if(this.tableData.length < 1){
          this.$message({
            type : 'error',
            message : '请选择左侧项目！！'
          })
          return;
        }
        this.$emit('selectedRows', this.tableData);
        this.isShow = false;
      }

    }
  }
</script>

<style>
  #knowledgePointDialog .el-transfer-panel{
    width:400px;
  }
  #knowledgePointDialog .el-checkbox+.el-checkbox{
    margin-left:0px;
  }
</style>
