<template>
    <div id="staff">
      <el-row >
        <el-col :span="2">
          <el-button
            v-if="staff.isShowBtn"
            size="mini" type="success"
            icon="el-icon-plus" @click="addBtnHandle">人员</el-button>
        </el-col>
      </el-row>
      <el-row  >

        <el-col :span="22" :offset="2" >
          <chooseStaffPage
            @deleteHandle="deleteHandle"
            ref="chooseStaffPage"
            :staffData.sync="staff.tableData"></chooseStaffPage>
        </el-col>
        <chooseStaffDialog
          @currentChange="currentChange"
          :data.sync="staff.dialog"></chooseStaffDialog>
      </el-row>
    </div>
</template>

<script>
  import chooseStaffDialog from '@/components/common/chooseStaffDialog'
  import chooseStaffPage from '@/components/common/chooseStaffPage'
  export default {
    components: {
      chooseStaffDialog,
      chooseStaffPage
    },
    data(){
      return {
        staff : {
          // 对话框
          dialog : {
            isShow : false,
            nodeTree : [],
          },
          // 表格数据
          tableData : [],
          // 是否显示按钮
          isShowBtn : true,
        },
      }
    },
    methods:{
      // ##################方法-----START
      // 改变 staff.tableData 处理函数---供父组件调用
      changeTableDataHandle(arr){
        this.staff.tableData = arr;
      },
      // 改变 staff.isShowBtn 处理函数---供父组件调用
      isShowBtnHandle(flag){
        this.staff.isShowBtn = flag;
      },
      // 改变 staff.isShowDelete 处理函数---供父组件调用
      isShowDeleteHandle(flag){
        this.$refs['chooseStaffPage'].isShowDeleteHandle(flag);
      },
      isShowJoinHandle(flag){
        this.$refs['chooseStaffPage'].isShowJoinHandle(flag);
      },

      // 改变对话框节点树，公司，部门信息
      setDialogNodeTree(node){
        console.log('node', node)
        this.staff.dialog.nodeTree = node;
      },
      // ##################方法-----END


      // ############事件-----START
      // 参与人员---对话框---确定按钮-----selectedRows-----获取选择人员列表
      currentChange(val){
        this.staff.dialog.isShow = false;
        this.$nextTick(function(){
          let userList = this.staff.tableData.map(function(it){
            // return it.username;
            return it.userId;
          })
          val.forEach(function(it){
            // if(!userList.includes(it.username)){
            if(!userList.includes(it.userId)){
              this.staff.tableData.push({
                userId : it.userId,
                companyId : it.companyId,
                parentCompanyId : it.parentCompanyId,
                companyName : it.companyName,
                deptName : it.deptName,
                username : it.username,
              })
            }
          }.bind(this))

          this.$emit('selectedRows', this.staff.tableData);
          return;
          // 辅助参与人员表格
          this.staff.tableData = val.map(function(it){
            return {
              companyId : it.companyId,
              parentCompanyId : it.parentCompanyId,
              companyName : it.companyName,
              deptName : it.deptName,
              username : it.username,
            }
          })
        }.bind(this));

      },
      // ############事件-----END



      // 添加按钮--显示对话框
      addBtnHandle(){
        this.staff.dialog.isShow = true;
      },

      // 删除人员
      deleteHandle(row){
        this.staff.tableData = this.staff.tableData.filter(function(it){
          // return it.username != row.username;
          return it.userId != row.userId;
        })
        this.$emit('selectedRows', this.staff.tableData);
      }
    }
  }
</script>

<style>
#staff{
  display: flex;
  flex-direction: column;
}
</style>
