<template>
  <div id="recordEmerMaterial">
    <div class="background-style">
      <el-button type="primary" icon="el-icon-d-arrow-left" size="medium" style="margin-left: 10px"
                 @click="backClick"></el-button>
      <h3 style="margin: 20px auto 20px 10px;letter-spacing: 2px;display: inline-block">人员列表</h3>
      <!--查询条件和功能按钮-->
      <div style="width: 70%;margin-top: 20px;margin: auto">
        <el-form label-width="100px" class="demo-ruleForm">
          <el-col :span="24">
            <el-col :span="12">
              <el-form-item label="队伍名称：">
                <el-input v-model="groupInfo.groupName" readonly="readonly"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="队伍专业：">
                <el-input v-model="groupInfo.major" readonly="readonly"></el-input>
              </el-form-item>
            </el-col>
          </el-col>
          <el-col :span="24">
            <el-col :span="12">
              <el-form-item label="组长：">
                <el-input v-model="groupInfo.leader" readonly="readonly"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="驻扎地：">
                <el-input v-model="groupInfo.location" readonly="readonly"></el-input>
              </el-form-item>
            </el-col>
          </el-col>
          <el-col :span="24">
            <el-col :span="12">
              <el-form-item label="成员数：">
                <el-input v-model="groupInfo.memberCount" type="number" readonly="readonly"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="成立时间：">
                <el-date-picker
                  v-model="groupInfo.createTime"
                  type="date"
                  style="width:100%;"
                  readonly="readonly"
                  placeholder="选择日期">
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-col>
          <el-col :span="24">
            <el-col :span="24">
              <el-form-item label="装备：" prop="manager">
                <el-input v-model="groupInfo.equipment" type="textarea" readonly="readonly"></el-input>
              </el-form-item>
            </el-col>

          </el-col>
        </el-form>
      </div>
      <!--查询条件和功能按钮结束-->
      <!--表格区,队伍人员-->
      <div style="width: 71%;margin-top: 10px;margin: auto">
        <el-col :span="24">
          <el-button type="primary" icon="el-icon-plus" @click="newGroupMemberDialog()" style="float: right;margin-bottom: 20px">添 加 成 员</el-button>
        </el-col>
        <el-col :span="24">
          <el-table
            :data="memberData"
            border
            fit
            highlight-current-row
            style="width: 100%;">
            <el-table-column
              type="index"
              label="序号"
              width="60"
              align="center"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="userName"
              label="姓名"
              width="150"
              align="center"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              label="职务"
              width="150"
              align="center"
              label-class-name="header-style">
              <template slot-scope="scope">
                <el-tag size="medium" v-if="scope.row.type==0">成员</el-tag>
                <el-tag size="medium" v-if="scope.row.type==1">副队长</el-tag>
                <el-tag size="medium" v-if="scope.row.type==2">队长</el-tag>
              </template>
            </el-table-column>
            <el-table-column
              prop="phone"
              label="电话"
              width="140"
              align="center"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="remark"
              label="备注"
              min-width="220"
              align="center"
              label-class-name="header-style">
            </el-table-column>

            <el-table-column fixed="right" label="操作"
                             label-class-name="header-style"
                             width="180"
                             align="center">
              <template slot-scope="scope">
                <el-button size="mini" type="primary" style="float: left"
                           @click="editMemberDialog(scope.row,scope.$index)">修改
                </el-button>
                <el-button size="mini" type="danger" style="float: left"
                           @click="deleteMemberDialog(scope.row,scope.$index)">删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </div>
      <!--表格区结束-->
      <!--对话框-->
      <el-dialog
        title="编辑人员"
        :visible.sync="dialog.dialogVisible"
        width="30%"
        center>
        <el-form label-width="100px" :model="groupMember" :rules="memberRules" ref="memberForm" class="demo-ruleForm">
          <el-col :span="24">
            <el-form-item label="姓名：" prop="userName">
              <el-input v-model="groupMember.userName"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="职务：">
              <el-select v-model="groupMember.type" placeholder="请选择">
                <el-option
                  key="普通成员"
                  label="普通成员"
                  value="0"
                >
                  普通成员
                </el-option>
                <el-option
                  key="副队长"
                  label="副队长"
                  value="1"
                >
                  副队长
                </el-option>
                <el-option
                  key="队长"
                  label="队长"
                  value="2"
                >
                  队长
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="电话：" prop="phone">
              <el-input v-model="groupMember.phone"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注：" prop="remark">
              <el-input v-model="groupMember.remark" type="textarea"></el-input>
            </el-form-item>
          </el-col>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialog.dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="addOrUpdate()">确 定</el-button>
        </span>
      </el-dialog>


      <el-dialog
        title="删除成员"
        :visible.sync="dialog.deleteDialogVisible"
        width="30%"
        center>
        <span>确认删除该成员</span>
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialog.deleteDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="deleteMember()">确 定</el-button>
        </span>
      </el-dialog>
      <!--对话框结束-->

    </div>
  </div>
</template>
<script>
  export default {
    name: 'recordEmerMaterial',
    data() {
      return {
        //---------------------查询数据----------------------
        //-------------------表格数据------------------------
        memberData: [],
        //------------------对话框数据-----------------------
        groupInfo: {
          groupId: 0,
          groupName: '',
          major: '',
          leader: '',
          memberCount: 0,
          createTime: '',
          equipment: '',
          location: ''

        },
        groupMember: {
          userName: '',
          phone: '',
          type: '0',
          remark: "",
          id:0,
        },
        memberRules: {
          userName: [{required: true, message: '请填写组员姓名', trigger: 'change'}],
          phone: [{required: true, message: '请输入手机号', trigger: 'change'}],
        },
        dialog: {
          dialogVisible: false,
          deleteDialogVisible:false,
          dialogTitle: "新增队员",
          edit: false,
          index:0,
        }
      }
    },
    watch: {
      $route(to, from) {
        if ((from.name === 'emerGroup' ||from.name === 'newEmerGroup')&& this.$route.name === 'newGroupMember') {
          this.initData()
          this.loadMember()
        }
      }
    }
    ,
    mounted: function () {
      this.initData()
      this.loadMember()
    },
    methods: {
      backClick: function () {
        this.$router.push({name:'emerGroup'});
      },
      newGroupMemberDialog() {
        this.dialog.dialogVisible = true
        this.dialog.dialogTitle = "新增队员"
      },
      addOrUpdate(){
        if(this.dialog.edit){
          this.updateMember()
        }else{
          this.addGroupMember()
        }
      },
      addGroupMember() {
        this.$refs['memberForm'].validate((valid) => {
          if (valid) {
            var params = new URLSearchParams()
            params.append("userName", this.groupMember.userName)
            params.append("phone", this.groupMember.phone)
            params.append("type", this.groupMember.type)
            params.append("remark", this.groupMember.remark)
            params.append("groupId", this.groupInfo.groupId)
            this.$http.post('groupMember/addOrUpdate', params).then(function (res) {
              if (res.data.success) {
                this.$message.success("创建成功")
                this.memberData.splice(this.memberData.length,0,res.data.data)
                this.groupInfo.memberCount=this.memberData.length;
                this.dialog.dialogVisible=false
              } else {
                this.$message.error(res.data.message)
              }
            }.bind(this)).catch(function (err) {
              console.info(err)
            }.bind(this))
          } else {
            console.log('error submit!!');
            return false;
          }
        });
      },
      initData() {
        this.groupInfo.groupId = this.$route.params.groupId
        this.groupInfo.groupName = this.$route.params.groupName
        this.groupInfo.leader = this.$route.params.leader
        this.groupInfo.location = this.$route.params.location
        this.groupInfo.equipment = this.$route.params.equipment
        this.groupInfo.major = this.$route.params.major
        this.groupInfo.memberCount = this.$route.params.memberCount
        this.groupInfo.createTime = this.$route.params.createTime
      },
      dateFormat(row, column) {
        //.replace(/年|月/g, "-").replace(/日/g, " ")
        return new Date(row.createDate).Format("yyyy-MM-dd hh:mm").toLocaleString();
      },
      loadMember: function () {
        var params=new URLSearchParams()
        params.append("groupId",this.groupInfo.groupId)
        this.memberData=[];
        this.$http.post("groupMember/find",params).then(function (res) {
          if(res.data.success){
            this.memberData=res.data.data;
            this.groupInfo.memberCount=this.memberData.length
          }else{
            this.$message.error("加载成员错误")
          }
        }.bind(this))
      },
      editMemberDialog:function (row,index) {
        this.dialog.edit=true
        this.dialog.dialogTitle="编辑队员"
        this.dialog.dialogVisible=true
        this.dialog.index=index

        this.groupMember.userName=row.userName
        this.groupMember.phone=row.phone
        this.groupMember.remark=row.remark
        this.groupMember.id=row.id
        this.groupMember.type=row.type+""

      },
      updateMember:function(){
        this.$refs['memberForm'].validate((valid) => {
          if (valid) {
            var params = new URLSearchParams()
            params.append("userName", this.groupMember.userName)
            params.append("phone", this.groupMember.phone)
            params.append("type", this.groupMember.type)
            params.append("remark", this.groupMember.remark)
            params.append("groupId", this.groupInfo.groupId)
            params.append("id", this.groupMember.id)
            this.$http.post('groupMember/addOrUpdate', params).then(function (res) {
              if (res.data.success) {
                this.$message.success("更新成功")
                this.memberData.splice(this.dialog.index,1)
                this.memberData.splice(this.dialog.index,0,res.data.data)
                this.dialog.dialogVisible=false
              } else {
                this.$message.error(res.data.message)
              }
            }.bind(this)).catch(function (err) {
              console.info(err)
            }.bind(this))
          } else {
            console.log('error submit!!');
            return false;
          }
        });
      },
      deleteMemberDialog:function(row,index){
        this.groupMember.id=row.id
        this.dialog.index=index
        this.dialog.deleteDialogVisible=true
      },
      deleteMember:function () {
        var params=new URLSearchParams()
        params.append("id",this.groupMember.id)
        params.append("groupId",this.groupInfo.groupId)
        this.$http.post("groupMember/delete",params).then(function (res) {
          if(res.data.success){
            this.$message.success("删除成功")
            this.dialog.deleteDialogVisible=false
            this.memberData.splice(this.dialog.index,1)
            this.groupInfo.memberCount=this.memberData.length;
          }else{
            this.$message.error("删除失败")
          }
        }.bind(this))
      }

    }
  }
</script>
<style>
</style>
