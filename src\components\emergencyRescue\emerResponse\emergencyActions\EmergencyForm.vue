<template>
  <div id="emergencyForm">
    <div class="background-style">
      <el-col :span="16" :offset="4" class="primary-background-title" v-show="showFlag==='newEmergency'">发布应急响应</el-col>
      <el-col :span="16" :offset="4" class="success-background-title" v-show="showFlag==='viewEmergency'">查看应急响应</el-col>
      <el-col :span="16" :offset="4" class="warning-background-title" v-show="showFlag==='updateEmergency'">修改应急响应</el-col>
      <el-col :span="16" :offset="4">
        <el-form :model="form" :rules="rules" ref="ruleForm" label-width="140px" class="demo-ruleForm">
          <el-col :span="24">
            <el-col :span="12">
              <el-form-item label="分类：" prop="classify">
                <el-cascader
                  :options="cascaderOptions"
                  v-model="form.classify"
                  @change="typeClick"
                  style="width: 100%"
                  placeholder="请选择">
                </el-cascader>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="级别：" prop="level">
                <el-select v-model="form.level" placeholder="请选择" @change="levelClick" style="width: 100%">
                  <el-option
                    v-for="item in levelOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-col>
          <el-col :span="24">
            <el-form-item label="名称：" prop="emergencyName">
              <el-input v-model="form.emergencyName" placeholder="请输入应急响应名称"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-col :span="12">
              <el-form-item label="预警信号：" prop="emergencyFlag">
                <el-select v-model="form.emergencyFlag" placeholder="请选择" style="width: 100%">
                  <el-option
                    v-for="item in emergencyFlagOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.label">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="签发人：" prop="issuer">
                <el-select
                v-model="form.issuer"
                filterable
                remote
                reserve-keyword
                clearable
                placeholder="请输入姓名后选择"
                :remote-method="remotePerson"
                :loading="personLoading"
                style="width: 100%">
                <el-option
                v-for="item in personOptions"
                :key="item.value"
                :label="item.label"
                :value="item">
                </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-col>
          <el-col :span="24" v-show="showFlag==='updateEmergency'">
            <el-form-item label="审核意见：" prop="examine">
              <el-input type="textarea" :autosize="{ minRows: 3}" v-model="form.examine"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="应急启动信息：" prop="beginMessage">
              <el-input type="textarea" :autosize="{ minRows: 3}" v-model="form.beginMessage"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="预警情况：" prop="emerSituation">
              <el-input type="textarea" :autosize="{ minRows: 3}" v-model="form.emerSituation" placeholder="例：受梅雨带影响，我市昨天夜里到今天已普遍出现大到暴雨，预计今天夜里到明天大到暴雨仍将持续，请注意防范。"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24" v-show="showFlag!=='viewEmergency'">
            <el-form-item label="参考预案：" prop="referPlan">
              <el-select v-model="form.referPlan" placeholder="请选择" @change="referPlanChange" style="width: 100%">
                <el-option
                  v-for="item in planOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="应急响应要求：" prop="emerRequire">
              <el-button style="margin-bottom: 10px" size="small" type="success" @click="openAddDialog">添加知识点</el-button>
              <el-input type="textarea" :autosize="{ minRows: 3}" v-model="form.emerRequire" placeholder="选择参考预案，该项可自动填充，如没有对应预案，请自定义应急响应要求"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-col :span="12">
              <el-form-item label="通知对象：" prop="noticedPeople">
                <el-button type="primary" size="medium" style="margin-left: 20px;margin-top: 2px" @click="choosePeople">选择通知人员</el-button>
              </el-form-item>
            </el-col>
          </el-col>
          <el-col :span="24">
            <el-form-item label="公司应急值班安排：" prop="dutyTable">
              <el-table
                :data="form.dutyTable"
                border
                style="width: 100%">
                <el-table-column
                  prop="dutyDate"
                  label="值班日期"
                  align="center"
                  label-class-name="inner-header-style"
                  width="120">
                </el-table-column>
                <el-table-column
                  prop="dutyPerson"
                  label="值班人"
                  align="center"
                  label-class-name="inner-header-style"
                  width="120">
                </el-table-column>
                <el-table-column
                  prop="phoneNumber"
                  label="手机长号"
                  align="center"
                  label-class-name="inner-header-style"
                  width="130">
                </el-table-column>
                <el-table-column
                  prop="shortNumber"
                  label="手机短号"
                  align="center"
                  label-class-name="inner-header-style"
                  min-width="120">
                </el-table-column>
                <el-table-column
                  label="操作"
                  align="center"
                  label-class-name="inner-header-style"
                  fixed="right"
                  width="120">
                  <template slot-scope="scope">
                    <el-button type="danger" size="mini" @click="deleteClick(scope.$index)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
              <div style="width: 100%;height: 40px;background-color: white;padding: 5px 0 5px 0;border-bottom: 1px solid #EBEEF5;border-left: 1px solid #EBEEF5;border-right: 1px solid #EBEEF5;">
                <div style="width: 100px;margin:auto">
                  <el-button type="text" size="medium" icon="el-icon-plus" @click="addPersonClick">添加人员</el-button>
                </div>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="24" style="margin-top: 10px" v-show="showFlag==='newEmergency'">
            <el-form-item>
              <el-button style="float: right;margin-left: 20px" @click="returnClick()">返回</el-button>
              <el-button type="success" style="float: right;margin-left: 20px" @click="saveClick()">保存</el-button>
              <el-button type="primary" style="float: right" @click="submitClick()">提交</el-button>
            </el-form-item>
          </el-col>
          <el-col :span="24" style="margin-top: 10px" v-show="showFlag==='viewEmergency'">
            <el-form-item>
              <el-button style="float: right;margin-left: 20px" @click="returnClick()">返回</el-button>
            </el-form-item>
          </el-col>
          <el-col :span="24" style="margin-top: 10px" v-show="showFlag==='updateEmergency'">
            <el-form-item>
              <el-button style="float: right;margin-left: 20px" @click="returnClick()">返回</el-button>
              <el-button type="warning" style="float: right;margin-left: 20px" @click="updateClick()">修改</el-button>
              <el-button type="primary" style="float: right" @click="submitClick()">提交</el-button>
            </el-form-item>
          </el-col>
        </el-form>
      </el-col>
    </div>

    <!--新增值班人员对话框-->
    <el-dialog title="添加值班人员" :visible.sync="addPerson">
      <el-form :model="personForm" :rules="personRules" ref="personForm" label-position="right" class="demo-ruleForm">
        <el-form-item label="姓名:" label-width="120px" prop="name">
          <el-select
          v-model="personForm.name"
          allow-create
          filterable
          remote
          reserve-keyword
          clearable
          placeholder="请输入姓名后选择"
          @change="handlePersonClick"
          :remote-method="remotePersonDuty"
          :loading="personLoading"
          style="width: 100%">
          <el-option
          v-for="item in personDutyOptions"
          :key="item.value"
          :label="item.label"
          :value="item">
          </el-option>
          </el-select>
          <!--<el-input v-model="personForm.name" style="width: 400px" placeholder="请输入值班人员姓名"></el-input>-->
        </el-form-item>
        <el-form-item label="值班日期:" label-width="120px" prop="dutyDate">
          <el-date-picker
            v-model="personForm.dutyDate"
            type="date"
            placeholder="选择日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="手机长号:" label-width="120px" prop="phoneNumber">
          <el-input v-model="personForm.phoneNumber" style="width: 400px"></el-input>
        </el-form-item>
        <el-form-item label="手机短号:" label-width="120px" prop="shortNumber">
          <el-input v-model="personForm.shortNumber" style="width: 400px"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelAddPerson">取 消</el-button>
        <el-button type="primary" @click="determineAddPerson">确 定</el-button>
      </div>
    </el-dialog>
    <!--新增值班人员对话框结束-->

    <!--添加知识点对话框开始-->
    <el-dialog title="添加知识点" :visible.sync="addKnowledgePoint">
      <el-select
        v-model="searchTag"
        multiple
        filterable
        remote
        reserve-keyword
        clearable
        placeholder="请输入标签名后选择,可多选"
        @change="labelClick"
        :remote-method="remoteTag"
        :loading="tagLoading"
        style="width: 300px">
        <el-option
          v-for="item in tagOptions"
          :key="item.value"
          :label="item.label"
          :value="item.label">
        </el-option>
      </el-select>
      <el-button type="primary" style="margin-left: 20px" @click="searchKnowledgePointClick">搜索</el-button>
      <el-table
        :data="knowledgeTable"
        border
        tooltip-effect="light"
        style="width: 100%;margin-top: 10px"
        @selection-change="handleSelectionChange">
        <el-table-column
          type="selection"
          width="55"
          label-class-name="inner-header-style">
        </el-table-column>
        <el-table-column
          prop="content"
          label="知识点"
          show-overflow-tooltip
          label-class-name="inner-header-style"
          min-width="200">
        </el-table-column>
      </el-table>
      <div style="margin-top: 10px">
        <el-pagination
          background
          layout="prev, pager, next"
          :current-page="currentKnowledgePage"
          :total="totalKnowledgeItem"
          @current-change="currentKnowledgePageClick">
        </el-pagination>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelAddKnowledgePoint">取 消</el-button>
        <el-button type="primary" @click="determineAddKnowledgePoint">确 定</el-button>
      </div>
    </el-dialog>
    <!--添加知识点对话框结束-->

    <!--人员选择对话框-->
    <el-dialog title="人员选择" :visible.sync="asideFlag">
      <div style="width: 100%;height:350px;overflow: auto">
        <el-tree
          :data="peopleTree"
          show-checkbox
          default-expand-all
          node-key="id"
          ref="tree"
          highlight-current
          :props="defaultProps">
        </el-tree>
      </div>
      <div  style="width: 100%;margin-bottom: 20px">
        <el-button style="float: right;margin-left: 20px;margin-right: 20px" @click="cancelAside">返回</el-button>
        <el-button type="primary" style="float: right" @click="determineChoose">确定</el-button>
      </div>
    </el-dialog>
    <!--人员选择对话框结束-->


  </div>
</template>
<script>
  export default {
    name: 'emergencyForm',
    data() {
      return {
        //-----------------------初始化数据-------------------------------------
        cascaderOptions:[],
        planOptions:[],
        levelOptions:[
          {value:'1级', label:'1级'},
          {value:'2级', label:'2级'},
          {value:'3级', label:'3级'},
          {value:'4级', label:'4级'},
          {value:'应急警报', label:'应急警报'}
        ],
        emergencyFlagOptions:[
          {value:'黄色预警',label:'黄色预警'},
          {value:'橙色预警',label:'橙色预警'},
          {value:'红色预警',label:'红色预警'}
        ],
        //-----------------------表单数据---------------------------------------
        form:{
          classify:[],
          level:'',
          emergencyName:'',
          emergencyFlag:'',
          issuer:'',
          beginMessage:'',
          emerSituation:'',
          referPlan:'',
          emerRequire:'',
          dutyTable:[],
          examine:'',
          noticeUserIds:[],
        },
        rules:{
          classify:[{ required: true, message: '请选择分类', trigger: 'change' }],
          level:[{ required: true, message: '请选择响应级别', trigger: 'change' }],
          emergencyName:[{ required: true, message: '请输入应急响应名称', trigger: 'change' }],
          emergencyFlag:[{ required: true, message: '请选择应急预警信号', trigger: 'change' }],
          issuer:[{ required: true, message: '请输入签发人', trigger: 'change' }],
          beginMessage:[{ required: true, message: '请输入应急启动信息', trigger: 'change' }],
          emerSituation:[{ required: true, message: '请输入预警情况', trigger: 'change' }],
          emerRequire:[{ required: true, message: '请输入应急响应要求', trigger: 'change' }],
        },
        //------------------------人员选择----------------------------
        personLoading:false,
        personOptions:[],
        personDutyOptions:[],
        addPerson:false,
        personForm:{
          name:'',
          dutyDate:'',
          dutyDateTemp:'',
          phoneNumber:'',
          shortNumber:''
        },
        personRules:{
          name:[{ required: true, message: '请选择人员', trigger: 'change' }],
          dutyDate:[{ required: true, message: '请选择日期', trigger: 'change' }],
        },
        //-------------------------表单类型控制-------------------------
        showFlag:'',
        //-------------------------缓存数据-----------------------------
        tempDate:'',//当日的时间戳
        shortEmerName:'',//没有日期的应急名称
        referPlanFlag:true,//为了不想在axios里再套一个axios,在参考预案改变时，设置一个标志
        currentEmerId:'',//查看和修改当前应急的ID
        startPlanPublicId:'',//启动应急的ID
        startPlanName:'',//启动应急的名称
        currentEmerStatus:0,//当前应急的状态
        currentEmerReferPlanId:'',//当前应急的参考预案的id
        //状态对应表
        statusTable:[
          {id:0,name:'未提交',labelType:'primary'},
          {id:1,name:'待审核',labelType:'warning'},
          {id:2,name:'被退回',labelType:'danger'},

          {id:3,name:'已签发',labelType:'success'},
          {id:4,name:'调整未提交',labelType:'primary'},
          {id:5,name:'调整待审核',labelType:'warning'},
          {id:6,name:'调整被退回',labelType:'danger'},
          {id:7,name:'调整已签发',labelType:'success'},

          {id:8,name:'解除未提交',labelType:'primary'},
          {id:9,name:'解除被退回',labelType:'success'},
          {id:10,name:'解除待审核',labelType:'warning'},

          {id:11,name:'待总结',labelType:'success'},
          {id:12,name:'总结未提交',labelType:'primary'},
          {id:13,name:'总结已提交',labelType:'success'},
          {id:14,name:'已完结',labelType:'info'},
        ],
        //---------------------侧边框----------------------------
        asideFlag:false,
        defaultProps: {
          children: 'subDept',
          label: 'name'
        },
        //-----------------------对话框------------------------------
        addKnowledgePoint:false,
        intentStr:'',
        searchTag:[],
        tagLoading:false,

        knowledgeTable:[],
        totalKnowledgeItem:10,
        currentKnowledgePage:1,
        selectedArray:[],
      }
    },
    computed:{
      tagOptions:function () {
        return this.$store.state.emergencyData.referLabels;
      },
      peopleTree:function () {
        return this.$store.state.sysManageData.deptUserTree;
      },
    },
    created:function () {
      this.getPlanType();
      this.searchKnowledgePointClick();//让知识点搜索先有数据
      this.$store.dispatch("getDeptUserTree");
    },
    mounted:function () {
      let dateTemp=new Date();
      this.tempDate=dateTemp.getTime();
    },
    watch:{
      $route(to, from){
        if((from.name==='emerResponse'||from.name==='emergencyProcess')&&this.$route.name==='emergencyForm'){
          this.getPlanType();
        }
      },
      //参考预案选择后，自动填充预警信号和应急响应要求
      referPlanFlag:function () {
        this.form.emergencyFlag='';
        this.form.emerRequire='';
        if(this.form.referPlan){
          this.$http.post('emgPlan/find',{id:this.form.referPlan}).then(function (res) {
            if(res.data.data.list.length){
              this.form.emergencyFlag=res.data.data.list[0].warnSignal;
              this.form.emerRequire=res.data.data.list[0].startupRequireName+'\n';
              this.form.emerRequire+=res.data.data.list[0].startupRequire+'\n\n';
              this.form.emerRequire+=res.data.data.list[0].levelRequireName+'\n';
              this.form.emerRequire+=res.data.data.list[0].levelRequire+'\n';
            }
          }.bind(this)).catch(function (err) {
            console.log(err);
            this.$message({
              showClose: true,
              message: '网络错误，请尝试重登录',
              type: 'error'
            });
          }.bind(this));
        }
      },
    },
    methods:{
      //--------------------------初始化-------------------------------
      //获取分类
      getPlanType: function () {
        this.$http.get('emgType/getAll/'+this.$tool.getStorage('LOGIN_USER').companyId).then(function (res) {
          this.editPlanTypeArray(res.data.data);
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message({
            showClose: true,
            message: '网络错误，请尝试重登录',
            type: 'error'
          });
        }.bind(this));
      },
      //编写分类,以及一切初始化工作
      editPlanTypeArray:function (typeTree) {
        this.cascaderOptions=[];
        for(let i=0;i<typeTree.length;i++){
          let tempArray={value:i,label:typeTree[i].typeName,id:typeTree[i].id};
          if(typeTree[i].subTypes.length){
            tempArray.children=[];
            for(let j=0;j<typeTree[i].subTypes.length;j++){
              tempArray.children.push({value:j,label:typeTree[i].subTypes[j].typeName,id:typeTree[i].subTypes[j].id});
            }
          }
          this.cascaderOptions.push(tempArray);
        }

        //该部分必须在分类获取之后进行，所以放这里了，反正每次进来都要执行这里
        if(this.$route.params.requireType==='newEmergency'){
          if(this.$route.params.emergencyType[0]>=0&&this.$route.params.emergencyLevel){
            this.form.classify=this.$route.params.emergencyType;
            this.form.level=this.$route.params.emergencyLevel;
            this.showFlag=this.$route.params.requireType;
          }
          this.editEmerName();
          this.searchSimilarPlan();
        }else if(this.$route.params.requireType==='viewEmergency'){
          this.showFlag=this.$route.params.requireType;
          this.currentEmerId=this.$route.params.emergencyId;
          this.searchEmerById();
        }else if(this.$route.params.requireType==='updateEmergency'){
          this.showFlag=this.$route.params.requireType;
          this.currentEmerId=this.$route.params.emergencyId;
          this.searchEmerById();
        }

      },
      //获取参考预案并自动填写表单
      searchSimilarPlan:function () {
        this.planOptions=[];
        if(this.form.classify[0]>=0){
          let params={pageSize:10,pageCurrent:1};
          if(this.form.level){
            params.respLevel=this.form.level;
          }
          if(this.form.classify[1]>=0){
            params.typeId=this.cascaderOptions[this.form.classify[0]].children[this.form.classify[1]].id;
          }else{
            params.typeId=this.cascaderOptions[this.form.classify[0]].id;
          }
          this.planOptions.push({value:0,label:'无'});
          this.$http.post('emgPlan/find',params).then(function (res) {
            if(res.data.data.size){
              for(let i=0;i<res.data.data.list.length;i++){
                this.planOptions.push({value:res.data.data.list[i].id,label:res.data.data.list[i].name});
              }
            }
            if(this.planOptions.length>1){
              this.form.referPlan=this.planOptions[1].value;
              this.referPlanFlag=~this.referPlanFlag;
            }else{
              this.form.referPlan=this.planOptions[0].value;
              this.referPlanFlag=~this.referPlanFlag;
            }
          }.bind(this)).catch(function (err) {
            console.log(err);
            this.$message({
              showClose: true,
              message: '网络错误，请尝试重登录',
              type: 'error'
            });
          }.bind(this));
        }
      },
      //根据id搜索预案，只是显示预案名称
      showReferPlan:function () {
        this.$http.post('emgPlan/find',{id:this.currentEmerReferPlanId}).then(function (res) {
          if(res.data.data.size){
            this.planOptions.push({value:this.currentEmerReferPlanId,label:res.data.data.list[0].name});
            this.form.referPlan=this.currentEmerReferPlanId;
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message({
            showClose: true,
            message: '网络错误，请尝试重登录',
            type: 'error'
          });
        }.bind(this));
      },
      //--------------------------表单响应事件-------------------------
      referPlanChange:function () {
        this.referPlanFlag=~this.referPlanFlag;
      },
      choosePeople:function () {
        this.asideFlag=true;
        this.$nextTick(function () {
          this.$refs.tree.setCheckedKeys(this.form.noticeUserIds,true);
        });
      },
      determineChoose:function () {
        this.form.noticeUserIds=this.$refs.tree.getCheckedKeys(true);
        this.asideFlag=false;
      },
      cancelAside:function () {
        this.asideFlag=false;
      },
      //-------------------------页面为查看时，数据的填充--------------
      searchEmerById:function () {
        let params=new URLSearchParams;
        params.append("id",this.currentEmerId);
        this.$http.post('planPublic/find',params).then(function (res) {
          if(res.data.data){
            this.editEmerForm(res.data.data.list[0]);
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message({
            showClose: true,
            message: '网络错误，请尝试重登录',
            type: 'error'
          });
        }.bind(this));
      },
      editEmerForm:function (val) {
        this.findEmerClassify(val.typeId,val.topTypeId);
        this.form.level=val.respLevel;
        this.form.emergencyName=val.name;
        this.form.emergencyFlag=val.warnSignal;
        this.personOptions=[];
        this.personOptions.push({value:val.signerUserId,label:val.signerUserName});
        this.form.issuer={value:val.signerUserId,label:val.signerUserName};
        this.form.beginMessage=val.startInfo;
        this.form.emerSituation=val.warnSituation;
        this.form.emerRequire=val.startupRequire;
        this.currentEmerReferPlanId=val.planId;
        this.showReferPlan();
        this.currentEmerStatus=val.status;
        this.startPlanPublicId=val.startPlanId?val.startPlanId:this.currentEmerId;
        this.startPlanName=val.name;
        this.form.examine=val.auditOpinion;

        this.form.noticeUserIds=val.noticeUserIds.length?val.noticeUserIds:[];//通知人员选择

        this.form.dutyTable=[];
        if(val.emgDuties.length){
          for(let i=0;i<val.emgDuties.length;i++){
            this.form.dutyTable.push({dutyDate:this.transferTime(val.emgDuties[i].dutyDate),dutyDateTemp:val.emgDuties[i].dutyDate,dutyPerson:val.emgDuties[i].name,phoneNumber:val.emgDuties[i].phone,shortNumber:val.emgDuties[i].shortPhone});
          }
        }
      },
      findEmerClassify:function (type,topType) {
        let tempClass=[];
        if(topType){
          tempClass[0]=this.cascaderOptions.findIndex(function (elem,index) {
            return elem.id===topType;
          });
          tempClass[1]=this.cascaderOptions[tempClass[0]].children.findIndex(function (elem,index) {
            return elem.id===type;
          });
        }else{
          tempClass[0]=this.cascaderOptions.findIndex(function (elem,index) {
            return elem.id===type;
          });
        }
        this.form.classify=tempClass;
      },
      //-------------------------自动填充表单--------------------------
      editEmerName:function () {
        this.form.emergencyName='';
        this.shortEmerName='';
        this.form.emergencyName=this.transferTime(this.tempDate);
        if(this.form.classify[1]>=0){
          this.form.emergencyName+=this.cascaderOptions[this.form.classify[0]].children[this.form.classify[1]].label;
          this.form.emergencyName+=this.form.level;
          this.form.emergencyName+='响应';
          this.shortEmerName+=this.cascaderOptions[this.form.classify[0]].children[this.form.classify[1]].label;
          this.shortEmerName+=this.form.level;
          this.shortEmerName+='响应';
        }else if(this.form.classify[0]>=0){
          this.form.emergencyName+=this.cascaderOptions[this.form.classify[0]].label;
          this.form.emergencyName+=this.form.level;
          this.form.emergencyName+='响应';
          this.shortEmerName+=this.cascaderOptions[this.form.classify[0]].label;
          this.shortEmerName+=this.form.level;
          this.shortEmerName+='响应';
        }
        this.editBeginMessage();
      },
      editBeginMessage:function () {
        this.form.beginMessage='';
        this.form.beginMessage='公司各部门、主要控股企业：\n';
        this.form.beginMessage+='    '+this.transferTime(this.tempDate,'y年m月d日')+'，启动'+this.shortEmerName+'，';
        this.form.beginMessage+='要求各主要控股企业按预案要求启动相应应急响应，全力做好防御工作。';
      },
      //-------------------------类型变化函数---------------------------
      typeClick:function () {
        this.editEmerName();
        this.searchSimilarPlan();
      },
      levelClick:function (val) {
        this.editEmerName();
        this.searchSimilarPlan();
      },
      //--------------------------远程人员查询-------------------------------
      remotePerson:function (val) {
        this.personLoading = true;
        this.$http.get('user/find?username='+val).then(function (res) {
          if(res.data.success){
            this.personOptions=[];
            for (let i = 0; i < res.data.data.list.length; i++) {
              this.personOptions.push({value:res.data.data.list[i].userId,label:res.data.data.list[i].username});
            }
            this.personLoading = false;
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
        });
      },
      editPerson:function (val) {
        let temp;
        this.personDutyOptions=[];
        for (let i = 0; i < val.length; i++) {
          temp = new Object;
          temp.value = val[i].userId;
          temp.label = val[i].username;
          temp.phone = val[i].mobile;
          temp.shortPhone=val[i].shortPhone;
          this.personDutyOptions.push(temp);
        }
      },
      remotePersonDuty:function (val) {
        this.personLoading = true;
        this.$http.get('user/find?username='+val).then(function (res) {
          if(res.data.success){
            this.editPerson(res.data.data.list);
            this.personLoading = false;
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
        });
      },
      handlePersonClick:function (val) {
        if(typeof (val)==='string'){
          this.$message({
            showClose: true,
            message: '此人员的手机信息需自行填写！',
            type: 'warning'
          });
        }else{
          this.personForm.phoneNumber=val.phone;
          this.personForm.shortNumber=val.shortPhone;
        }
      },
      //---------------------------人员表格事件----------------------------
      deleteClick:function (index) {
        this.form.dutyTable.splice(index,1);
      },
      addPersonClick:function () {
        this.addPerson=true;
      },
      determineAddPerson:function () {
        this.$refs['personForm'].validate((valid) => {
          if (valid) {
            if(typeof (this.personForm.name)==='string'){
              this.form.dutyTable.push({
                dutyDate:this.transferTime(this.personForm.dutyDate.getTime()),
                dutyDateTemp:this.personForm.dutyDate,
                dutyPerson:this.personForm.name,
                dutyPersonId:null,
                phoneNumber:this.personForm.phoneNumber,
                shortNumber:this.personForm.shortNumber
              });
            }else{
              this.form.dutyTable.push({
                dutyDate:this.transferTime(this.personForm.dutyDate.getTime()),
                dutyDateTemp:this.personForm.dutyDate,
                dutyPerson:this.personForm.name.label,
                dutyPersonId:this.personForm.name.value,
                phoneNumber:this.personForm.phoneNumber,
                shortNumber:this.personForm.shortNumber
              });
            }

            this.$refs['personForm'].resetFields();
            this.addPerson = false;
          } else {
            console.log('error submit!!');
            return false;
          }
        });
      },
      cancelAddPerson:function () {
        this.$refs['personForm'].resetFields();
        this.addPerson = false;
      },
      //---------------------------提交和返回-----------------------------
      //新建
      saveClick:function () {
        if(this.form.referPlan>0){
          this.$refs['ruleForm'].validate((valid) => {
            if (valid) {
              let params=new URLSearchParams;
              params.append("status",0);
              params.append("history",0);
              params.append("startPlanPublicName",this.form.emergencyName);
              this.sendRequire(params,'planPublic/add','应急保存成功');
            } else {
              console.log('error submit!!');
              return false;
            }
          });
        }else{
          this.$message({
            showClose: true,
            message: '请选择应急预案，若无应急预案，则无法创建应急响应',
            type: 'warning'
          });
        }

      },
      //修改
      updateClick:function () {
        if(this.form.referPlan>0) {
          this.$refs['ruleForm'].validate((valid) => {
            if (valid) {
              let params=new URLSearchParams;
              if(this.currentEmerStatus<=2){
                params.append("id",this.currentEmerId);
                params.append("startPlanId",this.startPlanPublicId);
                params.append("status",0);
                params.append("history",0);
                params.append("startPlanPublicName",this.form.emergencyName);
                this.sendRequire(params,'planPublic/update','应急修改成功');
              }else if(this.currentEmerStatus>=4&&this.currentEmerStatus<=6){//已经被调整但未发布了，再次调整则不需要新建
                params.append("id",this.currentEmerId);
                params.append("status",4);
                params.append("startPlanId",this.startPlanPublicId);
                params.append("startPlanPublicName",this.startPlanName);
                params.append("history",0);
                this.sendRequire(params,'planPublic/update','调整应急保存成功');//保存调整应急，不新建一个应急
              }else{
                params.append("status",4);
                params.append("startPlanId",this.startPlanPublicId);
                params.append("startPlanPublicName",this.startPlanName);
                params.append("history",0);
                this.sendRequire(params,'planPublic/add','调整应急保存成功');//调整应急，新建一个应急
                this.updatePlanPublic();//将原来的应急变为历史状态
              }

            } else {
              console.log('error submit!!');
              return false;
            }
          });
        }else{
          this.$message({
            showClose: true,
            message: '请选择应急预案，若无应急预案，则无法创建应急响应',
            type: 'warning'
          });
        }
      },
      //提交
      submitClick:function () {
        if(this.form.referPlan>0){
          this.$refs['ruleForm'].validate((valid) => {
            if (valid) {
              let params=new URLSearchParams;
              if(this.showFlag==='newEmergency'){
                params.append("status",1);
                params.append("history",0);
                this.sendRequire(params,'planPublic/add','提交成功');
              }else if(this.showFlag==='updateEmergency'){
                if(this.currentEmerStatus<=2){
                  params.append("id",this.currentEmerId);
                  params.append("startPlanId",this.startPlanPublicId);
                  params.append("status",1);
                  params.append("history",0);
                  this.sendRequire(params,'planPublic/update','提交成功');
                }else if(this.currentEmerStatus>=4&&this.currentEmerStatus<=6){//调整未发布的，提交就不需要新建
                  params.append("id",this.currentEmerId);
                  params.append("status",5);
                  params.append("startPlanId",this.startPlanPublicId);
                  params.append("startPlanPublicName",this.startPlanName);
                  params.append("history",0);
                  this.sendRequire(params,'planPublic/update','调整应急提交成功');
                } else{
                  params.append("status",5);
                  params.append("startPlanId",this.startPlanPublicId);
                  params.append("startPlanPublicName",this.startPlanName);
                  params.append("history",0);
                  this.sendRequire(params,'planPublic/add','调整应急提交成功');
                  this.updatePlanPublic();
                }
              }
            } else {
              console.log('error submit!!');
              return false;
            }
          });
        }else{
          this.$message({
            showClose: true,
            message: '请选择应急预案，若无应急预案，则无法创建应急响应',
            type: 'warning'
          });
        }
      },
      //返回
      returnClick:function () {
        this.cascaderOptions=[];
        this.planOptions=[];
        this.personOptions=[];
        this.$refs['ruleForm'].resetFields();
        this.$router.go(-1);
      },
      //----------------------------填写表单-------------------------
      sendRequire:function (params,urlStr,successStr) {
        if(this.form.classify[1]>=0){
          params.append("typeId",this.cascaderOptions[this.form.classify[0]].children[this.form.classify[1]].id);
          params.append("topTypeId",this.cascaderOptions[this.form.classify[0]].id);
        }else{
          params.append("typeId",this.cascaderOptions[this.form.classify[0]].id);
          params.append("topTypeId",0);
        }
        params.append("respLevel",this.form.level);
        params.append("name",this.form.emergencyName);
        params.append("warnSignal",this.form.emergencyFlag);
        params.append("signerUserId",this.form.issuer.value);
        params.append("signerUserName",this.form.issuer.label);
        params.append("startInfo",this.form.beginMessage);
        params.append("warnSituation",this.form.emerSituation);
        params.append("noticeUserIds",this.form.noticeUserIds);

        if(this.form.referPlan){
          params.append("planId",this.form.referPlan);
        }else{
          params.append("planId",this.currentEmerReferPlanId);
        }
        params.append("startupRequire",this.form.emerRequire);

        params.append("deptId",this.$tool.getStorage('LOGIN_USER').deptId);
        params.append("companyId",this.$tool.getStorage('LOGIN_USER').companyId);

        for(let i=0;i<this.form.dutyTable.length;i++){
          if(this.form.dutyTable[i].dutyPersonId){
            params.append("emgDuties["+i+"].userId",this.form.dutyTable[i].dutyPersonId);
            params.append("emgDuties["+i+"].dutyDate",Date(this.form.dutyTable[i].dutyDateTemp));
            params.append("emgDuties["+i+"].userName",this.form.dutyTable[i].dutyPerson);
            params.append("emgDuties["+i+"].phone",this.form.dutyTable[i].phoneNumber);
            params.append("emgDuties["+i+"].shortPhone",this.form.dutyTable[i].shortNumber);
          }else{
            params.append("emgDuties["+i+"].dutyDate",Date(this.form.dutyTable[i].dutyDateTemp));
            params.append("emgDuties["+i+"].name",this.form.dutyTable[i].dutyPerson);
            params.append("emgDuties["+i+"].phone",this.form.dutyTable[i].phoneNumber);
            params.append("emgDuties["+i+"].shortPhone",this.form.dutyTable[i].shortNumber);
          }
        }

        this.$http.post(urlStr,params).then(function (res) {
          if(res.data.success){
            this.$message({
              showClose: true,
              message: successStr,
              type: 'success'
            });
            this.cascaderOptions=[];
            this.planOptions=[];
            this.personOptions=[];
            this.$refs['ruleForm'].resetFields();
            this.$router.push({name:'emerResponse'});
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message({
            showClose: true,
            message: '网络错误，请尝试重登录',
            type: 'error'
          });
        }.bind(this));
      },
      updatePlanPublic:function () {
        let oldParams=new URLSearchParams;
        oldParams.append("id",this.currentEmerId);
        oldParams.append("startPlanId",this.startPlanPublicId);
        oldParams.append("planId",this.currentEmerReferPlanId);
        oldParams.append("history",1);

        this.$http.post('planPublic/update',oldParams).then(function (res) {
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message({
            showClose: true,
            message: '网络错误，请尝试重登录',
            type: 'error'
          });
        }.bind(this));
      },
      //----------------------------通过标签查询知识点------------------------
      openAddDialog:function (str) {
        this.addKnowledgePoint=true;
        this.intentStr=str;
      },
      labelClick:function (val) {
        if(this.searchTag.length){
          this.$store.dispatch("getLabels",this.searchTag);
        }else{
          this.$store.dispatch("getLabels",[]);
        }
      },
      remoteTag:function (val) {
        let params = new URLSearchParams;
        if (val !== null&&val!=='') {
          this.tagLoading = true;
          params.append("label", val);
          params.append("pageSize", 10);
          params.append("pageCurrent", 1);
          this.$http.post('label/find', params).then(function (res) {
            if (res.data.data.list.length !== 0) {
              this.editTag(res.data.data.list);
            }
            this.tagLoading = false;
          }.bind(this)).catch(function (err) {
            console.log(err);
          });
        }else {
          if(this.searchTag.length===0){
            this.$store.dispatch("getLabels",[]);
          }
        }

      },
      editTag:function (list) {
        this.tagOptions.splice(0);
        for(let i=0;i<list.length;i++){
          let temp={value:list[i].id,label:list[i].label};
          this.tagOptions.push(temp);
        }
      },
      searchKnowledgePointClick:function () {
        let params=new URLSearchParams;
        params.append("pageCurrent",1);
        this.currentKnowledgePage=1;
        this.sendKnowSearchRequest(params);
      },
      currentKnowledgePageClick:function (val) {
        if(val){
          let params=new URLSearchParams;
          this.currentKnowledgePage=Number(val);
          params.append("pageCurrent",Number(val));
          this.sendKnowSearchRequest(params);
        }
      },
      sendKnowSearchRequest:function (params) {
        if(this.searchTag.length){
          for(let i=0;i<this.searchTag.length;i++){
            params.append("labels["+i+"]",this.searchTag[i]);
          }
        }
        this.$http.post('knowledge/find', params).then(function (res) {
          if (res.data.data.list.length !== 0) {
            this.totalKnowledgeItem=res.data.data.total;
            this.knowledgeTable=res.data.data.list;
          }else{
            this.totalKnowledgeItem=0;
            this.knowledgeTable=[];
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
        });
      },
      //多选
      handleSelectionChange:function (val) {
        if(val.length){
          this.selectedArray=val;
        }
      },
      cancelAddKnowledgePoint:function () {
        this.addKnowledgePoint=false;
      },
      determineAddKnowledgePoint:function () {
        if(this.selectedArray.length){
          let tempContent='';
          for(let i=0;i<this.selectedArray.length;i++){
            tempContent+='>>>. '+this.selectedArray[i].content+'\n';
          }
          if(this.form.emerRequire.charAt(this.form.emerRequire.length-1)==='\n'||!this.form.emerRequire.length){
            this.form.emerRequire+=tempContent;
          }else {
            this.form.emerRequire+='\n'+tempContent;
          }
        }
        this.addKnowledgePoint=false;
      },

    }
  }
</script>
<style>
</style>
