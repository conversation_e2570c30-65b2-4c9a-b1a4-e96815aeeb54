<template>
  <div id="dangerPlanFormAccount">
    <div class="background-style">
      <el-col :span="22" :offset="1" class="success-background-title">{{planTitle}}</el-col>
      <el-col :span="16" :offset="4">
        <el-form :model="form"  label-width="120px">
          <el-col :span="24">
            <el-col :span="12">
              <el-form-item label="文件编号：">
                {{form.docNum}}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="记录编号：">
                {{form.recordNum}}
              </el-form-item>
            </el-col>
          </el-col>
        </el-form>
      </el-col>
      <el-col :span="22" :offset="1" style="margin-top: 10px">
        <table class="simple-table">
          <tr><td v-for="itemHead in tableHead">{{itemHead.title}}</td></tr>
          <tr v-for="row in tableContent"><td v-for="item in tableHead">{{row[item.index]}}</td></tr>
        </table>
        <el-button style="float: right;margin: 20px" @click="returnClick">返回</el-button>
        <el-button type="primary" style="float: right;margin: 20px" @click="download">下载</el-button>
        <el-button type="success" style="float: right;margin: 20px" @click="print">预览</el-button>
      </el-col>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'dangerPlanFormAccount',
    data() {
      return {
        planTitle:'',
        tableHead:[
          {index:'num',title:'序号'},
          {index:'inspectType',title:'检查类型'},
          {index:'inspectDeptName',title:'检查单位'},
          {index:'content',title:'检查内容'},
          {index:'inspectMemeber',title:'检查人员'},
          {index:'inspectDate',title:'检查日期'},
          {index:'remark',title:'备注'},
        ],
        dangerSafePlanId:0,
        tableContent:[],
        form:{
          docNum:'',
          recordNum:''
        },
        year:0,
        timer : null,
      }
    },
    created:function(){
      this.year=parseInt(this.$tool.formatDateTime(this.$route.params.year).substring(0,4));
      this.planTitle=this.year+"年安全检查计划";
      this.searchTable();
    },
    watch:{
      $route(to, from){
        if(from.name==='accountDangerIndex') {
          this.year=parseInt(this.$tool.formatDateTime(this.$route.params.year).substring(0,4));
          this.planTitle=this.year+"年安全检查计划";
          this.searchTable();
        }
      }
    },
    methods:{
      searchTable:function () {
        var params=new URLSearchParams();
        params.append("year",this.year)
        this.$http.post('danger/safePlan/detail',params).then(function (res) {
          if(res.data.success){
            this.tableContent=res.data.data.dangerSafePlanLists;
            this.dangerSafePlanId=res.data.data.id
            this.form = res.data.data;
            this.tableContent.forEach(function (item,index) {
              item.num=index+1;
            })
          }else{
            console.log('danger/safePlanList/detail'+'数据申请失败');
          }
        }.bind(this)).catch(function (err) {
          console.log('检查计划列表查找:'+err);
        }.bind(this));
      },
      download:function () {
        const loading = this.$loading({
          lock: true,
          text: 'Loading',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        this.$http({ // 用axios发送post请求
          method: 'get',
          url: '/report/dangerSafePlan/' + this.dangerSafePlanId, // 请求地址
          responseType: 'blob' // 表明返回服务器返回的数据类型
        }).then((res) => { // 处理返回的文件流
          //console.info(res)
          loading.close()
          const content = res
          const elink = document.createElement('a') // 创建a标签
          elink.download = this.planTitle + ".xlsx" // 文件名
          elink.style.display = 'none'
          const blob = new Blob([res.data])
          elink.href = URL.createObjectURL(blob)
          document.body.appendChild(elink)
          elink.click() // 触发点击a标签事件
          document.body.removeChild(elink)
        })
      },
      print(){
        let setting = {
          url : `report/getDangerSafePlanHtml/${this.dangerSafePlanId}`,
        };

        this.loginLoading=this.$loading({
          lock: true,
          text: '数据加载中',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.5)'
        });
        this.$store.dispatch('printOnLine', {
          url : setting.url
        }).then(function(res){
          this.loginLoading.close();//关闭登陆加载
          if(res.success){
            let myWindow=window.open("".href, "_blank");
            myWindow.document.write(res.data);
            window.clearTimeout(this.timer);
            this.timer = window.setTimeout(function(){
              myWindow.print();
            }, 300)
//                    myWindow.print()
          } else {
            this.$message({
              type : 'error',
              message : res.message || '预览失败！！'
            })
          }
        }.bind(this))
      },
      returnClick:function () {
        this.planTitle='';
        this.tableContent=[];
        this.$router.go(-1);
      },
    }
  }
</script>
<style>
</style>
