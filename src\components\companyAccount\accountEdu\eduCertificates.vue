<template>
  <div class="background-style" style="padding: 10px">
    <el-row style="margin:0">
      <el-col :span="6">
        <el-button type="primary" size="mini" @click="$router.back()">返回</el-button>
      </el-col>
    </el-row>
    <el-row style="margin:10px 0 0 0">
      <el-col :span="24">
        <egrid class="egrid"
               stripe border
               maxHeight="500"
               :data="egrid.data"
               :columns="egrid.columns"
               :columns-schema="egrid.columnsSchema">
        </egrid>
      </el-col>
    </el-row>
  </div>
</template>

<script>


  export default {
    data(){
      return {
        // 表格
        egrid : {
          data : [],
          columns : [
            { label: '姓名', prop: 'username' },
            { label: '职务(岗位)', prop: 'duty' },
            { label: '性别', prop: 'gender' },
            { label: '证件名称', prop: 'certificateName' },
            { label: '证书编号', prop: 'certificateId' },
            { label: '取证时间', prop: 'getDate' },
            { label: '复训日期', prop: 'eduCertificateRetrains' },
            { label: '有效期限', prop: 'expiryDate' },
            { label: '发证单位', prop: 'issuingDepartment' },
            { label: '备注', prop: 'remark' },
          ],
          // columnsProps 用于定义所有 columns 公共的属性
          columnsProps: {
            fit : true,
            sortable: true,
            align : 'center',
          },
          columnsSchema : {
            '姓名' : {
              width : 80
            },
            '性别' : {
              width : 50
            },
            '取证时间' : {
              width : 120
            },
            '复训日期' : {
              width : 120
            },
            '有效期限' : {
              width : 120
            },
            '发证单位' : {
              width : 150,
              showOverflowTooltip : true
            },
          },
        }
      }
    },
    created(){
      this.init();
    },
    watch:{
      $route(to,from){
        let data = to.params && to.params.row && to.params.row.data;
        if(to.name === 'eduCertificates') {
          if(data){
            this.searchBtnClickHandle();
          }
        }
      }
    },
    methods:{
      // 初始化
      init(){
        let data = this.$route.params.row.data;
        if(data){
          // 搜索
          this.searchBtnClickHandle();
        }
      },
      // 搜索按钮
      searchBtnClickHandle(){
        let data = this.$route.params.row.data;
        let list = data.map(function(it){
          return {
            username : it.eduUser.username || '',
            duty : it.eduUser.duty || '',
            gender : it.eduUser.gender ? '男' : '女',
            certificateName : it.certificateName || '',
            certificateId : it.certificateId || '',
            getDate : this.$tool.formatDateTime(it.getDate).substring(0, 10) || '',
            eduCertificateRetrains : this.$tool.formatDateTime(it.eduCertificateRetrains[0].retrainRecord).substring(0, 10) || '',
            expiryDate : this.$tool.formatDateTime(it.expiryDate).substring(0, 10) || '',
            issuingDepartment : it.issuingDepartment || '',
            remark : it.remark || '',
          }
        }.bind(this));
        this.egrid.data = list;
      },
    }
  }
</script>

<style>

</style>
