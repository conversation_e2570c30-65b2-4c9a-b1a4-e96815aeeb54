<template>
  <div id="emerResponse">
    <div class="background-style">

      <!--应急列表区-->
      <div style="width: 100%;">
        <div style="margin: 10px 0 0 20px">
          <el-radio-group v-model="radioResponseType" size="medium" @change="responseTypeChange">
            <el-radio-button  v-for="item in radioResponseButtons" :id="item.value" :label="item.value" :key="item.value">{{item.name}}</el-radio-button>
          </el-radio-group>
        </div>
        <div style="float: left;margin: 10px">
          <el-cascader
            :options="cascaderOptions"
            v-model="emerClass"
            placeholder="分类选择">
          </el-cascader>
          <el-select v-model="emerLevel" placeholder="级别选择" style="width: 120px">
            <el-option
              v-for="item in levelOptions"
              :key="item.value"
              :label="item.label"
              :value="item.label">
            </el-option>
          </el-select>
          <el-select v-model="emerStatus" placeholder="状态选择" style="width: 120px">
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
          <el-input placeholder="请输入预案名称" v-model="searchInput" style="width: 300px;margin-right: 10px">
          </el-input>
          <el-button icon="el-icon-search" @click="searchClick" type="primary">搜 索</el-button>
        </div>
        <div style="width: 100%;float: left">
          <div style="padding: 0 10px 0 10px">
            <el-table
              v-loading="tableLoading"
              :data="tableData"
              border
              highlight-current-row
              @row-dblclick="itemViewClick"
              style="width: 100%">
              <el-table-column
                prop="num"
                label="编号"
                width="60"
                align="center"
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                label="状态"
                width="150"
                align="center"
                label-class-name="header-style">
                <template slot-scope="scope">
                  <el-tag :type="scope.row.tagType" size="small">{{scope.row.emerStatus}}</el-tag>
                </template>
              </el-table-column>
              <el-table-column
                prop="emerName"
                label="应急响应名称"
                width="400"
                show-overflow-tooltip
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="companyName"
                label="发布公司"
                width="250"
                show-overflow-tooltip
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="createDate"
                label="创建日期"
                width="150"
                align="center"
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="emerTopClass"
                label="一级分类"
                width="170"
                align="center"
                show-overflow-tooltip
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="emerClass"
                label="二级分类"
                width="170"
                align="center"
                show-overflow-tooltip
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="emerLevel"
                label="级别"
                min-width="80"
                align="center"
                show-overflow-tooltip
                label-class-name="header-style">
              </el-table-column>
              <el-table-column fixed="right" label="操作" label-class-name="header-style" width="170">
                <template slot-scope="scope">
                  <!--<div v-if="scope.row.operateFlag===1">-->
                    <!--<el-button size="mini" type="success" @click="itemViewClick(scope.row)">查看</el-button>-->
                    <!--<el-button size="mini" type="danger" @click="itemDeleteClick(scope.row)">删除</el-button>-->
                  <!--</div>-->
                  <!--<div v-else>-->
                    <!--<el-button size="mini" type="success" @click="itemViewClick(scope.row)">查看</el-button>-->
                  <!--</div>-->
                  <div v-if="radioResponseType==='createrLook'">
                    <el-button size="mini" type="success" @click="itemViewClick(scope.row)">查看</el-button>
                    <el-button size="mini" type="danger" @click="itemDeleteClick(scope.row)">删除</el-button>
                  </div>
                  <div v-else>
                    <el-button size="mini" type="success" @click="itemViewClick(scope.row)">查看</el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div style="margin-top: 10px">
            <el-pagination
              background
              layout="prev, pager, next"
              :page-size="pageSize"
              :current-page="currentPage"
              :total="totalItem"
              @current-change="currentPageClick">
            </el-pagination>
          </div>
        </div>
      </div>
      <!--应急列表区结束-->

      <!--新增应急预警对话框-->
      <el-dialog title="应急类型选择" :visible.sync="addEmergencyVisible">
        <el-form :model="emerForm" label-position="right">
          <el-form-item label="应急类型:" label-width="120px">
            <el-cascader
              :options="chooseTypeOptions"
              v-model="emerForm.chooseType"
              placeholder="分类选择">
            </el-cascader>
          </el-form-item>
          <el-form-item label="级别:" label-width="120px">
            <el-select v-model="emerForm.chooseLevel" placeholder="级别选择">
              <el-option
                v-for="item in chooseLevelOptions"
                :key="item.value"
                :label="item.label"
                :value="item.label">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="addEmergencyVisible = false">取 消</el-button>
          <el-button type="primary" @click="determineAddEmergency">确 定</el-button>
        </div>
      </el-dialog>
      <!--新增应急预警对话框结束-->

    </div>
  </div>
</template>
<script>
  import {mapGetters} from 'vuex'
  export default {
    name: 'emerResponse',
    data() {
      return {
        //角色类型选择
        radioResponseButtons:[{value:'createrLook',name:'我创建的应急'},{value:'editorLook',name:'我记录的应急'}],
        radioResponseType:'createrLook',
        //------------------------搜索数据----------------------------
        emerClass:['全部分类'],
        cascaderOptions:[],
        emerLevel:'全部级别',
        levelOptions:[
          {value:'全部级别', label:'全部级别'},
          {value:'1级', label:'1级'},
          {value:'2级', label:'2级'},
          {value:'3级', label:'3级'},
          {value:'4级', label:'4级'},
          {value:'应急警报', label:'应急警报'}
        ],
        emerStatus:'全部状态',
        searchInput:'',
        //------------------------表格数据---------------------------
        tableLoading:false,
        tableData:[],
        currentPage:1,
        totalItem:1,
        pageSize:10,
        //----------------------对话框数据--------------------------------
        addEmergencyVisible:false,
        emerForm:{
          chooseType:[],
          chooseLevel:'1级'
        },
        chooseTypeOptions:[],
        chooseLevelOptions:[
          {value:'1级', label:'1级'},
          {value:'2级', label:'2级'},
          {value:'3级', label:'3级'},
          {value:'4级', label:'4级'},
          {value:'应急警报', label:'应急警报'}
        ],
        //----------------------------解除应急的对话框-----------------------
        relieveVisible:false,
        relieveForm:{
          id:'',
          planId:'',
          startId:'',
          relieveTime:'',
          relieveContent: '',
          relieveIssuer:''
        },
        relieveContentDefault:['公司各部门、主要控股企业：\n' + '      鉴于____(灾害名称)对宁波的影响逐渐减弱，经研究决定，于',
          '解除_____(类别)_____(级别)应急响应，同时要求各单位：\n' +
          '     1、抓紧做好恢复生产工作，确保正常生产开展。\n' +
          '     2、做好受损、受灾统计、汇总上报工作。\n' +
          '     3、对本次防御工作进行全面梳理，发现不足，总结经验，持续完善。'],
        personLoading:false,
        personOptions:[],
        relieveRules:{
          relieveTime: [{required: true, message: '请选择上报时间', trigger: 'change'}],
          relieveContent: [{required: true, message: '请填写上报内容', trigger: 'change'}],
          relieveIssuer: [{required: true, message: '请选择签发人', trigger: 'change'}],
        },

      }
    },
    computed:{
      statusOptions:function () {
        return this.$store.state.emergencyData.emergencyStatusArray;
      },
      statusTable:function () {
        return this.$store.state.emergencyData.statusTable;
      },
    },
    mounted:function () {
      this.$store.dispatch('getEmergencyStatusArray');
      this.getPlanType();
      this.searchClick();
    },
    watch:{
      $route(to, from){
        if((from.name==='emerMenu')&&this.$route.name==='emerResponse'){
          this.searchClick();
        }
      },
    },
    methods: {
      //--------------------------初始化-------------------------------
      //获取分类
      getPlanType: function () {
        this.$http.get('emgType/getAll/'+this.$tool.getStorage('LOGIN_USER').companyId).then(function (res) {
          this.editPlanTypeArray(res.data.data);
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message({
            showClose: true,
            message: '网络错误，请尝试重登录',
            type: 'error'
          });
        }.bind(this));
      },
      //编写分类
      editPlanTypeArray:function (typeTree) {
        this.cascaderOptions=[];
        this.chooseTypeOptions=[];
        this.cascaderOptions.push({value:'全部分类',label:'全部分类'});

        for(let i=0;i<typeTree.length;i++){
          let tempArray={value:i,label:typeTree[i].typeName,id:typeTree[i].id};
          if(typeTree[i].subTypes.length){
            tempArray.children=[];
            for(let j=0;j<typeTree[i].subTypes.length;j++){
              tempArray.children.push({value:j,label:typeTree[i].subTypes[j].typeName,id:typeTree[i].subTypes[j].id});
            }
          }
          this.cascaderOptions.push(tempArray);
          this.chooseTypeOptions.push(tempArray);
        }
      },
      //--------------------------新增区域响应事件----------------------
      addEmergency:function () {
        this.addEmergencyVisible=true;
      },

      //--------------------------搜索响应事件--------------------------
      searchClick:function () {
        let params=new URLSearchParams;
        this.currentPage=1;
        params.append("pageCurrent",1);
        this.sendRequest(params);
      },
      currentPageClick:function (val) {
        if(val){
          this.currentPage=val;
          let params=new URLSearchParams;
          params.append("pageCurrent",Number(val));
          this.sendRequest(params);
        }
      },
      //--------------------------表格响应事件--------------------------
      itemViewClick:function (row) {
        if(row.status<=2){//未签发
          this.$router.push({name:'viewEmergency',params:{emergencyId:row.id}});
        }else{//签发之后
          this.$router.push({name:'emergencyProcess',params:{emergencyId:row.id,leaderViewFlag:true}});
        }
      },
      itemDeleteClick:function (row) {
        this.$confirm('此操作将永久删除该响应, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let params=new URLSearchParams;
          params.append("id",Number(row.id));
          this.$http.post('planPublic/delete',params).then(function (res) {
            this.$message({
              showClose: true,
              message: '删除成功',
              type: 'success'
            });
            this.searchClick();
          }.bind(this)).catch(function (err) {
            console.log(err);
            this.$message({
              showClose: true,
              message: '网络错误，请尝试重登录',
              type: 'error'
            });
          }.bind(this));
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });
        });
      },
      //--------------------------对话框事件----------------------------
      //确定添加
      determineAddEmergency:function () {
        if(this.emerForm.chooseType[0]>=0){
          this.addEmergencyVisible = false;
          this.$router.push({name:'emergencyForm',params:{requireType:'newEmergency',emergencyType:this.emerForm.chooseType,emergencyLevel:this.emerForm.chooseLevel}});
        }else{
          this.$message({
            showClose: true,
            message: '请选择应急类型！',
            type: 'warning'
          });
        }
      },

      //--------------------------交互事件------------------------------
      responseTypeChange:function (val) {
        this.searchClick();
      },
      sendRequest:function (params) {
        if(this.emerClass[0]!=='全部分类'){
          if(this.emerClass[1]>=0){
            params.append("typeId",this.chooseTypeOptions[this.emerClass[0]].children[this.emerClass[1]].id);
          }else{
            params.append("typeId",this.chooseTypeOptions[this.emerClass[0]].id);
          }
        }
        if(this.emerLevel&&this.emerLevel!=='全部级别'){
          params.append("respLevel",this.emerLevel);
        }
        if(this.emerStatus&&this.emerStatus!=='全部状态'){
          params.append("status",this.emerStatus);
        }
        if(this.searchInput.trim()){
          params.append("name",this.searchInput.trim());
        }
        params.append("history",0);
        params.append("pageSize",this.pageSize);

        //不同角色的应急列表
        if(this.radioResponseType==='createrLook'){
          params.append("createUserId",this.$tool.getStorage('LOGIN_USER').userId);
        }else if(this.radioResponseType==='editorLook'){
          params.append("companyId",this.$tool.getStorage('LOGIN_USER').parentCompanyId);
        }else{
          this.$message.warning('没有角色分类，将显示全部应急');
        }
        this.tableLoading=true;
        this.$http.post('planPublic/find',params).then(function (res) {
          if(res.data.success){
            if(res.data.data.list.length){
              this.totalItem=res.data.data.total;
              this.tableLoading=false;
              this.editTable(res.data.data.list);
            }else {
              this.totalItem=0;
              this.tableData.splice(0);
              this.tableLoading=false;
            }
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message({
            showClose: true,
            message: '网络错误，请尝试重登录',
            type: 'error'
          });
        }.bind(this));
      },
      editTable:function (list) {
        this.tableData=[];
        for(let i=0;i<list.length;i++){
          let tempObj={
            id:list[i].id,
            num:(this.currentPage-1)*10+i+1,
            status:list[i].status,
            emerStatus:this.statusTable[Number(list[i].status)].name,
            tagType:this.statusTable[Number(list[i].status)].labelType,
            emerName:list[i].name,
            createDate:this.transferTime(list[i].createDate),
            emerTopClass:list[i].topTypeName,
            emerClass:list[i].typeName,
            emerLevel:list[i].respLevel,
            planId:list[i].planId,
            startId:list[i].startPlanId,
            companyId:list[i].companyId,
            companyName:list[i].companyName
          };
          if(this.radioResponseType==='createrLook'){
            if(Number(tempObj.status)===1||Number(tempObj.status)===14){
              tempObj.operateFlag=1;
            }else{
              tempObj.operateFlag=2;
            }
          }else{
            tempObj.operateFlag=2;
          }
          this.tableData.push(tempObj);
        }
        this.tableLoading=false;

      },
    }
  }
</script>
<style>
</style>
