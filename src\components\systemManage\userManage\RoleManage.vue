<template>
  <div id="roleManage" class="background-style">

    <div style="width: 100%;padding: 10px">
      <el-input placeholder="请输入内容" v-model="input" class="input-with-select" style="width: 400px">
        <el-select v-model="classValue"  slot="prepend" placeholder="请选择" style="width: 120px">
          <el-option
            v-for="item in roleOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-input>
      <el-button v-if="powerBtns.includes('searchRoleBtn')" type="primary" @click="searchClick" icon="el-icon-search">搜 索</el-button>
      <el-button  v-if="powerBtns.includes('addRoleBtn')" type="success" @click="newClick">新增角色</el-button>
    </div>
    <div style="width: 100%;padding: 10px">
      <el-table
        ref="singleTable"
        :data="tableData"
        highlight-current-row
        style="width: 100%;"
        stripe
        border>
        <el-table-column
          prop="id"
          label="角色ID"
          width="200"
          align="center"
          label-class-name="header-style">
        </el-table-column>
        <el-table-column
          prop="role"
          label="角色名称"
          width="200"
          align="center"
          show-overflow-tooltip
          label-class-name="header-style">
        </el-table-column>
        <el-table-column
          prop="description"
          label="备注"
          min-width="300"
          align="center"
          show-overflow-tooltip
          label-class-name="header-style">
        </el-table-column>
        <el-table-column fixed="right" label="操作" label-class-name="header-style" align="center" width="200">
          <template slot-scope="scope">
            <el-button v-if="!powerBtns.includes('updateRoleBtn') && powerBtns.includes('searchRoleBtn')" size="mini" type="primary" @click="itemUpdateClick(scope.row)">查看</el-button>
            <el-button  v-if="powerBtns.includes('updateRoleBtn')"  size="mini" type="primary" @click="itemUpdateClick(scope.row)">修改</el-button>
            <el-button v-if="powerBtns.includes('deleteRoleBtn')"  size="mini" type="danger" @click="itemDeleteClick(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        layout="prev, pager, next"
        :current-page="nowPage"
        :total="itemNumber"
        style="margin-top: 10px"
        @current-change="currentPage">
      </el-pagination>
    </div>


  </div>
</template>
<script>
  export default {
    name: 'roleManage',
    data() {
      return {
        roleOptions:[{value:"角色名",label:"角色名"}],
        classValue: '角色名',
        input: '',
        tableData: [],
        itemNumber: 0,
        nowPage: 1,
        isManager:0,
        // 权限按钮
        powerBtns : [],
      }
    },

    mounted:function () {
      this.init();
      // this.isManager=this.$tool.getStorage('LOGIN_USER').isManager;
    },
    created:function () {
      this.init();
      // this.isManager=this.$tool.getStorage('LOGIN_USER').isManager;
      this.searchClick();
    },
    watch:{
      $route(to, from) {
        if ((from.path === '/manage-menu/new-role'||from.path==='/manage-menu/update-role')&&this.$route.name==='roleManage') {
          this.init();
          this.searchClick();
        }
      }
    },
    methods: {
      init(){
        this.powerBtns = this.$tool.getPowerBtns2URL('manageMenu', '/manage-menu/user-manage', this.$route.path);

      },
      //-----------------------------按键事件-------------------------------
      //搜索按键
      searchClick: function () {
        console.log(this.$tool.getStorage('LOGIN_USER'));
        let params = new URLSearchParams();
        params.append("pageCurrent", 1);
        this.nowPage=1;
        this.sendRequest(params);
      },

      //-----------------------------交互事件-------------------------------
      //向服务器发送请求
      sendRequest: function (params) {
        if(this.input.trim()){
          params.append("role",this.input.trim());
        }
        params.append("pageSize", 10);
        this.$http.post('role/find', params).then(function (res) {
          if(res.data.success){
            this.itemNumber = res.data.data.total;
            this.tableData=res.data.data.list;
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message({
            showClose: true,
            message: '网络错误，请尝试重登录',
            type: 'error'
          });
        }.bind(this));
      },

      //-----------------------------表格事件-------------------------------
      //翻页
      currentPage: function (val) {
        this.nowPage = val;
        let params = new URLSearchParams();
        params.append("pageCurrent", val);
        this.sendRequest(params);
      },

      //-----------------------------按钮事件-------------------------------
      newClick:function () {
//        this.$message({
//          showClose: true,
//          message: '暂不开放此功能！',
//          type: 'success'
//        });
        this.$router.push('/manage-menu/new-role');
      },
      itemUpdateClick:function (row) {
//        this.$message({
//          showClose: true,
//          message: '暂不开放此功能！',
//          type: 'success'
//        });
        this.$router.push({name:'updateRole',params:{roleId:row.id}});
      },
      itemDeleteClick:function (row) {
        this.$confirm('此操作将永久删除该角色, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let params = new URLSearchParams;
          params.append("id", row.id);
          this.$http.post('role/delete', params).then(function (res) {
            this.$message({
              showClose: true,
              message: '删除角色成功！',
              type: 'success'
            });
            this.currentPage(this.nowPage);
          }.bind(this)).catch(function (err) {
            console.log(err);
            this.$message({
              showClose: true,
              message: '操作失败',
              type: 'error'
            });
          }.bind(this));
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });
        });
      },
    }
  }
</script>
<style>
</style>


