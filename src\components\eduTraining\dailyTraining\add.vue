<template>
  <el-container class="container">
    <el-main>
      <!--发布-->
      <el-form ref="info" label-width="100px" :model="info" :rules="rules">
        <el-row type="flex">
          <el-col :span="8">
            <el-form-item label="培训时间" prop="trainingDate">
              <el-date-picker v-model="info.trainingDate" type="date" placeholder="选择日期">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="学时" prop="trainingHours">
              <el-input-number :min="1" :max="10" v-model="info.trainingHours"></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="培训内容" prop="courses">
              <el-input v-model="info.courses"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex">
          <el-col :span="8">
            <el-form-item label="参加人员" prop="participants">
              <el-input v-model="info.participants">
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="组织部门" prop="department">
              <el-input v-model="info.department"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="地点" prop="location">
              <el-input v-model="info.location"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <!-- 是否打卡 -->
            <el-form-item label="是否打卡" prop="isPunch">
              <el-radio-group v-model="isPunch">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col v-if="isPunch" :span="12">
            <!-- 打卡位置: -->
            <el-button @click="dialogTableVisible = true">打卡位置:</el-button>
            <span>{{ location.poiname }}</span>
            <span style="margin-left: 20px;">详细地址:{{ location.poiaddress }}</span>
            <el-dialog width="400px" title="打卡位置" :visible.sync="dialogTableVisible" class="ka">
              <iframe class="if" id="mapPage" width="100%" height="600px" frameborder=0 src="https://apis.map.qq.com/tools/locpicker?search=1&type=1&key=MNFBZ-B5DWQ-37N5W-4AIZ6-P7EZQ-O4BDK&referer=myapp">
              </iframe>
            </el-dialog>
          </el-col>
        </el-row>
        <el-row type="flex" v-if="powerBtns.includes('addStaffBtn')">
          <el-col :span="24">
            <el-form-item label="人员列表" prop="eduDailyParticipants">
              <chooseStaff ref="chooseStaff" @selectedRows="selectedRows"></chooseStaff>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row type="flex">
          <el-col :span="8">
            <el-form-item label="通知标题" prop="notifyTitle">
              <el-input v-model="info.notifyTitle"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex">
          <el-col :span="24">
            <el-form-item label="通知内容" prop="notifyContent">
              <vue-editor v-model="info.notifyContent"></vue-editor>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" class="row" justify="center">
          <!-- <el-button @click="saveBtnClickHandle({ status: '0' })" size="small" :span="2" type="success">保存</el-button> -->
          <el-button @click="saveBtnClickHandle({ status: '1' })" v-if="isCreater" size="small" :span="2" type="primary">提交</el-button>
          <el-button size="small" :span="2" @click="$router.back();">返回</el-button>
        </el-row>
      </el-form>
    </el-main>
  </el-container>
</template>

<script>
import { VueEditor } from 'vue2-editor'
import chooseStaff from '@/components/common/chooseStaff'
export default {
  components: {
    chooseStaff,
    VueEditor
  },
  data() {
    return {
      // 打卡位置弹窗状态
      dialogTableVisible: false,
      isPunch: 0,
      //选择的打卡位置
      location: {
        "module": "locationPicker",
        "latlng": {
          "lat": 29.899166,
          "lng": 121.629406
        },
        "poiaddress": "",
        "poiname": "",
        "cityname": ""
      },
      // info表
      info: {
        // ID
        id: '',
        // 培训时间
        trainingDate: '',
        // 时长
        trainingHours: '',
        // 培训内容
        courses: '',
        // 参加人员
        participants: '全体人员',
        // 参与人员列表
        eduDailyParticipants: [],
        // 组织部门
        department: '',
        // 地址
        location: '',
        // 通知标题
        notifyTitle: '',
        // 通知内容
        notifyContent: '',
        // 状态 0 未发布    1 已发布    2 进行中    3 待评级    4 已完结
        status: '0',
        // 创建人ID
        createId: '0',
      },
      isCreater: true, // 是否显示提交按钮，创建者才能提交
      // 权限按钮
      powerBtns: [],
      // 校验规则
      rules: {
        trainingDate: [
          { required: true, message: '请选择培训时间', trigger: 'blur' }
        ],
        trainingHours: [
          { required: true, message: '请输入学时', trigger: 'blur' }
        ],
        courses: [
          { required: true, message: '请输入培训内容', trigger: 'blur' }
        ],
        participants: [
          { required: true, message: '请输入参加人员', trigger: 'blur' }
        ],
        department: [
          { required: true, message: '请输入组织部门', trigger: 'blur' }
        ],
        location: [
          { required: true, message: '请输入地点', trigger: 'blur' }
        ],
        isPunch: [
          { required: false, message: '请选择是否打卡', trigger: 'blur' }
        ],
        eduDailyParticipants: [
          { required: true, message: '请选择参与人员', trigger: 'blur' }
        ],
        notifyTitle: [
          { required: true, message: '请输入通知标题', trigger: 'blur' }
        ],
        notifyContent: [
          { required: true, message: '请输入通知内容', trigger: 'blur' }
        ]
      }
    }
  },
  watch: {
    $route(to, from) {
      // 如果来至列表页
      if (from.name === 'dailyTrainingIndex' && this.$route.name === 'dailyTrainingAdd') {
        this.init();
      }
    },
  },
  mounted() {
    this.init();
    window.addEventListener('message', (event) => {
      // 接收位置信息，用户选择确认位置点后选点组件会触发该事件，回传用户的位置信息
      var loc = event.data;
      if (loc && loc.module == 'locationPicker') {//防止其他应用也会向该页面post信息，需判断module是否为'locationPicker'
        console.log('location', loc);
        // 此处可以将用户的位置信息发送给后端，进行保存
        this.location = loc
      }
      this.dialogTableVisible = false;
    }, false);
  },
  methods: {
    // 初始化
    init() {
      this.info.eduDailyParticipants = [];
      this.info.notifyTitle = '';
      this.info.notifyContent = '';

      if (this.$route.params.status) {
        this.searchBtnClickHandle();

        if (this.info.createId != ''
          && this.info.createId != null
          && localStorage.LOGIN_USER.userId != ''
          && localStorage.LOGIN_USER.userId != null
          && this.info.createId != localStorage.LOGIN_USER.userId) {
          this.isCreater = false;
        }
      } else {
        this.clear();
      }

      this.judgeUserRole();
    },

    judgeUserRole() {
      // 获取权限按钮
      let url = '/edu-training-menu/daily-training-index';
      this.powerBtns = this.$tool.getPowerBtns('eduTrainingMenu', url);
    },
    // 清空数据
    clear() {
      this.info = this.$tool.clearObj({}, this.info);
    },
    // 根据id搜索信息
    searchBtnClickHandle() {
      this.clear();
      let id = this.$route.params.id;
      this.$store.dispatch('eduDailyInfoShow', { id: id }).then(function (res) {
        if (res.success) {
          let list = res.data.list[0];
          // 发布培训信息
          Object.entries(list).forEach(function (it) {
            if (it[1] && this.info.hasOwnProperty(it[0])) {
              this.info[it[0]] = it[1];
            }
          }.bind(this));
          // 通知
          if (list.eduDailyNotify.length) {
            this.info.notifyTitle = list.eduDailyNotify[0].title;
            this.info.notifyContent = list.eduDailyNotify[0].notify;
          }
          // 参与人员
          let staffList = list.eduDailyParticipants.map(function (it) {
            return {
              companyName: it.eduUser.companyName,
              deptName: it.eduUser.deptName,
              username: it.eduUser.username,
            }
          })
          // edit 添加和修改的时候，按钮显示；view 查看的时候，按钮隐藏
          this.$nextTick(() => {
            this.$refs.chooseStaff.isShowBtnHandle(true);
            this.$refs.chooseStaff.changeTableDataHandle(staffList);
          })
        } else {
          this.$message({
            type: 'error',
            message: res.message || '错误'
          })
        }
      }.bind(this));
    },
    // 未发布/已发布/进行中【开始按钮】--培训发布--保存按钮
    saveBtnClickHandle(options) {
      this.$refs.info.validate((valid) => {
        if (valid) {
          let id = this.$route.params.id;
          // 要选择参与人员
          if (this.info.eduDailyParticipants.length == 0) {
            this.$message({
              type: 'error',
              message: '请点击“参与人员”编辑图标，添加人员！！'
            })
            return;
          }
          // 判断培训的状态，0 未发布，1 已发布
          if (options) {
            this.info.status = options.status || '0';
          }
          // 通知
          let notify = {
            title: this.info.notifyTitle,
            notify: this.info.notifyContent,
          };
          if (id) {
            // 修改
            this.info.eduDailyNotify = [notify];
          } else {
            // 添加
            this.info.eduDailyNotify = [notify];
          }
          let params = this.$tool.filterObj({}, this.info);
          // 加入地址
          params.poiaddress = this.location.poiaddress;
          params.poiname = this.location.poiname;
          params.longitude = this.location.latlng.lng;
          params.latitude = this.location.latlng.lat;
          params.isPunch = this.isPunch;
          this.$store.dispatch('eduDailyInfoAddOrUpdate', params).then(function (res) {
            if (res.success) {
              this.$message({
                type: 'success',
                message: '操作成功'
              })
              this.$router.push({ name: 'dailyTrainingIndex' })
            } else {
              this.$message({
                type: 'error',
                message: res.message || '错误'
              })
            }
          }.bind(this))
        } else {
          this.$message({
            type: 'error',
            message: '请填写完整信息'
          })
          return false;
        }
      });
    },

    // 人员列表选择组件处理函数
    selectedRows(rows) {
      // 参与人员列表
      this.info.eduDailyParticipants = rows.map(function (it) {
        return {
          participate: true,
          userId: it.userId,
          eduDailyInfoId: '',
        }
      })
    },
  }
}
</script>

<style>
.container {
  background: #fff;
  padding: 0px 20px 20px;
}

.title {
  background: rgba(64, 158, 255, .1);
  color: #0f6fc6;
  border: 1px solid rgba(64, 158, 255, .2);
  border-radius: 5px;
}

.row {
  margin-top: 10px;
}

ka {
  display: flex;
  justify-content: center;
  align-items: center;

}

.if {
  border: 1px solid #eee;
  margin: 0 auto;
}
</style>
