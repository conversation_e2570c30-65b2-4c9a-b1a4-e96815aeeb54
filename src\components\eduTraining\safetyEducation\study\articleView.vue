<template>
  <el-container class="container" id="safeEducationView">
    <el-main>
      <el-row>
        <el-col :span="24">
          <h1 style="text-align: center;">{{info.newsName}}</h1>
        </el-col>
      </el-row>
      <el-row class="lineHeight">
        <el-col :span="4"  style="text-align: center;">作者：{{info.courseAuthor}}</el-col>
        <el-col :span="6" style="text-align: center;">发布时间：{{formatDateTime(info.createTime)}}</el-col>
        <el-col :span="6" style="text-align: center;">部门：{{info.deptName}}</el-col>
        <el-col :span="8" style="text-align: center;">出处：{{info.companyName}}</el-col>
      </el-row>

      <el-row class="lineHeight">
        <el-col :span="24">
      <!--    <vue-editor v-model="info.newsContent"></vue-editor>-->
          <div v-html="info.newsContent"></div>
        </el-col>
      </el-row>
      <el-row  class="lineHeight" v-if="info.listNewsFile.length > 0">
        <!--<el-col :span="6" style="text-align: center;">部门：{{info.deptName}}</el-col>-->
        <el-col :span="24">
          <el-table
            :data="info.listNewsFile"
            style="width: 100%">
            <el-table-column
              prop="fileName"
              label="文件名称"
              width="300">
              <template slot-scope="scope">
                <el-link type="primary" @click="showPdfDialog(scope.row)">{{scope.row.fileName}}</el-link>
                <!--<el-button type="text" size="mini">{{scope.row.fileName}}</el-button>-->
              </template>
            </el-table-column>
          </el-table>
          <!--<fileUpload :data="upload"></fileUpload>-->
        </el-col>
      </el-row>
      <el-row type="flex" class="row" justify="center">
        <el-button size="small" :span="2"  @click="$router.back();">返回</el-button>
      </el-row>
    </el-main>



    <!--安全生产目标结束-->

    <el-dialog :title="dialog.title"
               width="100%" top="0vh"
               :center="true"
               :visible.sync="dialog.isShow">
      <iframe :src="dialog.pdfUrl+'#toolbar=0'"  width="100%" height="810"></iframe>
    </el-dialog>
  </el-container>
</template>

<script>

  import _ from 'lodash'
  import { VueEditor } from 'vue2-editor'
  import chooseStaff from '@/components/common/chooseStaff'
  import fileUpload from '@/components/common/fileUploadFileServer'
  export default {
    components: {
      chooseStaff,
      VueEditor,
      fileUpload
    },
    data(){
      return {
        // info表
        info : {
          // ID
          id : '',
          // 文章名称
          newsName : '',
          // 文章类型
          newsType : '',
          // 文章内容
          newsContent : '',
          // 文章作者
          newsAuthor : '',
          // 部门
          deptName : '',
          // 文章出处
          newsSource : '',
          // 停留时间(s)
          stayTime : 0,
          // 所属主题
          // 是否必修
          // 文章积分
          newsScore : 0,
          // 发布时间
          createTime : 0,
          courseAuthor : '',
          companyName : '',

          // 文件上传的内容
          listNewsFile : [],

        },

        // pdf查看对话框
        dialog:{
          //查看安全风险告知书
          isShow:false,
          pdfUrl:'http://www.safe360.vip/safeFile.pdf',
          title:'查看安全风险告知书',
        },


        upload:{
          params:{
            contentId : this.$tool.getStorage('LOGIN_USER').userId,
            contentType:19
          },
          btns : {
            // 上传按钮
            upload : {
              isShow : false,
            },
            // 下载按钮
            download : {
              isShow : true,
            },
            // 删除按钮
            delete : {
              isShow : false,
            },
          },
          uploadUrl:'',
          uploadCookies:true,
          fileData : [],
        },
        timer : null,

      }
    },
    watch:{
      $route(to,from){
//        console.log('from:', from.name);
//        console.log('to:', to.name);
        // 如果来至列表页
        if(from.name == 'safetyEducationStudyIndex'
          && to.name == 'safetyEducationStudyArticleView') {
          this.debouncedGetAnswer()
        }
       /* if(from.name === 'safetyEducationStudyIndex'&&this.$route.name==='safetyEducationStudyArticleView'){
          console.log('kkkkk')
          this.debouncedGetAnswer()
        }*/
      },
    },
    mounted(){
      this.debouncedGetAnswer()
    },
    created: function () {
      this.debouncedGetAnswer = _.debounce(this.searchBtnClickHandle, 500)
    },
    methods:{
      init(){
        this.searchBtnClickHandle();
      },
      showPdfDialog:function (row) {
//
//        console.log(111,row);
        this.dialog.title = row.fileName;
        this.dialog.pdfUrl = row.filePath;
        this.dialog.isShow=true
      },
      formatDateTime(dateStr){
        let num = 10;
        let str = this.$tool.formatDateTime(dateStr) || '';
        return str ? str.substring(0, num) : str;
      },
      // 清空数据
      clear(){
        this.info = this.$tool.clearObj({}, this.info);
      },

      // 根据id搜索信息
      searchBtnClickHandle(){
        this.clear();
        let id = this.$route.params.id;
        this.viewOrEdit = true;
        if(id <= 0 || id == '') return;
        this.$store.dispatch('eduNewsFindById', { id : id }).then(function(res){
          if(res.success){
//            let list = res.data.list[0];
            let list = res.data;
            // 发布培训信息
            Object.entries(list).forEach(function(it){
              if(it[1] && this.info.hasOwnProperty(it[0])){
                this.info[it[0]] = it[1];
              }
            }.bind(this));
//            console.log('file:', this.info.listNewsFile)
//            return;
//            this.upload.fileData = list.listNewsFile;
//            console.log(111,this.upload.fileData);
            console.log('父组件数据：',this.upload);
            // 阅读完加分，如果没有阅读过，阅读时间>=页面停留时间，那么加分
            if(this.info.stayTime > 0){
              clearTimeout(this.timer);
              this.timer = setTimeout(function(){
                this.$store.dispatch('eduUserStudyAddUserNews', { studyId : id }).then(function(res) {
                  if (res.success) {
                    this.$message({
                      type: 'success',
                      message: '学习完成'
                    })
                  }
                }.bind(this))
              }.bind(this), this.info.stayTime * 1000)
            }

          } else {
            this.$message({
              type : 'error',
              message : res.message || '错误'
            })
          }
        }.bind(this));
      },

    }
  }
</script>

<style>
  .container{
    background:#fff;
    padding:0px 20px 20px;
  }
  #safeEducationView .lineHeight{
    margin-top:20px;
  }
  .title{
    background:rgba(64,158,255,.1);
    color:#0f6fc6;
    border: 1px solid rgba(64,158,255,.2);
    border-radius:5px;
  }
  .row{
    margin-top:10px;
  }
</style>
