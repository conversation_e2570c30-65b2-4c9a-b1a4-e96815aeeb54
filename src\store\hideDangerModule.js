/*
  created by m<PERSON><PERSON><PERSON> on 2018-4-12
  common data in hide danger
*/
import axios from 'axios'
import http from '@/assets/functions/axiosServer'
// 应急救援
export default {
  state: {},
  getters: {},
  mutations: {},
  actions : {
    /*
    * 安全首页
    * */
    // 统计数据--GET /danger/index/dangerTaskCount/{companyId}
    dangerTaskCount({ commit }, params){
      let url = 'danger/index/dangerTaskCount/' + params.companyId;
      return new Promise(function(resolve, project){
        http.get(url).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 最新隐患--GET /danger/index/latestDanger/{companyId}
    latestDanger({ commit }, params){
      let url = 'danger/index/latestDanger/' + params.companyId;
      return new Promise(function(resolve, project){
        http.get(url).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 柱状图--GET /danger/index/chartData/{year}/{companyId}
    chartData({ commit }, params){
      let url = 'danger/index/chartData/' + params.year + '/' + params.companyId;
      return new Promise(function(resolve, project){
        http.get(url).then(function (res) {
          resolve(res.data);
        });
      })
    },
    /*
     * 检查表管理
     * */
    // 添加知识点--联动查询
    getLabelConstruct({ commit }, params){
      let url = '/label/getLabelConstruct/' + params.get('companyId');
      return new Promise(function(resolve, project){
        http.get(url).then(function (res) {
          resolve(res.data);
        });
      })
    },


    // 检查表管理---修改按钮--检查项修改
    dangerInspectListUpdate({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('/danger/inspectList/update', params).then(function (res) {
          resolve(res.data);
        });
      })
    },

  }
};
