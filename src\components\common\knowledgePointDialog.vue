<template>
    <div id="knowledgePointDialog">
      <!--对话框开始-->
      <el-dialog title="添加知识点" :visible.sync="data.isShow">
        <el-container>
          <el-main>
            <el-row  class="row">
              <el-col :span="6">
                <el-button type="success"  @click="searchBtnClickHandle" size="small" icon="el-icon-search">搜索知识点</el-button>
              </el-col>
              <el-col :span="4">
                <el-select
                  v-model="selectedSearchValArr"
                  multiple
                  filterable
                  remote
                  reserve-keyword
                  clearable
                  placeholder="请输入标签名后选择,可多选"
                  style="width: 300px">
                  <el-option
                    v-for="item in labelList"
                    :key="item.value"
                    :label="item.label"
                    :value="item">
                  </el-option>
                </el-select>
              </el-col>
            </el-row>
            <!--自定义插槽 slot-->
            <slot name="firstSlot"></slot>
            <el-row  class="row">
              <el-table
                :data="knowledgeList.list"
                border
                tooltip-effect="light"
                :highlight-current-row="!data.isMultiple"
                @current-change="currentChangeHandle"
                @selection-change="selectionChangeHandle"
                style="width: 100%;margin-top: 10px">
                <el-table-column
                  v-if="data.isMultiple"
                  type="selection">
                </el-table-column>
                <el-table-column
                  prop="content"
                  label="知识点"
                  show-overflow-tooltip
                  label-class-name="inner-header-style"
                  min-width="200">
                </el-table-column>
              </el-table>
            </el-row>
            <el-row  class="row">
              <el-pagination
                background
                layout="prev, pager, next"
                :current-page="knowledgeList.pageNum"
                :total="knowledgeList.total"
                @current-change="pageChangeHandle">
              </el-pagination>
            </el-row>
          </el-main>
        </el-container>
         <span slot="footer" class="dialog-footer">
          <el-button @click="data.isShow = false">取 消</el-button>
          <el-button type="primary" @click="okBtnClickHandle">确 定</el-button>
        </span>
      </el-dialog>
    </div>
</template>

<script>
    export default {
      props : ['data'],
      /*
      * data 为 父组件 传入 子组件 的数据
      *      isMultiple       // 为否为多选
      *      isShow         // 是否显示
      * 子组件向父组件提交的事件
      *
       @current-change="currentChangeHandle"     // 单选
       @selection-change="selectionChangeHandle"   // 多选
       @currentChangeHandle=“currentChangeHandle” // 确定按钮
      * */
      data(){
        return {
          // 搜索框被选中的值
          selectedSearchValArr : [],
          // 表格
          table : {
            // 当前页码
            currentPage : 1,
            // 当前选中的行--无论是单行，还是多行
            currentSelectRows : [],
          },

        }
      },
      computed:{
        // 标签
        labelList : function(){
          return this.$store.state.emerHandleModule.labelList
        },
        // 知识点
        knowledgeList : function(){
          return this.$store.state.emerHandleModule.knowledgeList;
        }
      },
      mounted(){
        this.init();
      },
      methods: {
        init(){
          // 获取知识点标签列表
          this.$store.dispatch("labelListAction");
        },
        // 通过标签搜索知识点
        searchBtnClickHandle(){
          let params = {
            pageCurrent : this.table.currentPage,
            labels : [],
            companyId:this.$tool.getStorage('LOGIN_USER').companyId
          }
          params.labels = this.selectedSearchValArr.map(function(it){
            return it.label;
          })
          // 获取知识点列表
          this.$store.dispatch("knowledgeListAction", params);
        },
        // 分页显示
        pageChangeHandle(page){
          this.table.currentPage = page;
          this.searchBtnClickHandle();
        },
        // 选中行---单选
        currentChangeHandle(val){
          //console.log('单选：', val)
          this.table.currentSelectRows = [val];
          this.$emit("currentChangeHandle", this.table.currentSelectRows);
        },
        // 选中行---多选
        selectionChangeHandle(val){
         // console.log('多选：', val)
          this.table.currentSelectRows = val;
          this.$emit("selectionChangeHandle", this.table.currentSelectRows);
        },
        // 点击确定按钮
        okBtnClickHandle(){
          this.data.isShow = false;
          this.$emit("okBtnClickHandle", this.table.currentSelectRows);
        }
      }
    }
</script>

<style>
#knowledgePointDialog .el-dialog__body{
  padding : 0 20px;
}
</style>
