<template>
    <div id="">
      <div style="width: 100%;padding-top: 10px;padding-bottom:10px;float: left;background-color: #f2f2f2">
        <i class="el-icon-upload" style="color:#049ff1;float: left;margin:12px 10px 0 20px"></i>
        <span style="color:#049ff1;width: 300px;float: left;">{{upload.tip}}</span>
      </div>
      <div style="width: 100%;float:left;padding-top: 5px;padding-bottom: 5px">
        <el-upload
          class="upload-demo"
          :action="upload.url"
          :limit="upload.limit"
          :with-credentials="upload.cookies"
          :data="upload.params"
          :http-request="ossUpload"
          style="width: 300px;margin-bottom: 10px;">
          <!--
          :on-success="uploadSuccess"
          -->
          <el-button size="small" type="primary" v-if="upload.btns.upload.isShow">点击上传</el-button>
        </el-upload>
        <el-col :span="24">
          <el-table
            :data="upload.fileData"
            border
            style="width: 100%;">
            <el-table-column
              type="index"
              align="center"
              label-class-name="inner-header-style"
              width="50">
            </el-table-column>
            <el-table-column
              prop="fileName"
              label="文件名称"
              align="center"
              label-class-name="inner-header-style">
              <template slot-scope="scope">
                <a v-if="upload.btns.download.isShow == true" :href="scope.row.filePath">{{scope.row.fileName}}</a>
                <span
                  v-if="upload.btns.download.isShow == false" >{{scope.row.fileName}}</span>

               <!-- <el-button
                  v-if="upload.btns.download.isShow == false"
                  type="text" size="mini" style="color: blue"
                  @click="previewBtn(scope.row)">{{scope.row.fileName}}</el-button>-->


              </template>
            </el-table-column>
           <!-- <el-table-column
              prop="uploadTime"
              label="上传时间"
              align="center"
              :formatter="formatDateTime"
              label-class-name="inner-header-style"
              min-width="150">
            </el-table-column>-->
            <el-table-column
              label="操作"
              align="center"
              width="200"
              label-class-name="inner-header-style">
              <template slot-scope="scope">
                <!--<a v-if="upload.btns.delete.isShow" :href="scope.row.filePath">下载</a>-->
                <!--<el-button type="text" size="medium" style="color: #5daf34"
                           v-if="upload.btns.download.isShow"
                           @click="downloadFile(scope.row)">下载
                </el-button>-->
                <el-button type="text" size="medium" style="color: #dd6161"
                           v-if="upload.btns.delete.isShow"
                           @click="deleteUploadFile(scope.row,scope.$index)">刪除
                </el-button>

              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </div>


      <!--预览文件-->
      <el-dialog :title="previewDialog.title" width="100%" top="0vh" :center="true" :visible.sync="previewDialog.isShow">
        <iframe :src="previewDialog.filePath+'#toolbar=0'" width="100%" height="810"></iframe>
      </el-dialog>

    </div>
</template>

<script>

  import dealData from '@/assets/functions/dealData'
    export default {
      props : ['data'],
      data(){
        let that = this;
        return {
          // 上传文件
          upload : {
            // 提示信息
            tip : '其他文件资料',
            // 地址
            url : that.$http.defaults.baseURL + 'file/upload',
            limit : 10,
            // token
            cookies : true,
            // 上传参数
            params : {
              contentId: that.$route.params.id,
              contentType: 3,
//              fId: -1,
            },
            // 上传和删除按钮的显示
            btns : {
              // 上传按钮
              upload : {
                isShow : true,
              },
              // 下载按钮
              download : {
                isShow : true,
              },
              // 删除按钮
              delete : {
                isShow : true,
              },
            },
            // 文件列表
            fileData : [],
          },

          // 预览对话框
          previewDialog : {
            // 是否显示
            isShow: false,
            // 标题
            title: '',
            // 文件路径
            filePath: '',
          },

        }
      },
      watch:{
       /* 'data.fileData'(to,from){
          // 如果来至列表页
          console.log('to',to)
          console.log('from',from)
          this.init();
        },*/

        'data.fileData' : {
          immediate: true,    // 这句重要
          handler (val) {
            this.init();
          }
        }
      },
      methods:{
        // 初始化数据
        init(){
          // 把父组件传递的值给子组件
//          this.upload.fileData = [];
          console.log('子组件数据', this.data);
//          console.log(this.$store.state.articleData,9999999)
          Object.entries(this.data).forEach(function(it){
            if(this.upload.hasOwnProperty(it[0])){
              this.upload[it[0]] = it[1];
            }
          }.bind(this))
        },
        // 格式化时间
        formatDateTime(row, column, cellValue){
          let pro = column.property;
          let num = 10;
          let str = this.$tool.formatDateTime(row[pro]) || '';
          return str ? str.substring(0, num) : str;
        },

        // 预览文件-----新增的时候是无法预览的，弃用
        previewBtn(row){
          console.log(row);
//          this.previewDialog.title = row.fileName;
//          this.previewDialog.filePath = row.filePath;
//          this.previewDialog.isShow = true;
        },
        //上传文件
        uploadSuccess: function (response, file, fileList) {
//          console.log('file', file)
//          return;
          if (response.success) {
            this.loadFile();
          }
        },

        //上传图片
        ossUpload:function (item) {
          let type = item.file.type;
          let fileSize = item.file.size;
          if(fileSize > 1073741824/2){
          // if(fileSize > 349427){
            this.$message({
              type : 'error',
              message : "上传文件超过500MB ，请将文件压缩后再进行上传"
            })
            return;
          }
          console.log(type === 'video/mp4',99999)
          if(type != 'application/pdf' && type != 'video/mp4'){
            this.$message({
              type : 'error',
              message : "请上传PDF格式的文件"
            })
            return;
          }

          this.loginLoading=this.$loading({
            lock: true,
            text: '上传中',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.5)'
          });
//          console.log(item.file.type);

//          return;
          let fileData = {
            fileName : '',
          }
          //获取该文件对应的sign
          this.$http.get('sys/oss/sign?contentId=' + this.upload.params.contentId
            +'&contentType=' + this.upload.params.contentType
            + '&realName=' + item.file.name).then(function (res) {
            if(res.data){
              let params=new FormData();
              // 视频名称
              params.append("name",item.file.name);
              params.append("key",res.data.dir + item.file.name);
              params.append("policy",res.data.policy);
              params.append("OSSAccessKeyId",res.data.accessid);
              params.append('success_action_status','200');
              params.append("callback",res.data.callback);
              params.append("signature",res.data.signature);
              params.append("file",item.file);
              this.fileHttp.post('',params,{headers: {'Content-Type': 'multipart/form-data'}}).then(function (res) {
                if(res.data.file){
                  let resultStr=dealData.decode(res.data.file);
//                  console.log(resultStr,9999)
                  let resultJson=JSON.parse(resultStr);
                  this.upload.fileData.push( {
                    id : '',
                    eduNewsId : '',
                    fileId : resultJson.fId,
                    fileName : resultJson.fileName,
                    // 上传文件的信息，大小、类型等
                    fileData : item
                  });
                  this.loginLoading.close();//关闭登陆加载
                  this.$message.success('上传成功');
//                  console.log('子组件将数据传给父组件：', resultJson);
                  this.$emit('fileData', this.upload.fileData)
                }else{
                  this.loginLoading.close();//关闭登陆加载
                  this.$message.error('上传失败');
                }
              }.bind(this))
            }
          }.bind(this)).catch(function (err) {
            this.loginLoading.close();//关闭登陆加载
            this.$message.error('获取唯一标识失败');
          }.bind(this));
        },
        // 获取上传文件
        loadFile: function () {
          var params = new URLSearchParams()
          params.append("contentId", this.upload.params.contentId);
          params.append("contentType", this.upload.params.contentType);
//          params.append("fId", this.upload.params.fId);
          this.$store.dispatch('fileFindAction', params).then(function(res){
            if (res.success) {
              this.upload.fileData = res.data
              this.$emit('fileData', this.upload.fileData);
            }
          }.bind(this));
        },
        // 删除上传文件
        deleteUploadFile: function (row, index) {
          this.$confirm('此操作将删除该文件, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(function(){
//            console.log(row);
//            return;
            var params = new URLSearchParams()
            params.append("fId", row.fileId)
            this.$store.dispatch('fileDeleteAction', params).then(function (res) {
              if (res.success) {
                this.upload.fileData =  this.upload.fileData.filter(function(it){
                  return it.fileId != row.fileId
                })
//                console.log(111111,this.upload.fileData)
                this.$emit('fileData', this.upload.fileData)
//                this.loadFile();
              }
            }.bind(this))
          }.bind(this));
        },
        // 下载文件
        downloadFile: function (row) {
          const loading = this.$loading({
            lock: true,
            text: 'Loading',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });
          var params = new URLSearchParams()
          params.append("fId", row.fileId)
          this.$store.dispatch('fileDownloadAction', params).then(function(res){ // 处理返回的文件流
            loading.close()
            // 创建a标签
            const elink = document.createElement('a')
            // 文件名
            elink.download = row.fileName
            elink.style.display = 'none'
            const blob = new Blob([res.data])
            elink.href = URL.createObjectURL(blob)
            document.body.appendChild(elink)
            // 触发点击a标签事件
            elink.click();
            document.body.removeChild(elink)
          })
        },
      }
    }
</script>

<style>

</style>
