<template>
  <div id="dataReportTempletEdit" class="background-style">
    <el-container class="container">
      <el-main>
        <el-form ref="form" :model="form" label-width="100px">
          <el-row>
            <el-col :span="6">
              <el-form-item label="公司" >
                <el-input v-model="company"></el-input>
              </el-form-item>
            </el-col>

            <el-col  :span="8">
              <el-form-item label="时间范围" >
                <el-date-picker

                  v-model="startDate"
                  type="date"
                  placeholder="选择日期">
                </el-date-picker>

                <span>--</span>

                <el-date-picker
                  v-model="endDate"
                  type="date"
                  placeholder="选择日期">
                </el-date-picker>
              </el-form-item>
            </el-col>

            <el-col :span="4">
              <el-form-item label="周期" >
                <el-select v-model="cycle" placeholder="请选择">
                  <el-option
                    v-for="item in cycleOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row  class="tableRow" style="margin-top: 20px;">
            <el-col >
              <el-table :data="dataReportDetail.data" border style="width:60%;" >
                <el-table-column
                  prop="num"
                  label-class-name="header-style"
                  width="70"
                  label="序号"
                  type="index"
                ></el-table-column>
                <el-table-column
                  prop="name"
                  label-class-name="header-style"
                  label="名称"
                  min-width="300"
                ></el-table-column>

                <el-table-column
                  prop="unit"
                  label-class-name="header-style"
                  label="单位"
                  min-width="100"
                ></el-table-column>
                <el-table-column
                  prop="type"
                  label-class-name="header-style"
                  label="数据类型"
                  min-width="100"
                ></el-table-column>
                <el-table-column
                  fixed="right" label="操作"
                  label-class-name="header-style"
                  align="center" min-width="130"
                >
                  <template slot-scope="scope">
                    <el-button size="mini" type="success" >删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
            <!--<el-col style="margin-top:20px;">-->
            <!--<el-pagination-->
            <!--background-->
            <!--layout="prev, pager, next"-->
            <!--:current-page="dataReportDetail.pageCurrent"-->
            <!--:page-size="form.pageSize"-->
            <!--:total="2"-->
            <!--@current-change ="disasterPageChangeHandle">-->
            <!--</el-pagination></el-col>-->

            <el-col style="margin-top: 20px;">
              <el-button type="success">保存</el-button>
              <el-button type="primary" @click="$router.back()">发布</el-button>
              <el-button @click="$router.back()">返回</el-button>
            </el-col>
          </el-row>

        </el-form>

      </el-main>
    </el-container>
  </div>
</template>

<script>
  export default{
    name:'dataReportTempletEdit',

    data(){
      return {
        creatDate: '',
        company:'宁波交通投资集团有限公司',
        startDate:'2018.1.1',
        endDate:'2018.3.30',
        cycleOptions: [{
          value: '0',
          label: '无'
        }, {
          value: '1',
          label: '年度'
        }, {
          value: '2',
          label: '半年度'
        }, {
          value: '3',
          label: '季度'
        }, {
          value: '4',
          label: '月度'
        }],
        cycle:'1',
        dataReportDetail: {
          data:[
            {num: '1', name: '与所属企业签订安全生产工作责任书',value:'/', unit: '', type: '自建数据'},
            {num: '2', name: '签约率', unit: '%',value:'/', type: '自建数据'},
            {num: '3', name: '主要负责人召开会议',value:'1', unit: '次', type: '自建数据'},
            {num: '4', name: '主要负责人监督检查',value:'1', unit: '%', type: '系统数据'},
            {num: '5', name: '分管负责人召开会议',value:'3', unit: '次', type: '自建数据'},
            {num: '6', name: '分管负责人监督检查',value:'3', unit: '次', type: '系统数据'},
            {num: '7', name: '死亡事故',value:'/', unit: '起', type: '系统数据'},
            {num: '8', name: '死亡人数',value:'/', unit: '人', type: '系统数据'},
            {num: '9', name: '重伤事故',value:'/', unit: '起', type: '系统数据'},
            {num: '10', name: '重伤人数',value:'/', unit: '人', type: '系统数据'},
            {num: '11', name: '安全检查次数',value:'49', unit: '次', type: '系统数据'},
            {num: '12', name: '安全检查参加人数',value:'54', unit: '人', type: '系统数据'},
            {num: '13', name: '安全检查参加人次',value:'182', unit: '人次', type: '系统数据'},
            {num: '14', name: '发现隐患',value:'41', unit: '条', type: '系统数据'},
            {num: '15', name: '整改隐患',value:'41', unit: '条', type: '系统数据'},
            {num: '16', name: '整改率',value:'100', unit: '%', type: '系统数据'},
            {num: '17', name: '企业本级预案修订数',value:'/', unit: '', type: '系统数据'},
            {num: '18', name: '企业本级备案数',value:'/', unit: '', type: '系统数据'},
            {num: '19', name: '企业本级应急演练数',value:'/', unit: '', type: '系统数据'},
            {num: '20', name: '企业本级应急启动数',value:'/', unit: '', type: '系统数据'},
            {num: '21', name: '下级单位预案编制数',value:'/', unit: '', type: '系统数据'},
            {num: '22', name: '下级单位备案数',value:'/', unit: '', type: '系统数据'},
            {num: '23', name: '下级单位应急演练数',value:'4', unit: '', type: '系统数据'},
            {num: '24', name: '下级单位应急启动数',value:'/', unit: '', type: '系统数据'},
            {num: '25', name: '企业领导培训',value:'35', unit: '人', type: '系统数据'},
            {num: '26', name: '安全管理人员培训',value:'207', unit: '人', type: '系统数据'},
            {num: '27', name: '特种作业人员培训',value:'388', unit: '人', type: '系统数据'},
            {num: '28', name: '从业人员培训',value:'1687', unit: '人', type: '系统数据'},
            {num: '29', name: '外来务工人员培训',value:'3632', unit: '人', type: '系统数据'},
            {num: '30', name: '“三同时”审查项目',value:'/', unit: '个', type: '系统数据'},
          ],
          // 当前页
          pageCurrent : 1,
          // 页数大小
          pageSize : 10,
        }
        ,

      }
    },
    mounted:function(){

    },
    methods:{

    }
  }
</script>

<style>
</style>
