<template>
  <div id="postManage" class="background-style">
    <el-col :span="24" style="padding: 10px 0 0 10px">
      <el-input placeholder="请输入岗位名称" v-model="searchInput" style="width: 500px;margin-left: 10px">
        <template slot="prepend">岗位名称：</template>
      </el-input>
      <el-button type="primary" icon="el-icon-search" @click="searchTableData">搜  索</el-button>
      <el-button type="success" icon="el-icon-plus" @click="addPostClick" style="margin-left: 20px">添加岗位</el-button>
    </el-col>
    <el-col :span="24" style="padding: 10px 0 0 10px">
      <el-table
        ref="postTable"
        :data="tableData"
        highlight-current-row
        style="width: 100%;"
        stripe
        border>
        <el-table-column
          type="index"
          label="序号"
          width="70"
          align="center"
          label-class-name="header-style">
        </el-table-column>
        <el-table-column
          prop="postName"
          label="岗位名称"
          min-width="200"
          align="center"
          show-overflow-tooltip
          label-class-name="header-style">
        </el-table-column>
        <el-table-column
          prop="selfInspectFrequence"
          label="自查频率"
          width="150"
          align="center"
          label-class-name="header-style">
        </el-table-column>
        <el-table-column
          prop="superviseInspectFrequence"
          label="督查频率"
          width="150"
          align="center"
          label-class-name="header-style">
        </el-table-column>
        <el-table-column
          prop="inspectFrequence"
          label="排查频率"
          width="150"
          align="center"
          label-class-name="header-style">
        </el-table-column>
        <el-table-column fixed="right" label="操作" label-class-name="header-style" align="center" width="200">
          <template slot-scope="scope">
            <el-button size="mini" type="primary" @click="itemUpdateClick(scope.row)">修改</el-button>
            <el-button size="mini" type="danger" @click="itemDeleteClick(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        layout="prev, pager, next"
        style="margin-top: 10px"
        :current-page="currentPage"
        :total="totalNumber"
        @current-change="currentPageClick">
      </el-pagination>
    </el-col>


    <!--新增,修改岗位对话框-->
    <el-dialog :title="dialogTitle" width="75%" :visible.sync="dialogVisible">
      <el-form :model="dialogData" :rules="dialogRules" ref="postForm" label-position="right" class="demo-ruleForm">
        <el-form-item label="岗位名称:" label-width="120px" prop="postName">
          <el-input v-model.trim="dialogData.postName"></el-input>
        </el-form-item>
        <el-form-item label="岗位排序:" label-width="120px" prop="sort">
          <el-input v-model.trim="dialogData.sort"></el-input>
        </el-form-item>
        <el-form-item label="自查频率:" label-width="120px" prop="selfInspectCount" >
          <el-input v-model.number="dialogData.selfInspectCount" class="input-with-select">
            <el-select v-model="dialogData.selfInspectTimeLimit" slot="append" placeholder="请选择" style="width: 100px">
              <el-option v-for="item in frequenceOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-input>
        </el-form-item>
        <el-form-item label="督查频率:" label-width="120px" prop="superviseInspectCount">
          <el-input v-model.number="dialogData.superviseInspectCount"  class="input-with-select">
            <el-select v-model="dialogData.superviseInspectTimeLimit" slot="append" placeholder="请选择"  style="width: 100px">
              <el-option v-for="item in frequenceOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-input>
        </el-form-item>
        <el-form-item label="排查频率:" label-width="120px" prop="inspectCount">
          <el-input v-model.number="dialogData.inspectCount" class="input-with-select">
            <el-select v-model="dialogData.inspectCountTimeLimit" slot="append" placeholder="请选择"  style="width: 100px">
              <el-option v-for="item in frequenceOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-input>
        </el-form-item>
        <el-form-item label="自查检查表:" label-width="120px">
          <el-input v-model="dialogForm.selfInspectTableName" readonly class="input-with-select" style="width: 300px;"></el-input>
          <el-button type="primary" @click="searchReferTableClick(1);chooseTableVisible=true;">选择自查表</el-button>
        </el-form-item>

        <div v-if="dialogTitle==='修改岗位'">
          <el-form-item label="安全风险告知书:" label-width="150px">
            <el-button type="text" style="float: left;text-decoration: underline" @click="viewPdf('安全风险告知书',noticePDF.url)">{{noticePDF.name}}</el-button>
            <el-upload
              class="upload-demo"
              :action='upload.uploadUrl'
              :show-file-list="false"
              :with-credentials="upload.uploadCookies"
              :http-request="ossUploaNoticedPdf"
              :before-upload="beforeUpload"
              :data="upload.params"
              accept=".pdf"
              style="margin-left: 20px;float: left;">
              <el-button type="primary" size="mini" style="margin-left: 5px">上传告知书</el-button>
            </el-upload>
            <span class="el-upload__tip" style="margin-left: 10px">只能上传pdf文件</span>
          </el-form-item>
          <el-form-item label="安全生产承诺书:" label-width="150px">
            <el-button type="text" style="float: left;text-decoration: underline" @click="viewPdf('安全生产承诺书',responsePDF.url)">{{responsePDF.name}}</el-button>
            <el-upload
              class="upload-demo"
              :action='upload.uploadUrl'
              :show-file-list="false"
              :with-credentials="upload.uploadCookies"
              :http-request="ossUploaResponsedPdf"
              :before-upload="beforeUpload"
              :data="upload.params"
              accept=".pdf"
              style="margin-left: 20px;float: left;">
              <el-button type="primary" size="mini" style="margin-left: 5px">上传责任书</el-button>
            </el-upload>
            <span class="el-upload__tip" style="margin-left: 10px">只能上传pdf文件</span>
          </el-form-item>
          <el-form-item label="岗位责任清单:" label-width="120px" style="margin: 10px 0 0 0">
            <el-button type="primary" icon="el-icon-plus" size="mini" @click="addResponseTitle='添加岗位责任';addResponseContent='';addResponseFlag=true">岗位责任</el-button>
          </el-form-item>
          <el-table
            :data="dialogData.responseTable"
            border
            style="width: 100%">
            <el-table-column
              type="index"
              align="center"
              label-class-name="inner-header-style"
              width="50">
            </el-table-column>
            <el-table-column
              label="内容"
              prop="content"
              label-class-name="inner-header-style"
              min-width="200">
            </el-table-column>
            <el-table-column
              label="操作"
              align="center"
              label-class-name="inner-header-style"
              fixed="right"
              width="80">
              <template slot-scope="scope">
                <el-button type="text" icon="el-icon-edit-outline" style="color: #00f" size="small" @click="addResponseTitle='修改岗位责任';addResponseContent=scope.row.content;updateResponseData.id=scope.row.id;updateResponseData.index=scope.$index;addResponseFlag=true"></el-button>
                <el-button type="text" icon="el-icon-delete" style="color: #ff0000" size="small" @click="deleteResponseClick(scope.$index,scope.row)"></el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-form-item label="岗位主要任务清单:" label-width="140px" style="margin: 10px 0 0 0">
            <el-button type="primary" icon="el-icon-plus" size="mini" @click="addDutyTitle='添加岗位主要任务';addDutyForm.content='';addDutyForm.frequency='';addDutyFlag=true">主要任务</el-button>
          </el-form-item>
          <el-table
            :data="dialogData.dutyTable"
            border
            style="width: 100%">
            <el-table-column
              type="index"
              align="center"
              label-class-name="inner-header-style"
              width="50">
            </el-table-column>
            <el-table-column
              label="内容"
              prop="content"
              label-class-name="inner-header-style"
              min-width="200">
            </el-table-column>
            <el-table-column
              label="频率"
              prop="frequency"
              align="center"
              label-class-name="inner-header-style"
              width="150">
            </el-table-column>
            <el-table-column
              label="操作"
              align="center"
              label-class-name="inner-header-style"
              fixed="right"
              width="80">
              <template slot-scope="scope">
                <el-button type="text" icon="el-icon-edit-outline" style="color: #00f" size="small" @click="addDutyTitle='修改岗位主要任务';addDutyForm.content=scope.row.content;addDutyForm.frequency=scope.row.frequency;updateDutyData.id=scope.row.id;updateDutyData.index=scope.$index;addDutyFlag=true"></el-button>
                <el-button type="text" icon="el-icon-delete" style="color: #ff0000" size="small" @click="deleteDutyClick(scope.$index,scope.row)"></el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-form-item label="安全工作目标:" label-width="120px" style="margin: 10px 0 0 0">
            <el-button type="primary" icon="el-icon-plus" size="mini" @click="addGoalTitle='添加安全工作目标';addGoalContent='';addGoalFlag=true">工作目标</el-button>
          </el-form-item>
          <el-table
            :data="dialogData.goalTable"
            border
            style="width: 100%">
            <el-table-column
              type="index"
              align="center"
              label-class-name="inner-header-style"
              width="50">
            </el-table-column>
            <el-table-column
              label="内容"
              prop="content"
              label-class-name="inner-header-style"
              min-width="200">
            </el-table-column>
            <el-table-column
              label="操作"
              align="center"
              label-class-name="inner-header-style"
              fixed="right"
              width="80">
              <template slot-scope="scope">
                <el-button type="text" icon="el-icon-edit-outline" style="color: #00f" size="small" @click="addGoalTitle='修改安全工作目标';addGoalContent=scope.row.content;updateGoalData.id=scope.row.id;updateGoalData.index=scope.$index;addGoalFlag=true"></el-button>
                <el-button type="text" icon="el-icon-delete" style="color: #ff0000" size="small" @click="deleteGoalClick(scope.$index,scope.row)"></el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-form>
      <el-dialog width="40%" :title="addResponseTitle" :visible.sync="addResponseFlag" append-to-body>
        <el-input placeholder="请填写岗位责任" v-model="addResponseContent" type="textarea"></el-input>
        <div slot="footer" class="dialog-footer">
          <el-button @click="addResponseFlag=false">取 消</el-button>
          <el-button type="primary" @click="addResponseClick">确 定</el-button>
        </div>
      </el-dialog>
      <el-dialog width="40%" :title="addDutyTitle" :visible.sync="addDutyFlag" append-to-body>
        <el-form :model="addDutyForm" label-position="right" class="demo-ruleForm">
          <el-form-item label="内容:" style="margin: 0">
            <el-input placeholder="请输入任务内容" v-model="addDutyForm.content" type="textarea"></el-input>
          </el-form-item>
          <el-form-item label="频率:" style="margin: 0">
            <el-input placeholder="请输入频率" v-model="addDutyForm.frequency"></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="addDutyFlag=false">取 消</el-button>
          <el-button type="primary" @click="addDutyClick">确 定</el-button>
        </div>
      </el-dialog>
      <el-dialog width="40%" :title="addGoalTitle" :visible.sync="addGoalFlag" append-to-body>
        <el-input placeholder="请填写安全工作目标" v-model="addGoalContent" type="textarea"></el-input>
        <div slot="footer" class="dialog-footer">
          <el-button @click="addGoalFlag=false">取 消</el-button>
          <el-button type="primary" @click="addGoalClick">确 定</el-button>
        </div>
      </el-dialog>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible=false">取 消</el-button>
        <el-button type="primary" @click="determineDialogClick">确 定</el-button>
      </div>
    </el-dialog>
    <!--新增,修改岗位对话框结束-->


    <!--选择参考检查表-->
    <el-dialog title="选择检查表" :visible.sync="chooseTableVisible">
      <div style="">
        <span style="float: left;margin-left: 10px;line-height: 40px;display: inline-block">分类：</span>
        <el-cascader
          :props="tableProp"
          :options="tableTagOption"
          clearable
          filterable
          :debounce="400"
          change-on-select
          v-model="chooseTable.tableTag"
          placeholder="请选择或输入关键字"
          style="width: 70%;float: left;display: inline-block">
        </el-cascader>
        <el-button type="primary" style="float: left;margin-left: 20px;display: inline-block" @click="searchReferTableClick(1)">搜索</el-button>
      </div>

      <el-table
        :data="referTableData"
        border
        tooltip-effect="light"
        highlight-current-row
        @row-click="referTableClick"
        style="width: 100%;margin-top: 20px;float: left">
        <el-table-column
          type="index"
          width="55"
          label-class-name="inner-header-style">
        </el-table-column>
        <el-table-column
          prop="name"
          label="检查表"
          show-overflow-tooltip
          label-class-name="inner-header-style">
        </el-table-column>
      </el-table>
      <div style="margin-top: 5px;float: left;width: 100%;margin-bottom: 10px">
        <el-pagination
          background
          layout="prev, pager, next"
          :current-page="tableCurrentPage"
          :total="tableTotalItem"
          @current-change="tablePageClick">
        </el-pagination>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="chooseTableVisible=false">取 消</el-button>
        <el-button type="primary" @click="determineReferTable">确 定</el-button>
      </div>
    </el-dialog>
    <!--参考检查表结束-->
    <!--查看PDF-->
    <el-dialog :title="pdfDialogTitle" width="100%" top="0vh" :center="true" :visible.sync="viewPDFDialogVisible">
      <iframe :src="pdfUrl" width="100%" height="810"></iframe>
    </el-dialog>
    <!--查看PDF结束-->
  </div>
</template>
<script>
  import dealData from '@/assets/functions/dealData'
  export default {
    name: 'postManage',
    data() {
      return {
        //搜索数据
        searchInput:'',
        //表格数据
        tableData:[],
        currentPage:0,
        totalNumber:0,

        //对话框数据
        dialogTitle:'添加岗位',
        dialogVisible:false,
        dialogData:{
          id:'',
          postName:'',
          sort : '',
          selfInspectCount:'',
          selfInspectTimeLimit:'',
          superviseInspectCount:'',
          superviseInspectTimeLimit:'',
          inspectCount:'',
          inspectCountTimeLimit:'',
          responseTable:[],
          dutyTable:[],
          goalTable:[],
        },
        dialogRules:{
          postName: [
            { required: true, message: '请输入岗位名称', trigger: 'change' }
          ],
          selfInspectCount: [
            { type: 'number', message: '必须为数字值', trigger: 'change' }
          ],
          superviseInspectCount: [
            { type: 'number', message: '必须为数字值', trigger: 'change' }
          ],
          inspectCount: [
            { type: 'number', message: '必须为数字值', trigger: 'change' }
          ],
        },
        frequenceOptions:[
          {value:0,label:'/周'},
          {value:1,label:'/月'},
          {value:2,label:'/季度'},
          {value:3,label:'/半年'},
          {value:4,label:'/年'},
        ],
        dialogForm:{
          selfInspectId:0,
          selfInspectTableName:''
        },
        //小对话框的数据
        addResponseTitle:'添加岗位责任',
        addResponseFlag:false,
        updateResponseData:{id:'',index:''},
        addResponseContent:'',

        addDutyTitle:'添加岗位主要任务',
        addDutyFlag:false,
        updateDutyData:{id:'',index:''},
        addDutyForm:{content:'',frequency:''},

        addGoalTitle:'添加安全工作目标',
        addGoalFlag:false,
        updateGoalData:{id:'',index:''},
        addGoalContent:'',
        //----------------------选择检查表对话框----------------------------
        chooseTableVisible:false,
        chooseTable:{
          tableTag:[],
          tagLoading:false
        },
        tableProp:{
          children: 'dangerInspectTableLabels',
          label: 'label',
          value:'label'
        },
        referTableData:[],
        currentReferTable:'',
        tableCurrentPage:0,
        tableTotalItem:0,
        //----------------------上传责任书和告知书-------------------
        limitNum:1,
        noticePDF:{name:'',url:''},
        responsePDF:{name:'',url:''},
        noticeTypeNo:'20',
        responseTypeNo:'17',
        upload:{
          params:{
            contentId:'',
            contentType:''
          },
          uploadUrl:'',
          uploadCookies:true,
          fileList : [],
        },
        loginLoading:'',
        //查看PDF
        viewPDFDialogVisible:false,
        pdfDialogTitle:'',
        pdfUrl:'',
      }

    },
    mounted:function () {
      this.searchTableData();
      //检查表的标签，树形的
      this.$store.dispatch("getTableTreeLabels",this.$tool.getStorage('LOGIN_USER').companyId);
    },
    computed:{
      tableTagOption:function () {
        return this.$store.state.hideDangerData.tableTreeLabels;
      },
    },
    watch:{
      $route(to, from){
        if(this.$route.name==='postManage'){
          this.searchTableData();
        }
      },
    },
    methods:{
      searchTableData:function () {
        this.currentPage=1;
        this.searchRequest();
      },
      currentPageClick:function (val) {
        this.currentPage=val;
        this.searchRequest();
      },
      searchRequest:function () {
        let params=new URLSearchParams;
        params.append("postName",this.searchInput);
        params.append("pageCurrent",this.currentPage);
        params.append("companyId",this.$tool.getStorage('LOGIN_USER').companyId);
        this.$http.post('post/find',params).then(function (res) {
          if (res.data.success) {
            this.totalNumber=res.data.data.total;
            this.tableData=res.data.data.list;
            this.tableData.forEach(function (item) {
              item.selfInspectFrequence=item.selfInspectCount+' 次'+this.frequenceOptions[item.selfInspectTimeLimit].label;
              item.superviseInspectFrequence=item.superviseInspectCount+' 次'+this.frequenceOptions[item.superviseInspectTimeLimit].label;
              item.inspectFrequence=item.inspectCount+' 次'+this.frequenceOptions[item.inspectCountTimeLimit].label;
            }.bind(this))
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message({
            showClose: true,
            message: '岗位查找失败',
            type: 'error'
          });
        }.bind(this));
      },
      addPostClick:function () {
        this.dialogData.postName='';
        this.dialogData.sort=9999;
        this.dialogData.selfInspectCount=0;
        this.dialogData.selfInspectTimeLimit=1;
        this.dialogData.superviseInspectCount=0;
        this.dialogData.superviseInspectTimeLimit=1;
        this.dialogData.inspectCount=0;
        this.dialogData.inspectCountTimeLimit=1;
        this.dialogTitle='添加岗位';
        this.dialogVisible=true;
        this.dialogForm.selfInspectTableName=''
        this.dialogForm.selfInspectId=0
      },
      itemUpdateClick:function (row) {
        this.dialogData.id=row.id;
        this.dialogData.postName=row.postName;
        this.dialogData.sort=row.sort;
        this.dialogData.selfInspectCount=row.selfInspectCount;
        this.dialogData.selfInspectTimeLimit=row.selfInspectTimeLimit;
        this.dialogData.superviseInspectCount=row.superviseInspectCount;
        this.dialogData.superviseInspectTimeLimit=row.superviseInspectTimeLimit;
        this.dialogData.inspectCount=row.inspectCount;
        this.dialogData.inspectCountTimeLimit=row.inspectCountTimeLimit;
        this.dialogTitle='修改岗位';
        this.dialogVisible=true;
        if(row.selfInspectId){
          this.getInspectTableName(row.selfInspectId)
        }else{
          this.dialogForm.selfInspectTableName=''
          this.dialogForm.selfInspectId=0
        }
        this.searchResponseTable();
      },
      itemDeleteClick:function (row) {
        this.$confirm('确定删除该岗位?', '提示', {
          confirmButtonText: '删除',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$http.post('post/delete?id='+row.id).then(function (res) {
            if (res.data.success) {
              this.searchTableData();
            }
          }.bind(this)).catch(function (err) {
            console.log(err);
            this.$message({
              showClose: true,
              message: '删除岗位失败',
              type: 'error'
            });
          }.bind(this));
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });
        });
      },
      determineDialogClick:function () {
        this.$refs['postForm'].validate((valid) => {
          if (valid) {
            let params=new URLSearchParams;
            params.append("postName",this.dialogData.postName);
            if(this.dialogData.sort){
              params.append("sort",this.dialogData.sort);
            }
            params.append("selfInspectCount",this.dialogData.selfInspectCount);
            params.append("selfInspectTimeLimit",this.dialogData.selfInspectTimeLimit);
            params.append("superviseInspectCount",this.dialogData.superviseInspectCount);
            params.append("superviseInspectTimeLimit",this.dialogData.superviseInspectTimeLimit);
            params.append("inspectCount",this.dialogData.inspectCount);
            params.append("inspectCountTimeLimit",this.dialogData.inspectCountTimeLimit);
            params.append("selfInspectId",this.dialogForm.selfInspectId);
            params.append("companyId",this.$tool.getStorage('LOGIN_USER').companyId);
            if(this.dialogTitle==='添加岗位'){
              this.determineDialogRequest(params,'post/add','添加成功','添加失败');
            }else{
              params.append("id",this.dialogData.id);
              this.determineDialogRequest(params,'post/update','修改成功','修改失败');
            }
          } else {
            this.$message.warning('请按提示填写表单内容');
            return false;
          }
        });

      },
      determineDialogRequest:function (params,url,successStr,errStr) {
        this.$http.post(url,params).then(function (res) {
          if (res.data.success) {
            this.$message.success(successStr);
            this.searchTableData();
            this.dialogVisible=false;
          }else {
            this.$message.warning(res.data.message);
          }
          this.dialogForm.selfInspectId=0
          this.dialogForm.selfInspectTableName=''
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message.error(errStr);
        }.bind(this));
      },
      //---------------------------------选择参考检查表------------------------------------
      //-PS-这里的标签都是用来选参考检查表
      //搜索参考检查表
      searchReferTableClick:function (page) {
        this.tableCurrentPage=page;
        let params={pageCurrent:page,pageSize:10,labels:this.chooseTable.tableTag,dangerInspect:{companyId:this.$tool.getStorage('LOGIN_USER').companyId}};
        this.referTableData=[];
        this.$http.post('danger/inspect/find', params).then(function (res) {
          if (res.data.success) {
            this.tableTotalItem=res.data.data.total;
            this.referTableData=res.data.data.list;
          }
          this.chooseTable.tagLoading = false
        }.bind(this)).catch(function (err) {
          console.log(err);
        });
      },
      //翻页
      tablePageClick:function (val) {
        if(val){
          this.searchReferTableClick(val);
        }
      },
      //查看参考检查表
      viewReferTable:function (row) {

      },
      //选中某一行
      referTableClick:function (row) {
        if(row){
          this.dialogForm.selfInspectTableName=row.name
          this.dialogForm.selfInspectId=row.id
          this.currentReferTable=row;
        }else {
          this.currentReferTable='';
        }
      },
      //确定参考检查表
      determineReferTable:function () {
        if(this.currentReferTable){
          // this.$router.push({name:'investigationNewWorkflow',params:{referPlanId:this.currentReferTable.id,referName:this.currentReferTable.name,investigationType:this.investigationType,typeName:this.inpectTypeArr[this.investigationType]}})
        }else{
          this.$message.warning('请选择检查表!');
        }
        this.chooseTableVisible=false;
      },

      getInspectTableName:function (inspectId) {
        let params={dangerInspect:{id:inspectId,companyId:this.$tool.getStorage('LOGIN_USER').companyId}};
        this.$http.post("/danger/inspect/find",params).then(function (res) {
          if(res.data.success){
            if(res.data.data.size>0){
              this.dialogForm.selfInspectTableName=res.data.data.list[0].name;
              this.dialogForm.selfInspectId = inspectId;
              console.log(this.dialogForm.selfInspectTableName)
            }else{}
          }else{
            this.$message.error("获取检查表名字失败")
          }
        }.bind(this)).catch(function (err) {
          console.log(err)
        })
      },

      //三个表格的函数
      //查找三个表格和告知书、责任书
      searchResponseTable:function () {
        this.dialogData.responseTable=[];
        this.$http.post('postResponsible/find?postId='+this.dialogData.id+'&responsibleType=0').then(function (res) {
          if (res.data.success) {
           this.dialogData.responseTable=res.data.data;
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
        }.bind(this));
        this.dialogData.dutyTable=[];
        this.$http.post('postResponsible/find?postId='+this.dialogData.id+'&responsibleType=1').then(function (res) {
          if (res.data.success) {
            this.dialogData.dutyTable=res.data.data;
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
        }.bind(this));
        this.dialogData.goalTable=[];
        this.$http.post('postResponsible/find?postId='+this.dialogData.id+'&responsibleType=2').then(function (res) {
          if (res.data.success) {
            this.dialogData.goalTable=res.data.data;
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
        }.bind(this));
        //获取告知书
        this.$http.post('postResponsible/findFile?postId='+this.dialogData.id+'&responsibleType=3').then(function (res) {
          if (res.data.success) {
            if(res.data.data.length){
              this.noticePDF.name='安全风险告知书';
              this.noticePDF.url=res.data.data[0].filePath;
            }else{
              this.noticePDF.name='';
              this.noticePDF.url='';
            }
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
        }.bind(this));
        //获取责任书
        this.$http.post('postResponsible/findFile?postId='+this.dialogData.id+'&responsibleType=4').then(function (res) {
          if (res.data.success) {
            if(res.data.data.length){
              this.responsePDF.name='安全生产承诺书';
              this.responsePDF.url=res.data.data[0].filePath;
            }else {
              this.responsePDF.name='';
              this.responsePDF.url='';
            }
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
        }.bind(this));
      },
      //添加操作
      addResponseClick:function () {
        if(this.addResponseTitle==='添加岗位责任'){
          let params=new URLSearchParams;
          params.append("postId",this.dialogData.id);
          params.append("responsibleType",0);
          params.append("content",this.addResponseContent);
          this.$http.post('postResponsible/add',params).then(function (res) {
            if (res.data.success) {
              this.dialogData.responseTable.push({id:res.data.data.id,content:this.addResponseContent});
              this.addResponseFlag=false;
            }else{
              this.$message.warning('添加失败');
              this.addResponseFlag=false;
            }
          }.bind(this)).catch(function (err) {
            console.log(err);
          }.bind(this));
        }else {
          let params=new URLSearchParams;
          params.append("id",this.updateResponseData.id);
          params.append("content",this.addResponseContent);
          this.$http.post('postResponsible/update',params).then(function (res) {
            if (res.data.success) {
              this.dialogData.responseTable.splice(this.updateResponseData.index,1,{id:this.updateResponseData.id,content:this.addResponseContent});
              this.addResponseFlag=false;
            }else{
              this.$message.warning('修改失败');
              this.addResponseFlag=false;
            }
          }.bind(this)).catch(function (err) {
            console.log(err);
          }.bind(this));
        }
      },
      addDutyClick:function () {
        if(this.addDutyTitle==='添加岗位主要任务'){
          let params=new URLSearchParams;
          params.append("postId",this.dialogData.id);
          params.append("responsibleType",1);
          params.append("content",this.addDutyForm.content);
          params.append("frequency",this.addDutyForm.frequency);
          this.$http.post('postResponsible/add',params).then(function (res) {
            if (res.data.success) {
              this.dialogData.dutyTable.push({id:res.data.data.id,content:this.addDutyForm.content,frequency:this.addDutyForm.frequency});
              this.addDutyFlag=false;
            }else{
              this.$message.warning('添加失败');
              this.addDutyFlag=false;
            }
          }.bind(this)).catch(function (err) {
            console.log(err);
          }.bind(this));
        }else{
          let params=new URLSearchParams;
          params.append("id",this.updateDutyData.id);
          params.append("content",this.addDutyForm.content);
          params.append("frequency",this.addDutyForm.frequency);
          this.$http.post('postResponsible/update',params).then(function (res) {
            if (res.data.success) {
              this.dialogData.dutyTable.splice(this.updateDutyData.index,1,{id:this.updateDutyData.id,content:this.addDutyForm.content,frequency:this.addDutyForm.frequency});
              this.addDutyFlag=false;
            }else{
              this.$message.warning('修改失败');
              this.addDutyFlag=false;
            }
          }.bind(this)).catch(function (err) {
            console.log(err);
          }.bind(this));
        }

      },
      addGoalClick:function () {
        if(this.addGoalTitle==='添加安全工作目标'){
          let params=new URLSearchParams;
          params.append("postId",this.dialogData.id);
          params.append("responsibleType",2);
          params.append("content",this.addGoalContent);
          this.$http.post('postResponsible/add',params).then(function (res) {
            if (res.data.success) {
              this.dialogData.goalTable.push({id:res.data.data.id,content:this.addGoalContent});
              this.addGoalFlag=false;
            }else{
              this.$message.warning('添加失败');
              this.addGoalFlag=false;
            }
          }.bind(this)).catch(function (err) {
            console.log(err);
          }.bind(this));
        }else {
          let params=new URLSearchParams;
          params.append("id",this.updateGoalData.id);
          params.append("content",this.addGoalContent);
          this.$http.post('postResponsible/update',params).then(function (res) {
            if (res.data.success) {
              this.dialogData.goalTable.splice(this.updateGoalData.index,1,{id:this.updateGoalData.id,content:this.addGoalContent});
              this.addGoalFlag=false;
            }else{
              this.$message.warning('修改失败');
              this.addGoalFlag=false;
            }
          }.bind(this)).catch(function (err) {
            console.log(err);
          }.bind(this));
        }

      },

      //删除操作
      deleteResponseClick:function (index,row) {
        let params=new URLSearchParams;
        params.append("id",row.id);
        this.$http.post('postResponsible/delete',params).then(function (res) {
          if (res.data.success) {
            this.dialogData.responseTable.splice(index,1);
          }else{
            this.$message.warning('删除失败');
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
        }.bind(this));
      },
      deleteDutyClick:function (index,row) {
        let params=new URLSearchParams;
        params.append("id",row.id);
        this.$http.post('postResponsible/delete',params).then(function (res) {
          if (res.data.success) {
            this.dialogData.dutyTable.splice(index,1);
          }else{
            this.$message.warning('删除失败');
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
        }.bind(this));

      },
      deleteGoalClick:function (index,row) {
        let params=new URLSearchParams;
        params.append("id",row.id);
        this.$http.post('postResponsible/delete',params).then(function (res) {
          if (res.data.success) {
            this.dialogData.goalTable.splice(index,1);
          }else{
            this.$message.warning('删除失败');
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
        }.bind(this));

      },
      //上传之前的响应事件
      beforeUpload:function(file){
        const isPDF=file.type === 'application/pdf';
        if (!isPDF) {
          this.$message.warning('上传文件只能是 PDF 格式');
        }
        return isPDF;
      },
      //上传告知书
      ossUploaNoticedPdf:function (item) {
        this.upload.params.contentType=this.noticeTypeNo;//获取文件类型编码
        this.loginLoading=this.$loading({
          lock: true,
          text: '上传中',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.5)'
        });
        //获取该文件对应的sign
        this.$http.get('sys/oss/sign?contentId='+ this.$tool.getStorage('LOGIN_USER').userId +'&contentType='+this.upload.params.contentType+'&realName='+item.file.name).then(function (res) {
          if(res.data){
            let params=new FormData();
            params.append("name",item.file.name);
            params.append("key",res.data.dir + item.file.name);
            params.append("policy",res.data.policy);
            params.append("OSSAccessKeyId",res.data.accessid);
            params.append('success_action_status','200');
            params.append("callback",res.data.callback);
            params.append("signature",res.data.signature);
            params.append("file",item.file);
            this.fileHttp.post('',params,{headers: {'Content-Type': 'multipart/form-data'}}).then(function (res) {
              if(res.data.file){
                let resultStr=dealData.decode(res.data.file);
                let resultJson=JSON.parse(resultStr);
                let params = new URLSearchParams;
                params.append("content",resultJson.fId);
                params.append("responsibleType",3);
                params.append("postId",this.dialogData.id);
                this.$http.post('postResponsible/addFile', params).then(function (res) {
                  if (res.data.success) {
                    this.noticePDF.name='安全风险告知书';
                    this.noticePDF.url=res.data.data.filePath;
                    this.loginLoading.close();//关闭加载
                    this.$message.success('上传成功');
                  }
                }.bind(this)).catch(function (err) {
                  this.loginLoading.close();//关闭加载
                  this.$message.error('上传失败');
                  console.log(err);
                }.bind(this));
              }else{
                this.loginLoading.close();//关闭加载
                this.$message.error('上传失败');
              }
            }.bind(this))
          }
        }.bind(this)).catch(function (err) {
          this.loginLoading.close();//关闭加载
          this.$message.error('获取唯一标识失败');
        }.bind(this));
      },
      //上传责任书
      ossUploaResponsedPdf:function (item) {
        this.upload.params.contentType=this.responseTypeNo;//获取文件类型编码
        this.loginLoading=this.$loading({
          lock: true,
          text: '上传中',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.5)'
        });
        //获取该文件对应的sign
        this.$http.get('sys/oss/sign?contentId='+ this.$tool.getStorage('LOGIN_USER').userId +'&contentType='+this.upload.params.contentType+'&realName='+item.file.name).then(function (res) {
          if(res.data){
            let params=new FormData();
            params.append("name",item.file.name);
            params.append("key",res.data.dir + item.file.name);
            params.append("policy",res.data.policy);
            params.append("OSSAccessKeyId",res.data.accessid);
            params.append('success_action_status','200');
            params.append("callback",res.data.callback);
            params.append("signature",res.data.signature);
            params.append("file",item.file);
            this.fileHttp.post('',params,{headers: {'Content-Type': 'multipart/form-data'}}).then(function (res) {
              if(res.data.file){
                let resultStr=dealData.decode(res.data.file);
                let resultJson=JSON.parse(resultStr);
                let params = new URLSearchParams;
                params.append("content",resultJson.fId);
                params.append("responsibleType",4);
                params.append("postId",this.dialogData.id);
                this.$http.post('postResponsible/addFile', params).then(function (res) {
                  if (res.data.success) {
                    this.responsePDF.name='安全生产承诺书';
                    this.responsePDF.url=res.data.data.filePath;
                    this.loginLoading.close();//关闭加载
                    this.$message.success('上传成功');
                  }
                }.bind(this)).catch(function (err) {
                  this.loginLoading.close();//关闭加载
                  this.$message.error('上传失败');
                  console.log(err);
                }.bind(this));
              }else{
                this.loginLoading.close();//关闭加载
                this.$message.error('上传失败');
              }
            }.bind(this))
          }
        }.bind(this)).catch(function (err) {
          this.loginLoading.close();//关闭加载
          this.$message.error('获取唯一标识失败');
        }.bind(this));
      },
      viewPdf:function (name,url) {
        if(url){
          this.pdfDialogTitle=name;
          this.pdfUrl=url;
          this.viewPDFDialogVisible=true;
        }
      }
    }
  }
</script>
<style>
</style>
