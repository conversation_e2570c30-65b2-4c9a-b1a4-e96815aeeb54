<template>
  <div id="recordEmerMaterial">
    <div class="background-style">
      <el-button type="primary" icon="el-icon-d-arrow-left" size="medium" style="margin: 10px"
                 @click="backClick"></el-button>
      <el-radio-group v-model="recordType" style="margin: 10px;" fill=" #0168B7">
        <el-radio-button v-for="item in typeOptions" :label="item.value" :id="item.value" :key="item.value">&nbsp&nbsp&nbsp{{item.label}}&nbsp&nbsp&nbsp</el-radio-button>
      </el-radio-group>
      <!--查询条件和功能按钮-->
      <div style="width: 100%;margin-top: 20px">
        <div style="margin-left: 10px;display: inline-block">
          时间：
          <el-date-picker
            v-model="search.startData"
            type="date"
            placeholder="选择日期">
          </el-date-picker>
          至
          <el-date-picker
            v-model="search.endData"
            type="date"
            placeholder="选择日期">
          </el-date-picker>
        </div>
        <div style="margin-left: 10px;display: inline-block">
          <el-button class="search-btn" icon="el-icon-search" @click="searchClick">搜 索</el-button>
        </div>
        <div style="margin-right: 20px;display: inline-block;float: right">
          <el-button type="warning" @click="addRecord(0)" v-show="recordType===0" style="margin: 10px">出库登记</el-button>
          <el-button type="success" @click="addRecord(1)" v-show="recordType===2" style="margin: 10px">入库登记</el-button>
          <el-button type="primary" style="margin: 10px" @click="loadExcel">导出表格</el-button>
        </div>

      </div>
      <!--查询条件和功能按钮结束-->
      <!--表格区-->
      <div style="width: 100%;margin-top: 10px" >
        <!--出库表格-->
        <div style="padding: 10px 10px 20px 10px">
          <el-table
            :data="logsData"
            border
            highlight-current-row
            :row-class-name="judgeBalanceRow"
            style="width: 100%">
            <el-table-column type="expand" label-class-name="header-style" v-if="recordType===0">
              <template slot-scope="props">
                <el-table
                  :data="props.row.entryLogs"
                  highlight-current-row
                  style="width: 100%">
                  <el-table-column
                    prop="num"
                    align="center"
                    label-class-name="inner-header-style"
                    width="50">
                  </el-table-column>
                  <el-table-column
                    prop="operationType"
                    label="出/入库"
                    width="171"
                    align="center"
                    label-class-name="inner-header-style">
                    <template slot-scope="scope">
                      <el-tag v-if="scope.row.operationType==0" type="danger">
                        出库
                      </el-tag>
                      <el-tag v-else-if="scope.row.operationType==1" type="success">
                        归还
                      </el-tag>
                      <el-tag v-else="scope.row.operationType==2" type="success">
                        入库
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="createDate"
                    label="日期"
                    width="160"
                    :formatter="dateFormat"
                    align="center"
                    label-class-name="inner-header-style">
                  </el-table-column>
                  <el-table-column
                    prop="count"
                    label="入库数量"
                    width="120"
                    align="center"
                    label-class-name="inner-header-style">
                  </el-table-column>
                  <el-table-column
                    prop="loss"
                    label="损耗数量"
                    width="120"
                    align="center"
                    label-class-name="inner-header-style">
                  </el-table-column>
                  <el-table-column
                    prop="goodsUnit"
                    label="单位"
                    width="80"
                    align="center"
                    label-class-name="inner-header-style">
                  </el-table-column>
                  <el-table-column
                    prop="remark"
                    label="出入说明"
                    min-width="150"
                    align="center"
                    label-class-name="inner-header-style">
                  </el-table-column>
                  <el-table-column
                    prop="registerUser"
                    label="登记人"
                    width="125"
                    align="center"
                    label-class-name="inner-header-style">
                  </el-table-column>
                  <el-table-column fixed="right" label="操作" label-class-name="inner-header-style" align="center" width="160">
                    <template slot-scope="scope">
                      <el-button size="mini" type="warning" @click="putInGoods(scope.row,'update')" style="float: left">修改</el-button>
                      <el-button size="mini" type="danger" style="float: left" @click="deleteLogDialog(scope.row,scope.$index)">删除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </template>
            </el-table-column>
            <el-table-column
              label="序号"
              prop="num"
              width="60"
              align="center"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="operationType"
              label="出/入库"
              width="120"
              align="center"
              label-class-name="header-style">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.operationType==0" type="danger">
                  出库
                </el-tag>
                <el-tag v-else-if="scope.row.operationType==1" type="success">
                  归还
                </el-tag>
                <el-tag v-else="scope.row.operationType==2" type="success">
                  入库
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column
              prop="createDate"
              label="日期"
              width="160"
              :formatter="dateFormat"
              align="center"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="goodsName"
              label="物资名称"
              width="120"
              align="center"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="count"
              label="数量"
              width="120"
              align="center"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="goodsUnit"
              label="单位"
              width="80"
              align="center"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="entryAll"
              label="归还总数"
              width="120"
              align="center"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="lossAll"
              label="损耗总数"
              width="120"
              align="center"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="remark"
              label="出入说明"
              min-width="150"
              align="center"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="registerUser"
              label="登记人"
              width="120"
              align="center"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column fixed="right" label="操作" label-class-name="header-style" align="center" width="160">
              <template slot-scope="scope">
                <div v-if="scope.row.operationType===0">
                  <el-button size="mini" type="primary" @click="putInGoods(scope.row)" style="float: left" v-if="scope.row.unbalanceFlag">归还</el-button>
                  <el-button size="mini" type="danger" style="float: left" @click="deleteLogDialog(scope.row,scope.$index)">删除</el-button>
                </div>
                <div v-else>
                  <el-button size="mini" type="success" @click="updateGoods(scope.row,scope.$index)" style="float: left">修改</el-button>
                  <el-button size="mini" type="danger" style="float: left" @click="deleteLogDialog(scope.row,scope.$index)">删除</el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div style="margin-top: 10px">
          <el-pagination
            background
            layout="prev, pager, next"
            :page-size="10"
            :current-page="currentPage"
            :total="totalItem"
            @current-change="currentPageClick">
          </el-pagination>
        </div>

      </div>
      <!--表格区结束-->
      <!--出入库对话框-->
      <el-dialog :title="dialogTitle" center :visible.sync="dialogFormVisible">
        <div>
          <el-form :model="dialogForm" :rules="rules" ref="dialogForm" label-width="100px" class="demo-ruleForm">
            <el-form-item label="物品名称">
              <el-input v-model="dialogForm.goodsName" readonly="readonly"></el-input>
            </el-form-item>
            <!--<el-form-item label="出/入库">-->
              <!--<el-select v-model="dialogForm.type" readonly="readonly" style="width: 100%">-->
                <!--<el-option label="出库" value="0"></el-option>-->
                <!--<el-option label="入库" value="2"></el-option>-->
              <!--</el-select>-->
            <!--</el-form-item>-->
            <el-form-item label="数量">
              <el-input v-model="dialogForm.num" type="number"></el-input>
            </el-form-item>
            <el-form-item label="单位">
              <el-input v-model="dialogForm.unit" readonly="readonly"></el-input>
            </el-form-item>
            <el-form-item label="出入说明">
              <el-dropdown @command="editExplain">
                <el-button type="primary" size="small">
                  说明参考<i class="el-icon-arrow-down el-icon--right"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item v-for="item in selectOptions" :key="item.id" :command="item.content">{{item.name}}</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
              <el-input v-model="dialogForm.remarks"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button style="float: right;margin-right: 20px;" @click="cancelClick">取消</el-button>
              <el-button type="primary" @click="saveOrEdit()" style="float: right;margin-right: 20px">保存</el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-dialog>
      <!--出入库对话框结束-->

      <!--归还对话框-->
      <el-dialog :title="putInTitle" center :visible.sync="putInVisible">
        <div>
          <el-form :model="putInForm" :rules="putInRules" ref="putInForm" label-width="100px" class="demo-ruleForm">
            <el-form-item label="物品名称">
              <el-input v-model="dialogForm.goodsName" readonly="readonly"></el-input>
            </el-form-item>
            <el-form-item label="单位">
              <el-input v-model="dialogForm.unit" readonly="readonly"></el-input>
            </el-form-item>
            <el-form-item label="入库数量" prop="num">
              <el-input v-model="putInForm.num" type="number"></el-input>
            </el-form-item>
            <el-form-item label="损耗数量" prop="lossNum">
              <el-input v-model="putInForm.lossNum" type="number"></el-input>
            </el-form-item>
            <el-form-item label="出入说明" prop="remarks">
              <el-input v-model="putInForm.remarks"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button style="float: right;margin-right: 20px;" @click="$refs['putInForm'].resetFields();putInVisible=false;">取消</el-button>
              <el-button type="primary" @click="putInRequest" style="float: right;margin-right: 20px">保存</el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-dialog>
      <!--归还对话框结束-->

      <el-dialog
        title="提示"
        :visible.sync="dialogDelLogVisible"
        width="30%"
        center>
        <span>确认删除该物资操作记录</span>
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogDelLogVisible = false">取 消</el-button>
          <el-button type="primary" @click="deleteLog()">确 定</el-button>
        </span>
      </el-dialog>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'recordEmerMaterial',
    data() {
      return {
        //---------------------查询数据----------------------
        recordDateRange: '',
        recordType: 0,
        typeOptions: [
          {value: 0, label: '出库记录'},
          {value: 2, label: '入库记录'}
        ],
        //-------------------表格数据------------------------
        logsData: [],
        currentPage:0,
        totalItem:0,
        //------------------对话框数据-----------------------
        dialogDelLogVisible:false,
        deleteLogId:0,
        dialogTitle: '',
        dialogFormVisible: false,
        dialogForm: {
          goodsName: '',
          type: '0',
          num: 1,
          unit: '',
          remarks: '',
          emgGoodsLogId:0,
        },
        rules: {
          num: [{required: true, message: '请选择输入物资数量', trigger: 'change', min: 0, type: 'number'}],
        },
        //说明参考数据
        selectOptions:[
          {id:'explain01',name:'调拨',content:'调拨'},
          {id:'explain02',name:'报废',content:'报废'},
          {id:'explain03',name:'领用',content:'领用'}
        ],
        //---------------------入库数据------------------------
        putInTitle:'',
        putInVisible:false,
        putInForm:{
          goodsLogId:'',
          oriGoodsLogId:'',//修改入库时，已有的对应出库ID
          total:'',
          num:'',
          lossNum:'',
          remarks:''
        },
        putInRules:{},

        emgGoodsId: 0,
        addLog: true,
        editIndex:0,
        deleteIndex:0,
        search:{
          startData:null,
          endData:null,
        },

      }
    },
    watch: {
      $route(to, from) {
        if (from.name === 'emerMaterial' && this.$route.name === 'recordEmerMaterial') {
          this.emgGoodsId = this.$route.params.emgGoodsId
          this.dialogForm.goodsName = this.$route.params.goodsName
          this.dialogForm.unit = this.$route.params.unit
          this.loadLogs(-1, null, null)
        }
      },
      recordType:function (val) {
        this.loadLogs(-1, null, null)
      }
    }
    ,
    mounted: function () {
      this.emgGoodsId = this.$route.params.emgGoodsId
      this.dialogForm.goodsName = this.$route.params.goodsName
      this.dialogForm.unit = this.$route.params.unit

      this.loadLogs(-1, null, null)
    },
    methods: {
      dateFormat(row, column) {
        //.replace(/年|月/g, "-").replace(/日/g, " ")
        return new Date(row.createDate).Format("yyyy-MM-dd hh:mm").toLocaleString();
      },
      backClick: function () {
        this.$router.go(-1);
      },
      addRecord: function (val) {
        if(val===0){
          this.dialogTitle = '出库登记';
          this.dialogForm.type='0'
          this.addLog = true
          this.dialogForm.num=0
          this.dialogForm.remarks=''
          this.dialogFormVisible = true;
        }else{
          this.dialogTitle = '入库登记';
          this.dialogForm.type='2'
          this.addLog = true
          this.dialogForm.num=0
          this.dialogForm.remarks=''
          this.dialogFormVisible = true;
        }

      },
      //------------------------表格函数-----------------
      searchClick: function () {
        //console.info(this.search)
        this.loadLogs(this.recordType,this.search.startData,this.search.endData)
      },
      updateGoods: function (row,index) {
        this.dialogTitle = '修改记录';
        this.dialogFormVisible = true;
        this.addLog = false
        this.dialogForm.type=row.operationType+""
        this.dialogForm.num=row.count
        this.dialogForm.remarks=row.remark
        this.dialogForm.emgGoodsLogId=row.id
        this.editIndex=index
      },
      deleteLogDialog: function (row,index) {
        this.dialogDelLogVisible=true
        this.deleteLogId=row.id
        this.deleteIndex=index
      },
      putInGoods:function (row,type) {
        if(type==='update'){
          this.putInTitle='修改记录';
          this.putInForm.total=row.count+row.loss;
          this.putInForm.oriGoodsLogId=row.goodsLogId;
          this.putInForm.goodsLogId=row.id;
          this.putInForm.num=row.count;
          this.putInForm.lossNum=row.loss;
          this.putInForm.remarks=row.remark;
          this.putInVisible=true;
        }else{
          this.putInForm.total=row.count-row.entryAll-row.lossAll;
          if(this.putInForm.total){
            this.putInTitle='归还登记';
            this.putInForm.goodsLogId=row.id;
            this.putInForm.num=this.putInForm.total;
            this.putInForm.lossNum=0;
            this.putInForm.remarks='';
            this.putInVisible=true;
          }else{
            this.$message.success("该条记录，出入库已平衡！")
          }
        }

      },
      judgeBalanceRow:function ({row}) {//判断该行是否为出库且出入不平衡
        if(this.recordType===0&&row.operationType===0){
          if((row.count-row.entryAll-row.lossAll)>0){
            return 'warning-row';
          }else{
            return '';
          }
        }else{
          return '';
        }
      },
      //------------------------对话框函数-------------------
      deleteLog:function(){
        var params=new URLSearchParams()
        params.append("id",this.deleteLogId)
        this.$http.post('emgGoodsLog/delete',params).then(function (res) {
          if(res.data.success){
            this.$message.success("删除成功")
            this.searchClick();
          }else{
            this.$message.error("删除失败")
          }
          this.dialogDelLogVisible=false
        }.bind(this))
      },
      cancelClick: function () {
        this.dialogTitle = '';
        this.dialogFormVisible = false;
      },
      //出入库，对应的修改
      saveOrEdit: function () {
        this.$refs['dialogForm'].validate((valid) => {
          if (valid) {
            var params = new URLSearchParams()
            params.append("goodsId", this.emgGoodsId)
            params.append("operationType", this.dialogForm.type)
            params.append("remark", this.dialogForm.remarks)
            params.append("count", this.dialogForm.num)
            if (this.addLog) {
              this.$http.post("emgGoodsLog/add", params).then(function (res) {
                if(res.data.success){
                  this.$message.success("添加成功")
                  this.logsData.splice(0,0,res.data.data)
                  this.searchClick();
                }else{
                  this.$message.error("添加失败")
                }
                this.dialogFormVisible=false
              }.bind(this)).catch(function (err) {
                console.log(err)
              })
            } else {
              params.append("id",this.dialogForm.emgGoodsLogId)
              this.$http.post("emgGoodsLog/update", params).then(function (res) {
                if(res.data.success){
                  this.$message.success("更新成功")
                  this.logsData.splice(this.editIndex,1,res.data.data)
                }else{
                  this.$message.error("更新失败")
                }
                this.dialogFormVisible=false
              }.bind(this)).catch(function (err) {
                console.log(err)
              })
            }
          } else {
            console.log('error submit!!');
            return false;
          }
        })
      },
      loadLogs: function (operType, startDate, endDate) {
        var params = new URLSearchParams()
        params.append("operationType",this.recordType)
        if (startDate) {
          params.append("startDate", startDate)
        }
        if (endDate) {
          params.append("endDate", endDate)
        }
        params.append("goodsId",this.emgGoodsId)
        this.$http.post('emgGoodsLog/find', params).then(function (res) {
          if (res.data.success) {
            this.currentPage=1
            this.totalItem=res.data.data.total
            this.logsData = res.data.data.list
            if(this.recordType === 0 ){
              this.logsData.forEach(function (item,index) {
                item.num=index+1;
                item.unbalanceFlag=(item.count-item.entryAll-item.lossAll)>0;
              })
            }else{
              this.logsData.forEach(function (item,index) {
                item.num=index+1;
              })
            }

          }
        }.bind(this)).catch(function (err) {

        })
      },
      //翻页
      currentPageClick:function (val) {
        if(val){
          var params = new URLSearchParams()
          params.append("operationType",this.recordType)
          if (this.search.startData) {
            params.append("startDate", this.search.startData)
          }
          if (this.search.startData) {
            params.append("endDate",this.search.endData)
          }
          params.append("goodsId",this.emgGoodsId)
          params.append("pageCurrent",val)
          this.$http.post('emgGoodsLog/find', params).then(function (res) {
            if (res.data.success) {
              this.currentPage=val
              this.totalItem=res.data.data.total
              this.logsData = res.data.data.list
              if(this.recordType === 0 ){
                this.logsData.forEach(function (item,index) {
                  item.num=(Number(val)-1)*10+index+1;
                  item.unbalanceFlag=(item.count-item.entryAll-item.lossAll)>0;
                })
              }else{
                this.logsData.forEach(function (item,index) {
                  item.num=(Number(val)-1)*10+index+1;
                })
              }

            }
          }.bind(this)).catch(function (err) {

          })
        }
      },
      //添加或修改归还
      putInRequest:function () {
        if(((Number(this.putInForm.num)+Number(this.putInForm.lossNum))>Number(this.putInForm.total))&&this.putInTitle==='归还登记'){
          this.$message.warning("归还和损耗的数量大于出库数量，请核对后提交")
        }else{
          let params=new URLSearchParams;
          params.append("goodsId", this.emgGoodsId)
          params.append("operationType", 1)
          params.append("goodsName",this.dialogForm.goodsName)
          params.append("goodsUnit",this.dialogForm.unit)
          params.append("remark", this.putInForm.remarks)
          params.append("count", this.putInForm.num)
          params.append("loss",this.putInForm.lossNum)
          if(this.putInTitle==='修改记录'){
            params.append("id",this.putInForm.goodsLogId);
            params.append("goodsLogId",this.putInForm.oriGoodsLogId);
            this.$http.post("emgGoodsLog/update", params).then(function (res) {
              if(res.data.success){
                this.$message.success("修改成功")
                this.searchClick();
              }else{
                this.$message.error("修改失败")
              }
              this.putInVisible=false
            }.bind(this)).catch(function (err) {
              console.log(err)
            })
          }else{
            params.append("goodsLogId",this.putInForm.goodsLogId);
            this.$http.post("emgGoodsLog/add", params).then(function (res) {
              if(res.data.success){
                this.$message.success("添加成功")
                this.logsData.splice(0,0,res.data.data)
                this.searchClick();
              }else{
                this.$message.error("添加失败")
              }
              this.putInVisible=false
            }.bind(this)).catch(function (err) {
              console.log(err)
            })
          }
        }

      },
      editExplain:function (content) {
        this.dialogForm.remarks=content;
      },
      //-------------------------导出表格-----------------------------
      loadExcel:function () {
        const loading = this.$loading({
          lock: true,
          text: 'Loading',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        var params = new URLSearchParams()
        params.append("goodsId", this.emgGoodsId)
        params.append("operationType",0)
        this.$http({ // 用axios发送post请求
          method: 'post',
          url: 'emgGoodsLog/downGoodsLogFile', // 请求地址
          data: params, // 参数
          responseType: 'blob' // 表明返回服务器返回的数据类型
        }).then((res) => { // 处理返回的文件流
          //console.info(res)
          loading.close()
          const content = res
          const elink = document.createElement('a') // 创建a标签
          elink.download = this.dialogForm.goodsName+'使用记录.xls' // 文件名
          elink.style.display = 'none'
          const blob = new Blob([res.data])
          elink.href = URL.createObjectURL(blob)
          document.body.appendChild(elink)
          elink.click() // 触发点击a标签事件
          document.body.removeChild(elink)
        })
      },

    }
  }
</script>
<style>
  .el-table .warning-row {
    color: #f00;
  }

</style>
