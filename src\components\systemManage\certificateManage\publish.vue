<template>
  <div id="trainingPlanIndex">
    <div class="background-style">

      <!--搜索区-->
      <div style="margin:10px 0 10px 10px;float: left">
        <el-button
          @click="addBtnClickHandle"
          type="success" icon="el-icon-plus" >新增</el-button>
      </div>

      <!--表格区-->
      <div style="width: 100%;">
        <div style="margin: 10px">
          <el-table
            border
            :data="tableData.list"
            style="width: 100%">
            <el-table-column
              type="index"
              label="编号"
              width="100"
              align="center"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="deptName"
              label="名称"
              min-width="150"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              fixed="right" label="操作"
              label-class-name="header-style"
              align="center" width="300">
              <template slot-scope="scope">
                <template>
                  <el-button size="mini" type="primary" @click="itemUpdateClick(scope.row)">修改</el-button>
                  <el-button size="mini" type="danger" @click="itemDeleteClick(scope.row)">删除</el-button>
                </template>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div >
          <el-pagination
            background
            layout="prev, pager, next"
            :current-page="tableData.pageNum"
            :page-size="form.pageSize"
            :total="tableData.total"
            @current-change ="disasterPageChangeHandle">
          </el-pagination>
        </div>
      </div>


      <!--对话框-->
      <el-dialog
        title="新增发证单位"
        :visible.sync="dialog.isShow"
        width="60%"
        :before-close="handleClose">
        <el-form label-width="100px">
          <el-row  class="row">
            <el-col :span="24">
              <el-form-item label="名称：">
                <el-input v-model="dialog.form.deptName"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row  class="row">
            <el-col :span="2">
              <el-form-item>
                <el-button
                  type="danger"  size="mini"
                  @click="dialogOkBtnClickHandle">确定</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-dialog>
    </div>
  </div>
</template>
<script>
  export default {
    data() {
      return {
        form : {
          // 当前页
          pageCurrent : 1,
          // 页数大小
          pageSize : 5,
        },
        tableData : {},
        // 对话框
        dialog : {
          // 是否显示
          isShow : false,
          form : {
            id : '',
            // 名称
            deptName : '',
          },
        },
      }
    },
    mounted(){
      this.init();
    },
    watch:{
      $route(to,from){
        if(to.name === 'certificateManagePublish') {
          this.init();
        }
      }
    },
    methods:{
      // 初始化
      init(){
        // 搜索
        this.searchBtnClickHandle();
      },
      clear(){
        this.dialog.form = this.$tool.clearObj({}, this.dialog.form);
      },
      // 分页
      disasterPageChangeHandle(page){
        this.form.pageCurrent = page;
        this.searchBtnClickHandle();
      },
      // 搜索按钮
      searchBtnClickHandle(){
        this.clear();
        this.$store.dispatch('eduCertIssueDeptFind', this.form).then(function(res){
          if(res.success){
            this.tableData = res.data;
          } else {
            this.$message({
              type : 'error',
              message : res.message || '错误'
            })
          }
        }.bind(this));
      },
      // 修改
      itemUpdateClick(row){
        this.$tool.cloneObj(this.dialog.form, row);
        this.dialog.isShow = true;
      },
      // 删除按钮
      itemDeleteClick(row){
        this.$confirm('此操作将永久删除, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
          .then(function(){
            this.$store.dispatch('eduCertIssueDeptDelete', {
              id : row.id
            }).then(function(res){
              if(res.success){
                this.$message({
                  type : 'success',
                  message : '删除成功'
                })
                this.searchBtnClickHandle();
              } else {
                this.$message({
                  type : 'error',
                  message : res.message || '删除失败！！'
                })
              }
            }.bind(this))
          }.bind(this))
      },
      // 新增按钮
      addBtnClickHandle(){
        this.dialog.form.id = '';
        this.dialog.isShow = true;
      },
      // 对话框---确定按钮
      dialogOkBtnClickHandle(){
        let form = this.dialog.form;
        if(form.deptName == ''){
          this.$message({
            type : 'error',
            message : '名称不得为空！！'
          })
          return;
        }
        this.$store.dispatch('eduCertIssueDeptAddOrUpdate', this.dialog.form).then(function(res){
          if(res.success){
            this.$message({
              type : 'success',
              message : '操作成功'
            })
            this.searchBtnClickHandle();
            this.dialog.isShow = false;
          } else {
            this.$message({
              type : 'error',
              message :  res.message || '操作失败'
            })
          }
        }.bind(this));
      },
      // 对话框--关闭
      handleClose(){
        this.dialog.form = this.$tool.clearObj({}, this.dialog.form);
        this.dialog.isShow = false;
      }
    }
  }
</script>
<style>
</style>
