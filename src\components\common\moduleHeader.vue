<template>
  <div id="moduleHeader">
    <div style="position:absolute;top:0;left:150px;right:0;height:49px;min-width: 1000px;background-color: #fff;border-bottom: 1px solid #ccc;">
      <el-row style="height: 50px;width: 100%;margin: 0;padding: 0">
       <h3 style="font-weight: 600;font-size:21px;letter-spacing: 2px;float: left;font-family: 黑体;margin: 15px 0 0 40px">安全生产管理系统</h3>

        <div style="float: right;margin: 10px 50px 0 0">
          <el-row style="margin: 0;padding: 0">
            <img :src="headLogoPath" style="float: left;display: inline-block;width: 34px;height: 34px;border-radius: 17px;" @click="openPersonManageClick"/>
            <el-button class="login-btn-b" style="float: left;display: inline-block" @click="openPersonManageClick">{{userName}}</el-button>
            <el-badge :value="noticeCount"  class="item" style="float: left;display: inline-block;margin-top: 7px">
              <el-button style="border: 0px;background-color:transparent;color: black;" size="small" type="info" @click="myNotice" icon="el-icon-message" circle></el-button>
            </el-badge>
            <span style="float: left;display: inline-block;margin-top: 8px">|</span>
            <el-button class="login-btn-b" @click="outClick" style="float: left;display: inline-block">退出</el-button>
          </el-row>
        </div>
      </el-row>
    </div>

    <!--修改个人信息-->
    <el-dialog title="个人信息管理" :visible.sync="personManageVisible" append-to-body>
      <el-tabs v-model="activeName">

        <el-tab-pane label="基本信息" name="baseDataTab">
          <div style="width: 100%;height: 90px">
            <div style="width: 60px;height: 90px;margin: 0 auto 0 auto">
              <img :src="headLogoPath" style="width: 60px;height: 60px;border-radius: 30px;display: block"/>
              <el-upload
                class="avatar-uploader"
                :action='upload.uploadUrl'
                :show-file-list="false"
                :with-credentials="upload.uploadCookies"
                :http-request="ossUploadRequest"
                :data="upload.params">
                <el-button size="mini" type="text" style="margin-left: 5px">更换头像</el-button>
              </el-upload>
            </div>
          </div>
          <div style="width: 100%">
            <el-form :model="personData" :rules="personDataRule" ref="personForm" label-width="100px" class="demo-ruleForm">
              <el-form-item label="登陆名" prop="loginName">
                <el-input v-model="personData.loginName"></el-input>
              </el-form-item>
              <el-form-item label="手机号" prop="phone">
                <el-input v-model="personData.phone"></el-input>
              </el-form-item>
              <el-form-item label="手机短号" prop="shortPhone">
                <el-input v-model="personData.shortPhone"></el-input>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="changeBaseData">保存修改</el-button>
                <el-button @click="resetPersonForm">重置</el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>

        <el-tab-pane label="修改密码" name="changePasswordTab">
          <el-form :model="passwordData" status-icon :rules="passwordDataRule" ref="passwordForm" label-width="100px" class="demo-ruleForm">
            <el-form-item label="新密码" prop="password">
              <el-input
                type="password"
                v-model="passwordData.password"
                auto-complete="off"
                placeholder="密码必须包含大小写字母、数字和特殊字符"
              ></el-input>
            </el-form-item>
            <el-form-item label="确认密码" prop="checkPassword">
              <el-input type="password" v-model="passwordData.checkPassword" auto-complete="off"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="changePassword">修改密码</el-button>
              <el-button @click="resetPasswordForm">重置</el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>

      </el-tabs>
    </el-dialog>
  </div>
</template>
<script>
  import {mapActions} from 'vuex'
  import dealData from '../../assets/functions/dealData'
  export default {
    name: 'moduleHeader',
    props : ['data'],
    data(){
      let that = this;
      let validatePass = (rule, value, callback) => {
        if (value === "") {
          callback(new Error("请输入密码"));
        } else if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}/.test(value)) {
          callback(new Error("密码必须包含大小写字母、数字和特殊字符"));
        } else {
          if (this.passwordData.checkPassword) {
            //如果确认密码有内容则进行比较
            this.$refs.passwordForm.validateField("checkPassword");
          }
          callback();
        }
      };
      let validatePass2 = (rule, value, callback) => {
        if (value === '') {
          callback(new Error('请再次输入密码'));
        } else if (value !== this.passwordData.password) {
          callback(new Error('两次输入密码不一致!'));
        } else {
          callback();
        }
      };
      return {
        // 上传文件
        userName:'登陆',

        //个人管理对话框
        personManageVisible:false,
        activeName:'baseDataTab',
        personData:{
          loginName:'',
          phone:'',
          shortPhone:''
        },
        personDataRule:{
          loginName:[{required: true, message: '登录名不能为空', trigger: 'change'}],
        },
        passwordData:{
          password:'',
          checkPassword:''
        },
        passwordDataRule:{
          password:[{ validator: validatePass, trigger: 'change' }],
          checkPassword:[{validator: validatePass2, trigger: 'change'}]
        },
        //用户头像的上传
        upload:{
          params:{
            contentId:'',
            contentType:18
          },
          uploadUrl:'',
          uploadCookies:true,
        },
      }
    },
    computed:{
      noticeCount:function(){
        return localStorage.NOTICE_COUNT;
      },
      headLogoPath:function () {
        if(this.$store.state.mutations.headLogoPath){
          return this.$store.state.mutations.headLogoPath;
        }else if(this.$tool.getStorage('LOGIN_USER').headPath){
          return this.$tool.getStorage('LOGIN_USER').headPath
        }else{
          return '../../static/imgs/public/person_icon.png';
        }
        // return this.$store.state.mutations.headLogoPath;
      }
    },
    mounted(){
      // 初始化数据
      this.init();
    },
    methods:{
      // 初始化数据
      init(){
        // // 把父组件传递的值给子组件
        // Object.entries(this.data).forEach(function(it){
        //   if(this.upload.hasOwnProperty(it[0])){
        //     this.upload[it[0]] = it[1];
        //   }
        // }.bind(this))
        if(localStorage.SAFE_PLATFORM_USERNAME){
          this.userName=localStorage.SAFE_PLATFORM_USERNAME;
        }
      },
      outClick:function () {
        this.$confirm('确定退出本系统?', '提示', {
          confirmButtonText: '退出',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$router.push('/');
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消退出'
          });
        });
      },
      myNotice:function () {
        this.$router.push('/my-notice');
      },

      //------------------------------修改用户基本信息---------------------
      //打开对话框，清除提示和内容
      openPersonManageClick:function () {
        this.personData.loginName=this.$tool.getStorage('LOGIN_USER').loginName;
        this.personData.phone=this.$tool.getStorage('LOGIN_USER').mobile;
        this.personData.shortPhone=this.$tool.getStorage('LOGIN_USER').shortPhone;
        this.activeName='baseDataTab';
        this.personManageVisible=true;
        this.$nextTick(function () {
          this.$refs['passwordForm'].resetFields();
        })
      },
      changeBaseData:function () {
        this.$refs['personForm'].validate((valid) => {
          if (valid) {
            let params = new URLSearchParams;
            params.append("userId",this.$tool.getStorage('LOGIN_USER').userId);
            if(this.personData.loginName!==this.$tool.getStorage('LOGIN_USER').loginName){
              params.append("loginName",this.personData.loginName);
            }
            params.append("mobile",this.personData.phone);
            params.append("shortPhone",this.personData.shortPhone);
            this.$http.post('user/updateUserSimple', params).then(function (res) {
              if (res.data.success) {
                //修改浏览器中的用户信息
                let tempUser=this.$tool.getStorage('LOGIN_USER');
                tempUser.loginName=this.personData.loginName;
                tempUser.mobile=this.personData.phone;
                tempUser.shortPhone=this.personData.shortPhone;
                this.$tool.setStorage('LOGIN_USER', tempUser);
                this.$message.success('修改成功');
                this.personManageVisible = false;
              }else{
                this.$message.error(res.data.message);
              }
            }.bind(this)).catch(function (err) {
              console.log('user/updateUserSimple'+err);
              this.$message.error('修改基本信息失败');
            }.bind(this));
          } else {
            console.log('error submit!!');
            return false;
          }
        });
      },
      changePassword:function () {
        this.$refs['passwordForm'].validate((valid) => {
          if (valid) {
            let params = new URLSearchParams;
            params.append("userId",this.$tool.getStorage('LOGIN_USER').userId);
            params.append("password",this.passwordData.password);
            this.$http.post('user/updateUserSimple', params).then(function (res) {
              if (res.data.success) {
                this.$message.success('修改密码成功');
                this.personManageVisible = false;
              }else{
                this.$message.error(res.data.message);
              }
            }.bind(this)).catch(function (err) {
              console.log('user/updateUserSimple'+err);
              this.$message.error('修改密码失败');
            }.bind(this));
          } else {
            console.log('error submit!!');
            return false;
          }
        });
      },
      resetPersonForm:function () {
        this.personData.loginName=this.$tool.getStorage('LOGIN_USER').loginName;
        this.personData.phone=this.$tool.getStorage('LOGIN_USER').mobile;
        this.personData.shortPhone=this.$tool.getStorage('LOGIN_USER').shortPhone;
      },
      resetPasswordForm:function () {
        this.$refs['passwordForm'].resetFields();
      },

      //签名直传公司logo
      ossUploadRequest:function (item) {
        //获取该文件对应的sign
        this.$http.get('sys/oss/sign?contentId='+ this.$tool.getStorage('LOGIN_USER').userId +'&contentType=18&realName=' + item.file.name).then(function (res) {
          if(res.data){
            let params=new FormData();
            params.append("name",item.file.name);
            params.append("key",res.data.dir + item.file.name);
            params.append("policy",res.data.policy);
            params.append("OSSAccessKeyId",res.data.accessid);
            params.append('success_action_status','200');
            params.append("callback",res.data.callback);
            params.append("signature",res.data.signature);
            params.append("file",item.file);
            this.fileHttp.post('',params,{headers: {'Content-Type': 'multipart/form-data'}}).then(function (res) {
              if(res.data.file){
                let resultStr=dealData.decode(res.data.file);
                let resultJson=JSON.parse(resultStr);
                this.$message.success('上传成功');
                this.$http.post("/user/updateUserSimple?userId="+this.$tool.getStorage('LOGIN_USER').userId+'&headFileId='+resultJson.fId).then(function (res) {
                  let userObj=this.$tool.getStorage('LOGIN_USER');
                  userObj.headFileId=resultJson.fId;
                  userObj.headPath=this.fileHttp.defaults.baseURL+resultJson.path;
                  this.$tool.setStorage('LOGIN_USER', userObj);
                  this.$store.dispatch('sendHeadLogoPath',userObj.headPath);
                }.bind(this)).catch( () => {
                  this.$message.error('人员信息未更新成功');
                })
              }else{
                this.$message.error('上传图片失败');
              }

            }.bind(this))
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message.error('获取唯一标识失败');
        }.bind(this));
      },

    }
  }
</script>

<style>
  .login-btn-b {
    background-color: rgba(0,0,0,0) !important;
    border-color: rgba(0,0,0,0) !important;
    text-decoration: underline !important;
    margin-left: 0 !important;
    color:black!important;
  }
</style>
