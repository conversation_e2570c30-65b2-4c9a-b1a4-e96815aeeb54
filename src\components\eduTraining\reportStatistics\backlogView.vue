<template>
  <el-container class="container">
    <el-main>
      <!--发布-->
      <el-form ref="info" label-width="100px" :model="info">
        <el-row type="flex">
          <el-col :span="24">
            <el-form-item  label="任务名称">
              <el-input disabled v-model="info.title"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex">
          <el-col :span="24">
            <el-form-item label="描述">
              <el-input disabled type="textarea" v-model="info.remark"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="EXCEL上传">
              <fileUpload
                @fileData="fileDataFn"
                ref="uploadComponent"
                :data="upload"
              ></fileUpload>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="文件:">
              <el-button type="text" @click="showPdfDialog">{{
                info.fileName
              }}</el-button>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" class="row" justify="center">
          <!-- <el-button
            @click="saveBtnClickHandle({ status: 0 })"
            size="small"
            :span="2"
            type="success"
            >保存</el-button
          > -->
          <el-button
            @click="saveBtnClickHandle({ status: 1 })"
            size="small"
            :span="2"
            type="success"
            >提交</el-button
          >
          <el-button size="small" :span="2" @click="$router.back()"
            >返回</el-button
          >
        </el-row>
      </el-form>
    </el-main>
    <!--安全生产目标结束-->

  </el-container>
</template>

<script>
import { VueEditor } from "vue2-editor";
import chooseStaff from "./chooseStaff";
import fileUpload from "@/components/common/fileUploadXLSX";
export default {
  components: {
    chooseStaff,
    VueEditor,
    fileUpload,
  },
  data() {
    return {
      // info表
      info: {
        // ID
        id: "",
        title: "",
        remark: "",
        fileId: "",
        // status: "",
        // 用户id
        // userIds: [],
      },
      // 辅助字段
      assist: {
        time: "", // 时间期限
        // 用户
        userList: [],
        // excel路劲
        excelPath: "",
      },

      upload: {
        params: {
          contentId: this.$tool.getStorage("LOGIN_USER").userId,
          contentType: 26,
        },
        btns: {
          // 上传按钮
          upload: {
            isShow: true,
          },
          // 下载按钮
          download: {
            isShow: false,
          },
          // 删除按钮
          delete: {
            isShow: true,
          },
        },
        uploadUrl: "",
        uploadCookies: true,
        fileData: [],
      },

      viewOrEdit: false,
      // 权限按钮
      powerBtns: [],
      // pdf查看对话框
      dialog: {
        //查看安全风险告知书
        isShow: false,
        pdfUrl: "http://www.safe360.vip/safeFile.pdf",
        title: "查看安全风险告知书",
      },
    };
  },
  watch: {
    $route(to, from) {
      // 如果来至列表页
      if (
        from.name === "ReportStatisticsIndex" &&
        this.$route.name === "ReportStatisticsAdd"
      ) {
        this.init();
      }
    },
  },
  activated() {
    this.init();
  },
  mounted() {
    this.init();
  },
  methods: {

    fileDataFn(data) {
      console.log("父组件获取子组件数据：", data);
      this.info.listNewsFile = data.map(function (e) {
        return {
          id: "",
          eduNewsId: "",
          fileId: e.fileId,
          fileName: e.fileName,
        };
      });
      console.log(888, this.info.listNewsFile);
    },
    // 文章查看
    showPdfDialog() {
      // console.log(this.info);
      // this.dialog.title = this.info.title;
      // this.dialog.pdfUrl = this.info.filePath;
      // this.dialog.isShow = true;
      window.open(this.info.filePath);

    },
    // 初始化
    init() {
      // this.searchBtnClickHandle();
      //从路由中获取参数info
      this.info = this.$route.params.info;

    },
    timeChange(e) {
      // console.log(e);
      if (e) {
        this.info.startTime = e[0];
        this.info.endTime = e[1];
      }
    },

    articleChangeFn(item) {
      //        console.log(111,item);
      //        console.log(222,this.assist.articleList);
      this.info.listNewsFile = [];
      for (var i = 0; i < item.length; i++) {
        for (var j = 0; j < this.assist.articleList.length; j++) {
          if (this.assist.articleList[j].value == item[i]) {
            this.info.listNewsFile.push({
              fileId: item[i],
              fileName: this.assist.articleList[j].label,
            });
            break;
          }
        }
      }
      //        console.log(111,this.info.listNewsFile)
    },
    // 清空数据
    clear() {
      this.info.id = "";
      this.info.title = "";
      this.info.remark = "";
      this.info.fileId = "";
      this.info.status = "";
      this.info.userIds = [];

      this.upload.fileData = [];
      this.viewOrEdit = false;
    },
    // 根据id搜索信息
    searchBtnClickHandle() {
      this.clear();
      let _this = this;
      let id = this.$route.params.id;
      this.viewOrEdit = true;
      let url = "sys/sysManageFile/findUserReportForm";

      _this.$http.post(url, { id: id }).then(function (res) {
        if (res.data.success) {
          _this.info = res.data.data;
          _this.info.listNewsFile = [
            {
              id: "",
              eduNewsId: "",
              fileId: res.data.data.fileId,
              fileName: res.data.data.fileName,
            },
          ];

        } else {
          // _this.$message({
          //   showClose: true,
          //   message: "2失败！",
          //   type: "error",
          // });
        }
      });
    },
    // 未发布/已发布/进行中【开始按钮】--培训发布--保存按钮
    saveBtnClickHandle(options) {
      let _this = this;
      let params = this.info;
      debugger
      if (!this.info.listNewsFile || this.upload.fileData.length == 0) {
        return this.$message({
          showClose: true,
          message: "请上传文件！",
          type: "error",
        });
      }
      params["userFileId"] =        this.info.listNewsFile[this.info.listNewsFile.length - 1].fileId;
      // params["fileId"] =1;

      params["status"] = options.status;

      let url = "sys/sysManageFile/addOrUpdateReportFormUser";

      _this.$http.post(url, params).then((res) => {
        console.log(res);
        if (res.data.success) {
          _this.$message.success("操作成功");
        } else {
          _this.$message({
            showClose: true,
            message: "操作失败！",
            type: "error",
          });
        }
      });
    },
  },
};
</script>

<style>
.container {
  background: #fff;
  padding: 0px 20px 20px;
}

.title {
  background: rgba(64, 158, 255, 0.1);
  color: #0f6fc6;
  border: 1px solid rgba(64, 158, 255, 0.2);
  border-radius: 5px;
}

.row {
  margin-top: 10px;
}
</style>
