<template>
    <div id="emerHandleAdd">
      <el-row class="row">
        <el-col :span="24" class="title" v-if="pageStatus.view">事件详情</el-col>
        <el-col :span="24" class="title" v-else-if="pageStatus.edit">修改应急事件</el-col>
        <el-col :span="24" class="title" v-else="pageStatus.view">添加应急事件</el-col>
      </el-row>
      <el-form
        :model="ruleForm"
        :rules="rules"
        ref="ruleForm"
        label-width="120px"
        class="demo-ruleForm">
        <el-col :span="24">
          <el-col :span="8">
            <el-form-item label="事件标题" prop="eventTitle">
              <el-input v-model="ruleForm.eventTitle" :readonly="pageStatus.view"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="事件类型" prop="topTypeId">
              <el-cascader
                :readonly="pageStatus.view"
                v-model="planTypeListArr"
                @change="planTypeListChangeHandle"
                :options="planTypeList"></el-cascader>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="发生时间" prop="eventTime">
              <el-date-picker
                :readonly="pageStatus.view"
                v-model="ruleForm.eventTime"
                type="datetime"
                placeholder="选择日期时间">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-col>
        <el-col :span="24">
          <el-col :span="8">
            <el-form-item label="事件地点">
              <el-input v-model="ruleForm.location"
                        :readonly="pageStatus.view"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="死亡人数">
              <el-input v-model="ruleForm.deathNum"
                        :readonly="pageStatus.view">
                <template slot="append">人</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="受伤人数">
              <el-input v-model="ruleForm.injuriesNum"
                        :readonly="pageStatus.view">
                <template slot="append">人</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-col>
        <el-col :span="24">
          <el-col :span="8">
            <el-form-item label="经济损失">
              <el-input v-model="ruleForm.econLoss"
                        :readonly="pageStatus.view">
                <template slot="append">元</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item label="桩号">
              <el-col :span="6">
                <el-input v-model="ruleForm.pileDirection"
                          :readonly="pageStatus.view"></el-input>
              </el-col>
              <el-col :span="2">方向</el-col>
              <el-col :span="6">
                <el-input v-model="ruleForm.pileK"
                          :readonly="pageStatus.view"></el-input>
              </el-col>
              <el-col :span="1">K</el-col>
              <el-col :span="6">
                <el-input v-model="ruleForm.pileM"
                          :readonly="pageStatus.view"></el-input>
              </el-col>
              <el-col :span="1">M</el-col>
            </el-form-item>
          </el-col>
        </el-col>
        <el-col :span="24">
          <el-col :span="8">
            <el-form-item label="道路中断类型">
              <el-select v-model="ruleForm.interruptType"
                         :readonly="pageStatus.view" placeholder="请选择道路中断类型">
                <el-option label="全幅" value="1"></el-option>
                <el-option label="半幅" value="2"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" class="customer">
            <el-form-item label="道路中断时间">
              <el-input v-model="ruleForm.interruptTime"
                        :readonly="pageStatus.view" class="input-with-select">
                <el-select slot="append"
                           :readonly="pageStatus.view" v-model="ruleForm.interruptTimeUnit" placeholder="请选择">
                  <el-option label="天" value="1"></el-option>
                  <el-option label="小时" value="2"></el-option>
                </el-select>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" class="customer">
            <el-form-item label="道路拥挤长度">
              <el-input v-model="ruleForm.blockLength"
                        :readonly="pageStatus.view" class="input-with-select">
                <el-select slot="append"
                           :readonly="pageStatus.view" v-model="ruleForm.blockLengthUnit" placeholder="请选择">
                  <el-option label="米" value="1"></el-option>
                  <el-option label="公里" value="2"></el-option>
                </el-select>
              </el-input>
            </el-form-item>
          </el-col>
        </el-col>
        <el-col :span="24">
          <el-col :span="8">
            <el-form-item label="危化品是否泄露">
              <el-select
                style="width:100%;"
                :readonly="pageStatus.view"v-model="ruleForm.dangerousChemical" placeholder="请选择">
                <el-option label="无" value="1"></el-option>
                <el-option label="是" value="2"></el-option>
                <el-option label="否" value="2"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="危化品泄露类型">
              <el-select
                style="width:100%;"
                :readonly="pageStatus.view" v-model="ruleForm.dangerousChemicalType" placeholder="">
                <el-option label="无" value="1"></el-option>
                <el-option label="有毒气体" value="2"></el-option>
                <el-option label="易燃易爆气体" value="2"></el-option>
                <el-option label="易燃易爆固体" value="2"></el-option>
                <el-option label="易燃易爆液体" value="2"></el-option>
                <el-option label="腐蚀性液体" value="2"></el-option>
                <el-option label="其他" value="2"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="危化品泄露量">
              <el-input
                :readonly="pageStatus.view" v-model="ruleForm.chemicalLeakage"></el-input>
            </el-form-item>
          </el-col>
        </el-col>
        <el-col :span="24">
          <el-col :span="8">
            <el-form-item label="危化品名">
              <el-input
                :readonly="pageStatus.view" v-model="ruleForm.chemicalName"></el-input>
            </el-form-item>
          </el-col>
        </el-col>
        <el-col :span="24" style="margin: 30px 0;">
          <el-col :offset="6" :span="12">
            <el-form-item>
              <template v-if="pageStatus.add">
                <el-button size="small" type="primary" @click="nextBtnClickHandle">下一步</el-button>
              </template>
              <template v-if="pageStatus.view">
                <!--<el-button size="small" type="warn" @click="updateBtnClickHandle">更改</el-button>-->
              </template>
              <template v-if="pageStatus.edit">
                <el-button size="small" type="success" @click="saveBtnClickHandle">保存</el-button>
              </template>
              <el-button size="small" @click="$router.back()">返回</el-button>
            </el-form-item>
          </el-col>
        </el-col>
      </el-form>
    </div>
</template>

<script>
    export default {
      data() {
        return {
          // 表单字段
          ruleForm: {
            // 事件标题
            eventTitle : '',
            // 事件类型
            typeId : 0,
            topTypeId : 0,
            // 发生时间
            eventTime : '',
            // 事件地点
            location : '',
            // 装号---方向--K---M
            pileDirection : '',
            "pileK": "",
            "pileM": "",
            // 死亡人数
            "deathNum": 0,
            // 受伤人数
            "injuriesNum": 0,
            // 经济损失
            "econLoss": 0,
            // 道路中断类型
            "interruptType": "",
            // 道路中断时间---单元
            "interruptTime": 0,
            "interruptTimeUnit": "天",
            // 道路拥挤长度---单位
            "blockLength": 0,
            "blockLengthUnit": "米",
            // 危化品是否泄露
            "dangerousChemical": '',
            // 危化品泄露类型
            "dangerousChemicalType": "",
            // 危化品泄露量
            "chemicalLeakage": "",
            // 危化品名
            chemicalName : '',



          },
          // 事件类型--选中的数组
          planTypeListArr : [],
          // 页面的状态
          pageStatus : {
            // 默认添加
            add : true,
            // 查看详情
            view : false,
            // 更新页面
            edit : false
          },
          // 表单规则
          rules: {
            eventTitle: [
              { required: true, message: '请输入事件标题', trigger: 'change' },
            ],
            topTypeId: [
              { required: true, message: '请输入事件标题', trigger: 'change' },
            ],
            eventTime: [
              { required: true, message: '请输入事件标题', trigger: 'change' },
            ],

          },
        }
      },
      created(){
        if(this.$route.params.status == 'view'){
          this.viewPage();
        } else if (this.$route.params.status === 'edit'){
          this.editView();
        } else if (this.$route.params.status == 'add'){
          // 初始化
          this.init();
        }
      },
      watch:{
        $route(to,from){
          if(to.name === 'emerHandle') {
            this.init();
          }

        },
        '$route.params.status'(to, from){
          // 查看
          if(to === 'add'){
            this.pageStatus.add = true;
            this.pageStatus.edit = false;
            this.pageStatus.view = false;
            // 重置表单
            this.$refs['ruleForm'].resetFields();
          } else if(to === 'view'){
            this.viewPage();
          } else if ( to === 'edit'){
            this.editView();
          }
        }
      },
      computed: {
        // 分类列表
        planTypeList: function () {
          return this.$store.state.emerHandleModule.planTypeList
        },
        // 事件
        emgEvent : function(){
          return this.$store.state.emerHandleModule.emgEvent;
        },
      },
      methods: {
        // 初始化
        init(){
          this.clear();
          // 获取分类列表
          this.$store.dispatch("planTypeListAction");
        },
        // 清除数据
        clear(){
          this.ruleForm = this.$tool.clearObj({}, this.ruleForm);
          this.ruleForm.interruptTimeUnit = '天';
          this.ruleForm.blockLengthUnit = '米';
          this.planTypeListArr = [];
        },
        // 查看页面
        viewPage(){
          let that = this;
          let list = this.emgEvent;
          Object.entries(list).forEach(function(it){
            if(that.ruleForm.hasOwnProperty(it[0])){
              that.ruleForm[it[0]] = it[1];
            }
          })
          if(list.topTypeId) that.planTypeListArr.push(list.topTypeId);
          if(list.typeId) that.planTypeListArr.push(list.typeId);
          that.pageStatus.add = false;
          that.pageStatus.edit = false;
          that.pageStatus.view = true;
        },
        // 修改页面
        editView(){
          let that = this;
          let list = this.emgEvent;
          Object.entries(list).forEach(function(it){
            if(that.ruleForm.hasOwnProperty(it[0])){
              that.ruleForm[it[0]] = it[1];
            }
          })
          if(list.topTypeId) that.planTypeListArr.push(list.topTypeId);
          if(list.typeId) that.planTypeListArr.push(list.typeId);
          that.pageStatus.add = false;
          that.pageStatus.view = false;
          that.pageStatus.edit = true;
        },
        // 修改页面---保存按钮
        saveBtnClickHandle(){
          this.ruleForm['id'] = this.emgEvent.id;
          this.$store.dispatch('emgEventUpdateAction', this.ruleForm).then(function(res){
            if(res.success){
              this.$message({
                type : 'success',
                message : '更新成功'
              })
              this.$store.commit('emgEventMutation',res.data);
            } else {
              this.$message({
                type : 'error',
                message : '更新失败！！！'
              })
            }
            delete this.ruleForm.id;
            this.pageStatus.add = false;
            this.pageStatus.view = true;
            this.pageStatus.edit = false;
          }.bind(this))
        },
        // 点击更新按钮
        updateBtnClickHandle(){
          this.pageStatus.add = false;
          this.pageStatus.view = false;
          this.pageStatus.edit = true;
        },
        // 类别改变处理函数
        planTypeListChangeHandle(value){
          this.ruleForm.topTypeId = value[0];
          this.ruleForm.typeId = value[1] || 0;
        },
        // 下一步按钮点击事件处理函数---添加页面
        nextBtnClickHandle(){
          this.$store.dispatch('emgEventCountEventLevelAction', this.ruleForm).then(function(res){
            if(res.success){
              this.$message({
                type : 'success',
                message : '添加成功'
              })
              this.$store.commit('emgEventMutation', res.data);
              // 这时候 eventId 和 handleId 都是空
//              this.$router.push({ name : 'emerHandleChoiceEvent', params : { data : res.data } })
              this.$router.push({ name : 'emerHandleChoiceEvent' })
            } else {
              this.$message({
                type : 'error',
                message : '添加失败！！！'
              })
            }
          }.bind(this))
        }
      }
    }
</script>

<style>
  #emerHandleAdd{
    background : #fff;
    padding:10px 50px;
    height:600px;
  }
  .title{
    text-align: center;
    margin-bottom: 20px;
    font-size: large;
    letter-spacing: 2px;
    color:#3576AA;
    border-left:5px solid #049ff1;
    border-radius: 5px;
    background-color: rgb(236,248,255);
    height: 50px;
    line-height: 50px;
  }
  .customer .el-select .el-input {
    width: 80px;
  }
  .input-with-select .el-input-group__append {
    background-color: #fff;
  }


</style>
