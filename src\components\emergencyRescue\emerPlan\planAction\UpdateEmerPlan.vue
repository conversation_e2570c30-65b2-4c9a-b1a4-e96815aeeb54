<template>
  <div id="updateEmerPlan">
    <div class="background-style">
      <el-col :span="16" :offset="4" class="warning-background-title">修改应急预案</el-col>
      <el-col :span="16" :offset="4">
        <el-form :model="form" :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm">
          <el-col :span="24">
            <el-form-item label="预案名称：" prop="planName">
              <el-input v-model="form.planName"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-col :span="12">
              <el-form-item label="分类：" prop="classify">
                <el-cascader
                  :options="cascaderOptions"
                  v-model="form.classify"
                  style="width: 100%"
                  placeholder="请选择">
                </el-cascader>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="级别：" prop="level">
                <el-select v-model="form.level" placeholder="请选择" style="width: 100%">
                  <el-option
                    v-for="item in levelOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-col>
          <el-col :span="24">
            <el-col :span="12">
              <el-form-item label="预警信号：" prop="planFlag">
                <el-select v-model="form.planFlag" placeholder="请选择" style="width: 100%">
                  <el-option
                    v-for="item in planFlagOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.label">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="上报间隔：" prop="interval">
                <el-input placeholder="请输入时长" v-model="form.interval" type="number"><template slot="append">小时</template></el-input>
              </el-form-item>
            </el-col>
          </el-col>
          <!--<el-col :span="24">-->
            <!--<el-col :span="12">-->
              <!--<el-form-item label="通知对象：" prop="noticedPeople">-->
                <!--<el-input v-model="form.noticedPeople" placeholder="请点击右侧按钮选择人员" readonly="readonly"></el-input>-->
              <!--</el-form-item>-->
            <!--</el-col>-->
            <!--<el-col :span="12">-->
              <!--<el-button type="primary" size="medium" style="margin-left: 20px;margin-top: 2px" @click="choosePeople">选择人员</el-button>-->
            <!--</el-col>-->
          <!--</el-col>-->
          <!--全自编表单-->
          <el-col :span="24">
            <el-form :model="editForm" :rules="editRules" ref="wholeRuleForm" label-width="20px" class="demo-ruleForm">
              <el-col :span="24">
                <el-col :span="5">
                  <el-form-item  prop="firstDept">
                    <el-input  type="textarea" :autosize="{ minRows: 1,maxRows: 3}" v-model="editForm.firstDept" placeholder="操作实施者"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="19">
                  <el-form-item  label=":" prop="firstContent">
                    <el-input type="textarea" :autosize="{ minRows: 3}" v-model="editForm.firstContent" placeholder="请输入具体操作内容"></el-input>
                  </el-form-item>
                </el-col>
              </el-col>
              <el-col :span="24">
                <el-col :span="5">
                  <el-form-item  prop="secondDept">
                    <el-input type="textarea" :autosize="{ minRows: 1,maxRows: 3}" v-model="editForm.secondDept" placeholder="操作实施者"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="19">
                  <el-form-item  label=":" prop="secondContent">
                    <el-input type="textarea" :autosize="{ minRows: 3}" v-model="editForm.secondContent" placeholder="请输入具体操作内容"></el-input>
                  </el-form-item>
                </el-col>
              </el-col>
              <el-col :span="24">
                <el-col :span="5">
                  <el-form-item  prop="thirdDept">
                    <el-input type="textarea" :autosize="{ minRows: 1,maxRows: 3}" v-model="editForm.thirdDept" placeholder="操作实施者"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="19">
                  <el-form-item  label=":" prop="thirdDept01">
                    <el-input v-model="editForm.thirdDept01" placeholder="操作实施者" style="width: 150px;margin-bottom: 10px"></el-input>
                    <el-button style="margin-bottom: 10px" size="small" type="success" @click="openAddDialog('thirdContent01')">添加知识点</el-button>
                    <el-input type="textarea" :autosize="{ minRows: 3}" v-model="editForm.thirdContent01" placeholder="请输入具体操作内容"></el-input>
                  </el-form-item>
                  <el-form-item prop="thirdDept02">
                    <el-input v-model="editForm.thirdDept02" placeholder="操作实施者" style="width: 150px;margin-bottom: 10px"></el-input>
                    <el-button style="margin-bottom: 10px" size="small" type="success" @click="openAddDialog('thirdContent02')">添加知识点</el-button>
                    <el-input type="textarea" :autosize="{ minRows: 3}" v-model="editForm.thirdContent02" placeholder="请输入具体操作内容"></el-input>
                  </el-form-item>
                </el-col>
              </el-col>
              <el-col :span="24">
                <el-col :span="5">
                  <el-form-item  prop="fourthDept">
                    <el-input type="textarea" :autosize="{ minRows: 1,maxRows: 3}" v-model="editForm.fourthDept" placeholder="操作实施者"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="19">
                  <el-form-item  label=":" prop="fourthDeptContent">
                    <el-input type="textarea" :autosize="{ minRows: 3}" v-model="editForm.fourthDeptContent" placeholder="请输入具体操作内容"></el-input>
                  </el-form-item>
                </el-col>
              </el-col>
            </el-form>
          </el-col>
          <!--全自编表单结束-->
          <el-col :span="24">
            <el-form-item label="应急物资：" prop="goodsArray">
              <el-col :span="2" v-if="isShowGoodsTypeList===false">
                <el-button type="primary" size="small" @click="isShowGoodsTypeList = true">添加</el-button>
              </el-col>
              <el-col :span="24" v-else>
                <el-select
                  multiple
                  filterable
                  remote
                  reserve-keyword
                  clearable
                  placeholder="请输入物资名称后选择"
                  :loading="goodsLoading"
                  :remote-method="goodsTypeListHandle"
                  v-model="goodsTypeListArr"
                  style="width: 400px;">
                  <el-option
                    v-for="item in goodsOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item">
                  </el-option>
                </el-select>
                <el-button type="success" plain size="small" @click="goodsTypeListChangeHandle">确定</el-button>
                <el-button type="info" plain size="small" @click="isShowGoodsTypeList = false">取消</el-button>
              </el-col>
              <el-col :span="24">
                <el-table
                  :data="form.goodsArray"
                  style="width: 100%">
                  <el-table-column
                    type="index"
                    width="50">
                  </el-table-column>
                  <el-table-column
                    prop="label"
                    label="物资名称"
                    width="180">
                  </el-table-column>
                  <el-table-column
                    label="操作"
                    align="center"
                    label-class-name="inner-header-style">
                    <template slot-scope="scope">
                      <el-button type="danger" size="mini" @click="emgHandleListsDelHandle(scope.$index)">删除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </el-col>
            </el-form-item>
          </el-col>
          <el-col :span="24" style="margin-top: 10px">
            <el-form-item label="附件：">
              <div style="width: 100%;float:left">
                <upload-file :fileList="fileList" :data="fileUploadParams"></upload-file>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="24" style="margin-top: 10px">
            <el-form-item>
              <el-button style="float: right;margin-left: 20px" @click="returnClick()">返回</el-button>
              <el-button type="warning" style="float: right" @click="saveClick()">修改</el-button>
            </el-form-item>
          </el-col>
        </el-form>
      </el-col>
    </div>

    <!--侧边框开始-->
    <transition enter-active-class="animated slideInRight" leave-active-class="animated slideOutRight">
      <div style="position: fixed;z-index:3;top: 116px;bottom: 0;right: 20px;width: 400px;border-left: 1px solid #DCDCDC;background-color: white" v-show="asideFlag">
        <!--头部条-->
        <div style="display: inline-block;width: 100%;height: 50px;background-color: rgb(51,122,183)">
          <h3 style="color: white;letter-spacing: 2px;margin: 15px 0 0 20px;width: 100px;float: left">人员选择</h3>
          <img src="../../../../static/imgs/public/cancel.png" class="cancel-icon" @click="cancelAside"/>
        </div>
        <!--头部条结束-->
        <!--边框内容-->
        <div style="display: inline-block;width: 100%;position:absolute;top:80px;right:0;bottom:20px;overflow: auto">
          <el-tree
            :data="peopleTree"
            show-checkbox
            default-expand-all
            node-key="id"
            ref="tree"
            highlight-current
            :props="defaultProps">
          </el-tree>
          <el-button style="float: right;margin-left: 20px;margin-right: 20px" @click="cancelAside">返回</el-button>
          <el-button type="primary" style="float: right" @click="determineChoose">确定</el-button>
        </div>
        <!--边框内容结束-->
      </div>
    </transition>
    <!--侧边框结束-->

    <!--对话框开始-->
    <el-dialog title="添加知识点" :visible.sync="addKnowledgePoint">
      <el-select
        v-model="searchTag"
        multiple
        filterable
        remote
        reserve-keyword
        clearable
        placeholder="请输入标签名后选择,可多选"
        @change="labelClick"
        :remote-method="remoteTag"
        :loading="tagLoading"
        style="width: 300px">
        <el-option
          v-for="item in tagOptions"
          :key="item.value"
          :label="item.label"
          :value="item.label">
        </el-option>
      </el-select>
      <el-button type="primary" style="margin-left: 20px" @click="searchKnowledgePointClick">搜索</el-button>
      <el-table
        :data="knowledgeTable"
        border
        tooltip-effect="light"
        style="width: 100%;margin-top: 10px"
        @selection-change="handleSelectionChange">
        <el-table-column
          type="selection"
          width="55"
          label-class-name="inner-header-style">
        </el-table-column>
        <el-table-column
          prop="content"
          label="知识点"
          show-overflow-tooltip
          label-class-name="inner-header-style"
          min-width="200">
        </el-table-column>
      </el-table>
      <div style="margin-top: 10px">
        <el-pagination
          background
          layout="prev, pager, next"
          :current-page="currentKnowledgePage"
          :total="totalKnowledgeItem"
          @current-change="currentKnowledgePageClick">
        </el-pagination>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelAddKnowledgePoint">取 消</el-button>
        <el-button type="primary" @click="determineAddKnowledgePoint">确 定</el-button>
      </div>
    </el-dialog>
    <!--对话框结束-->

  </div>
</template>
<script>
  import UploadFile from '../../../common/smallComponent/uploadFile.vue'
  export default {
    name: 'updateEmerPlan',
    data() {
      return {
        //------------------预案分类---------------------
        cascaderOptions: [],
        tempTypeString:[],
        levelOptions:[
          {value:'应急警报', label:'应急警报'},
          {value:'4级', label:'4级'},
          {value:'3级', label:'3级'},
          {value:'2级', label:'2级'},
          {value:'1级', label:'1级'}
        ],
        planOptions:[],

        //------------------表单数据---------------------
        tempPlanId:'',
        form:{
          classify:[],
          level:'',
          planName:'',
          planFlag:'',
          interval:'',
          noticedPeople:'',
          goodsArray:[],
        },
        editForm:{
          firstDept:'',
          firstContent:'',
          secondDept:'',
          secondContent:'',
          thirdDept:'',
          thirdDept01:'',
          thirdDept02:'',
          thirdContent01:'',
          thirdContent02:'',
          fourthDept:'解除应急通知模板',
          fourthDeptContent:'',
        },

        addItem:true,
        editItem:false,

        planFlagOptions:[
          {value:'蓝色预警',label:'蓝色预警'},
          {value:'黄色预警',label:'黄色预警'},
          {value:'橙色预警',label:'橙色预警'},
          {value:'红色预警',label:'红色预警'}
        ],
        // 物资类型列表
        isShowGoodsTypeList : false,
        goodsTypeListArr:[],
        goodsLoading:false,
        goodsOptions:[],

        //---------------------表单规则-----------------------
        rules:{
          classify:[{required:true,message:'请选择分类',trigger:'change'}],
          level:[{required:true,message:'请选择等级',trigger:'change'}],
          planFlag:[{required:true,message:'请选择预警信号',trigger:'change'}],
          planName:[{required:true,message:'请输入预案名称',trigger:'change'}],
        },
        editRules:{
          firstDept:[{required:true,message:'请填写机构名称',trigger:'change'}],
          secondDept:[{required:true,message:'请填写机构名称',trigger:'change'}],
          thirdDept:[{required:true,message:'请填写机构名称',trigger:'change'}]
        },

        //---------------------侧边框----------------------------
        asideFlag:false,
        peopleTree: [{
          id: 1,
          label: '一级 1',
          children: [{
            id: 4,
            label: '二级 1-1',
            children: [{
              id: 9,
              label: '三级 1-1-1'
            }, {
              id: 10,
              label: '三级 1-1-2'
            }]
          }]
        }, {
          id: 2,
          label: '一级 2',
          children: [{
            id: 5,
            label: '二级 2-1'
          }, {
            id: 6,
            label: '二级 2-2'
          }]
        }, {
          id: 3,
          label: '一级 3',
          children: [{
            id: 7,
            label: '二级 3-1'
          }, {
            id: 8,
            label: '二级 3-2'
          }]
        }],
        defaultProps: {
          children: 'children',
          label: 'label'
        },
        //-----------------------对话框------------------------------
        addKnowledgePoint:false,
        intentStr:'',
        tagLoading:false,

        searchTag:[],
        knowledgeTable:[],
        totalKnowledgeItem:0,
        currentKnowledgePage:1,

        selectedArray:[],
        //--------------------------上传和下载-----------------------------
        cookies: true,
        fileUploadParams: {
          contentId: 0,
          contentType: 0
        },
        fileList:[],
      }
    },
    computed:{
      tagOptions:function () {
        return this.$store.state.emergencyData.referLabels;
      }
    },
    components : {
      UploadFile
    },
    created:function () {
      this.getPlanType();
      this.getPlanData();
      this.tempPlanId=this.$route.params.planId;

      this.searchKnowledgePointClick();//让知识点搜索先有数据
    },
    watch:{
      $route(to, from){
        if(from.name==='emerPlan'&&this.$route.name==='updateEmerPlan'){
          this.getPlanType();
          this.getPlanData();
          this.tempPlanId=this.$route.params.planId;
        }
      }
    },
    methods:{
      //------------------------初始操作---------------------
      //获取预案所有分类
      getPlanType:function () {
        this.$http.get('emgType/getAll/'+this.$tool.getStorage('LOGIN_USER').companyId).then(function (res) {
          this.editPlanTypeArray(res.data.data);
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message({
            showClose: true,
            message: '网络错误，请尝试重登录',
            type: 'error'
          });
        }.bind(this));
      },
      //编辑预案所有分类
      editPlanTypeArray:function (typeTree) {
        this.cascaderOptions.splice(0);
        for(let i=0;i<typeTree.length;i++){
          let tempArray={value:typeTree[i].id,label:typeTree[i].typeName};
          if(typeTree[i].subTypes.length){
            tempArray.children=[];
            for(let j=0;j<typeTree[i].subTypes.length;j++){
              tempArray.children.push({value:typeTree[i].subTypes[j].id,label:typeTree[i].subTypes[j].typeName});
            }
          }
          this.cascaderOptions.push(tempArray);
        }
      },
      //-----------------------读取应急预警原有数据------------
      getPlanData:function () {
        if(this.$route.params.planId){

          //获取预案内容
          this.$http.post('emgPlan/find',{id:this.$route.params.planId}).then(function (res) {
            let formData=res.data.data.list[0];
            if(formData.topTypeId>0){
              this.form.classify[0]=formData.topTypeId;
              this.form.classify[1]=formData.typeId;
            }else{
              this.form.classify[0]=formData.typeId;
            }
            this.form.planName=formData.name;
            this.form.level=formData.respLevel;
            this.form.planFlag=formData.warnSignal;
            this.form.interval=formData.timeInterval;
            this.form.goodsArray.splice(0);
            for(let k=0;k<formData.emgPlanGoods.length;k++){
              this.form.goodsArray.push({value:formData.emgPlanGoods[k].id,label:formData.emgPlanGoods[k].name});
            }

            this.editForm.firstDept=formData.leaderRequireName;
            this.editForm.firstContent=formData.leaderRequire;
            this.editForm.secondDept=formData.officeRequireName;
            this.editForm.secondContent=formData.officeRequire;
            this.editForm.thirdDept=formData.commandRequireName;
            this.editForm.thirdDept01=formData.startupRequireName;
            this.editForm.thirdDept02=formData.levelRequireName;
            this.editForm.thirdContent01=formData.startupRequire;
            this.editForm.thirdContent02=formData.levelRequire;
            this.editForm.fourthDeptContent=formData.relieveTemplate;
          }.bind(this)).catch(function (err) {
            console.log(err);
            this.$message({
              showClose: true,
              message: '网络错误，请尝试重登录',
              type: 'error'
            });
          }.bind(this));
          //附件列表
          let loadParams=new URLSearchParams;
          this.fileUploadParams.contentId=this.$route.params.planId;
          loadParams.append("contentId",this.$route.params.planId);
          loadParams.append("contentType",0);
          this.$http.post('file/find',loadParams).then(function (res) {
            if(res.data.success){
              this.fileList=res.data.data;
              for(let i=0;i<this.fileList.length;i++){
                this.fileList[i].name=this.fileList[i].fileName;
              }
            }else{
              this.fileList=[];
            }
          }.bind(this)).catch(function (err) {
            console.log('获取附件错误'+err);
          }.bind(this));

        }
      },
      //-----------------------页面数据自动填充--------------
      //填充预案类型
      editPlanType:function () {
        if(this.$route.params.firstClass.value){
          if(this.$route.params.secondClass.value){
            this.form.classify[0]=this.$route.params.firstClass.value;
            this.form.classify[1]=this.$route.params.secondClass.value;
            this.tempTypeString[0]=this.$route.params.firstClass.label;
            this.tempTypeString[1]=this.$route.params.secondClass.label;
          }else{
            this.form.classify[0]=this.$route.params.firstClass.value;
            this.tempTypeString[0]=this.$route.params.firstClass.label;
          }
        }

        if(this.$route.params.level){
          this.form.level=this.$route.params.level;
        }
        this.changePlanName();
        this.searchSimilarPlan();
      },
      //清空表单中的指定项
      clearForm:function () {
        this.form.planFlag='';
        this.form.interval='';
        this.form.noticedPeople='';
        this.editForm.firstDept='';
        this.editForm.firstContent='';
        this.editForm.secondDept='';
        this.editForm.secondContent='';
        this.editForm.thirdDept='';
        this.editForm.thirdDept01='';
        this.editForm.thirdContent01='';
        this.editForm.thirdDept02='';
        this.editForm.thirdContent02='';
        this.editForm.fourthDeptContent='';
        this.form.goodsArray.splice(0);
      },
      //-----------------------表单操作----------------------
      //人员选择
      choosePeople:function () {
        this.asideFlag=true;
      },
      determineChoose:function () {
        alert('该功能未开发完全');
        //console.log(this.$refs.tree.getCheckedKeys());
        this.asideFlag=false;
      },
      cancelAside:function () {
        this.asideFlag=false;
      },
      //物资操作
      goodsTypeListHandle(val){
        let params = new URLSearchParams;
        this.goodsLoading = true;
        params.append("name", val);
        params.append("pageSize", 20);
        params.append("pageCurrent", 1);
        this.$http.post('emgGoodsType/find', params).then(function (res) {
          if (res.data.data.list.length !== 0) {
            this.editGoodsOptions(res.data.data.list);
          }
          this.goodsLoading = false;
        }.bind(this)).catch(function (err) {
          console.log(err);
        });
      },
      editGoodsOptions:function (list) {
        this.goodsOptions.splice(0);
        for(let i=0;i<list.length;i++){
          let temp={value:list[i].id,label:list[i].name};
          this.goodsOptions.push(temp);
        }
      },
      // 物资列表选择，并且映射到table中
      goodsTypeListChangeHandle(){
        let goodsArr = this.form.goodsArray.map(function(it){
          return it.label;
        })
        this.goodsTypeListArr.forEach(function(it){
          // 如果没有添加进去
          if(goodsArr.indexOf(it.label) === -1){
            this.form.goodsArray.push(it)
          }
        }.bind(this));
        this.goodsTypeListArr=[];
        this.isShowGoodsTypeList = false;
      },
      emgHandleListsDelHandle:function (index) {
        this.form.goodsArray.splice(index,1);
      },

      //------------------------交互操作-----------------------
      saveClick:function () {
        this.$refs['ruleForm'].validate((valid) => {
          if (valid) {
            this.$refs['wholeRuleForm'].validate((valid) => {
              if (valid) {
                let params=new URLSearchParams;
                params.append("id",this.tempPlanId);
                if(this.form.classify[1]){
                  params.append("typeId",this.form.classify[1]);
                  params.append("topTypeId",this.form.classify[0]);
                }else{
                  params.append("typeId",this.form.classify[0]);
                  params.append("topTypeId",0);
                }
                params.append("name",this.form.planName);
                params.append("timeInterval",Number(this.form.interval));
                params.append("timeIntervalName",'上报间隔');
                params.append("warnSignal",this.form.planFlag);
                params.append("warnSignalName",'预警信号');
                params.append("respLevel",this.form.level);
                params.append("respLevelName",'级别');

                params.append("leaderRequireName",this.editForm.firstDept);
                params.append("leaderRequire",this.editForm.firstContent);
                params.append("officeRequireName",this.editForm.secondDept);
                params.append("officeRequire",this.editForm.secondContent);
                params.append("commandRequireName",this.editForm.thirdDept);
                params.append("startupRequireName",this.editForm.thirdDept01);
                params.append("startupRequire",this.editForm.thirdContent01);
                params.append("levelRequireName",this.editForm.thirdDept02);
                params.append("levelRequire",this.editForm.thirdContent02);
                params.append("relieveTemplate",this.editForm.fourthDeptContent);

                for(let i=0;i<this.form.goodsArray.length;i++){
                  params.append("emgPlanGoods["+i+"].id",this.form.goodsArray[i].value);
                  params.append("emgPlanGoods["+i+"].name",this.form.goodsArray[i].label);
                }
                params.append("companyId",this.$tool.getStorage('LOGIN_USER').companyId);
                this.$http.post('emgPlan/update',params).then(function (res) {
                  if(res.data.success){
                    this.$message({
                      showClose: true,
                      message: '修改预案成功',
                      type: 'success'
                    });
                    this.$router.push({name:'emerPlan'});
                  }
                }.bind(this)).catch(function (err) {
                  console.log(err);
                  this.$message({
                    showClose: true,
                    message: '网络错误，请尝试重登录',
                    type: 'error'
                  });
                }.bind(this));

              } else {
                console.log('error submit!!');
                return false;
              }
            });
          } else {
            console.log('error submit!!');
            return false;
          }
        });
      },
      returnClick:function () {
        this.$refs['ruleForm'].resetFields();
        this.$refs['wholeRuleForm'].resetFields();
        this.$router.go(-1);
      },

      //----------------------------通过标签查询知识点------------------------
      openAddDialog:function (str) {
        this.addKnowledgePoint=true;
        this.intentStr=str;
      },
      labelClick:function (val) {
        if(this.searchTag.length){
          this.$store.dispatch("getLabels",this.searchTag);
        }else{
          this.$store.dispatch("getLabels",[]);
        }
      },
      remoteTag:function (val) {
        let params = new URLSearchParams;
        if (val !== null&&val!=='') {
          this.tagLoading = true;
          params.append("label", val);
          params.append("pageSize", 10);
          params.append("pageCurrent", 1);
          params.append("companyId",this.$tool.getStorage('LOGIN_USER').companyId);
          this.$http.post('label/find', params).then(function (res) {
            if (res.data.data.list.length !== 0) {
              this.editTag(res.data.data.list);
            }
            this.tagLoading = false;
          }.bind(this)).catch(function (err) {
            console.log(err);
          });
        }else {
          if(this.searchTag.length===0){
            this.$store.dispatch("getLabels",[]);
          }
        }

      },
      editTag:function (list) {
        this.tagOptions.splice(0);
        for(let i=0;i<list.length;i++){
          let temp={value:list[i].id,label:list[i].label};
          this.tagOptions.push(temp);
        }
      },
      searchKnowledgePointClick:function () {
        let params=new URLSearchParams;
        params.append("pageCurrent",1);
        this.currentKnowledgePage=1;
        this.sendKnowSearchRequest(params);
      },
      currentKnowledgePageClick:function (val) {
        if(val){
          let params=new URLSearchParams;
          this.currentKnowledgePage=Number(val);
          params.append("pageCurrent",Number(val));
          this.sendKnowSearchRequest(params);
        }
      },
      sendKnowSearchRequest:function (params) {
        if(this.searchTag.length){
          for(let i=0;i<this.searchTag.length;i++){
            params.append("labels["+i+"]",this.searchTag[i]);
          }
        }
        params.append("companyId",this.$tool.getStorage('LOGIN_USER').companyId);
        this.$http.post('knowledge/find', params).then(function (res) {
          if (res.data.data.list.length !== 0) {
            this.totalKnowledgeItem=res.data.data.total;
            this.knowledgeTable=res.data.data.list;
          }else{
            this.totalKnowledgeItem=0;
            this.knowledgeTable=[];
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
        });
      },
      //多选
      handleSelectionChange:function (val) {
        if(val.length){
          this.selectedArray=val;
        }
      },
      cancelAddKnowledgePoint:function () {
        this.addKnowledgePoint=false;
      },
      determineAddKnowledgePoint:function () {
        if(this.selectedArray.length){
          let tempContent='';
          for(let i=0;i<this.selectedArray.length;i++){
            tempContent+='>>>. '+this.selectedArray[i].content+'\n';
          }
          switch (this.intentStr){
            case 'thirdContent01':
              if(this.editForm.thirdContent01.charAt(this.editForm.thirdContent01.length-1)==='\n'||!this.editForm.thirdContent01.length){
                this.editForm.thirdContent01+=tempContent;
              }else {
                this.editForm.thirdContent01+='\n'+tempContent;
              }
              break;
            case 'thirdContent02':
              if(this.editForm.thirdContent02.charAt(this.editForm.thirdContent02.length-1)==='\n'||!this.editForm.thirdContent02.length){
                this.editForm.thirdContent02+=tempContent;
              }else {
                this.editForm.thirdContent02+='\n'+tempContent;
              }
              break;
            default:
              break;
          }
        }
        this.addKnowledgePoint=false;
      },
    }
  }
</script>
<style>
</style>
