<template>
  <div id="changeFormEditWorkflow">
    <div class="background-style">
      <el-col :span="16" :offset="4" class="primary-background-title">{{ titleStr }}</el-col>
      <el-col :span="16" :offset="4">
        <el-form :model="form" ref="ruleForm" label-width="100px" class="demo-ruleForm" label-position="right">
          <el-collapse v-model="activeName" accordion>
            <el-collapse-item name="1">
              <template slot="title">
                <div style="font-size: 16px;color: #2d57ae;font-weight: bold;padding-left: 20px">隐患信息</div>
              </template>
              <el-col :span="24">
                <el-col :span="12">
                  <el-form-item label="检查单编号：" prop="checkNum" style="margin: 0">
                    {{ form.checkNum }}
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="检查类型：" style="margin: 0">
                    {{ dangerData.typeName }}
                  </el-form-item>
                </el-col>
              </el-col>
              <div v-if="dangerData.typeName != '自查'">
                <el-col :span="24">
                  <el-col :span="12">
                    <el-form-item label="受检单位：" prop="targetDeptName" style="margin: 0;" label-width="90px">
                      {{ form.targetDeptName }}
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="检查单位：" prop="publicDeptName" style="margin: 0;" label-width="90px">
                      {{ form.publicDeptName }}
                    </el-form-item>
                  </el-col>
                </el-col>
                <el-col :span="24">
                  <el-col :span="12">
                    <el-form-item label="受检单位承办人：" prop="targetContractorUserName" style="margin: 0;" label-width="140px">
                      {{ form.targetContractorUserName }}
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="受检单位现场负责人：" prop="targetLiveChargeUser" style="margin: 0" label-width="160px">
                      {{ form.targetLiveChargeUser }}
                    </el-form-item>
                  </el-col>
                </el-col>
              </div>
            </el-collapse-item>
            <el-collapse-item name="2">
              <template slot="title">
                <div style="font-size: 16px;color: #2d57ae;font-weight: bold;padding-left: 20px">临时措施</div>
              </template>
              <el-col :span="24">
                <el-col :span="12">
                  <el-form-item label="临时处理时间：" prop="tempMeasureTime" label-width="120px">
                    <el-date-picker type="date" v-model="tempMeasureTime" style="width: 100%;" placeholder="选择日期"></el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="整改通知单：" label-width="120px">
                    <el-button type="success" size="small" @click="viewNoticeForm">查看通知单</el-button>
                  </el-form-item>
                </el-col>
              </el-col>
              <el-col :span="24">
                <el-form-item label="临时措施：" prop="tempMeasure">
                  <el-input type="textarea" :autosize="{ minRows: 3 }" v-model="tempMeasure"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="24" style="margin-bottom: 10px">
                <el-form-item label="临时措施照片：" label-width="120px" style="margin: 0">
                  <upload-picture :data="{ contentId: replyId, contentType: 11 }" :picFileList="tempPictureList"></upload-picture>
                </el-form-item>
              </el-col>
            </el-collapse-item>
            <el-collapse-item name="3">
              <template slot="title">
                <div style="font-size: 16px;color: #2d57ae;font-weight: bold;padding-left: 20px">整改信息</div>
              </template>
              <el-col :span="24">
                <el-form-item label="整改回执：" prop="reformReply">
                  <el-button type="primary" size="mini" @click="chooseReformTime">填入参考内容</el-button>
                  <el-input type="textarea" :autosize="{ minRows: 3 }" v-model="reformReply"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="整改附件：">
                  <upload-file :fileList="fileList" :data="{ contentId: replyId, contentType: 15 }"></upload-file>
                </el-form-item>
              </el-col>
            </el-collapse-item>
          </el-collapse>
        </el-form>
      </el-col>
      <el-col :span="22" :offset="1">
        <el-table border v-loading="loading" :row-class-name="judgeOverTime" :data="tableData">
          <el-table-column type="index" label="序号" width="50" fixed label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column prop="inspectProject" label="检查项目" width="150" fixed label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column prop="inspectContent" min-width="400" label="检查标准内容" label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column prop="inspectResult" width="300" label="检查结果记录" label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column prop="hiddenDangerLevel" width="150" label="隐患级别" label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column width="320" label="隐患照片" label-class-name="inner-header-style">
            <template slot-scope="scope">
              <picture-card :picFileList="scope.row.dangerPics"></picture-card>
            </template>
          </el-table-column>
          <el-table-column prop="dangerType" width="150" label="隐患类型" label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column prop="changeRequire" width="200" label="整改要求" label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column prop="deadline" width="120" label="整改期限" label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column width="320" label="整改照片" label-class-name="inner-header-style">
            <template slot-scope="scope">
              <picture-card :picFileList="scope.row.changePics"></picture-card>
            </template>
          </el-table-column>
          <el-table-column prop="changeTime" width="120" label="整改时间" :formatter="changeFormat" label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column prop="changeExplain" width="180" label="整改说明" label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column width="100" label="操作" fixed="right" label-class-name="inner-header-style">
            <template slot-scope="scope">
              <el-button size="mini" type="primary" @click="itemEditClick(scope.row, scope.$index)">记录</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
      <el-col :span="16" :offset="4" style="margin-top: 10px" v-show="signBtnVisible">
        <el-form label-width="100px" class="demo-ruleForm">
          <el-form-item label="签字：" prop="examine">
            <img :src="signUrl" style="width: 150px" />
            <el-button size="small" style="margin-left: 20px" @click="signBtn">{{ signButtonStr }}</el-button>
          </el-form-item>
        </el-form>
      </el-col>
      <el-col :span="22" :offset="1">
        <div style="float: right;margin: 20px">
          <el-button type="warning" @click="reassignClick">改派</el-button>
          <el-button type="primary" @click="submitClick">提交</el-button>
          <el-button type="success" @click="saveClick">保存</el-button>
          <el-button @click="$router.push({ name: 'hideDangerWorkflow' })">返回</el-button>
        </div>
      </el-col>
    </div>

    <!--记录对话框-->
    <el-dialog :title="recordTitle" :visible.sync="recordVisible">
      <el-form :model="recordForm" ref="recordForm" label-position="left" class="demo-ruleForm">
        <el-form-item label="检查结果记录：" prop="inspectResult" label-position="left" style="margin: 5px">
          {{ recordForm.inspectResult }}
        </el-form-item>
        <el-form-item label="整改要求：" prop="changeRequire" label-position="left" style="margin: 5px">
          {{ recordForm.changeRequire }}
        </el-form-item>
        <el-form-item label="整改时间：" prop="changeTime" label-position="left" style="margin: 5px">
          <el-date-picker v-model="recordForm.changeTime" type="date" placeholder="选择日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="整改图片：" label-position="left" style="margin: 5px">
          <upload-picture :data="changePictureParams" :picFileList="picFileList"></upload-picture>
        </el-form-item>
        <el-form-item label="整改说明：" prop="changeExplain" label-position="top">
          <el-input type="textarea" :rows="2" v-model="recordForm.changeExplain"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="success" @click="determineSave">保存</el-button>
        <el-button @click="recordDialogClose">返回</el-button>
      </div>
    </el-dialog>
    <!--记录对话框结束-->

    <!--选择负责人-->
    <search-people-dialog @determineClick="selectPersonClick" :data="selectPersonData" :defaultPersonId="selectPersonData.defaultPerson.value"></search-people-dialog>

    <!--判断对话框-->
    <judge-dialog ref="judgeDialog" @buttonClick="judgeExamine"></judge-dialog>

    <el-dialog title="请扫码签字" :visible.sync="qrcodeVisible" width="30%">
      <div style="margin-left: 50%">
        <div id="qrcode" ref="qrCodeUrl" style="margin-left: -50px"></div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="updateSignDetermine()">确定</el-button>
        <el-button @click="closeSignDialog">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import SearchPeopleDialog from '../../../common/smallComponent/searchSinglePeople.vue'
import PictureCard from '../../../common/smallComponent/pictureCard.vue'
import UploadFile from '../../../common/smallComponent/uploadFile.vue'
import UploadPicture from '../../../common/smallComponent/uploadPicture.vue'
import JudgeDialog from '../../../common/smallComponent/judgeDialog.vue'
import QRCode from 'qrcodejs2'
export default {
  name: 'changeFormEditWorkflow',
  data() {
    return {
      titleStr: '',
      currentId: '',

      activeName: '3',
      tableData: [],
      loading: false,
      form: {},
      //整改单的内容
      reformReply: '',//整改回执
      tempMeasureTime: '',//临时措施时间
      tempMeasure: '',//临时措施
      superviseReply: '',//督办回执

      personLoading: false,
      //记录对话框
      recordTitle: '检查结果记录',
      recordVisible: false,
      recordForm: {
        id: '',
        inspectResult: '',
        changeRequire: '',
        changeTime: '',
        changeExplain: '',
      },
      currentIndex: '',
      levelOption: ['1级', '2级', '3级', '4级', '5级'],
      replyArray: [],
      replyId: -1,//整改是否已经存在，在则为整改单ID
      //--------------------------上传和下载-----------------------------
      changePictureParams: {
        contentId: 0,
        contentType: 6
      },
      fileList: [],//整改附件
      tempPictureList: [],//临时措施照片
      picFileList: [],//编辑对话框的整改图片
      //------------------选择负责人的对话框-----------------------
      selectPersonData: { title: '请选择负责人', isShow: false, defaultPerson: { value: 0, label: '' } },
      //当前整改单数据
      dangerData: {},
      nodeData: {},
      //参考审核人
      leaderUser: {},
      qrcodeVisible: false,
      signUrl: '',//签名图
      openSignDialogTime: '',
      signDialogTimer: '',//定时查询器
      qrcode: '',//签名二维码扫码图
      nodeStatus: ''

    }
  },
  computed: {
    personOptions: function () {
      return this.$store.state.sysManageData.personOptions;
    },
    signButtonStr: function () {
      if (this.signUrl) {
        return "更换签名"
      } else {
        return "扫码签名"
      }
    },
    signBtnVisible: function () {
      console.log(this.nodeStatus)
      return parseInt(this.nodeStatus) === 7;//节点状态为7时显示签名
    }
  },
  components: {
    SearchPeopleDialog,
    PictureCard,
    UploadFile,
    UploadPicture,
    JudgeDialog
  },
  created: function () {
    if (this.$route.params.dangerData) {
      this.titleStr = this.$route.params.dangerData.name;
      this.nodeData = this.$route.params.dangerData.nodeData;
      this.dangerData = this.$route.params.dangerData;
      this.leaderUser = { value: this.$route.params.dangerData.examineUserId, label: this.$route.params.dangerData.examineUsername };
      this.searchDataById(this.$route.params.dangerData.id);
      this.nodeStatus = this.$route.params.dangerData.currentStatus;
      this.getUserSign();
    }
  },
  watch: {
    $route(to, from) {
      if ((from.name === 'hideDangerWorkflow' || from.name === 'taskNotice') && this.$route.name === 'changeFormEditWorkflow') {
        if (this.$route.params.dangerData) {
          this.titleStr = this.$route.params.dangerData.name;
          this.nodeData = this.$route.params.dangerData.nodeData;
          this.dangerData = this.$route.params.dangerData;
          this.leaderUser = { value: this.$route.params.dangerData.examineUserId, label: this.$route.params.dangerData.examineUsername };
          this.searchDataById(this.$route.params.dangerData.id);
          this.nodeStatus = this.$route.params.dangerData.currentStatus;
          this.getUserSign();
        }
      }
    },
    qrcodeVisible: function (val) {
      if (!val) {
        clearInterval(this.signDialogTimer);
      }
    }
  },
  methods: {
    // 创建二维码
    creatQrCode:function(){
        let contentId = this.$route.params.dangerData.id;
        let userId = this.$tool.getLoginer().userId;

        // let url = this.$store.state.mutations.currentBaseUrl+"esign-page?contentId="+contentId+"&userId="+userId;
        let url = window.location.origin+'/#/'+"esign-page?contentId="+contentId+"&userId="+userId;
        if(this.qrcode){

        }else{
          this.qrcode = new QRCode(this.$refs.qrCodeUrl, {
            // text: 'http://localhost:8084/#/esign-page', // 需要转换为二维码的内容
            // text: 'http://**************:8084/#/esign-page', // 需要转换为二维码的内容
            // text: 'http://**********/#/esign-page', // 需要转换为二维码的内容
            // text: 'http://www.baidu.com', // 需要转换为二维码的内容
            text : url,
            width: 100,
            height: 100,
            colorDark: '#000000',
            colorLight: '#ffffff',
            correctLevel: QRCode.CorrectLevel.H
          })
        }
      },
    searchDataById: function (id) {
      //清除之前数据
      this.tableData.splice(0);
      this.personOptions.splice(0);
      this.fileList.splice(0);
      this.tempPictureList.splice();
      for (let item in this.form) {
        delete this.form[item];
      }
      this.replyId = -1;//整改单的ID
      this.reformReply = '';
      this.tempMeasureTime = new Date();
      this.tempMeasure = '';
      this.superviseReply = '';
      this.activeName = '3';

      this.currentId = id;
      this.$http.post('danger/inspectPublic/detail', { id: id }).then(function (res) {
        if (res.data.success) {
          this.form = res.data.data;
          this.personOptions.push({ value: this.form.reformChargeUserId, label: this.form.reformChargeUsername });
          this.replyArray[0] = this.form.publicDeptName + '：\n  ' + this.form.name + this.transferTime(this.form.dangerCheckTime, 'y年m月d日') + '所列整改项目，已于';
          this.replyArray[1] = '整改完毕，请验收。';
          this.chooseReformTime();
        }
      }.bind(this)).catch(function (err) {
        this.$message.error('danger/inspectPublic/detail');
        console.log(err);
      });
      //获取整改单内容
      this.$http.get('danger/reformSheet/find?dangerInspectPublicId=' + id + '&applyUserId=' + this.dangerData.subProcessStartUserId).then(function (res) {
        if (res.data.success) {
          if (res.data.data.length) {
            this.replyId = res.data.data[0].id;//整改单的ID
            this.reformReply = res.data.data[0].reformReply;
            this.tempMeasureTime = res.data.data[0].tempMeasureTime;
            this.tempMeasure = res.data.data[0].tempMeasure;
            this.superviseReply = res.data.data[0].superviseReply;
            this.fileList = res.data.data[0].reformAttachments;
            for (let i = 0; i < this.fileList.length; i++) {
              this.fileList[i].name = this.fileList[i].fileName;
            }
            this.tempPictureList = res.data.data[0].measurePics;
            this.tempPictureList.forEach(function (item) {
              item.name = item.fileName;
              item.oriUrl = this.fileHttp.defaults.baseURL + item.path;
              item.url = item.oriUrl + '?x-oss-process=image/resize,m_fixed,h_70,w_70';
            }.bind(this))
          } else {
            let replyParams = new URLSearchParams;
            replyParams.append("dangerInspectPublicId", this.currentId);
            replyParams.append("processInstanceId", this.dangerData.processInstanceId);
            replyParams.append("applyUserId", this.$tool.getStorage('LOGIN_USER').userId);
            this.$http.post('danger/reformSheet/add', replyParams).then(function (res) {
              if (res.data.success) {
                this.replyId = res.data.data.id;
              }
            }.bind(this)).catch(function (err) {
              this.$message.error('danger/reformSheet/add');
              console.log(err);
            });
          }
        }
      }.bind(this)).catch(function (err) {
        this.$message.error('/danger/reformReply/find');
        console.log(err);
      });

      this.loading = true;//让表格缓冲显示
      this.$http.post('danger/inspectListPublic/find', { inspectPublicId: id, needChange: 1 }).then(function (res) {
        if (res.data.success) {
          this.tableData = res.data.data;
          this.tableData.forEach(function (item) {
            item.deadline = this.transferTime(item.deadline);
          }.bind(this));
          this.loading = false;
        }
      }.bind(this)).catch(function (err) {
        this.$message.error('danger/inspectListPublic/find');
        console.log(err);
      });
    },
    //自动填入整改回执
    chooseReformTime: function () {
      let currentTime = new Date();
      this.reformReply = this.replyArray[0] + this.transferTime(currentTime, 'y年m月d日') + this.replyArray[1];
    },
    //查找人员
    remotePerson: function (val) {
      this.$store.dispatch('getPersonOptions', val);
    },
    viewNoticeForm: function () {
      this.$router.push({ name: 'investigationViewWorkflow', params: { dangerData: this.dangerData, onlyShow: true } })
    },
    //-----------------------------------表格功能---------------------------------
    //改时间格式
    changeFormat: function (row) {
      return this.transferTime(row.changeTime);
    },
    itemEditClick: function (row, index) {
      this.picFileList.splice(0);
      this.recordForm.id = row.id;
      this.changePictureParams.contentId = row.id;//上传整改照片关联的隐患ID
      this.recordForm.inspectResult = row.inspectResult;
      this.recordForm.changeRequire = row.changeRequire;
      if (row.changeTime) { this.recordForm.changeTime = row.changeTime; } else {
        let currentDate = new Date();
        this.recordForm.changeTime = currentDate.getTime();
      }
      this.recordForm.changeExplain = row.changeExplain;
      this.picFileList = row.changePics.slice(0);
      this.picFileList.forEach(function (item) {
        item.name = item.fileName;
        item.oriUrl = this.fileHttp.defaults.baseURL + item.path;
        item.url = item.oriUrl + '?x-oss-process=image/resize,m_fixed,h_70,w_70';
      }.bind(this));
      this.currentIndex = index;
      this.recordVisible = true;
    },
    //判断该条记录是否过期了
    judgeOverTime: function ({ row }) {
      if (row.changeOverTime || row.superviseChangeOverTime) {
        return 'warning-row';
      } else {
        return '';
      }
    },
    //检查结果记录确定
    determineSave: function () {
      this.$refs["recordForm"].validate((valid) => {
        if (valid) {
          let tempObj = { id: this.recordForm.id, changeTime: this.recordForm.changeTime, changeExplain: this.recordForm.changeExplain };
          this.$http.post('danger/inspectListPublic/update', tempObj).then(function (res) {
            if (res.data.success) {
              this.loading = true;//让表格缓冲显示
              this.$http.post('danger/inspectListPublic/find', { inspectPublicId: this.currentId, needChange: 1 }).then(function (res) {
                if (res.data.success) {
                  this.tableData = res.data.data;
                  this.tableData.forEach(function (item) {
                    item.deadline = this.transferTime(item.deadline);
                  }.bind(this));
                  this.loading = false;
                }
              }.bind(this)).catch(function (err) {
                this.$message.error('danger/inspectListPublic/find');
                console.log(err);
              });
              this.recordVisible = false;
            }
          }.bind(this)).catch(function (err) {
            console.log('danger/inspectListPublic/update');
            console.log(err);
          });
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    recordDialogClose: function () {
      this.loading = true;//让表格缓冲显示
      this.$http.post('danger/inspectListPublic/find', { applyUserId: this.dangerData.subProcessStartUserId, inspectPublicId: this.currentId, needChange: 1 }).then(function (res) {
        if (res.data.success) {
          this.tableData = res.data.data;
          this.tableData.forEach(function (item) {
            item.deadline = this.transferTime(item.deadline);
          }.bind(this));
          this.loading = false;
        }
      }.bind(this)).catch(function (err) {
        this.$message.error('danger/inspectListPublic/find');
        console.log(err);
      });
      this.recordVisible = false;
    },
    //--------------------------------提交----------------------------------
    saveClick: function () {
      this.newOrUpdateReformSheet();
    },
    submitClick: function () {
      if (this.dangerData.hasReassign) {//当前为被改派状态
        let flowParams = new URLSearchParams;
        flowParams.append("id", this.dangerData.ressignNodeId);
        flowParams.append("taskId", this.dangerData.taskId);
        flowParams.append("fromUserId", this.dangerData.fromUserId);
        this.$http.post('activiti/reassignReturn', flowParams).then(function (res) {
          if (res.data.success) {
            this.newOrUpdateReformSheet();
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message.error('提交执行失败');
        }.bind(this));
      } else {//可以在流程上进行下一步
        let finishChange = true;
        this.tableData.forEach(function (item) {
          if (item.changeTime == null) {
            finishChange = false;
          }
        }.bind(this));
        if (!finishChange) {
          this.$message.warning('请完成记录整改后再提交！');
        } else {
          if (this.reformReply && this.reformReply != '') {
            if (this.nodeData.check) {
              this.$refs.judgeDialog.openJudgeDialog();
            } else {//不需要审核
              this.doTaskClick(false, 0);
            }
          } else {
            this.$message.warning('请填写整改回执！');
          }
        }

      }
    },
    //是否需要审核
    judgeExamine: function (val) {
      if (val) {
        this.selectPersonData.title = '请选择审核人';
        this.selectPersonData.defaultPerson = this.leaderUser;
        this.selectPersonData.isShow = true;
      } else {
        this.doTaskClick(false, 0);
      }
    },
    selectPersonClick: function (val) {
      if (val) {
        this.selectPersonData.isShow = false;
        if (this.selectPersonData.title === '请选择负责人') {//改派
          this.reassignRequest(val);
        } else {//提交审核
          this.doTaskClick(true, val);
        }
      } else {
        this.$message.warning('请选择负责人');
      }
    },
    doTaskClick: function (checkFlag, examinePerson) {
      let flowParams = new URLSearchParams;
      flowParams.append("taskId", this.dangerData.taskId);
      flowParams.append("check", checkFlag);
      flowParams.append("type", this.dangerData.type);
      if (examinePerson) {
        flowParams.append("applyUserId", examinePerson);
      } else {
        if (this.form.checkUserId) { flowParams.append("applyUserId", this.form.checkUserId) }//不写人员则交给验收人
      }
      this.$http.post('dangerFlow/doTask', flowParams).then(function (res) {
        if (res.data.success) {
          this.newOrUpdateReformSheet();
        }
      }.bind(this)).catch(function (err) {
        console.log(err);
        this.$message.error('流程执行失败');
      }.bind(this));
    },
    //新建或者修改整改单
    newOrUpdateReformSheet: function () {
      let urlStr = 'danger/reformSheet/update';
      let replyParams = new URLSearchParams;
      replyParams.append("dangerInspectPublicId", this.currentId);
      replyParams.append("processInstanceId", this.dangerData.processInstanceId);
      replyParams.append("tempMeasure", this.tempMeasure);
      if (this.tempMeasureTime) {
        if (typeof (this.tempMeasureTime) === 'number') {
          let tempDate = new Date();
          tempDate.setTime(this.tempMeasureTime);
          replyParams.append("tempMeasureTime", tempDate);
        } else {
          replyParams.append("tempMeasureTime", this.tempMeasureTime);
        }
      }
      replyParams.append("reformReply", this.reformReply);
      replyParams.append("id", this.replyId);
      this.$http.post(urlStr, replyParams).then(function (res) {
        if (res.data.success) {
          this.$message.success('操作成功！');
          this.$router.push({ name: 'hideDangerWorkflow' });
        }
      }.bind(this)).catch(function (err) {
        this.$message.error(urlStr);
        console.log(err);
      });
    },

    //改派任务，流程上的状态一直是该状态，任务转给别人
    reassignClick: function () {
      this.selectPersonData.title = '请选择负责人';
      this.selectPersonData.defaultPerson = { value: 0, label: '' };
      this.selectPersonData.isShow = true;
    },
    reassignRequest: function (val) {
      let flowParams = new URLSearchParams;
      flowParams.append("taskId", this.dangerData.taskId);
      flowParams.append("assignUserId", val);
      flowParams.append("fromUserId", this.$tool.getStorage('LOGIN_USER').userId);
      this.$http.post('activiti/reassignTask', flowParams).then(function (res) {
        if (res.data.success) {
          this.$message.success('改派成功');
          this.$router.push({ name: 'hideDangerWorkflow' });
        }
      }.bind(this)).catch(function (err) {
        console.log(err);
        this.$message.error('改派失败');
      }.bind(this));
    },
    // 签字
    signBtn() {
      // 给一个二维码，扫描
      this.openSignDialogTime = new Date();
      this.qrcodeVisible = true;
      this.signDialogTimer = setInterval(this.getCurrentSign, 1000);//为方法名时不加括号，为方法时加引号
      console.log()
      this.$nextTick(function () {
        this.creatQrCode();
      }.bind(this));
    },
    //获取用户默认签名
    getUserSign: function () {
      this.signUrl = '';
      this.$http.post('user/getUserSignFile').then(function (res) {
        if (res.data.success) {
          this.signUrl = res.data.data.userSignPath;
        }
      }.bind(this)).catch(function (err) {
        console.log(err);
      });
    },
    //实时获取签名
    getCurrentSign: function () {
      let params = new URLSearchParams;
      params.append("contentId", this.$route.params.dangerData.id);
      params.append("contentType", 23);
      params.append("userId", this.$tool.getLoginer().userId);
      params.append("uploadTime", this.openSignDialogTime);
      this.$http.post('file/getTimerSignFile', params).then(function (res) {
        if (res.data.success) {
          if (res.data.data) {
            this.signUrl = res.data.data.fullPath;
            this.closeSignDialog();
          }
        }
      }.bind(this)).catch(function (err) {
        console.log(err);
      });
    },
    //确定用户签名
    updateSignDetermine: function () {
      let params = new URLSearchParams;
      params.append("contentId", this.$route.params.dangerData.id);
      params.append("contentType", 23);
      params.append("userId", this.$tool.getLoginer().userId);
      params.append("uploadTime", this.openSignDialogTime);
      this.$http.post('file/getTimerSignFile', params).then(function (res) {
        if (res.data.success) {
          if (res.data.data) {
            this.signUrl = res.data.data.fullPath;
          } else {
            this.$message.info('未获取签名！');
          }
          this.closeSignDialog();
        }
      }.bind(this)).catch(function (err) {
        console.log(err);
      });
    },
    //关闭签字二维码对话框
    closeSignDialog: function () {
      this.qrcodeVisible = false;
    }
  }

}
</script>
<style>
.el-table .warning-row {
  color: #f00;
}
</style>
