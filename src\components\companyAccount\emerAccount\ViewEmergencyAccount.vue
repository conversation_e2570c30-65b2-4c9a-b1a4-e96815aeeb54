<template>
  <div id="viewEmergency">
    <div class="background-style">
      <el-col :span="16" :offset="4" class="success-background-title">查看应急响应</el-col>
      <el-col :span="16" :offset="4">
        <el-form :model="form" ref="ruleForm" label-width="120px" class="demo-ruleForm">
          <el-col :span="24">
            <el-form-item label="响应名称：" prop="emergencyName">
              <el-input v-model="form.emergencyName" :disabled="true"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="应急启动：" prop="beginMessage">
              <el-input type="textarea" :autosize="{ minRows: 3}" :disabled="true"
                        v-model="form.beginMessage"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="预警信号：" prop="emergencyFlag">
              <el-input v-model="form.emergencyFlag" :disabled="true" readonly="readonly"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="预警信息：" prop="emerSituation">
              <el-input type="textarea" :autosize="{ minRows: 3}" :disabled="true"
                        v-model="form.emerSituation"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="应急响应要求：" prop="emerRequire">
              <el-input type="textarea" :autosize="{ minRows: 3}" :disabled="true"
                        v-model="form.emerRequire"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="值班安排：">
              <el-table
                :data="form.dutyTable"
                border
                style="width: 100%">
                <el-table-column
                  prop="dutyDate"
                  label="值班日期"
                  align="center"
                  label-class-name="inner-header-style"
                  width="120">
                </el-table-column>
                <el-table-column
                  prop="dutyPerson"
                  label="值班人"
                  align="center"
                  label-class-name="inner-header-style"
                  width="120">
                </el-table-column>
                <el-table-column
                  prop="phoneNumber"
                  label="手机长号"
                  align="center"
                  label-class-name="inner-header-style"
                  width="130">
                </el-table-column>
                <el-table-column
                  prop="shortNumber"
                  label="手机短号"
                  align="center"
                  label-class-name="inner-header-style"
                  min-width="120">
                </el-table-column>
              </el-table>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="审核意见：" prop="examine">
              <div v-if="isLeader">
                <el-dropdown @command="editExamine">
                  <el-button type="primary" size="small">
                    审核参考<i class="el-icon-arrow-down el-icon--right"></i>
                  </el-button>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item v-for="item in selectOptions" :key="item.id" :command="item.content">
                      {{item.name}}
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
                <el-input type="textarea" :disabled="true" :autosize="{ minRows: 3}" v-model="form.examine"></el-input>
              </div>
              <div v-else>
                <el-input type="textarea" :disabled="true" :autosize="{ minRows: 3}" v-model="form.examine"
                          readonly="readonly"></el-input>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="24" style="margin-top: 10px">
            <el-form-item>
              <el-button style="float: right;margin-left: 20px" @click="returnClick()">返回</el-button>
              <el-button type="primary" style="float: right;margin-left: 20px" @click="downloadFile()">下载</el-button>
            </el-form-item>
          </el-col>
        </el-form>
      </el-col>
    </div>
  </div>
</template>
<script>
  import {mapGetters} from 'vuex'

  export default {
    name: 'viewEmergency',
    data() {
      return {
        //表单数据
        form: {
          emergencyName: '',
          beginMessage: '',
          emergencyFlag: '',
          emerSituation: '',
          emerRequire: '',
          dutyTable: [],
          examine: ''
        },
        //参考审核数据
        selectOptions: [
          {id: 'examine01', name: '同意签发', content: '经审核，同意签发该应急响应。'},
          {id: 'examine02', name: '退回修改', content: '经审核，该应急响应内容有待修改，修改意见如下：'},
          {id: 'examine03', name: '建议删除', content: '经审核，该应急响应必要性较低，建议删除。'}
        ],
        //暂存数据
        currentEmerId: '',
        currentPlanId: '',
        currentStatus: '',
        startPlanPublicId: '',
        //身份判断
        isLeader: false,
      }
    },
    computed: mapGetters(['getCurrentUser']),
    created: function () {
      if (this.$route.params.emergencyId) {
        this.currentEmerId = this.$route.params.emergencyId;
        if (this.$route.params.onlyShow) {
          this.searchEmerById();
        } else {
          this.searchEmerById(this.getCurrentUser.username);
        }
      }
    },
    watch: {
      $route(to, from) {
        if ((from.name === 'accountEmerList') && this.$route.name === 'viewEmergencyAccount') {
          if (this.$route.params.emergencyId) {
            this.currentEmerId = this.$route.params.emergencyId;
            this.searchEmerById();
          }
        }
      }
    },
    methods: {
      searchEmerById: function (leaderName) {
        let params = new URLSearchParams;
        params.append("id", this.currentEmerId);
        this.$http.post('planPublic/find', params).then(function (res) {
          if (res.data.data) {
            this.editEmerForm(res.data.data.list[0]);
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message({
            showClose: true,
            message: '网络错误，请尝试重登录',
            type: 'error'
          });
        }.bind(this));
      },
      editEmerForm: function (val) {
        let currentDate = new Date();
        this.currentPlanId = val.planId;
        this.form.emergencyName = val.name;
        if (this.currentStatus === 3 || this.currentStatus >= 7) {
          this.form.beginMessage = val.startInfo;
        } else {
          this.form.beginMessage = val.startInfo + '  签发时间：' + this.transferTime(currentDate.getTime(), 'y年m月d日');
        }
        this.form.emergencyFlag = val.warnSignal;
        this.form.emerSituation = val.warnSituation;
        this.form.emerRequire = val.startupRequire;
        this.form.examine = val.auditOpinion;
        this.startPlanPublicId = val.startPlanId;
        this.currentStatus = Number(val.status);

        if (this.currentStatus === 1 || this.currentStatus === 5 || this.currentStatus === 9) {
          this.isLeader = true;
        } else {
          this.isLeader = false;
        }
        this.form.dutyTable = [];
        if (val.emgDuties.length) {
          for (let i = 0; i < val.emgDuties.length; i++) {
            this.form.dutyTable.push({
              dutyDate: this.transferTime(val.emgDuties[i].dutyDate),
              dutyDateTemp: val.emgDuties[i].dutyDate,
              dutyPerson: val.emgDuties[i].name,
              phoneNumber: val.emgDuties[i].phone,
              shortNumber: val.emgDuties[i].shortPhone
            });
          }
        }
      },
      //填写审核意见
      editExamine: function (content) {
        this.form.examine = content;
      },
      //签发
      signUp: function () {
        let currentTime = new Date();
        let params = new URLSearchParams();
        params.append("id", this.currentEmerId);
        params.append("planId", this.currentPlanId);
        params.append("startPlanId", this.startPlanPublicId);

        //签发也可以修改内容
        params.append("name", this.form.emergencyName);
        params.append("startInfo", this.form.beginMessage);
        params.append("warnSituation", this.form.emerSituation);
        params.append("startupRequire", this.form.emerRequire);

        params.append("auditOpinion", this.form.examine);
        params.append("signDate", currentTime);
        if (this.currentStatus === 1) {
          params.append("status", 3);
        } else if (this.currentStatus === 5) {
          params.append("status", 7);
        } else if (this.currentStatus === 9) {
          params.append("status", 11);
        }
        this.$http.post('planPublic/update', params).then(function (res) {
          if (res.data.success) {
            this.$message({
              showClose: true,
              message: '该应急响应已签发！',
              type: 'success'
            });
            this.$router.push({name: 'emerResponse'});
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message({
            showClose: true,
            message: '网络错误，请尝试重登录',
            type: 'error'
          });
        }.bind(this));
      },
      //签退
      sendBack: function () {
        this.$confirm('此操作将退回该应急响应, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let params = new URLSearchParams();
          params.append("id", this.currentEmerId);
          params.append("planId", this.currentPlanId);
          params.append("startPlanId", this.startPlanPublicId);
          params.append("name", this.form.emergencyName);
          params.append("startInfo", this.form.beginMessage);
          params.append("warnSituation", this.form.emerSituation);
          params.append("auditOpinion", this.form.examine);
          if (this.currentStatus === 1) {
            params.append("status", 2);
          } else if (this.currentStatus === 5) {
            params.append("status", 6);
          } else if (this.currentStatus === 9) {
            params.append("status", 10);
          }
          this.$http.post('planPublic/update', params).then(function (res) {
            if (res.data.success) {
              this.$message({
                showClose: true,
                message: '退回成功！',
                type: 'success'
              });
              this.$router.push({name: 'emerResponse'});
            }
          }.bind(this)).catch(function (err) {
            console.log(err);
            this.$message({
              showClose: true,
              message: '网络错误，请尝试重登录',
              type: 'error'
            });
          }.bind(this));
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });
        });
      },
      //返回
      returnClick: function () {
        this.$refs['ruleForm'].resetFields();
        this.$router.go(-1);
      },
      downloadFile: function () {
        const loading = this.$loading({
          lock: true,
          text: 'Loading',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        this.$http({ // 用axios发送post请求
          method: 'get',
          url: '/report/emgPlanPublicExcel/' + this.currentEmerId, // 请求地址
          responseType: 'blob' // 表明返回服务器返回的数据类型
        }).then((res) => { // 处理返回的文件流
          //console.info(res)
          loading.close()
          const content = res
          const elink = document.createElement('a') // 创建a标签
          elink.download = this.form.emergencyName + ".xlsx" // 文件名
          elink.style.display = 'none'
          const blob = new Blob([res.data])
          elink.href = URL.createObjectURL(blob)
          document.body.appendChild(elink)
          elink.click() // 触发点击a标签事件
          document.body.removeChild(elink)
        })
      },
    }
  }
</script>
<style>
</style>
