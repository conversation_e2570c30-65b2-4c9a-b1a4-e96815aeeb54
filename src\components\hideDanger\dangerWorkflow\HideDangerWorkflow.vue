<template>
  <div id="hideDangerWorkflow">
    <div class="background-style">
      <!--表格区-->
      <div style="width: 100%;">
        <div style="width: 100%">
          <!--待办和已办-->
          <div style="float: left;margin: 10px 0 0 20px;width: 300px;">
            <el-radio-group v-model="radioResponseType" size="medium" @change="responseTypeChange">
              <el-radio-button  v-for="item in radioResponseButtons" :id="item.value" :label="item.value" :key="item.value">{{item.name}}</el-radio-button>
            </el-radio-group>
          </div>
          <!--待办和已办结束-->
          <!--新增检查区-->
          <div style="float:right;height: 40px;margin:10px 20px 0 0;">
            <!--<el-button type="info" icon="el-icon-plus" @click="searchReferTableClick(1);chooseTableVisible=true;investigationType=0">新增自查</el-button>-->
            <el-button type="info" @click="selfInspect"
                       v-if="powerBtns.includes('zichaBtn') && !viewRole ">新增自查</el-button>
            <el-button type="primary"
                       v-if="powerBtns.includes('paichaBtn')"
                       @click="investigationType=2;searchReferTableClick(1);chooseTableVisible=true;">新增排查</el-button>
            <el-button type="success"
                       v-show="haveChildrenCompany && powerBtns.includes('duchaBtn')"
                       @click="investigationType=1;searchReferTableClick(1);chooseTableVisible=true;">新增督查</el-button>
          </div>
          <!--新增检查区结束-->
        </div>
        <!--列表-->
        <div style="width: 100%;float: left;">
          <div style="padding: 10px">
            <el-table
              :data="tableData"
              v-loading="mainTableLoading"
              border
              highlight-current-row
              :row-class-name="judgeOverTime"
              @row-dblclick="itemViewClick"
              style="width: 100%">
              <el-table-column
                prop="num"
                width="60"
                align="center"
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                :label="statusLabel"
                width="130"
                align="center"
                label-class-name="header-style">
                <template slot-scope="scope">
                  <el-tag :type="scope.row.statusColor" size="small">{{scope.row.statusName}}</el-tag>
                </template>
              </el-table-column>
              <el-table-column
                prop="checkNum"
                label="检查单编号"
                width="150"
                align="center"
                show-overflow-tooltip
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="name"
                label="检查单名称"
                width="200"
                align="center"
                show-overflow-tooltip
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="createTime"
                label="创建日期"
                :formatter="changeTimeTransfer"
                width="120"
                align="center"
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="targetDeptName"
                label="受检单位"
                min-width="300"
                show-overflow-tooltip
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="publicDeptName"
                label="发布单位"
                min-width="300"
                show-overflow-tooltip
                label-class-name="header-style">
              </el-table-column>
              <el-table-column fixed="right" label="操作" label-class-name="header-style" width="170">
                <template slot-scope="scope">
                  <div v-if="radioResponseType==='toDoList'">
                    <el-button size="mini" type="success" @click="itemViewClick(scope.row)">流程</el-button>
                    <el-button  size="mini" type="warning" @click="itemOperateClick(scope.row)">操作</el-button>
                  </div>
                  <div v-else>
                    <el-button size="mini" type="success" @click="itemViewClick(scope.row)">流程</el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div>
            <el-pagination
              background
              layout="prev, pager, next"
              :current-page="currentPage"
              :total="totalItem"
              :page-size="pageSize"
              @current-change="currentPageClick">
            </el-pagination>
          </div>
        </div>
        <!--列表结束-->
      </div>
    </div>

    <!--选择参考检查表-->
    <el-dialog title="选择检查表" :visible.sync="chooseTableVisible">
      <div style="float: left;width: 100%">
        <span style="float: left;margin-left: 10px;line-height: 40px;display: inline-block">分类：</span>
        <el-cascader
          :props="tableProp"
          :options="tableTagOption"
          clearable
          filterable
          :debounce="400"
          change-on-select
          v-model="chooseTable.tableTag"
          placeholder="请选择或输入关键字"
          style="width: 70%;float: left;display: inline-block">
        </el-cascader>
        <el-button type="primary" style="float: left;margin-left: 20px;display: inline-block" @click="searchReferTableClick(1)">搜索</el-button>
      </div>

      <el-table
        ref="multipleReferTable"
        :data="referTableData"
        border
        tooltip-effect="light"
        highlight-current-row
        @row-click="referTableClick"
        @selection-change="referTableSelectionChange"
        style="width: 100%;margin-top: 10px;float: left">
        <el-table-column
          type="selection"
          width="50"
          label-class-name="inner-header-style">
        </el-table-column>
        <el-table-column
          type="index"
          width="55"
          label-class-name="inner-header-style">
        </el-table-column>
        <el-table-column
          prop="name"
          label="检查表"
          show-overflow-tooltip
          label-class-name="inner-header-style">
        </el-table-column>
        <!--<el-table-column fixed="right" label="操作" label-class-name="inner-header-style" align="center" width="120">-->
        <!--<template slot-scope="scope">-->
        <!--<el-button size="mini" type="success" @click="viewReferTable(scope.row)">查看</el-button>-->
        <!--</template>-->
        <!--</el-table-column>-->
      </el-table>
      <div style="margin: 5px 0 10px 0;float: left;width: 100%;">
        <el-pagination
          background
          layout="prev, pager, next"
          :current-page="tableCurrentPage"
          :total="tableTotalItem"
          @current-change="tablePageClick">
        </el-pagination>
      </div>
      <div style="float: left;padding: 5px 0 10px 0;width: 100%;">
        <span style="float: left">已选检查表：</span>
        <el-tag
          v-for="(tag, index) in otherSelectedItems"
          :key="tag.id"
          size="mini"
          @close="otherSelectedTagClose(index,tag)"
          closable>
          {{tag.name}}
        </el-tag>
        <el-tag
          v-for="(tag, index) in currentSelectedItems"
          :key="tag.id"
          size="mini"
          @close="currentSelectedTagClose(index,tag)"
          closable>
          {{tag.name}}
        </el-tag>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="chooseTableVisible=false">取 消</el-button>
        <el-button type="primary" @click="determineReferTable">确 定</el-button>
      </div>
    </el-dialog>
    <!--参考检查表结束-->

    <!--查看流程图-->
    <el-dialog title="查看流程" width="94%" :visible.sync="workflowVisible">
      <div style="display: inline-block;width: 100%;">
        <workflow-process :data="workflowData" :processInstanceId="workflowData.processInstanceId"></workflow-process>
      </div>
    </el-dialog>
    <!--查看流程图结束-->

  </div>
</template>
<script>
  import WorkflowProcess from '../../common/workflowProcess.vue'
  export default {
    name: 'hideDangerWorkflow',
    data() {
      return {
        //待办和已办
        radioResponseButtons:[{value:'toDoList',name:'我的待办'},{value:'doneList',name:'我的已办'}],
        radioResponseType:'toDoList',
        //标签对应表
        statusOptions:[
          {value:0,label:'未发布',type:'primary'},
          {value:1,label:'发布审核',type:'warning'},
          {value:2,label:'修改检查单',type:'danger'},
          {value:3,label:'待检查',type:'success'},
          {value:4,label:'发布检查单',type:'primary'},
          {value:5,label:'隐患评估',type:'primary'},
          {value:6,label:'整改单审核',type:'warning'},
          {value:7,label:'待整改',type:'success'},
          {value:8,label:'整改审核',type:'warning'},
          {value:9,label:'验收',type:'warning'},
          {value:10,label:'督办整改',type:'primary'},
          {value:11,label:'督办审核',type:'warning'},
          {value:12,label:'督办验收',type:'warning'},
          {value:13,label:'督办整改审核',type:'warning'},
          {value:14,label:'验收审核',type:'warning'},
          {value:15,label:'已完成',type:'info'},
        ],

        //表格数据
        mainTableLoading:false,
        tableData:[],
        currentPage:0,
        totalItem:0,
        pageSize:10,

        //----------------------检查表对话框----------------------------
        chooseTableVisible:false,
        chooseTable:{
          tableTag:[],
          tagLoading:false
        },
        tableProp:{
          children: 'dangerInspectTableLabels',
          label: 'label',
          value:'label'
        },
        referTableData:[],
        currentReferTable:'',
        tableCurrentPage:0,
        tableTotalItem:0,

        //----------------------检查表多选数据----------------------------
        currentSelectedIds:[],//当前页面的已选检查表ID
        currentSelectedItems:[],//当前页面的已选检查表的项
        otherSelectedIds:[],//,其他页面的已选检查表的ID
        otherSelectedItems:[],//其他页面的已选检查表的项
        tempCurrentSelectedIds:[],//翻页时存下当前的选择
        tempCurrentSelectedItems:[],

        // ---------------------需要传到新建页面的数据-------------------------
        // 新增检查的类型
        investigationType:'',//有检查，排查，自查，四个类型，其中自查为0
        //检查类型
        inpectTypeArr:['自查','督查','排查'],
        //状态的表头名字要随代办和已办改变
        statusLabel:'状态',
        //当前公司是否有子公司,没有子公司则不能新建检查
        haveChildrenCompany:false,

        //-----------------------查看流程对话框-------------------------------
        workflowVisible:false,
        workflowData:{
          processInstanceId:'',
          finish:false,
        },
        selfInspectId:0,//自查检查表的id，如果公司，部门，岗位有绑定自查检查表




        // 权限按钮
        powerBtns : [],
        //浏览角色模式
        viewRole : false,
      }
    },
    computed:{
      tableTagOption:function () {
        return this.$store.state.hideDangerData.tableTreeLabels;
      },
    },
    components : {
      'workflow-process' : WorkflowProcess
    },
    // mounted:function () {
    //   //检查表的标签，树形的
    //   this.$store.dispatch("getTableTreeLabels",this.$tool.getStorage('LOGIN_USER').companyId);
    //   this.judgeHaveChildrenCompany();
    //   this.searchClick();
    // },
    watch:{
      $route(to, from){
       /* if((from.name==='investigationNewWorkflow'||from.name==='investigationUpdateWorkflow'||from.name==='investigationViewWorkflow'||from.name==='investigationEditWorkflow'||from.name==='investigationEditWorkflow'||from.name==='changeFormUpdateWorkflow'||from.name==='changeFormViewWorkflow'||from.name==='changeFormEditWorkflow'||from.name==='superviseEditWorkflow')&&this.$route.name==='hideDangerWorkflow') {
          this.searchClick();
        }*/
        if(this.$route.name==='hideDangerWorkflow') {
          this.init();
        }
      }
    },
    mounted(){
      this.init();
    },

    methods:{
      // 初始化
      init(){
        this.$store.dispatch("getTableTreeLabels",this.$tool.getStorage('LOGIN_USER').companyId);
        this.viewRole = this.$tool.judgeViewRole();
        this.powerBtns = this.$tool.getPowerBtns('hideDangerMenu', this.$route.path);
        this.judgeHaveChildrenCompany();
        // 搜索
        this.searchClick();
      },
      //-----------------------搜索待办和已办-----------------------------
      responseTypeChange:function (val) {
        this.searchClick();
      },
      searchClick:function () {
        this.currentPage=1;
        this.sendRequest();
      },
      currentPageClick:function (val) {
        if(val){
          this.currentPage=val;
          this.sendRequest();
        }
      },
      sendRequest:function () {
        let requestUrl='';
        if(this.radioResponseType==='toDoList'){
          requestUrl='dangerFlow/getUndoTask?pageCurrent='+this.currentPage+'&pageSize='+this.pageSize;
          this.statusLabel='状态';
        }else{
          requestUrl='dangerFlow/getHistoryTask?pageCurrent='+this.currentPage+'&pageSize='+this.pageSize;
          this.statusLabel='操作';
        }
        this.mainTableLoading=true;
        this.$http.get(requestUrl).then(function (res) {
          if (res.data.success) {
            this.totalItem=res.data.data.page.total;
            this.editTableData(res.data.data.data);
          }
          this.mainTableLoading=false;
        }.bind(this)).catch(function (err) {
          console.log(err);
        });
      },
      editTableData:function (list) {
        this.tableData=[];
        for(let i=0;i<list.length;i++){
          let tempObj=list[i].dangerInspectPublic?list[i].dangerInspectPublic:{};
          tempObj.num=(this.currentPage-1)*this.pageSize+i+1;
          tempObj.taskId=list[i].id;
          tempObj.statusName=list[i].name;
          if(this.radioResponseType==='toDoList'){
            if(list[i].extActNode){//流程节点信息
              tempObj.statusColor=list[i].extActNode.statusColor;
              tempObj.operateUrl=list[i].extActNode.url;
              let strTOJson=JSON.parse(list[i].extActNode.config);
              tempObj.nodeData=strTOJson.types[tempObj.type];
              tempObj.currentStatus=list[i].extActNode.status;
            }
            tempObj.processInstanceId=list[i].processInstanceId;
            tempObj.operateId=list[i].userId;
            tempObj.subProcessStartUserId=list[i].subProcessStartUserId;
            tempObj.hasReassign=list[i].hasReassign;
            if(tempObj.hasReassign){//改派的信息
              tempObj.fromUserId=list[i].extActTaskReassign.fromUserId;
              tempObj.ressignNodeId=list[i].extActTaskReassign.id;
            }
            //检查类型
            tempObj.typeName=this.inpectTypeArr[tempObj.type];
          }else{//已办任务

          }
          this.tableData.push(tempObj);
        }
      },
      //时间转化
      changeTimeTransfer:function (row) {
        return this.transferTime(row.createTime);
      },

      //----------------------------------检查表的查看，修改，记录和删除---------------------------------------
      //判断该条记录是否过期了
      judgeOverTime:function ({row}) {
        if(row.overTimeFlag){
          return 'warning-row';
        }else{
          return '';
        }
      },
      itemViewClick:function (row) {
        this.workflowData.processInstanceId=row.processInstanceId;
        this.workflowVisible=true;
      },
      itemOperateClick:function (row) {
        if(row.operateUrl){
          this.$router.push({name:row.operateUrl,params:{dangerData:row}});
        }else{
          this.$message.warning('节点数据获取失败');
        }
      },

      //---------------------------------选择参考检查表------------------------------------
      //-PS-这里的标签都是用来选参考检查表
      //搜索参考检查表
      searchReferTableClick:function (page) {
        this.tableCurrentPage=page;
        let params={pageCurrent:page,pageSize:10,labels:this.chooseTable.tableTag,dangerInspect:{companyId:this.$tool.getStorage('LOGIN_USER').companyId}};
//        let params={pageCurrent:page,pageSize:10,labels:this.chooseTable.tableTag};

        this.referTableData=[];
        this.$http.post('danger/inspect/find', params).then(function (res) {
          if (res.data.success) {
            this.tableTotalItem=res.data.data.total;
            this.referTableData=res.data.data.list;
            this.toggleSelectionItem();
          }
          this.chooseTable.tagLoading = false
        }.bind(this)).catch(function (err) {
          console.log(err);
        });
      },
      //翻页
      tablePageClick:function (val) {
        //翻页时存下当前选择的内容，因为翻页会触发多选函数
        this.tempCurrentSelectedIds.splice(0);
        this.tempCurrentSelectedItems.splice(0);
        this.tempCurrentSelectedIds=[...this.currentSelectedIds];
        this.tempCurrentSelectedItems=[...this.currentSelectedItems];
        if(val){
          this.searchReferTableClick(val);
        }
      },
      //查看参考检查表
      viewReferTable:function (row) {

      },
      //选中某一行
      referTableClick:function (row) {
        if(row){
          this.$refs.multipleReferTable.toggleRowSelection(row);
        }
      },
      //多选参考检查表
      referTableSelectionChange:function (list) {
        this.currentSelectedItems=list;//当前选择项目暂时
        this.currentSelectedIds.splice(0);
        this.currentSelectedItems.forEach(function (item) {
          this.currentSelectedIds.push(item.id);
        }.bind(this))
      },
      //勾选检查表
      toggleSelectionItem:function () {
        let allSelectedIds=this.otherSelectedIds.concat(this.tempCurrentSelectedIds);
        let allSelectedItems=this.otherSelectedItems.concat(this.tempCurrentSelectedItems);

        if(allSelectedIds.length&&this.referTableData.length){
          let tempIds=[];//暂存当前需要勾选的项目，用于筛选其他页面的勾选项
          this.referTableData.forEach(function (item) {//查找当前页的选择项，并勾选
            if(allSelectedIds.indexOf(item.id)>=0){
              tempIds.push(item.id);
              this.$nextTick(function () {
                this.$refs.multipleReferTable.toggleRowSelection(item);
              }.bind(this));
            }
          }.bind(this));

          this.otherSelectedIds.splice(0);
          this.otherSelectedItems.splice(0);
          allSelectedItems.forEach(function (item) {
            if(tempIds.indexOf(item.id)<0){//获取其他页面的选择项
              this.otherSelectedIds.push(item.id);
              this.otherSelectedItems.push(item);
            }
          }.bind(this));
        }
      },
      //标签形式删除选中的检查表
      otherSelectedTagClose:function (index,item) {
        this.otherSelectedItems.splice(index,1);
        this.otherSelectedIds.splice(this.otherSelectedIds.indexOf(item.id),1);
      },
      currentSelectedTagClose:function (index,item) {
        this.currentSelectedItems.splice(index,1);
        this.currentSelectedIds.splice(this.currentSelectedIds.indexOf(item.id),1);
        this.$refs.multipleReferTable.toggleRowSelection(item);
      },
      //确定参考检查表
      determineReferTable:function () {
        let referTableList=this.otherSelectedIds.concat(this.currentSelectedIds);
       /* this.currentSelectedItems.forEach(function (item) {
          referTableNameStr+=item.name+'; ';
        })
        this.otherSelectedItems.forEach(function (item) {
          referTableNameStr+=item.name+'; ';
        })*/
        if(referTableList.length){
          let referTableNameStr= this.currentSelectedItems[0].name;
          this.$router.push({name:'investigationNewWorkflow',params:{referPlanId:referTableList,referName:referTableNameStr,investigationType:this.investigationType,typeName:this.inpectTypeArr[this.investigationType]}})
        }else{
          this.$message.warning('请选择检查表!');
        }
        this.chooseTableVisible=false;
      },
      selfInspect:function () {
        this.investigationType=0
        this.$http.get("/danger/inspect/getSelfInspectId").then(function (res) {
          if(res.data.success&&res.data.data!=0){
            this.selfInspectId=res.data.data
            this.startSelfInspect(res.data.data)
          }else{
            this.$message.error("获取失败或者没有绑定检查表")
            this.searchReferTableClick(1);
            this.chooseTableVisible=true;
          }
        }.bind(this)).catch(function (err) {

        })
      },
      startSelfInspect:function (inspectId) {
        let params={dangerInspect:{id:inspectId}};
        this.$http.post("/danger/inspect/find",params).then(function (res) {
          if(res.data.success){
            if(res.data.data.size>0){
              var inspect=res.data.data.list[0]
              this.$router.push({name:'investigationNewWorkflow',params:{referPlanId:[inspect.id],referName:inspect.name,investigationType:0,typeName:this.inpectTypeArr[this.investigationType]}})
            }
          }else{
            this.$message.error("获取检查表名字失败")
          }
        }.bind(this)).catch(function (err) {
          console.log(err)
        })
      },
      judgeHaveChildrenCompany:function () {
        this.$http.post('dept/find?type=1&parentId='+this.$tool.getStorage('LOGIN_USER').companyId).then(function (res) {
          if(res.data.success){
            this.haveChildrenCompany=res.data.data.length>0;
//            console.log(this.powerBtns);
//            console.log(this.powerBtns.includes('duchaBtn'));
//            console.log(2222,this.haveChildrenCompany && this.powerBtns.includes('duchaBtn'))
          }
        }.bind(this)).catch(function (err) {
          console.log('获取受检单位时的错误:'+err);
        }.bind(this));
      },

    }
  }
</script>
<style>
  .el-tag + .el-tag {
    margin-left: 10px;
  }
  .button-new-tag {
    margin-left: 10px;
    height: 32px;
    line-height: 30px;
    padding-top: 0;
    padding-bottom: 0;
  }
  .input-new-tag {
    width: 90px;
    margin-left: 10px;
    vertical-align: bottom;
  }
  .el-table .warning-row {
    color: #f00;
  }
</style>
