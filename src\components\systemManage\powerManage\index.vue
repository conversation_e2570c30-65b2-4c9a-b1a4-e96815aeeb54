<template>
    <div id="" class="background-style">
      <el-container style="background:#fff;">
        <el-container>
          <el-aside width="250px" style="padding:10px 0 0 10px;">
            <el-tree :data="tree.data" :props="tree.defaultProps" @node-click="handleNodeClick" highlight-current ></el-tree>
          </el-aside>
          <el-main>
            <el-row>
              <el-button icon="el-icon-plus" size="mini" type="primary" @click="addBtnClickHandle">添加</el-button>
            </el-row>
            <el-row style="margin-top:10px;">
              <el-table
                :data="tableData"
                border>
                <el-table-column
                  label-class-name="header-style"
                  label="菜单ID"
                  prop="id"
                  width="70">
                </el-table-column>
                <el-table-column
                  label-class-name="header-style"
                  label="菜单名称"
                  prop="permissionName"
                  width="100">
                </el-table-column>
                <el-table-column
                  label-class-name="header-style"
                  label="上级菜单"
                  prop="parentId"
                  width="100">
                </el-table-column>
                <el-table-column
                  label-class-name="header-style"
                  label="图标"
                  prop="icon"
                  width="100">
                  <!--<template slot-scope="scope">-->
                    <!--{{powerDialog.assist.typeArr[scope.row.type]}}-->
                  <!--</template>-->
                </el-table-column>
                <el-table-column
                  label-class-name="header-style"
                  label="类型"
                  prop="type"
                  width="100">
                </el-table-column>
                <el-table-column
                  label-class-name="header-style"
                  label="排序号"
                  prop="orderNum"
                  width="100">
                </el-table-column>
                <el-table-column
                  label-class-name="header-style"
                  label="菜单URL"
                  prop="url"
                  show-overflow-tooltip
                  min-width="100">
                </el-table-column>
                <el-table-column
                  label-class-name="header-style"
                  label="授权标识"
                  show-overflow-tooltip
                  prop="perms"
                  width="100">
                </el-table-column>
                <el-table-column
                  fixed="right" label="操作"
                  label-class-name="header-style"
                  align="center" width="200">
                  <template slot-scope="scope">
                    <el-button size="mini" type="primary" @click="itemUpdateClick(scope.row)">修改</el-button>
                    <el-button size="mini" type="danger" @click="itemDeleteClick(scope.row)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-row>
          </el-main>
        </el-container>
        <el-footer>
          <!--添加修改对话框-->
          <el-dialog
            :before-close="handleClose"
            title="权限信息对话框"
            width="80%"
            append-to-body
            :visible.sync="powerDialog.isShow">
            <el-form ref="form" label-width="100px">
              <el-row type="flex" class="row">
                <el-col :span="8">
                  <el-form-item label="类型：">
                    <el-radio-group v-model="powerDialog.form.type">
                      <el-radio-button label="0">目录</el-radio-button>
                      <el-radio-button label="1">菜单</el-radio-button>
                      <el-radio-button label="2">按钮</el-radio-button>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="菜单名称：">
                    <el-input v-model="powerDialog.form.permissionName"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="按钮英文名称：">
                    <el-input v-model="powerDialog.form.name"></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" class="row">
                <el-col :span="8">
                  <el-form-item label="上级菜单：">
                    <el-input v-model="powerDialog.form.parentPermissionName"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="菜单URL：">
                    <el-input v-model="powerDialog.form.url"></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" class="row">
                <el-col :span="8">
                  <el-form-item label="授权标识：">
                    <el-input v-model="powerDialog.form.perms"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="排序号：">
                    <el-input v-model="powerDialog.form.orderNum"></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" class="row">
                <el-col :span="8">
                  <el-form-item label="图标：">
                    <el-input v-model="powerDialog.form.icon"></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" class="row" justify="center">
                <el-button
                  @click="submitBtnClickHandle"
                  size="small" :span="2" type="primary">提交</el-button>
              </el-row>
            </el-form>
          </el-dialog>
        </el-footer>
      </el-container>
    </div>
</template>

<script>
    export default {
      data(){
        return {
          // 树节点
          tree : {
            // 数据
            data: [],
            // 当前被选中的节点
            currentNode : {},
            // 节点默认参数设置
            defaultProps: {
              children: 'list',
              label: 'permissionName'
            },
          },
          // 表格数据
          tableData : [],
          // 权限信息对话框
          powerDialog : {
            // 是否显示
            isShow : false,
            // 表格承载的数据
            form : {
              id : '',
              // 类型
              type : '',
              // 菜单名称
              permissionName : '',
              // 菜单对应的name属性，仅按钮使用
              name : '',
              // 上级菜单
              parentId : '',
              // 菜单URL
              url : '',
              // 授权标识
              perms : '',
              // 排序号
              orderNum : '',
              // 图标
              icon : '',
              parentPermissionName :''
            },
            // 辅助的信息
            assist:{
              // 上级菜单名称
              // parentName : '',
              // 类型数组
              typeArr : ['目录', '菜单', '按钮'],
            },
          }
        }
      },
      mounted(){
        this.init();
      },
      watch:{
        $route(to,from){
          if(to.name === 'powerManageIndex') {
            this.init();
          }
        }
      },
      methods:{
        // 初始化
        init(){
          // 搜索
          this.searchBtnClickHandle();
        },
        // 清理数据
        clear(){
          this.tree.currentNode = {};
          this.powerDialog.form = this.$tool.clearObj({}, this.powerDialog.form);
          // this.powerDialog.assist.parentName = '';
        },
        // 点击树
        handleNodeClick(data) {
          this.tree.currentNode = data;
          this.tableData=[data];
//          let list = data.list;
//          this.tableData = list.length > 0 ? data.list : [data];
        },
        // 搜索按钮
        searchBtnClickHandle(){
          this.clear();
          let params = this.$tool.jsonToForm({});
          this.$store.dispatch('permissionFindAllMenu', params).then(function(res){
            if(res.success){
              this.tree.data = res.data;
            }
          }.bind(this));
        },
        // 修改按钮
        itemUpdateClick(row){
          this.$tool.cloneObj(this.powerDialog.form, row);
          let params = this.$tool.filterObj({}, this.powerDialog.form);
          // this.powerDialog.assist.parentName = this.tree.currentNode.permissionName;
          this.powerDialog.isShow = true;
        },
        // 删除按钮
        itemDeleteClick(row){
          this.$confirm('此操作将永久删除, 是否继续?', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            })
            .then(function(){
              let params = this.$tool.jsonToForm({ id : row.id});
              this.$store.dispatch('permissionDelete', params).then(function(res){
                if(res.success){
                  this.$message({
                    type : 'success',
                    message : '删除成功'
                  })
                  this.tableData = [];
                  this.searchBtnClickHandle();
                } else {
                  this.$message({
                    type : 'error',
                    message : res.message || '删除失败！！'
                  })
                }
              }.bind(this))
            }.bind(this))
        },
        // 添加按钮
        addBtnClickHandle(){
          let form = this.powerDialog.form;
          let node = this.tree.currentNode;
//          console.log('node',node);
          let type = parseInt(node.type);
          if(node){
            form.parentId = node.id || '0';
            form.url = node.url || '';
            form.type = type >= 0 && type < 2 ? type + 1 : type;
            this.powerDialog.form.parentPermissionName = node.permissionName;
          }
          this.powerDialog.isShow = true;
        },
        // 对话框---关闭
        handleClose(){
          this.clear();
          this.powerDialog.isShow = false;
        },
        // 对话框---提交按钮
        submitBtnClickHandle(){
          let name = 'permissionAdd';
          let form = this.powerDialog.form;
          // 如果类型为目录，则上级目录没有
          if(form.type == 0) form['parentId'] = '0';
          let data = this.$tool.filterObj({}, form);
          if(!data.url){
            data.url = '';
          }
          let params = this.$tool.jsonToForm(data);
          if(params.get('id')){
            name = 'permissionUpdate';
//            console.log(1111111, params)
//            return;
          }

//          console.log('data:', data)
//          return;

          this.$store.dispatch(name, params).then(function (res) {
            if(res.success){
              this.$message({
                type : 'success',
                message : '操作成功'
              })
              this.powerDialog.isShow = false;
              this.searchBtnClickHandle();
            } else {
              this.$message({
                type : 'error',
                message : '操作失败'
              })
            }
          }.bind(this))
        },
      }
    }
</script>

<style>

</style>
