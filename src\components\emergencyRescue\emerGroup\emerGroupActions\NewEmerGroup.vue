<template>
  <div id="newEmerGroup">
    <div class="background-style">
      <el-col :span="16" :offset="4" class="primary-background-title">
        {{dialogTitle}}
      </el-col>
      <el-col :span="16" :offset="4">
        <el-form :model="form" :rules="rules" ref="groupForm" label-width="100px" class="demo-ruleForm">
          <el-col :span="24">
            <el-col :span="12">
              <el-form-item label="队伍名称：" prop="groupName">
                <el-input v-model="form.groupName"
                          placeholder="请输入" style="width: 100%">
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="队伍专业：" prop="groupMajor">
                <el-input v-model="form.groupMajor"
                          placeholder="请输入" style="width: 100%">
                </el-input>
              </el-form-item>
            </el-col>
          </el-col>
          <el-col :span="24">

            <el-col :span="12">
              <el-form-item label="驻扎地：" prop="location">
                <el-input v-model="form.location"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="队长：" prop="leader">
                <el-input v-model="form.leader"></el-input>
              </el-form-item>
            </el-col>
          </el-col>
          <el-col :span="24">
            <el-col :span="12">
              <el-form-item label="联系电话：" prop="phone">
                <el-input v-model="form.phone"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="成立时间：">
                <el-date-picker
                  v-model="form.createDate"
                  type="date"
                  style="width: 100%"
                  placeholder="选择日期">
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-col>
          <el-col :span="24">
            <el-form-item label="装备：" prop="equipment">
              <el-input v-model="form.equipment" type="textarea"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-button style="float: right;margin-left: 20px" @click="returnClick()">返回</el-button>
            <el-button type="primary" style="float: right;margin-left: 20px" @click="addGroupClick()">保存</el-button>
          </el-col>
        </el-form>
      </el-col>
    </div>
  </div>
</template>

<script>
  export default {
    name: "newEmerGroup",
    data() {
      return {
        dialogTitle: '新增队伍',
        form: {
          groupName: '',
          groupMajor: '',
          location: '',
          equipment: '',
          leader: '',
          groupId: 0,
          phone:'',
          createDate:'',
        },
        rules: {
          groupName: [{required: true, message: '请输入小组名称', trigger: 'change'}],
          groupMajor: [{required: true, message: '请输入小组专业', trigger: 'change'}],
          location: [{required: true, message: '请输入地点', trigger: 'change'}],
        },
        groupData: [],
        edit: false,
      }
    },
    watch: {
      $route(to, from) {
        if (from.name === 'emerGroup' && this.$route.name === 'newEmerGroup') {
          this.initEditData()
        }
      }
    },
    mounted: function () {
      this.initEditData()
    },
    methods: {
      returnClick: function () {
        this.$refs['groupForm'].resetFields();
        this.$router.go(-1);
      },
      initEditData: function () {
        this.edit = this.$route.params.edit
        if (this.edit) {
          this.dialogTitle = "编辑队伍"
          this.form.groupId = this.$route.params.groupId
          this.form.groupName = this.$route.params.groupName
          this.form.groupMajor = this.$route.params.major
          this.form.leader = this.$route.params.leader
          this.form.location = this.$route.params.location
          this.form.equipment = this.$route.params.equipment
          this.form.phone=this.$route.params.phone
          this.form.createDate=new Date(this.$route.params.createDate)
        } else {
          this.dialogTitle = "新增队伍";
          this.form.createDate=new Date();
        }
      },
      addGroupClick: function () {
        this.$refs['groupForm'].validate((valid) => {
          if (valid) {
            var params = new URLSearchParams()
            params.append("groupName", this.form.groupName)
            params.append("major", this.form.groupMajor)
            params.append("location", this.form.location)
            params.append("equipment", this.form.equipment)
            params.append("leader", this.form.leader)
            params.append("phone",this.form.phone)
            params.append("createTime",this.form.createDate)
            params.append("companyId",this.$tool.getStorage('LOGIN_USER').companyId);
            if (this.edit) {
              params.append("id", this.form.groupId)
            }
            this.$http.post("group/addOrUpdate", params).then(function (res) {
              if (res.data.success) {
                if(this.edit){
                  this.$message.success("编辑成功");
                  this.$router.go(-1);
                }else{
                  this.$message.success("创建成功")
                  this.$router.push({name:'newGroupMember',params:{groupId:res.data.data.id
                    ,groupName:res.data.data.groupName,leader:res.data.data.leader,location:res.data.data.location
                    ,equipment:res.data.data.equipment,major:res.data.data.major,createTime:res.data.data.createTime,phone:res.data.data.phone}});
                }
                this.$refs['groupForm'].resetFields();
              } else {
                this.$message.error(res.data.message)
              }
            }.bind(this)).catch(function (err) {
            }.bind(this))
          } else {
            console.log('error submit!!');
            return false;
          }
        })
      }
    }
  }
</script>

<style scoped>

</style>
