<template>
  <el-container class="container">
    <el-main>
      <el-form ref="form" label-width="150px" :rules="rules" :model="form">
        <el-row type="flex" class="row">
          <el-col :span="8">
            <el-form-item label="名称" prop="title">
              <span v-if="pageStatus === 'view'">{{form.title}}</span>
              <el-input v-if="pageStatus === 'edit'" v-model="form.title"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="年份" prop="year">
              <span v-if="pageStatus === 'view'">{{form.year && $tool.formatDateTime(form.year).substring(0,4)}}</span>
              <el-date-picker
                v-if="pageStatus === 'edit'"
                v-model="form.year"
                type="year"
                placeholder="选择日期">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="预算费用（元）" prop="totalBudget">
              <span v-if="pageStatus === 'view'">{{form.totalBudget}}</span>
              <el-input v-if="pageStatus === 'edit'" v-model="form.totalBudget"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row  class="row" style="border:1px solid #f1f1f1;padding:10px 0px;"  v-if="pageStatus === 'edit'">
          <el-col :span="24">
            <el-form-item>
              <el-button type="danger"  size="mini"
                         icon="el-icon-plus"
                         @click="addItemHandle">项目</el-button>
              <el-button type="success"  size="mini"
                         v-if="assist.costBudgetItems.id"
                         icon="el-icon-edit"
                         @click="editItemHandle">修改项目</el-button>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="项目">
              <el-input v-model="assist.costBudgetItems.item"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="费用预算（元）">
              <el-input v-model="assist.costBudgetItems.itemTotalBudget"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item label="备注">
              <el-input
                v-model="assist.costBudgetItems.remark"
                type="textarea" :rows="4" ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" class="row">
          <el-col :span="24">
            <el-form-item label="项目" prop="costBudgetItems">
              <el-table
                highlight-current-row
                @current-change="handleCurrentChange"
                :data="form.costBudgetItems">
                <el-table-column
                  type="index"
                  label="序号"
                  width="50">
                </el-table-column>
                <el-table-column
                  prop="item"
                  label="项目"
                  show-overflow-tooltip
                  width="200">
                </el-table-column>
                <el-table-column
                  prop="itemTotalBudget"
                  label="费用预算（元）"
                  show-overflow-tooltip
                  width="200">
                </el-table-column>
                <el-table-column
                  prop="remark"
                  label="备注"
                  show-overflow-tooltip
                  width="200">
                </el-table-column>
                <el-table-column
                  label="操作"
                  width="300"
                  align="center"
                  label-class-name="inner-header-style">
                  <template slot-scope="scope">
                    <el-button
                      v-if="scope.row.id"
                      type="danger"  size="mini"
                      @click="detailBtnClickHandle(scope.$index, scope.row, form.costBudgetItems)">明细</el-button>
                    <el-button
                      v-if="pageStatus === 'edit'"
                      type="danger"  size="mini" @click="deleteBtnClickHandle(scope.$index, scope.row, form.costBudgetItems)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row type="flex" class="row">
          <el-col :span="24">
            <el-form-item label="情况说明">
              <span v-if="pageStatus === 'view'">{{form.remark}}</span>
              <el-input type="textarea" :rows="4" v-if="pageStatus === 'edit'" v-model="form.remark"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row type="flex" class="row" v-if="$route.params.id">
          <el-col :span="24">
            <el-form-item label="附件：">
              <fileUpload ref="upload" :data="upload"></fileUpload>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" class="row" justify="center">
          <el-button
            @click="submitBtnClickHandle"
            v-if="pageStatus === 'edit'"
            size="small" :span="2" type="primary">提交</el-button>
          <el-button size="small" :span="2"  @click="$router.back();">返回</el-button>
        </el-row>
      </el-form>
    </el-main>
    <el-footer>
      <el-dialog
        title="项目明细"
        :visible.sync="dialog.isShow"
        width="80%"
        :before-close="handleClose">
        <el-form label-width="120px">
          <el-row  class="row" v-if="pageStatus === 'edit'">
            <el-col :span="8">
              <el-form-item label="项目：">
                <el-input v-model="dialog.costBudgetItems.item"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="费用预算（元）">
                <el-input v-model="dialog.costBudgetItems.itemBudget"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item>
                <el-button
                  type="danger"  size="mini"
                  @click="dialogAddBtnClickHandle">添加</el-button>
              </el-form-item>
            </el-col>
            <el-col :span="16">
              <el-form-item label="备注">
                <el-input
                  v-model="dialog.costBudgetItems.remark"
                  type="textarea" :rows="2" ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex" class="row">
            <el-col :span="24">
              <el-form-item label="项目" prop="costBudgetItems">
                <el-table
                  show-summary
                  :data="dialog.tableData">
                  <el-table-column
                    type="index"
                    label="序号"
                    width="50">
                  </el-table-column>
                  <el-table-column
                    prop="item"
                    label="项目"
                    show-overflow-tooltip
                    width="200">
                  </el-table-column>
                  <el-table-column
                    prop="itemBudget"
                    label="费用预算（元）"
                    show-overflow-tooltip
                    width="100">
                  </el-table-column>
                  <el-table-column
                    prop="remark"
                    label="备注"
                    show-overflow-tooltip
                    width="200">
                  </el-table-column>
                  <el-table-column
                    v-if="pageStatus === 'edit'"
                    label="操作"
                    width="200"
                    align="center"
                    label-class-name="inner-header-style">
                    <template slot-scope="scope">
                      <el-button type="danger"  size="mini" @click="dialogDeleteBtnClickHandle(scope.row)">删除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-dialog>
    </el-footer>
  </el-container>
</template>

<script>

  import fileUpload from '@/components/common/fileUpload'
  export default {
    components: {
      fileUpload
    },
    data(){
      return {
        form : {
          // 名称
          title : '',
          // 年份
          year : '',
          // 预算总额
          totalBudget : '',
          // 项目
          costBudgetItems : [],
          // 情况说明
          remark : '',
        },
        assist : {
          // 当前被选中项目的index
          selectedIndex : -1,
          // 项目
          costBudgetItems : {
            // 计划ID
            budgetPlanId : -1,
            id : '',
            // 项目
            item : '',
            // 预算费用
            itemTotalBudget : '',
            // 备注
            remark : '',
          },
        },
        // 上传文件
        upload : {
          // 上传参数
          params : {
            contentId: '',
            contentType: 8
          },
          btns : {
            // 上传按钮
            upload : {
              isShow : true,
            },
            // 删除按钮
            delete : {
              isShow : true,
            },
          },
        },
        // 对话框
        dialog : {
          // 是否显示
          isShow : false,
          // 项目
          costBudgetItems : {
            // 项目ID
            budgetItemId : -1,
            // 本子项ID
            id : '',
            // 项目
            item : '',
            // 预算费用
            itemBudget : '',
            // 备注
            remark : '',
          },
          // 表格
          tableData : [],

        },
        rules: {
          title: [
            {required: true, message: '请输入名称', trigger: 'blur'},
          ],
          year: [
            { type: 'date', required: true, message: '请选择日期', trigger: 'blur' }
          ],
          totalBudget: [
            {required: true, message: '请输入内容', trigger: 'blur'},
          ],
          costBudgetItems: [
            { type: 'array', required: true, message: '请至少添加一个项目', trigger: 'blur' }
          ],
        },
        // 状态
        pageStatus : 'edit'
      }
    },
    watch:{
      $route(to,from){
        // 如果来至列表页
        if(from.name === 'costBudgetIndex'){
          this.init();
        }
      }
    },
    created(){
      this.init();
    },
    mounted(){
      this.init();
    },
    methods:{
      // 初始化
      init(){
        if(this.$route.params.status){
          this.searchBtnClickHandle();
        } else {
          this.clear();
        }
      },
      // 清空数据
      clear(){
        this.form = this.$tool.clearObj({}, this.form);
        this.assist.costBudgetItems = this.$tool.clearObj({}, this.assist.costBudgetItems);
        this.pageStatus = 'edit';
      },
      // 添加到项目
      addItemHandle(){
        let data = this.assist.costBudgetItems;
        if(!data.item || !data.itemTotalBudget){
          this.$message({
            type : 'error',
            message : '项目名称、预算费用不得为空！！'
          })
          return;
        }
        this.form.costBudgetItems.push({
          item : data.item,
          itemTotalBudget : data.itemTotalBudget,
          remark : data.remark,
        })
      },
      // 修改项目
      editItemHandle(){
        let params = this.assist.costBudgetItems;
        this.$store.dispatch('costBudgetItemAddOrUpdate', params).then(function(res){
          if(res.success){
            if(res.success){
              this.$message({
                type : 'success',
                message : '更新成功'
              })
              this.searchBtnClickHandle();
            } else {
              this.$message({
                type : 'error',
                message : res.message || '更新失败！！'
              })
            }
          }
        }.bind(this))
      },
      // 格式化时间
      formatDateTime(row, column, cellValue){
        let pro = column.property;
        let num = 10;
        // 年份4位 1999
        if(pro === 'createYear') num = 4;
        let str = this.$tool.formatDateTime(row[pro] || 0);
        return str ? str.substring(0, num) : str;
      },
      // 提交---添加或更新
      submitBtnClickHandle(){
        let id = this.$route.params.id;
        let params = Object.assign({}, this.form);
        if(id) params['id'] = id;

        this.$refs['form'].validate(function(valid) {
          if(valid){
            this.$store.dispatch('costBudgetPlanAddOrUpdate', params).then(function (res) {
              if(res.success){
                this.$message({
                  type : 'success',
                  message : '操作成功'
                })
                this.$router.push({ name : 'costBudgetIndex' })
              } else {
                this.$message({
                  type : 'error',
                  message : '操作失败'
                })
              }
            }.bind(this))
          } else {
            return false;
          }
        }.bind(this))
      },
      // 根据id搜索信息
      searchBtnClickHandle(){
        this.clear();
        let id = this.$route.params.id;
        let btns =  this.upload.btns;
        this.upload.params.contentId = id;
        this.$store.dispatch('costBudgetPlanShow', { id : id }).then(function(res){
          if(res.success){
            let list = res.data;
            this.$tool.cloneObj(this.form, list);
            // 将 string 转化为 date
            this.form.year = new Date(this.form.year);
            this.pageStatus = this.$route.params.status || 'edit';
            btns.delete.isShow = btns.upload.isShow = this.pageStatus == 'view' ? false : true;
          }
        }.bind(this));
      },
      // 表格--单选
      handleCurrentChange(val){
        this.$tool.cloneObj(this.assist.costBudgetItems, val);

      },
      // 删除子项
      deleteBtnClickHandle(index, item, rows) {
        rows.splice(index, 1);
      },
      // 明细按钮
      detailBtnClickHandle(index, item, rows) {
        this.dialog.costBudgetItems.budgetItemId = item.id;
        // 获取子项列表
        this.getSubItemList(item.id);
        this.dialog.isShow = true;
      },
      // 获取预算计划子项列表
      getSubItemList(itemId){
        let params = {
          // 计划ID
          id : itemId
        }
        this.$store.dispatch('costBudgetItemShow', params).then(function(res){
          if(res.success){
            let list = res.data.costBudgetSubItems;
            this.dialog.tableData = list || [];
          }
        }.bind(this));
      },
      // 对话框---添加按钮
      dialogAddBtnClickHandle(){
        let data = this.dialog.costBudgetItems;
        if(data.item == '' || data.itemBudget == ''){
          this.$message({
            type : 'error',
            message : '项目或者预算费用不得为空！！'
          })
          return;
        }
        let itemId = this.dialog.costBudgetItems.budgetItemId;
        let params = {
          item : data.item,
          itemBudget : data.itemBudget,
//          budgetPlanId : this.$route.params.id,
          budgetItemId : itemId,
        };
        this.updateItem(params);

      },
      // 对话框---添加或者删除子项
      updateItem(params){
        this.$store.dispatch('costBudgetSubItemAddOrUpdate', params).then(function(res){
          if(res.success){
            this.$message({
              type : 'success',
              message : '操作成功'
            })
            this.getSubItemList(this.dialog.costBudgetItems.budgetItemId);
          } else {
            this.$message({
              type : 'error',
              message : '操作失败'
            })
          }
        }.bind(this));
      },
      // 对话框---删除按钮
      dialogDeleteBtnClickHandle(item){
        this.$confirm('此操作将永久删除, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
          .then(function(){
            this.$store.dispatch('costBudgetSubItemDelete', {
              id : item.id
            }).then(function(res){
              if(res.success){
                this.$message({
                  type : 'success',
                  message : '删除成功'
                })
                this.getSubItemList(this.dialog.costBudgetItems.budgetItemId);
              } else {
                this.$message({
                  type : 'error',
                  message : '删除失败'
                })
              }
            }.bind(this));
          }.bind(this))
      },
      // 关闭对话框之前
      handleClose(){
        this.dialog = this.$tool.clearObj({},this.dialog);
        this.dialog.isShow = false;
      }
    }
  }
</script>

<style>
  .container{
    background:#fff;
    padding:0px 20px 20px;
  }
  .row{
    margin-top:10px;
  }
</style>
