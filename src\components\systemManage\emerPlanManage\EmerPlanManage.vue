<template>
  <div id="emerPlanManage" style="height: 100%;width: 100%;">
    <div  class="background-style" style="padding: 0 0 0 20px">
      <el-col :span="14">
        <el-col :span="24" class="primary-background-title">预案分类管理</el-col>
        <el-col :span="24">
          <div style="float: right;margin-right: 10px">
            <!--<el-button @click="copySystemDataClick">复制系统数据</el-button>-->
            <el-button type="primary" @click="addClass">添加一级分类</el-button>
          </div>
        </el-col>
        <el-col :span="24" style="margin: 10px 0 0 0">
          <el-tree
            :data="classTree"
            :props="defaultProps"
            highlight-current
            show-checkbox
            node-key="id"
            default-expand-all
            :expand-on-click-node="false"
            :render-content="renderContent"
            style="width: 100%">
          </el-tree>
        </el-col>
      </el-col>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'emerPlanManage',
    data() {
      return {
        classTree:[],
        treeId:10,
        defaultProps: {
          children: 'subTypes',
          label: 'typeName'
        },
      }
    },
    created:function () {
      this.getClassTree();
    },
    watch:{

    },
    methods:{
      getClassTree:function () {
        this.$http.get('emgType/getAll/'+this.$tool.getStorage('LOGIN_USER').companyId).then(function (res) {
          this.classTree=res.data.data;
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message({
            showClose: true,
            message: '网络错误，请尝试重登录',
            type: 'error'
          });
        }.bind(this));
      },
      addClass:function () {
        this.$prompt('请输入分类名称', '编辑', {
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        }).then(({ value }) => {
          if(value.trim()){
            let params=new URLSearchParams;
            params.append("typeName",value.trim());
            params.append("parentId",0);
            params.append("companyId",this.$tool.getStorage('LOGIN_USER').companyId);
            this.$http.post('emgType/addOrUpdate',params).then(function (res) {
              this.$message({
                showClose: true,
                message: '添加成功',
                type: 'success'
              });
              this.getClassTree();
            }.bind(this)).catch(function (err) {
              console.log(err);
              this.$message({
                showClose: true,
                message: '网络错误，请尝试重登录',
                type: 'error'
              });
            }.bind(this));
          }else{
            this.$message({
              type: 'warning',
              message: '分类名不能为空'
            });
          }
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '取消输入'
          });
        });
      },
      append(data) {
        this.$prompt('请输入分类名称', '编辑', {
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        }).then(({ value }) => {
          if(value.trim()){
            let params=new URLSearchParams;
            params.append("typeName",value.trim());
            params.append("parentId",data.id);
            params.append("companyId",this.$tool.getStorage('LOGIN_USER').companyId);
            this.$http.post('emgType/addOrUpdate',params).then(function (res) {
              this.$message({
                showClose: true,
                message: '添加成功',
                type: 'success'
              });
              this.getClassTree();
            }.bind(this)).catch(function (err) {
              console.log(err);
              this.$message({
                showClose: true,
                message: '网络错误，请尝试重登录',
                type: 'error'
              });
            }.bind(this));
          }else{
            this.$message({
              type: 'warning',
              message: '分类名不能为空'
            });
          }
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '取消输入'
          });
        });
      },
      edit(data){
        this.$prompt('请输入修改后的名称', '编辑', {
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        }).then(({ value }) => {
          if(value.trim()){
            let params=new URLSearchParams;
            params.append("typeName",value.trim());
            params.append("id",data.id);
            params.append("companyId",this.$tool.getStorage('LOGIN_USER').companyId);
            this.$http.post('emgType/addOrUpdate',params).then(function (res) {
              this.$message({
                showClose: true,
                message: '修改成功',
                type: 'success'
              });
              this.getClassTree();
            }.bind(this)).catch(function (err) {
              console.log(err);
              this.$message({
                showClose: true,
                message: '网络错误，请尝试重登录',
                type: 'error'
              });
            }.bind(this));
          }else{
            this.$message({
              type: 'warning',
              message: '分类名不能为空'
            });
          }
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '取消输入'
          });
        });
      },
      remove(node, data) {
        this.$confirm('此操作将永久删除该工程, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let params=new URLSearchParams;
          params.append("id",data.id);
          params.append("companyId",this.$tool.getStorage('LOGIN_USER').companyId);
          this.$http.post('emgType/delete',params).then(function (res) {
            this.$message({
              showClose: true,
              message: '删除成功',
              type: 'success'
            });
            this.getClassTree();
          }.bind(this)).catch(function (err) {
            console.log(err);
            this.$message({
              showClose: true,
              message: '网络错误，请尝试重登录',
              type: 'error'
            });
          }.bind(this));
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });
        });
      },

      renderContent(h, { node, data, store }) {
        if(node.level===2||node.level>2){
          return (
            <span style="flex: 1; display: flex; align-items: center; justify-content: space-between; font-size: 14px; padding-right: 8px;">
            <span>
            <span>{node.label}</span>
          </span>
          <span>
          <el-button style="font-size: 14px;width: 40px" type="text"></el-button>
          <el-button style="font-size: 14px;color: #67C23A" type="text" on-click={ () => this.edit(data) }>编辑</el-button>
          <el-button style="font-size: 14px;color: #E60228" type="text" on-click={ () => this.remove(node, data) }>删除</el-button>
          </span>
          </span>);
        }else{
          return (
            <span style="flex: 1; display: flex; align-items: center; justify-content: space-between; font-size: 14px; padding-right: 8px;">
            <span>
            <span>{node.label}</span>
          </span>
          <span>
          <el-button style="font-size: 14px;" type="text" on-click={ () => this.append(data) }>添加子分类</el-button>
          <el-button style="font-size: 14px;color: #67C23A" type="text" on-click={ () => this.edit(data) }>编辑</el-button>
          <el-button style="font-size: 14px;color: #E60228" type="text" on-click={ () => this.remove(node, data) }>删除</el-button>
          </span>
          </span>);
        }
      },
      copySystemDataClick:function () {
        this.$http.get('emgType/copyEmgTypeAndPlan/0'+'/'+this.$tool.getStorage('LOGIN_USER').companyId).then(function (res) {
          this.$message.success('复制成功！');
          this.getClassTree();
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message({
            showClose: true,
            message: '复制系统数据失败！',
            type: 'error'
          });
        }.bind(this));
      },
    }
  }
</script>
<style>
</style>
