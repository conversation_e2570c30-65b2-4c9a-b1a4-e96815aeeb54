<template>
  <div class="background-style" style="padding: 10px">
    <el-row style="margin:0">
      <el-col :span="6">
        <el-button type="primary" size="mini" @click="$router.back()">返回</el-button>
      </el-col>
    </el-row>
    <el-container>
      <el-aside width="400px" style="margin-top:20px;">
        <egrid class="navbar"
               stripe border
               maxHeight="500"
               :data="navbar.data"
               @current-change="selectionChange"
               :columns-handler="columnsHandler"
               :columns="navbar.columns"
               :column-type="navbar.columnType">
        </egrid>
      </el-aside>
      <el-main>
        <egrid class="egrid"
               stripe border
               maxHeight="500"
               :data="egrid.data"
               :columns="egrid.columns"
               :column-type="egrid.columnType">
        </egrid>
      </el-main>
    </el-container>
  </div>
</template>

<script>
  // 单元格的组件
  var Editor = {
    template:
      `<div>
          <el-button
            type="danger" size="mini" @click="downloadBtnClickHandler">下载</el-button>
        </div>`,
    props: ['row', 'col'],
    methods:{
      // 下载按钮
      downloadBtnClickHandler(){
        this.$emit('row-download', this.row)
      }
    }
  }


  export default {
    data(){
      return {
        form : {
          id : '',
        },
        // 表格---左边表名称
        navbar : {
          data : [],
          columns : [
            { label: '调查表名称', prop: 'title' },
          ],
          // columnsProps 用于定义所有 columns 公共的属性
          columnsProps: {
            fit : true,
            sortable: true,
            align : 'center',
          },
          columnsSchema : {

          },
          columnType : 'index'
        },
        // 表格----右边详细
        egrid : {
          data : [],
          columns : [
            { label: '项目', prop: 'items' },
            { label: '选择人数', prop: 'count' },
          ],
          // columnsProps 用于定义所有 columns 公共的属性
          columnsProps: {
            fit : true,
            sortable: true,
            align : 'center',
          },
          columnsSchema : {

          },
          columnType : 'index'
        }
      }
    },
    watch:{
      $route(to,from){
        if(to.name == 'eduRequirementInvestigation') {
          // 加载左边栏
          this.loadNavbar(to.params.row.data);
        }
      }
    },
    created(){
      this.loadNavbar(this.$route.params.row.data);
    },
    methods:{
      // 加载左边栏
      loadNavbar(rows){
        // 传递过来的数据
        this.navbar.data = rows.map(function(it){
          return {
            id : it.id,
            title : it.title
          }
        })
      },
      // 点击左边侧边栏
      selectionChange (row) {
        this.form.id = row.id;
        this.searchBtnClickHandle();
      },
      // 搜索按钮
      searchBtnClickHandle(){
        this.$store.dispatch('eduReqInvFind', this.form).then(function(res){
          if(res.success){
            let items = res.data.list[0].eduRequirementSurveyItems;

            let list = items.map(function(it){
              return {
                items : it.items || '',
                count : it.count || 0,
              }
            }.bind(this));
            this.egrid.data = list;
          } else {
            this.egrid.data = [];
          }
        }.bind(this));
      },
      // egrid---操作列
      columnsHandler (cols) {
        let that = this;
        return cols.concat({
          label: '操作',
          fixed: 'right',
          width: 100,
          component: Editor,
          listeners: {
            'row-download'(row){
              let setting = {
                url : 'report/edu/eduRequirementInvestExcel/' + row.id,
                filename : row.title + ".xlsx",
              }
              that.$tool.download(that, setting);
            }
          }
        });
      },
    }
  }
</script>

<style>

</style>
