<template>
  <div id="manageMenu" class="background-body">
    <moduleHeader></moduleHeader>
    <div style="position:absolute;top:0;left:0;bottom:-20px;width:150px;background-color: #0f6fc6;overflow:auto;">
      <el-row style="height: 50px;margin: 0;padding: 0">
        <div class="home-block" @click="$router.push( '/menu')">
          <img style="margin: 23px 0 0 20px;float: left" src="../../static/imgs/public/home-icon.png"/>
                    <span style="color:#f2f2f2;float: left;margin: 20px 0 0 10px;font-weight: 700;">返回首页</span>
        </div>
      </el-row>
      <el-row style="margin: 0;font-weight: 700;padding: 0">
        <el-menu
          :default-active="activeUrl"
          class="el-menu-vertical-demo"
          router
          unique-opened
          background-color="#0f6fc6"
          text-color="#fff"
          active-text-color="#fff"
          style="width: 150%;margin: 0;padding: 0">
          <el-submenu :key="group.id" :index="group.id" v-for="group in systemGroupMenu" >
            <template slot="title">
              <i :class="group.icon"></i>
              <span style="font-size: 16px">{{group.permissionName}}</span>
            </template>
            <el-menu-item :key="item.id" v-for="item in group.list" :index="item.url">{{item.permissionName}}</el-menu-item>
          </el-submenu>
          <el-menu-item :key="item.id" v-for="item in systemMenu" :index="item.url" style="font-size: 16px"><i :class="item.icon"></i>{{item.permissionName}}</el-menu-item>

        </el-menu>
      </el-row>

    </div>

    <div style="position:absolute;left:150px;right:0;top:50px;bottom:-20px;overflow:auto;min-width: 1000px;background-color: #f2f2f2">
      <keep-alive>
        <router-view></router-view>
      </keep-alive>
    </div>
  </div>
</template>
<script>
  import moduleHeader from '@/components/common/moduleHeader'

  export default {
    name: 'manageMenu',
    data() {
      return {
        userName:'用户名',
        activeUrl:'/safety-input-menu/cost-budget-index-department',
        currentPath:'费用预算',
        systemMenu:[],
        systemGroupMenu:[],
      }
    },
    created:function () {
      let tempMenu=this.$tool.getStorage('SAFE_PLATFORM_MENU').subMenu.safetyInputMenu;
      let list = [];
      for(let i=0;i<tempMenu.length;i++){
        if(tempMenu[i].list.length){
          tempMenu[i].id=String(tempMenu[i].id);
          this.systemGroupMenu.push(tempMenu[i]);
        }else{
          this.systemMenu.push(tempMenu[i]);
        }
      }
      if(tempMenu[0].list.length > 0){
        let url = tempMenu[0].list[0].url;
        this.$router.push(url);
        this.activeUrl= url;
      }else{
        let url = tempMenu[0].url;
        this.$router.push(url);
        this.activeUrl= url;
      }
      // this.$router.push('/safety-input-menu/cost-budget-index-department');//默认到达页
      if(localStorage.SAFE_PLATFORM_USERNAME){
        this.userName=localStorage.SAFE_PLATFORM_USERNAME;
      }
    },
    components: {
      moduleHeader
    },
    methods:{
      breadcrumbClick:function () {//第二级面包屑点击响应
        this.$router.push('/safety-input-menu/cost-budget-index-department');//默认到达页
        this.activeUrl='/safety-input-menu/cost-budget-index-department';
        this.currentPath='费用预算';
      }
    }

  }
</script>
<style scoped>
  .home-block{width: 100%;height: 50px;}
  .home-block:hover{background-color: #0f6fc6;cursor: pointer}
    .el-menu-item.is-active {
   background-color: #ce7036 !important;
}
</style>
