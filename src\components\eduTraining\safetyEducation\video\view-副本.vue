<template>
  <el-container class="container" id="safeEducationView">
    <el-main>
      <el-row>
        <el-col :span="24">
          <h1 style="text-align: center;">{{info.courseName}}</h1>
        </el-col>
      </el-row>
      <el-row class="lineHeight">
        <el-col :span="6"  style="text-align: center;">类型：{{info.courseType}}</el-col>
        <el-col :span="6" style="text-align: center;">讲解人：{{info.courseTeacher}}</el-col>
        <el-col :span="6" style="text-align: center;">学时：{{info.courseTime}}</el-col>
      </el-row>
      <el-row class="lineHeight">
        <el-col :span="24" style="margin:20px;">
          <img :src="info.courseImgPath"/>
        </el-col>
      </el-row>
      <el-row class="lineHeight">
        <el-col :span="24" style="margin:20px;">
          <video id="myVideo" class="video-js" :src="info.courseViewPath">
           <!-- <source  type="video/mp4">-->
          </video>
        </el-col>
      </el-row>
      <el-row class="lineHeight">
        <el-col :span="24">
          <!--    <vue-editor v-model="info.newsContent"></vue-editor>-->
          <div v-html="info.courseText"></div>
        </el-col>
      </el-row>
      <el-row type="flex" class="row" justify="center">
        <el-button size="small" :span="2"  @click="$router.back();">返回</el-button>
      </el-row>
    </el-main>
  </el-container>
</template>

<script>
  import { VueEditor } from 'vue2-editor'
  import chooseStaff from '@/components/common/chooseStaff'
  export default {
    components: {
      chooseStaff,
      VueEditor
    },
    data(){
      return {
        // info表
        info : {
          // ID
          id : '',


          // 视频名称
          courseName : '',
          // 视频类型
          courseType : '',
          // 视频讲解人
          courseTeacher : '',
          // 视频学时
          courseTime : 0,
          // 视频封面图片id
//          courseImg : '',
          // 视频封面图片
          courseImgPath : '',
          // 视频地址
          courseViewPath : '',
          // 视频内容
          courseText : '',




        },

        timer : null,
        myPlayer : null,
      }
    },
    watch:{
      $route(to,from){
        // 如果来至列表页
        if(from.name === 'safetyEducationVideoIndex'&&this.$route.name==='safetyEducationVideoView'){
          this.init();
        }
      },

    },
    mounted(){
      this.init();
    },
    beforeRouteLeave(to, form, next) {
      this.addScore();
      next()
    },

    // currentTime
    methods:{
      // 初始化
      init(){
        this.searchBtnClickHandle();
      },
      //初始化视频
      initVideo() {
        //初始化视频方法
        let myVideo = document.getElementById('myVideo')
        console.log(myVideo.duration)
//        this.$video(myVideo, {});
          this.myPlayer = null;
          this.myPlayer = this.$video(myVideo, {
            //确定播放器是否具有用户可以与之交互的控件。没有控件，启动视频播放的唯一方法是使用autoplay属性或通过Player API。
            controls: true,
            //自动播放属性,muted:静音播放
            autoplay: "muted",
            //建议浏览器是否应在<video>加载元素后立即开始下载视频数据。
            preload: "auto",
            //设置视频播放器的显示宽度（以像素为单位）
            width: "800px",
            //设置视频播放器的显示高度（以像素为单位）
            height: "400px"

          });
      },
      // 视频加分
      addScore(){
        let duration = this.myPlayer.duration();
        let currentTime = this.myPlayer.currentTime();
        let params = {
          studyId : this.$route.params.id,
          isComplete : duration == currentTime ? 1 : 0,
          courseSchedule : this.$tool.getPercent(currentTime, duration),
          courseStop : currentTime
        }
        console.log(params);
//        return;
        // eduUserStudyAddUserNews
        this.$store.dispatch('eduUserStudyAddUserCourse',params).then(function(res){
          if(res.success){
            this.$message({
              type : 'success',
              message : '阅读√'
            })
          } else {
            this.$message({
              type : 'error',
              message : res.message || '错误'
            })
          }
        }.bind(this));

      },
      formatDateTime(dateStr){
//        let pro = column.property;
        let num = 10;
        let str = this.$tool.formatDateTime(dateStr) || '';
        return str ? str.substring(0, num) : str;
      },


      // 根据id搜索信息
      searchBtnClickHandle(){
        let id = this.$route.params.id;
//        console.log('id = ', id)
//        this.viewOrEdit = true;

        this.$store.dispatch('eduCourseFindById', { id : id }).then(function(res){
          if(res.success){

            this.info.sourceSrc = '';
//            return;
            let list = res.data;
            // 发布培训信息
            Object.entries(list).forEach(function(it){
              if(it[1] && this.info.hasOwnProperty(it[0])){
                this.info[it[0]] = it[1];
              }
            }.bind(this));
            clearTimeout(this.timer)
            this.timer = setTimeout(function(){
              this.initVideo();
            }.bind(this), 300)

          } else {
            this.$message({
              type : 'error',
              message : res.message || '错误'
            })
          }
        }.bind(this));
      },

    }
  }
</script>

<style>
  .container{
    background:#fff;
    padding:0px 20px 20px;
  }
  #safeEducationView .lineHeight{
    margin-top:20px;
  }
  .title{
    background:rgba(64,158,255,.1);
    color:#0f6fc6;
    border: 1px solid rgba(64,158,255,.2);
    border-radius:5px;
  }
  .row{
    margin-top:10px;
  }
</style>
