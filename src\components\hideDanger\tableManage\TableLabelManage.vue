<template>
  <div id="tableLabelManage">
    <div class="background-style">
      <el-col :xs="14" :sm="14" :md="14" :lg="14" :xl="10" style="padding:10px 0 0 20px">
        <el-col :span="24" class="primary-background-title" style="margin-top: 10px;">
          本公司检查表分类管理
        </el-col>
        <el-col :span="24">
          <div style="float: right;margin:0 0 10px 0">
            <!--<el-button @click="copySystemDataClick">复制系统数据</el-button>-->
            <el-button type="primary" plain  @click="addLabel">添加一级分类</el-button>
          </div>
        </el-col>
        <el-col :span="24">
          <el-tree
            :data="treeArray"
            :props="defaultProps"
            empty-text="标签加载中"
            highlight-current
            default-expand-all
            style="width: 100%"
            :expand-on-click-node="false">
            <span class="custom-tree-node" slot-scope="{ node, data }" style="width: 100%;">
              <span style="float: left">{{ node.label }}</span>
              <span style="float: right">
                <el-button
                  type="text"
                  size="mini"
                  @click="update(data)">
                  修改
                </el-button>
                <el-button
                  type="text"
                  size="mini"
                  style="color: #f56c6c"
                  @click="remove(node, data)">
                  删除
                </el-button>
                <el-button
                  type="text"
                  size="mini"
                  style="color: #E6A23C"
                  @click="append(data)">
                  增加子分类
                </el-button>
              </span>
            </span>
          </el-tree>
        </el-col>
      </el-col>
    </div>
    <!--新增和编辑对话框-->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible">
     分类名称：<el-input style="width: 200px" v-model="labelInput"></el-input>
      <div slot="footer" class="dialog-footer">
        <el-button type="success" @click="determineSave">保存</el-button>
        <el-button @click="dialogVisible=false">返回</el-button>
      </div>
    </el-dialog>
    <!--新增和编辑对话框结束-->
  </div>
</template>
<script>
  export default {
    name: 'tableLabelManage',
    data() {
      return {
        //----------------------------树形标签-------------------------
        treeArray:[],
        defaultProps: {
          children: 'dangerInspectTableLabels',
          label: 'label'
        },

        dialogTitle:'新增分类',
        dialogVisible:false,
        labelInput:'',
        parentId:'',//父节点的ID
        currentlevel:'',//需要新增的分类的层级
        currentId:'',//本节点的ID

      }
    },
    mounted:function () {
      this.getAllLabels();
    },
    watch:{
      $route(to,from){
        if(this.$route.name==='tableLabelManage'){
          this.getAllLabels();
        }
      }
    },
    methods:{
      //添加子分类
      append(data) {
        this.parentId=data.id;
        this.labelInput='';
        this.dialogTitle='新增分类';
        this.currentlevel=data.level+1;
        this.dialogVisible=true;
      },
      //删除分类
      remove(node, data) {
        this.$confirm('确定删除该分类?', '提示', {
          confirmButtonText: '删除',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$http.get('danger/inspectTableLabel/delete/'+data.id+'/'+this.$tool.getStorage('LOGIN_USER').companyId).then(function (res) {
            if (res.data.success) {
              this.getAllLabels();
              this.dialogVisible=false;
            }
          }.bind(this)).catch(function (err) {
            console.log(err);
            this.$message({
              showClose: true,
              message: '删除分类失败',
              type: 'error'
            });
          }.bind(this));
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });
        });

      },
      //修改分类名称
      update(data){
        this.currentId=data.id;
        this.labelInput=data.label;
        this.dialogTitle='编辑分类';
        this.dialogVisible=true;
      },
      //确定修改或者添加分类
      determineSave(){
        if(this.labelInput.trim()){
          if(this.dialogTitle==='新增分类'){
            let params=new URLSearchParams;
            params.append("label",this.labelInput.trim());
            params.append("parentId",this.parentId);
            params.append("level",this.currentlevel);
            params.append("companyId",this.$tool.getStorage('LOGIN_USER').companyId);
            this.$http.post('danger/inspectTableLabel/add',params).then(function (res) {
              if (res.data.success) {
                this.getAllLabels();
                this.dialogVisible=false;
              }
            }.bind(this)).catch(function (err) {
              console.log(err);
              this.$message({
                showClose: true,
                message: '新增分类失败',
                type: 'error'
              });
            }.bind(this));
          }else{
            let params=new URLSearchParams;
            params.append("id",this.currentId);
            params.append("label",this.labelInput.trim());
            params.append("companyId",this.$tool.getStorage('LOGIN_USER').companyId);
            this.$http.post('danger/inspectTableLabel/update',params).then(function (res) {
              if (res.data.success) {
                this.getAllLabels();
                this.dialogVisible=false;
              }
            }.bind(this)).catch(function (err) {
              console.log(err);
              this.$message({
                showClose: true,
                message: '修改分类失败',
                type: 'error'
              });
            }.bind(this));
          }

        }else{
          this.$message.warning('请填写分类名称！');
        }

      },
      //添加一级分类
      addLabel(){
        this.parentId=0;
        this.labelInput='';
        this.dialogTitle='新增分类';
        this.currentlevel=1;
        this.dialogVisible=true;
      },
      getAllLabels:function () {
        this.$http.get('danger/inspectTableLabel/getLabelStructure?companyId='+this.$tool.getStorage('LOGIN_USER').companyId).then(function (res) {
          if (res.data.success) {
            this.treeArray=res.data.data;
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message({
            showClose: true,
            message: '检查表分类读取失败',
            type: 'error'
          });
        }.bind(this));
      },
      copySystemDataClick:function () {
        this.$http.get('danger/inspectTableLabel/copyLabelStructure/0'+'/'+this.$tool.getStorage('LOGIN_USER').companyId).then(function (res) {
          this.$message.success('复制成功！');
          this.getAllLabels();
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message({
            showClose: true,
            message: '复制系统数据失败！',
            type: 'error'
          });
        }.bind(this));
      },
    }
  }
</script>
<style>
</style>
