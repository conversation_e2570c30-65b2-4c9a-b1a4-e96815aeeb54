<template>
  <div id="managerView">
    <div class="managerView-mainImg">
      <el-row style="margin: 0">
        <el-col :span="12" style="padding-right: 5px">
          <el-col
            :span="24"
            class="card-shadow-style"
            style="height: 400px; background-color: white"
          >
            <div
              style="
                height: 35px;
                line-height: 40px;
                margin: 0 20px 0 20px;
                border-bottom: 2px solid #049ff1;
                color: #049ff1;
                font-size: large;
              "
            >
              年度安全概况
            </div>
            <div style="padding: 5px 0 0 20px">
              <el-row style="margin: 0"> 隐患排查与治理 </el-row>
              <el-row style="margin: 10px 0 0 0">
                <el-col
                  :span="6"
                  style="font-size: 14px; text-align: center; color: gray"
                  >安全检查</el-col
                >
                <el-col
                  :span="6"
                  style="font-size: 14px; text-align: center; color: gray"
                  >发现隐患</el-col
                >
                <el-col
                  :span="6"
                  style="font-size: 14px; text-align: center; color: gray"
                  >整改隐患</el-col
                >
                <el-col
                  :span="6"
                  style="font-size: 14px; text-align: center; color: gray"
                  >未整改隐患</el-col
                >
              </el-row>
              <el-row style="margin: 0">
                <el-col :span="6" class="click-text show-text"
                  ><span
                    @click="
                      $router.push({
                        name: 'investigation',
                        params: { taskFlag: true, viewList: true },
                      })
                    "
                    >{{
                      safeIndexData.dangerIndexInfo.dangerInspectCount
                    }}</span
                  ></el-col
                >
                <!--<el-col :span="6" class="click-text show-text" ><span @click="dangerDialogTab='全部隐患';searchDangerTableData();dangerDialogVisible=true">{{safeIndexData.dangerIndexInfo.dangerCount}}</span></el-col>-->
                <el-col :span="6" class="click-text show-text"
                  ><span
                    @click="
                      dangerDialogTab = '全部隐患';
                      searchDangerTableData();
                      dangerDialogVisible = true;
                    "
                    >{{
                      safeIndexData.dangerIndexInfo.dangerReformCount +
                      safeIndexData.dangerIndexInfo.dangerUnReformCount
                    }}</span
                  ></el-col
                >
                <el-col :span="6" class="click-text show-text"
                  ><span
                    @click="
                      dangerDialogTab = '已整改';
                      searchDangerTableData();
                      dangerDialogVisible = true;
                    "
                    >{{ safeIndexData.dangerIndexInfo.dangerReformCount }}</span
                  ></el-col
                >
                <el-col :span="6" class="click-text show-text"
                  ><span
                    @click="
                      dangerDialogTab = '已超期';
                      searchDangerTableData();
                      dangerDialogVisible = true;
                    "
                    >{{
                      safeIndexData.dangerIndexInfo.dangerUnReformCount
                    }}</span
                  ></el-col
                >
              </el-row>

              <el-row style="margin: 10px 0 0 0"> 动态作业情况 </el-row>
              <el-row style="margin: 10px 0 0 0">
                <el-col
                  :span="6"
                  style="font-size: 14px; text-align: center; color: gray"
                  >重大风险</el-col
                >
                <el-col
                  :span="6"
                  style="font-size: 14px; text-align: center; color: gray"
                  >较大风险</el-col
                >
                <el-col
                  :span="6"
                  style="font-size: 14px; text-align: center; color: gray"
                  >一般风险</el-col
                >
                <el-col
                  :span="6"
                  style="font-size: 14px; text-align: center; color: gray"
                  >低风险</el-col
                >
              </el-row>
              <el-row style="margin: 0">
                <el-col :span="6" class="show-text"
                  ><span>{{
                    riskData.filter((i) => i.rlevel === "4").length
                  }}</span></el-col
                >
                <el-col :span="6" class="show-text"
                  ><span>{{
                    riskData.filter((i) => i.rlevel === "3").length
                  }}</span></el-col
                >
                <el-col :span="6" class="show-text"
                  ><span>{{
                    riskData.filter((i) => i.rlevel === "2").length
                  }}</span></el-col
                >
                <el-col :span="6" class="show-text"
                  ><span>{{
                    riskData.filter((i) => i.rlevel === "1").length
                  }}</span></el-col
                >
              </el-row>
              <el-row style="margin: 10px 0 0 0">
                <el-col
                  :span="8"
                  style="font-size: 16px; text-align: center; color: #0f6fc6"
                >
                  <div
                    style="width: 100%; height: 130px"
                    id="pieOfEmerRes"
                  ></div>
                </el-col>
                <el-col :span="8">
                  <div
                    style="width: 100%; height: 130px"
                    id="pieOfSafetyInput"
                  ></div>
                </el-col>
                <el-col :span="8">
                  <div
                    style="width: 100%; height: 130px"
                    id="pieOfEmerHandle"
                  ></div>
                </el-col>
              </el-row>
            </div>
          </el-col>
        </el-col>

        <el-col :span="12" style="padding-left: 5px">
          <el-col
            :span="24"
            class="card-shadow-style"
            style="height: 400px; background-color: white"
          >
            <div
              style="
                height: 35px;
                line-height: 40px;
                margin: 0 20px 0 20px;
                border-bottom: 2px solid #049ff1;
                color: #049ff1;
                font-size: large;
              "
            >
              隐患统计分析
            </div>
            <div style="padding: 5px 0 0 20px">
              <el-radio-group
                v-model="radioType"
                size="small"
                @change="pieDataTypeClick"
              >
                <el-radio-button
                  v-for="item in radioTypeArray"
                  :label="item.value"
                  :key="item.value"
                  >{{ item.label }}</el-radio-button
                >
              </el-radio-group>
            </div>
            <div style="padding-top: 15px">
              <div style="width: 600px; height: 400px" id="pieChart"></div>
            </div>
            <div
              style="
                position: absolute;
                right: 30px;
                bottom: 100px;
                height: 30px;
                width: 100px;
                z-index: 3;
              "
              v-show="pieIsVisual"
            >
              <div class="round-gray-text">模拟数据</div>
            </div>
          </el-col>
        </el-col>
      </el-row>
      <el-row style="margin: 0">
        <el-col :span="12" style="padding-right: 5px">
          <el-col
            :span="24"
            class="card-shadow-style"
            style="height: 400px; background-color: white"
          >
            <div
              style="
                height: 35px;
                line-height: 40px;
                margin: 0 20px 0 20px;
                border-bottom: 2px solid #049ff1;
                color: #049ff1;
                font-size: large;
              "
            >
              安全警示趋势
            </div>
            <div style="padding: 20px 0 0 10px; position: relative">
              <div
                style="width: 600px; height: 350px; margin-top: -10px"
                id="trendChart"
              ></div>
              <div
                style="
                  position: absolute;
                  right: 30px;
                  bottom: 100px;
                  height: 30px;
                  width: 100px;
                  z-index: 3;
                "
                v-show="trendIsVisual"
              >
                <div class="round-gray-text">模拟数据</div>
              </div>
            </div>
          </el-col>
        </el-col>
        <el-col :span="12" style="padding-left: 5px">
          <!--表格区-->
          <el-col
            :span="24"
            class="card-shadow-style"
            style="height: 400px; background-color: white; overflow: hidden"
          >
            <div
              style="
                height: 35px;
                line-height: 40px;
                margin: 0 20px 0 20px;
                border-bottom: 2px solid #049ff1;
                color: #049ff1;
                font-size: large;
              "
            >
              安全告警信息
              <!--表格区-->
              <div style="margin-bottom: 20px; width: 100%">
                <div style="width: 100%; margin: auto">
                  <div style="">
                    <el-table
                      v-loading="loading"
                      empty-text="暂无数据"
                      element-loading-text="同步数据中"
                      element-loading-spinner="el-icon-loading"
                      element-loading-background="rgba(255, 255, 255, 0.9)"
                      :data="alarmTableData"
                      highlight-current-row
                      :class="{ tableScroll: tableScroll }"
                      style="overflow-y: auto; height: 100%; min-height: 320px"
                      stripe
                      border
                    >
                      <el-table-column
                        prop="index"
                        width="50"
                        label="序号"
                        align="center"
                        label-class-name="header-style"
                      >
                      </el-table-column>
                      <!-- 区域名称 -->
                      <el-table-column
                        prop="dynamicName"
                        label="区域名称"
                        width="100"
                        align="center"
                        show-overflow-tooltip
                        label-class-name="header-style"
                      />
                      <!-- 告警类型 告警时间 人员/车辆编号 -->
                      <el-table-column
                        prop="alertType"
                        label="告警类型"
                        align="center"
                        show-overflow-tooltip
                        label-class-name="header-style"
                      >
                        <!-- 告警类型(3危险区域，4较大风险区域，5出边界) -->
                        <template slot-scope="scope">
                          <span v-if="scope.row.alertType == 3">危险区域</span>
                          <span v-if="scope.row.alertType == 4"
                            >较大风险区域</span
                          >
                          <span v-if="scope.row.alertType == 5">出边界</span>
                        </template>
                      </el-table-column>
                      <el-table-column
                        prop="startTime"
                        label="告警开始时间"
                        :formatter="formatDateTime"
                        align="center"
                        show-overflow-tooltip
                        label-class-name="header-style"
                      />
                      <el-table-column
                        prop="deviceCode"
                        label="人员/车辆编号"
                        align="center"
                        show-overflow-tooltip
                        label-class-name="header-style"
                      />
                    </el-table>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
        </el-col>
      </el-row>
    </div>

    <!--隐患列表-->
    <el-dialog
      :title="dangerDialogTitle"
      :visible.sync="dangerDialogVisible"
      width="80%"
    >
      <div style="margin: 0">
        <el-radio-group
          v-model="dangerDialogTab"
          size="small"
          @change="changeDangerTab"
        >
          <el-radio-button label="全部隐患"></el-radio-button>
          <el-radio-button label="已整改"></el-radio-button>
          <el-radio-button label="未整改"></el-radio-button>
        </el-radio-group>
      </div>
      <div style="margin: 5px 0 5px 0">
        <el-table
          :data="dangerTableData"
          border
          v-loading="dangerTableLoading"
          max-height="400"
          style="width: 100%"
        >
          <el-table-column
            type="index"
            align="center"
            width="50"
            fixed
            label-class-name="inner-header-style"
          >
          </el-table-column>
          <el-table-column
            prop="checkName"
            label="检查单名称"
            width="150"
            label-class-name="inner-header-style"
          >
          </el-table-column>
          <el-table-column
            prop="inspectProject"
            label="检查项目"
            width="150"
            label-class-name="inner-header-style"
          >
          </el-table-column>
          <el-table-column
            prop="inspectContent"
            min-width="400"
            label="检查标准内容"
            label-class-name="inner-header-style"
          >
          </el-table-column>
          <el-table-column
            prop="inspectResult"
            width="300"
            label="检查结果记录"
            label-class-name="inner-header-style"
          >
          </el-table-column>
          <el-table-column
            prop="deptName"
            width="200"
            label="受检单位"
            label-class-name="inner-header-style"
          >
          </el-table-column>
          <el-table-column
            min-width="200"
            label="隐患照片"
            label-class-name="inner-header-style"
          >
            <template slot-scope="scope">
              <picture-card :picFileList="scope.row.dangerPics"></picture-card>
            </template>
          </el-table-column>
          <el-table-column
            prop="changeTime"
            width="120"
            label="整改时间"
            :formatter="changeTimeFormat"
            label-class-name="inner-header-style"
          >
          </el-table-column>
          <el-table-column
            prop="changeExplain"
            width="180"
            label="整改说明"
            label-class-name="inner-header-style"
          >
          </el-table-column>
        </el-table>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dangerDialogVisible = false">确 定</el-button>
      </div>
    </el-dialog>
    <!--隐患列表结束-->

    <!--刘杰1012 增 起-->
    <!--隐患统计分析饼图点击弹出框-->
    <el-dialog
      :title="dangerDialogTitle"
      :visible.sync="pieDialogVisible"
      width="80%"
    >
      <div style="margin: 5px 0 5px 0">
        <el-table
          :data="pieChartDialogData"
          border
          v-loading="dangerTableLoading"
          max-height="400"
          style="width: 100%"
        >
          <el-table-column
            type="index"
            align="center"
            width="50"
            fixed
            label-class-name="inner-header-style"
          >
          </el-table-column>
          <el-table-column
            prop="inspectProject"
            label="检查项目"
            width="150"
            label-class-name="inner-header-style"
          >
          </el-table-column>
          <el-table-column
            prop="inspectContent"
            min-width="400"
            label="检查标准内容"
            label-class-name="inner-header-style"
          >
          </el-table-column>
          <el-table-column
            prop="inspectResult"
            width="300"
            label="检查结果记录"
            label-class-name="inner-header-style"
          >
          </el-table-column>
          <el-table-column
            prop="deptName"
            width="200"
            label="受检单位"
            label-class-name="inner-header-style"
          >
          </el-table-column>
          <el-table-column
            prop="hiddenDangerLevel"
            width="200"
            label="隐患级别"
            label-class-name="inner-header-style"
          >
          </el-table-column>
          <el-table-column
            prop="dangerType"
            width="200"
            label="隐患类型"
            label-class-name="inner-header-style"
          >
          </el-table-column>
          <el-table-column
            min-width="200"
            label="隐患照片"
            label-class-name="inner-header-style"
          >
            <template slot-scope="scope">
              <picture-card :picFileList="scope.row.dangerPics"></picture-card>
            </template>
          </el-table-column>
          <el-table-column
            prop="changeTime"
            width="120"
            label="整改时间"
            :formatter="changeTimeFormat"
            label-class-name="inner-header-style"
          >
          </el-table-column>
          <el-table-column
            prop="deadline"
            width="120"
            label="整改期限"
            :formatter="changeTimeFormat"
            label-class-name="inner-header-style"
          >
          </el-table-column>
          <el-table-column
            prop="changeExplain"
            width="180"
            label="整改说明"
            label-class-name="inner-header-style"
          >
          </el-table-column>
        </el-table>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="pieDialogVisible = false">确 定</el-button>
      </div>
    </el-dialog>
    <!--刘杰1012 增 终-->
  </div>
</template>
<script>
import safeHeader from '@/components/common/header'
import PictureCard from '@/components/common/smallComponent/pictureCard.vue'
import { mapActions } from 'vuex'
export default {
  name: 'managerView',
  data() {
    return {
      riskData: [],
      tableScroll: false,
      //搜索数据
      personOptions: [
        { value: "姓名",label: "姓名" },
      ],
      classValue: ['姓名'],
      input: '',
      year: '',//年份选择器对应的值，为date格式
      //表格数据
      tableData: [],
      alarmTableData: [],
      itemNumber: 0,
      nowPage: 1,
      pageSize: 13,
      multipleSelection: [],
      loading: false,
      //选择器的数据
      eduDegreeOptions: [
        { value: "初中",label: "初中" },
        { value: "高中",label: "高中" },
        { value: "大专",label: "大专" },
        { value: "本科",label: "本科" },
        { value: "硕士",label: "硕士" },
        { value: "博士",label: "博士" },
      ],
      genderOptions: [
        { value: false,label: '女' },
        { value: true,label: '男' },
      ],
      postList: [],
      currentCompanyPostList: [],//当前公司的岗位，只会查找一次

      dateRange: '',
      /*班组的信息*/

      //-----------------部门选择数据----------------------------
      defaultProps: {
        children: 'subDept',
        label: 'name'
      },
      searchDeptIds: [],
      //-----------------上传下载的baseurl-----------------
      cookies: true,
      uploadBaseUrl: '',
      //对话框数据
      uploadVisible: false,
      fileList: [],
      //年度安全概况
      chooseYear: '',//年份选择器对应的值，为date格式
      safeSumYear: '',//搜索的年份，为number格式
      safeIndexData: {
        "dangerIndexInfo": {
          "dangerCount": 36,
          "dangerUnReformCount": 29,
          "dangerInspectCount": 37,
          "dangerReformCount": 7
        },
        "eduInfo": {
          "eduDailyInfoTotal": 0,
          "eduDailyInfoFail": 0,
          "eduEntryTrainingFail": 0,
          "eduEntryTrainingTotal": 0,
          "eduReassignmentFail": 0,
          "eduReassignmentTotal": 11,
          "eduCertificateTotal": 23
        },
        "emgIndexInfo": {
          "emgPlanPublicCount": 8,
          "emgHandlePublicCount": 0
        },
        "safeInfo": {
          "regTotal": 148432.6,
          "budgetRate": '22.66%',
        }
      },
      //0925 刘杰增加,记录预算余额，用作扇形图中投入占的面积
      remainBudget: 506609.765,

      //隐患统计分析
      radioType: 1,
      radioTypeArray: [
        { value: 1,label: '按等级' },
        { value: 2,label: '按类型' },
        { value: 3,label: '按整改状态' },
      ],
      dangerType: [{ '': 3 },{ '个人防护': 6 },{ '安全管理类': 1 },{ '作业场所类': 1 },{ '消防设施': 4 },{ '电气管理': 3 },{ '设备设施类': 4 },{ '警示标牌': 1 },{ '文明施工': 1 }],
      dangerStatus: [{ '超期未整改': 30 },{ '待整改': 7 },{ '已整改': 14 }],
      dangerLevel: [{ '无': 1 },{ '重大(A级)': 2 },{ '一般(B级)': 11 },{ '一般(C级)': 37 }],
      pieChartData: [],//饼图数据
      pieChartDialogData: [],//饼图对话框数据 刘杰1012 增
      pieDialogVisible: false,//饼图对话框是否显示 刘杰1012 增
      pieIsVisual: false,

      //安全警示趋势数据
      trendMonthArr: ['1月','2月','3月','4月','5月','6月','7月','8月','9月','10月','11月','12月'],
      trendData: [],
      trendIsVisual: false,

      //0925刘杰 安全月报数据
      monthData: [
        { monthTest: '9月',changeflag: 1,inspecflag: 1 },
        { monthTest: '8月',changeflag: 1,inspecflag: 1 },
        { monthTest: '7月',changeflag: 1,inspecflag: 0 },
        { monthTest: '6月',changeflag: 1,inspecflag: 1 },
        { monthTest: '5月',changeflag: 0,inspecflag: 1 },
        { monthTest: '4月',changeflag: 1,inspecflag: 1 },
        { monthTest: '3月',changeflag: 1,inspecflag: 1 },
        { monthTest: '2月',changeflag: 1,inspecflag: 1 },
        { monthTest: '1月',changeflag: 1,inspecflag: 1 },
      ],
      //隐患对话框
      dangerDialogTitle: '隐患列表',
      dangerDialogVisible: false,
      dangerTableData: [],
      dangerDialogTab: '全部隐患',
      dangerTableLoading: false,
      //隐患分类了的数据
      dangerWholeData: { allDanger: [],doneDanger: [],noDanger: [] }
    }
  },
  components: {
    safeHeader,PictureCard
  },
  mounted() {
    this.getData()
    this.year=new Date();
    //获取所有用户数据
    this.getAlarmList();
    //当前公司和下级
    this.$store.dispatch("getAllDept");
    //获取所有角色
    this.$store.dispatch("getRoleList");
    //获取班组
    this.$store.dispatch("getTeam");
    //所有公司
    this.$store.dispatch("getTopAllDeptTree");

    this.uploadBaseUrl=this.$http.defaults.baseURL+'user/addBatch';
    //标题条视图按钮
    this.$store.dispatch('changeViewFlag',{ menuFlag: true,managerViewFlag: false,gisFlag: true });
    this.chooseYear=new Date();
    this.drawSafeWarningTrend();
    this.searchStaticsClick();

  },
  watch: {
    $route(to,from) {
      if(this.$route.name==='managerView') {
        //标题条视图按钮
        this.$store.dispatch('changeViewFlag',{ menuFlag: true,managerViewFlag: false,gisFlag: true });
      }
      if(this.$route.name==='userManage') {
        this.$store.dispatch("getRoleList");
      }
    }
  },
  methods: {
    getData() {
      this.$http
        .post("/sys/sysMineDynamic/findMineDynamicInfo",{})
        .then((res) => {
          const map=res.data.data.list.map((i) => {
            i.rdate=i.rdate&&JSON.parse(i.rdate);
            return i;
          });
          this.riskData=map.filter((i) => {
            return i.rdate;
          });
          this.pieOfEmerRes(1);
          this.pieOfEmerHandle(1);
          this.pieOfSafetyInput(1);
        });
    },
    //获取报警列表
    getAlarmList: function() {
      const list={
        '0868120295820036': 'Nj-112-02挖机',
        '0868120295826462': 'Nj-112-09挖机',
        '0868120295824053': 'Nj-112-06挖机',
        '0868120295820085': 'Nj-214-01 铲车',
        '0868120295826694': '00014号车',
        '0868120295826702': '00018号车',
        '0868120295826496': '00011号车',
        '0868120295826421': '00002号车',
        '0868120295821786': '00003号车',
      }
      this.$http.get('/sys/sysMineDynamic/findMineAlarmInfo').then(function(res) {
        if(res.data) {
          const a=res.data.data.list.map(i => {
            i.deviceCode=list[i.deviceCode]
            return i
          });
          if(a.length>10) {
            this.alarmTableData=a.concat(a);
            this.tableScroll=true;
          } else {
            this.alarmTableData=a;
          }

        }
      }.bind(this)).catch(function(err) {
        console.log(err);
      });
    },
    pieDataTypeClick: function(val) {
      if(val===1) {
        this.pieChartData=this.dangerLevel;
      } else if(val===2) {
        this.pieChartData=this.dangerType;
      } else {
        this.pieChartData=this.dangerStatus;
      }
      let pieChartData=this.echarts.init(document.getElementById('pieChart'));
      pieChartData.off('click')
      pieChartData.setOption({
        color: ['#C1232B','#E87C25','#27727B','#FBD659'],
        //          color:['#FF9D52','#ED704E','#FFCA6C','#999999','#C15133'],
        title: { show: false },
        tooltip: { trigger: 'item' },
        legend: {},
        series: [
          {
            name: '隐患数量',
            type: 'pie',
            radius: '55%',
            center: ['50%','40%'],
            data: this.pieChartData,
            itemStyle: {
              emphasis: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      });
      // 处理点击事件  刘杰 1012 增
      pieChartData.on('click',function(params) {
        this.searchDangerPieData(this.radioType,params.name);
        this.pieDialogVisible=true;
        //          alert("饼图点击事件： 名称："+(params.name)+"，值："+(params.value));
      }.bind(this));

    },
    //爆破作业数量
    pieOfEmerRes: function() {
      let pieChartData=this.echarts.init(document.getElementById('pieOfEmerRes'));
      var placeHolderStyle={
        label: {
          show: false
        },
        labelLine: {
          show: false
        },
        color: "rgba(0,0,0,0)",
        borderWidth: 0
      };
      pieChartData.setOption({
        title: [
          {
            text: this.riskData.filter(i => i.rtype==='1').length||'0',
            left: "36%",
            top: "40%",
            textStyle: {
              color: "#EC3535",
              fontSize: 10,
              align: "center"
            }
          }
        ],

        legend: {
          legend: {
            orient: "vertical", //设置图例的方向
            right: 10,
            top: "center",
            itemGap: 30 //设置图例的间距
          }
        },

        graphic: {
          type: "text",
          left: "28%",
          top: "75%",
          style: {
            text: "爆破作业数量",
            textAlign: "center",
            fill: "#999",
            fontSize: 10
          }
        },

        //第一个图表
        series: [
          {
            type: "pie",
            radius: ["65%","55%"],
            center: ["40%","50%"],
            startAngle: 225,
            labelLine: {
              show: false
            },
            label: {
              position: "center"
            },
            data: [
              {
                value: 100,
                itemStyle: {
                  color: "#3a3a3a"
                }
              },
              {
                value: 35,
                itemStyle: placeHolderStyle
              }
            ]
          },
          //上层环形配置
          {
            type: "pie",
            radius: ["65%","55%"],
            center: ["40%","50%"],
            hoverAnimation: false, //鼠标移入变大
            startAngle: 225,
            labelLine: {
              show: false
            },
            label: {
              position: "center"
            },
            data: [
              {
                value: 50,
                itemStyle: {
                  color: new this.echarts.graphic.LinearGradient(1,0,0,1,[
                    {
                      offset: 0,
                      color: "#FF8989"
                    },
                    {
                      offset: 1,
                      color: "#EC3535"
                    }
                  ])
                }
              },
              {
                value: 35,
                itemStyle: placeHolderStyle
              }
            ]
          },
          // 外圆线
          {
            type: "pie",
            radius: ["80%","79%"],
            center: ["40%","50%"],
            hoverAnimation: false, //鼠标移入变大
            startAngle: 225,
            labelLine: {
              show: false
            },
            label: {
              position: "center"
            },
            data: [
              {
                value: 75,
                itemStyle: {
                  color: new this.echarts.graphic.LinearGradient(1,0,0,1,[
                    {
                      offset: 0,
                      color: "#FF8989"
                    },
                    {
                      offset: 1,
                      color: "red"
                    }
                  ])
                }
              },
              {
                value: 25,
                itemStyle: placeHolderStyle
              }
            ]
          }
        ]
      });
    },
    //特种作业数量
    pieOfSafetyInput: function(val) {
      let pieChartData=this.echarts.init(document.getElementById('pieOfSafetyInput'));
      var placeHolderStyle={
        label: {
          show: false
        },
        labelLine: {
          show: false
        },
        color: "rgba(0,0,0,0)",
        borderWidth: 0
      };
      pieChartData.setOption({
        title: [
          {
            text: this.riskData.filter(i => i.rtype==='2').length||'0',
            left: "36%",
            top: "40%",
            textStyle: {
              color: "#EC3535",
              fontSize: 10,
              align: "center"
            }
          }
        ],

        legend: {
          legend: {
            orient: "vertical", //设置图例的方向
            right: 10,
            top: "center",
            itemGap: 30 //设置图例的间距
          }
        },

        graphic: {
          type: "text",
          left: "28%",
          top: "75%",
          style: {
            text: "特种作业数量",
            textAlign: "center",
            fill: "#999",
            fontSize: 10
          }
        },

        //第一个图表
        series: [
          {
            type: "pie",
            radius: ["65%","55%"],
            center: ["40%","50%"],
            startAngle: 225,
            labelLine: {
              show: false
            },
            label: {
              position: "center"
            },
            data: [
              {
                value: 100,
                itemStyle: {
                  color: "#3a3a3a"
                }
              },
              {
                value: 35,
                itemStyle: placeHolderStyle
              }
            ]
          },
          //上层环形配置
          {
            type: "pie",
            radius: ["65%","55%"],
            center: ["40%","50%"],
            hoverAnimation: false, //鼠标移入变大
            startAngle: 225,
            labelLine: {
              show: false
            },
            label: {
              position: "center"
            },
            data: [
              {
                value: 50,
                itemStyle: {
                  color: new this.echarts.graphic.LinearGradient(1,0,0,1,[
                    {
                      offset: 0,
                      color: "#FF8989"
                    },
                    {
                      offset: 1,
                      color: "#EC3535"
                    }
                  ])
                }
              },
              {
                value: 35,
                itemStyle: placeHolderStyle
              }
            ]
          },
          // 外圆线
          {
            type: "pie",
            radius: ["80%","79%"],
            center: ["40%","50%"],
            hoverAnimation: false, //鼠标移入变大
            startAngle: 225,
            labelLine: {
              show: false
            },
            label: {
              position: "center"
            },
            data: [
              {
                value: 75,
                itemStyle: {
                  color: new this.echarts.graphic.LinearGradient(1,0,0,1,[
                    {
                      offset: 0,
                      color: "#FF8989"
                    },
                    {
                      offset: 1,
                      color: "red"
                    }
                  ])
                }
              },
              {
                value: 25,
                itemStyle: placeHolderStyle
              }
            ]
          }
        ]
      });
    },
    //告警信息数量
    pieOfEmerHandle: function(val) {
      let pieChartData=this.echarts.init(document.getElementById('pieOfEmerHandle'));
      var placeHolderStyle={
        label: {
          show: false
        },
        labelLine: {
          show: false
        },
        color: "rgba(0,0,0,0)",
        borderWidth: 0
      };
      pieChartData.setOption({
        title: [
          {
            text: (this.alarmTableData.length||0)>10? (this.alarmTableData.length||0)/2:(this.alarmTableData.length||0),
            left: "36%",
            top: "40%",
            textStyle: {
              color: "#EC3535",
              fontSize: 10,
              align: "center"
            }
          }
        ],

        legend: {
          legend: {
            orient: "vertical", //设置图例的方向
            right: 10,
            top: "center",
            itemGap: 30 //设置图例的间距
          }
        },

        graphic: {
          type: "text",
          left: "28%",
          top: "75%",
          style: {
            text: "告警信息数量",
            textAlign: "center",
            fill: "#999",
            fontSize: 10
          }
        },

        //第一个图表
        series: [
          {
            type: "pie",
            radius: ["65%","55%"],
            center: ["40%","50%"],
            startAngle: 225,
            labelLine: {
              show: false
            },
            label: {
              position: "center"
            },
            data: [
              {
                value: 100,
                itemStyle: {
                  color: "#3a3a3a"
                }
              },
              {
                value: 35,
                itemStyle: placeHolderStyle
              }
            ]
          },
          //上层环形配置
          {
            type: "pie",
            radius: ["65%","55%"],
            center: ["40%","50%"],
            hoverAnimation: false, //鼠标移入变大
            startAngle: 225,
            labelLine: {
              show: false
            },
            label: {
              position: "center"
            },
            data: [
              {
                value: 50,
                itemStyle: {
                  color: new this.echarts.graphic.LinearGradient(1,0,0,1,[
                    {
                      offset: 0,
                      color: "#FF8989"
                    },
                    {
                      offset: 1,
                      color: "#EC3535"
                    }
                  ])
                }
              },
              {
                value: 35,
                itemStyle: placeHolderStyle
              }
            ]
          },
          // 外圆线
          {
            type: "pie",
            radius: ["80%","79%"],
            center: ["40%","50%"],
            hoverAnimation: false, //鼠标移入变大
            startAngle: 225,
            labelLine: {
              show: false
            },
            label: {
              position: "center"
            },
            data: [
              {
                value: 75,
                itemStyle: {
                  color: new this.echarts.graphic.LinearGradient(1,0,0,1,[
                    {
                      offset: 0,
                      color: "#FF8989"
                    },
                    {
                      offset: 1,
                      color: "red"
                    }
                  ])
                }
              },
              {
                value: 25,
                itemStyle: placeHolderStyle
              }
            ]
          }
        ]
      });
    },
    //绘制安全警示趋势图  1015刘杰 增
    drawSafeWarningTrend: function() {
      //获取当前的年份
      let nowYear=new Date().getFullYear();
      this.$http.get(`/index/safeWarningTrend/${nowYear}`).then((res) => {
        this.trendData=res.data.data[nowYear]&&Object.values(res.data.data[nowYear])||[];
        if(this.trendData.length===0) {
          this.trendData=[14,15,15,14,16,15,16,16,15,14,14,15];
          this.trendIsVisual=true;
        } else {
          this.trendIsVisual=false;
        }
        let trendChartData=this.echarts.init(document.getElementById('trendChart'));
        trendChartData.setOption({
          tooltip: {
            trigger: 'axis',
          },
          legend: {
          },
          grid: {
            left: '1%',
            right: '10%',
            bottom: '10%',
            top: '15%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: this.trendMonthArr
          },
          yAxis: {
            type: 'value'
          },
          series: [{
            name: '隐患个数',
            type: 'line',
            smooth: true,
            data: this.trendData,
            label: {
              normal: {
                show: false,
                position: 'top',
                color: '#FBD249',
              }
            },
            areaStyle: {
              normal: {
                //                    color:'#0F6FC6',
                color: new this.echarts.graphic.LinearGradient(0,0,0,1,[{
                  offset: 0,
                  color: '#FBD249'
                },{
                  offset: 1,
                  color: '#fff'
                }])

              }
            },

            lineStyle: {
              normal: {
                color: '#F6A623',
              }
            }
          }]
        });

      })
    },


    searchStaticsClick: function() {
      this.safeSumYear=this.chooseYear.getFullYear();
      // 刘杰1015 增 起
      //先加载缓存，用缓存的数据绘制图表，数据获取到之后再更新
      //        window.localStorage.clear();
      let f1=JSON.parse(window.localStorage.getItem('safeIndexData'));
      if(f1!=null) {
        this.safeIndexData=f1;

        let f2=JSON.parse(window.localStorage.getItem('remainBudget'));
        if(f2!=null) {
          this.remainBudget=f2;
        }
      }

      let f3=JSON.parse(window.localStorage.getItem('dangerType'));
      if(f3!=null) {
        this.dangerType=f3;
      }

      let f4=JSON.parse(window.localStorage.getItem('dangerStatus'));
      if(f4!=null) {
        this.dangerStatus=f4;
      }

      let f5=JSON.parse(window.localStorage.getItem('dangerLevel'));
      if(f5!=null) {
        this.dangerLevel=f5;
      }

      // this.pieOfEmerRes(1); //应急响应扇形图
      // this.pieOfEmerHandle(1);//应急处置扇形图
      // this.pieOfSafetyInput(1);//安全投入扇形图
      // this.pieDataTypeClick(1);//隐患分析饼图
      //刘杰1015 增 终

      //年度安全概况
      this.$http.get('index/safeIndex/'+this.safeSumYear).then(function(res) {
        if(res.data.success) {
          if(res.data.data.safeInfo) {
            this.safeIndexData=res.data.data;

            let rateNumber=Number(this.safeIndexData.safeInfo.budgetRate);
            this.remainBudget=this.safeIndexData.safeInfo.regTotal/rateNumber-this.safeIndexData.safeInfo.regTotal;
            this.safeIndexData.safeInfo.budgetRate=(rateNumber*100).toFixed(2)+'%';

            window.localStorage.setItem('safeIndexData',JSON.stringify(this.safeIndexData));//刘杰1015 增
            window.localStorage.setItem('remainBudget',JSON.stringify(this.remainBudget));//刘杰1015 增


          } else {
            this.safeIndexData={
              "dangerIndexInfo": {
                "dangerCount": 0,
                "dangerUnReformCount": 0,
                "dangerInspectCount": 0,
                "dangerReformCount": 8
              },
              "eduInfo": {
                "eduDailyInfoTotal": 0,
                "eduDailyInfoFail": 0,
                "eduEntryTrainingFail": 0,
                "eduEntryTrainingTotal": 0,
                "eduReassignmentFail": 0,
                "eduReassignmentTotal": 0,
                "eduCertificateTotal": 0
              },
              "emgIndexInfo": {
                "emgPlanPublicCount": 0,
                "emgHandlePublicCount": 0
              },
              "safeInfo": {
                "regTotal": 0,
                "budgetRate": 0
              }
            };
          }
        }


      }.bind(this)).catch(function(err) {
        console.log(err);
      });

      //隐患统计分析
      this.$http.get('index/analysisHiddenDangers/'+this.safeSumYear).then(function(res) {
        if(res.data.success) {
          this.dangerType=[];
          this.dangerStatus=[];
          this.dangerLevel=[];
          this.pieIsVisual=false;
          if(res.data.data.dangerType) {
            let tempObj=res.data.data;
            for(let typeItem in tempObj.dangerType) {
              this.dangerType.push({ 'name': typeItem? typeItem:'其他','value': tempObj.dangerType[typeItem] });
            }
            for(let statusItem in tempObj.dangerStatus) {
              this.dangerStatus.push({ 'name': statusItem? statusItem:'其他','value': tempObj.dangerStatus[statusItem] });
            }
            for(let levelItem in tempObj.dangerLevel) {
              this.dangerLevel.push({ 'name': levelItem? levelItem:'其他','value': tempObj.dangerLevel[levelItem] });
            }
            window.localStorage.setItem('dangerType',JSON.stringify(this.dangerType));//刘杰1015 增
            window.localStorage.setItem('dangerStatus',JSON.stringify(this.dangerStatus));//刘杰1015 增
            window.localStorage.setItem('dangerLevel',JSON.stringify(this.dangerLevel));//刘杰1015 增

            this.radioType=1;
            this.pieDataTypeClick(1);
          } else {
            this.radioType=1;
            this.pieIsVisual=true;
            this.dangerType=[{ value: 335,name: '文明施工' },
            { value: 310,name: '电气管理' },
            { value: 234,name: '消防设施' },
            { value: 135,name: '个人防护' },
            { value: 1548,name: '其他' }];
            this.dangerStatus=[{ value: 335,name: '已完成' },
            { value: 310,name: '未完成' }];
            this.dangerLevel=[{ value: 305,name: '其他' },
            { value: 548,name: '一般(C级)' },
            { value: 234,name: '一般(B级)' },
            { value: 135,name: '重大(A级)' }
            ];
            this.pieDataTypeClick(1);
          }
        }
      }.bind(this)).catch(function(err) {
        console.log(err);
      });

      //安全警示趋势
      this.drawSafeWarningTrend();//绘制安全警示趋势图 1015 刘杰 增

      //绘制部门安全绩效图
      // this.drawDepartmentScore();

      //0925刘杰
      //绘制控股公司安全绩效图
      // this.drawCompanyScore();
      //绘制安全月报展示
      //        this.monthShow();
    },
    drawDepartmentScore: function() {
      var colors=['rgba(251,210,73,0.7)'];
      var aCategorys=['0','办公室','财务部','审计部','资产部','安全部','-'];
      var topdata=[0,90,96,96,97,95];//峰值data
      var aSeries=[{
        name: '安全绩效',
        type: 'line',
        symbol: 'circle',
        symbolSize: 12,
        itemStyle: {
          normal: {
            color: '#F6A623',
            borderColor: "#ffffff",
            borderWidth: 2
          }
        },
        lineStyle: {
          normal: {
            opacity: 0
          }
        },

        data: [0,90,96,96,97,95]
      },];

      aCategorys.forEach(function(v,i,a) {
        var name=v;
        if(v!=='') {
          var data=[];
          for(var j=0;j<aCategorys.length;j++) {
            data.push('-');
          }
          data[i-1]=0;
          data[i]=topdata[i];
          data[i+1]=0;
          aSeries.push({
            name: name,
            type: 'pictorialBar',
            smooth: false,
            legendHoverLink: false,
            symbol: 'path://M0,10 L10,10 C5.5,10 5.5,5 5,0 C4.5,5 4.5,10 0,10 z',
            barCategoryGap: '-130%',
            areaStyle: {
              normal: {
                color: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [{
                    offset: 0,
                    color: colors[i-1] // 0% 处的颜色
                  },{
                    offset: 1,
                    color: colors[i-1] // 100% 处的颜色
                  }],
                  globalCoord: false // 缺省为 false
                }
              }
            },
            data: data,
          });
        }
      });

      let departmentData=this.echarts.init(document.getElementById('departmentScore'));
      departmentData.setOption({
        color: colors,
        tooltip: {
          trigger: 'axis',
          formatter: function(params) {
            var rValue=params[0].name+'<br>';
            params.forEach(function(v,i,a) {
              if(v.data!==0&&v.data!=="-"&&v.seriesType=="line") {
                rValue+='<span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:'+v.color+'"></span>'+v.seriesName+':'+v.data+'<br>';
              }
            })
            return rValue;
          }
        },
        legend: {
          icon: 'circle',
          itemWidth: 14,
          itemHeight: 14,
          itemGap: 15,
          data: ['安全绩效'],
          // right: '20%',
          textStyle: {
            fontSize: 14,
            color: '#424242'
          }
        },
        xAxis: [{
          type: 'category',
          boundaryGap: false,
          data: aCategorys,
          axisLabel: {
            textStyle: {
              fontSize: 14
            }
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#f7f7f7'
            }
          }
        }],
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        yAxis: [{
          type: 'value',
          splitLine: {
            show: true,
            lineStyle: {
              color: '#f7f7f7'
            }
          }
        }],
        series: aSeries
      });
    },

    // 格式化时间
    formatDateTime(row,column,cellValue) {
      var date=new Date(row.startTime*1000); //时间戳为10位需*1000，时间戳为13位的话不需乘1000
      let Y=date.getFullYear()+'-';
      let M=date.getMonth()+1<10? '0'+(date.getMonth()+1)+'-':date.getMonth()+1+'-';
      let D=date.getDate()<10? '0'+date.getDate()+' ':date.getDate()+' ';
      let h=date.getHours()<10? '0'+date.getHours()+':':date.getHours()+':';
      let m=date.getMinutes()<10? '0'+date.getMinutes()+':':date.getMinutes()+':';
      let s=date.getSeconds()<10? '0'+date.getSeconds():date.getSeconds();
      return Y+M+D+h+m+s;
    },
    //0925刘杰
    drawCompanyScore: function() {
      var colors=['rgba(251,210,73,0.7)'];
      var aCategorys=['0','交工公司','营运公司','检测公司','-'];
      var topdata=[0,97,96,95,0];//峰值data
      var aSeries=[{
        name: '安全绩效',
        type: 'line',
        symbol: 'circle',
        symbolSize: 12,
        itemStyle: {
          normal: {
            color: '#F6A623',
            borderColor: "#ffffff",
            borderWidth: 2
          }
        },
        lineStyle: {
          normal: {
            opacity: 0
          }
        },

        data: [0,97,96,96]
      },];

      aCategorys.forEach(function(v,i,a) {
        var name=v;
        if(v!=='') {
          var data=[];
          for(var j=0;j<aCategorys.length;j++) {
            data.push('-');
          }
          data[i-1]=0;
          data[i]=topdata[i];
          data[i+1]=0;
          aSeries.push({
            name: name,
            type: 'pictorialBar',
            smooth: false,
            legendHoverLink: false,
            symbol: 'path://M0,10 L10,10 C5.5,10 5.5,5 5,0 C4.5,5 4.5,10 0,10 z',
            barCategoryGap: '-130%',
            areaStyle: {
              normal: {
                color: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [{
                    offset: 0,
                    color: colors[i-1] // 0% 处的颜色
                  },{
                    offset: 1,
                    color: colors[i-1] // 100% 处的颜色
                  }],
                  globalCoord: false // 缺省为 false
                }
              }
            },
            data: data,
          });
        }
      });

      let departmentData=this.echarts.init(document.getElementById('companyScore'));
      departmentData.setOption({
        color: colors,
        tooltip: {
          trigger: 'axis',
          formatter: function(params) {
            var rValue=params[0].name+'<br>';
            params.forEach(function(v,i,a) {
              if(v.data!==0&&v.data!=="-"&&v.seriesType=="line") {
                rValue+='<span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:'+v.color+'"></span>'+v.seriesName+':'+v.data+'<br>';
              }
            })
            return rValue;
          }
        },
        legend: {
          icon: 'circle',
          itemWidth: 14,
          itemHeight: 14,
          itemGap: 15,
          data: ['安全绩效'],
          // right: '20%',
          textStyle: {
            fontSize: 14,
            color: '#424242'
          }
        },
        xAxis: [{
          type: 'category',
          boundaryGap: false,
          data: aCategorys,
          axisLabel: {
            textStyle: {
              fontSize: 14
            }
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#f7f7f7'
            }
          }
        }],
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        yAxis: [{
          type: 'value',
          splitLine: {
            show: true,
            lineStyle: {
              color: '#f7f7f7'
            }
          }
        }],
        series: aSeries
      });
    },
    //刘杰 1012 增 起
    //隐患分析点击事件对话框
    searchDangerPieData: function(radioType,name) {

      this.pieChartDialogData=[];
      let params={ needChange: 1,downward: true };
      //        {needChange:1,downward:true};
      let startDate=new Date();
      startDate.setFullYear(this.safeSumYear,0,1);
      let endDate=new Date();
      endDate.setFullYear(this.safeSumYear,11,31);
      params.startDate=startDate; params.endDate=endDate;
      switch(radioType) {
        case 1: params.hiddenDangerLevel=name; break;
        case 2: params.dangerType=name; break;
        case 3: params.dangerStatus=name; break;

      }
      this.dangerTableLoading=true;
      this.$http.post('index/findAnalysis',params).then(function(res) {
        if(res.data.success) {
          this.dangerTableLoading=false;
          let tempList=[];
          tempList=res.data.data;
          this.pieChartDialogData=tempList;
        }
      }.bind(this)).catch(function(err) {
        this.$message.error('隐患数据获取失败！');
        console.log(err);
      });
    },
    //刘杰 1012 增 终

    //查看隐患对话框
    searchDangerTableData: function() {
      this.dangerWholeData.allDanger=[];
      this.dangerWholeData.doneDanger=[];
      this.dangerWholeData.noDanger=[];
      let params={ needChange: 1,downward: true };
      let startDate=new Date();
      startDate.setFullYear(this.safeSumYear,0,1);
      let endDate=new Date();
      endDate.setFullYear(this.safeSumYear,11,31);
      params.startDate=startDate; params.endDate=endDate;
      this.dangerTableLoading=true;
      this.$http.post('index/findAnalysis',params).then(function(res) {
        if(res.data.success) {
          this.dangerTableLoading=false;
          let tempList=[];
          tempList=res.data.data;
          for(let i=0;i<tempList.length;i++) {
            this.dangerWholeData.allDanger.push(tempList[i]);
            if(tempList[i].changeTime) {
              this.dangerWholeData.doneDanger.push(tempList[i]);
            } else {
              this.dangerWholeData.noDanger.push(tempList[i]);
            }
          }
          this.changeDangerTab();
        }
      }.bind(this)).catch(function(err) {
        this.$message.error('隐患数据获取失败！');
        console.log(err);
      });
    },
    //切换隐患类别
    changeDangerTab: function() {
      this.dangerTableData=[];
      if(this.dangerDialogTab!=='全部隐患') {
        if(this.dangerDialogTab==='已整改') {
          this.dangerTableData=[...this.dangerWholeData.doneDanger];
        } else {//未整改
          this.dangerTableData=[...this.dangerWholeData.noDanger];
        }
      } else {//全部
        this.dangerTableData=[...this.dangerWholeData.allDanger];
      }
    },

    //改时间格式
    changeTimeFormat: function(row) {
      return this.transferTime(row.changeTime);
    },

  }
}
</script>
<style scoped>
@keyframes move {
  0% {
    transform: translate3d(0, 0, 0);
  }
  100% {
    transform: translate3d(0, -50%, 0);
  }
}
.tablescroll {
  width: 100%;
  min-height: 310px;
  overflow: hidden;
}
.tableScroll >>> .el-table__body-wrapper tbody {
  animation: move 6s infinite linear;
}
.tableScroll >>> .el-table__body-wrapper tbody:hover {
  animation-play-state: paused;
}
.managerView-main {
  position: absolute;
  top: 85px;
  left: 0;
  right: 0;
  bottom: 0;
  min-width: 1200px;
  background-color: #f2f2f2;
  overflow-y: scroll;
}
.managerView-mainImg {
  width: 1200px;
  min-height: 880px;
  margin: 5px auto 0 auto;
}
.round-gray-text {
  width: 100px;
  height: 100px;
  border: 2px solid #9f9f9f;
  border-radius: 50px;
  color: #9f9f9f;
  text-align: center;
  line-height: 100px;
  transform: rotate(-45deg);
  -ms-transform: rotate(-45deg); /* Internet Explorer */
  -moz-transform: rotate(-45deg); /* Firefox */
  -webkit-transform: rotate(-45deg); /* Safari 和 Chrome */
  -o-transform: rotate(-45deg); /* Opera */
}
.click-text {
  cursor: pointer;
  font-size: 26px;
  text-align: center;
  color: red;
}
.click-text:hover {
  color: rgb(246, 166, 35);
}

.show-text {
  font-size: 26px;
  text-align: center;
  color: #ed704e;
}
</style>
