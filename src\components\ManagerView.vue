<template>
  <div id="managerView">
    <safeHeader></safeHeader>
    <div class="managerView-main" >
      <div class="managerView-mainImg" >
        <el-row style="margin: 0">
          <el-col :span="12" style="padding-right: 5px">
            <el-col :span="24" class="card-shadow-style" style="height: 400px;background-color: white">
              <div style="height: 35px;line-height: 40px;margin: 0 20px 0 20px;border-bottom: 2px solid #049ff1;color:#049ff1;font-size: large">
                年度安全概况
              </div>
              <div style="padding: 5px 0 0 20px">
                年度：
                <el-date-picker
                  v-model="chooseYear"
                  type="year"
                  size="small"
                  placeholder="选择年份"
                  style="margin: 0">
                </el-date-picker>
                <el-button type="primary" icon="el-icon-search" size="small" style="margin: 0" @click="searchStaticsClick">搜索</el-button>
              </div>
              <div style="padding: 5px 0 0 20px">
                <el-row style="margin: 0">
                  隐患排查与治理
                </el-row>
                <el-row  style="margin: 10px 0 0 0">
                  <el-col :span="6" style="font-size: 14px;text-align: center;color:gray" >安全检查</el-col>
                  <el-col :span="6" style="font-size: 14px;text-align: center;color:gray">发现隐患</el-col>
                  <el-col :span="6" style="font-size: 14px;text-align: center;color:gray">整改隐患</el-col>
                  <el-col :span="6" style="font-size: 14px;text-align: center;color:gray">未整改隐患</el-col>
                </el-row>
                <el-row style="margin: 0">
                  <el-col :span="6" class="click-text show-text" ><span @click="$router.push({name:'investigation',params:{taskFlag:true,viewList:true}})">{{safeIndexData.dangerIndexInfo.dangerInspectCount}}</span></el-col>
                  <!--<el-col :span="6" class="click-text show-text" ><span @click="dangerDialogTab='全部隐患';searchDangerTableData();dangerDialogVisible=true">{{safeIndexData.dangerIndexInfo.dangerCount}}</span></el-col>-->
                  <el-col :span="6" class="click-text show-text" ><span @click="dangerDialogTab='全部隐患';searchDangerTableData();dangerDialogVisible=true">{{safeIndexData.dangerIndexInfo.dangerReformCount+safeIndexData.dangerIndexInfo.dangerUnReformCount}}</span></el-col>
                  <el-col :span="6" class="click-text show-text" ><span @click="dangerDialogTab='已整改';searchDangerTableData();dangerDialogVisible=true">{{safeIndexData.dangerIndexInfo.dangerReformCount}}</span></el-col>
                  <el-col :span="6" class="click-text show-text" ><span @click="dangerDialogTab='已超期';searchDangerTableData();dangerDialogVisible=true">{{safeIndexData.dangerIndexInfo.dangerUnReformCount}}</span></el-col>
                </el-row>

                <el-row style="margin: 10px 0 0 0">
                  教育培训情况
                </el-row>
                <el-row style="margin: 10px 0 0 0">
                  <el-col :span="6" style="font-size: 14px;text-align: center;color:gray">三级培训</el-col>
                  <el-col :span="6" style="font-size: 14px;text-align: center;color:gray">持证培训</el-col>
                  <el-col :span="6" style="font-size: 14px;text-align: center;color:gray">日常培训</el-col>
                  <el-col :span="6" style="font-size: 14px;text-align: center;color:gray">转岗培训</el-col>
                </el-row>
                <el-row style="margin: 0">
                  <el-col :span="6" class="show-text"><span @click="$router.push({name:'threeLevelTrainingIndex',params:{taskFlag:true,viewList:1}})">{{safeIndexData.eduInfo.eduEntryTrainingTotal}}</span></el-col>
                  <el-col :span="6"  class="show-text"><span @click="$router.push({name:'threeLevelTrainingIndex',params:{taskFlag:true,viewList:2}})">{{safeIndexData.eduInfo.eduCertificateTotal}}</span></el-col>
                  <el-col :span="6"  class="show-text"><span @click="$router.push({name:'threeLevelTrainingIndex',params:{taskFlag:true,viewList:3}})">{{safeIndexData.eduInfo.eduDailyInfoTotal}}</span></el-col>
                  <el-col :span="6"  class="show-text"><span @click="$router.push({name:'threeLevelTrainingIndex',params:{taskFlag:true,viewList:4}})">{{safeIndexData.eduInfo.eduReassignmentTotal}}</span></el-col>
                </el-row>
                <el-row style="margin: 10px 0 0 0">
                  <el-col :span="8" style="font-size:16px;text-align: center;color:#0f6fc6">
                    <div style="width: 100%;height: 130px;" id="pieOfEmerRes"  @click="$router.push({name:'emerResponse',params:{taskFlag:true,editTaskFlag:'view'}})"></div>
                  </el-col>
                  <el-col :span="8">
                    <div style="width: 100%;height: 130px;" id="pieOfEmerHandle"  @click="$router.push({name:'emerHandle',params:{taskFlag:true,editTaskFlag:'deal'}})"></div>
                  </el-col>
                  <el-col :span="8" >
                    <div style="width: 100%;height: 130px;" id="pieOfSafetyInput" @click="$router.push({name:'budgetaryTrackingIndex',params:{taskFlag:true}})">
                    </div>
                  </el-col>
                </el-row>
              </div>
            </el-col>
          </el-col>

          <el-col :span="12" style="padding-left: 5px">
            <el-col :span="24" class="card-shadow-style" style="height: 400px;background-color: white">
              <div style="height: 35px;line-height: 40px;margin: 0 20px 0 20px;border-bottom: 2px solid #049ff1;color:#049ff1;font-size: large">
                隐患统计分析
              </div>
              <div style="padding: 5px 0 0 20px">
                <el-radio-group v-model="radioType" size="small" @change="pieDataTypeClick">
                  <el-radio-button v-for="item in radioTypeArray" :label="item.value" :key="item.value">{{item.label}}</el-radio-button>
                </el-radio-group>
              </div>
              <div style="padding-top: 15px" >
                <div style="width: 600px;height: 400px;" id="pieChart"></div>
              </div>
              <div style="position: absolute;right: 30px;bottom: 100px;height: 30px;width: 100px;z-index: 3" v-show="pieIsVisual">
                <div class="round-gray-text">模拟数据</div>
              </div>
            </el-col>
          </el-col>
        </el-row>

        <el-row style="margin: 0">
          <el-col :span="12" style="padding-right: 5px">
            <el-col :span="24" class="card-shadow-style" style="height: 400px;background-color: white">
              <div style="height: 35px;line-height: 40px;margin: 0 20px 0 20px;border-bottom: 2px solid #049ff1;color:#049ff1;font-size: large">
                安全警示趋势
              </div>
              <div style="padding:20px 0 0 10px;position: relative">
                <div style="width: 600px;height: 350px;margin-top: -10px" id="trendChart"></div>
                <div style="position: absolute;right: 30px;bottom: 100px;height: 30px;width: 100px;z-index: 3" v-show="trendIsVisual">
                  <div class="round-gray-text">模拟数据</div>
                </div>
              </div>
            </el-col>
          </el-col>
          <el-col :span="12" style="padding-left: 5px">
            <el-col :span="24" class="card-shadow-style" style="height: 400px;background-color: white;position: relative">
              <div style="height: 35px;line-height: 40px;margin: 0 20px 0 20px;border-bottom: 2px solid #049ff1;color:#049ff1;font-size: large">
                部门安全绩效
              </div>
              <div style="width: 600px;height: 350px;" id="departmentScore">
              </div>
              <div style="position: absolute;right: 30px;bottom: 100px;height: 30px;width: 100px;z-index: 3">
                <div class="round-gray-text">模拟数据</div>
              </div>
            </el-col>
          </el-col>
        </el-row>

        <el-row style="margin: 0" show="false">
          <el-col :span="12" style="padding-right: 5px">
            <el-col :span="24" class="card-shadow-style" style="height: 400px;background-color: white">
              <div style="height: 35px;line-height: 40px;margin: 0 20px 0 20px;border-bottom: 2px solid #049ff1;color:#049ff1;font-size: large">
                安全月报展示
              </div>
              <div style="width: 590px;height: 350px;margin-top:0px" id="monthShow">
                <el-table stripe  :data="monthData"  style="height: 100%;width: 100%">
                  <el-table-column label="月份" align="center" width="80" prop="monthTest"></el-table-column>
                  <el-table-column label="安全教育"  align="center" >
                    <template slot-scope="scope" >
                      <img src="../static/imgs/manager-view-green.png" height="25px" width="40px" style="margin: 0px;" >
                    </template>
                  </el-table-column>
                  <el-table-column label="持证管理" align="center">
                    <template slot-scope="scope" >
                      <img src="../static/imgs/manager-view-green.png" height="25px" width="40px" style="margin: 0px;" >
                    </template>
                  </el-table-column>
                  <el-table-column label="隐患排查"  align="center">
                    <template slot-scope="scope" >
                      <img v-if="scope.row.inspecflag==1" src="../static/imgs/manager-view-green.png" height="25px" width="40px" style="margin: 0px;" >
                      <img v-if="scope.row.inspecflag==0" src="../static/imgs/manager-view-red.png" height="25px" width="40px" style="margin: 0px;" >                    </template>
                  </el-table-column>
                  <el-table-column label="隐患整改"  align="center">
                    <template slot-scope="scope" >
                      <img v-if="scope.row.changeflag==1" src="../static/imgs/manager-view-green.png" height="25px" width="40px" style="margin: 0px;" >
                      <img v-if="scope.row.changeflag==0" src="../static/imgs/manager-view-yellow.png" height="25px" width="40px" style="margin: 0px;" >
                    </template>
                  </el-table-column>
                  <el-table-column label="应急管理" align="center" >
                    <template slot-scope="scope" >
                      <img src="../static/imgs/manager-view-green.png" height="25px" width="40px" style="margin: 0px;" >
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-col>
          </el-col>
          <el-col :span="12" style="padding-left: 5px">
            <el-col :span="24" class="card-shadow-style" style="height: 400px;background-color: white">
              <div style="height: 35px;line-height: 40px;margin: 0 20px 0 20px;border-bottom: 2px solid #049ff1;color:#049ff1;font-size: large">
                控股公司安全绩效
              </div>
              <div style="width: 600px;height: 350px;" id="companyScore">
              </div>
              <div style="position: absolute;right: 30px;bottom: 100px;height: 30px;width: 100px;z-index: 3">
                <div class="round-gray-text">模拟数据</div>
              </div>
            </el-col>

          </el-col>

        </el-row>


      </div>

      <!--尾部说明 刘杰 1025 增-->
      <el-col
        :span="24"
        style="height: 50px; bottom: 0; background-color: #3396fb"
      >
        <div style="width: 1200px; margin: 0 auto; text-align: center">
        <span style="line-height: 50px; color: #fff; font-size: 14px"
        >2023© 宁波交通投资集团有限公司 版权所有</span
        >
        </div>
      </el-col>
      <!--尾部说明结束-->
    </div>

    <!--隐患列表-->
    <el-dialog :title="dangerDialogTitle" :visible.sync="dangerDialogVisible" width="80%">
      <div style="margin:0">
        <el-radio-group v-model="dangerDialogTab" size="small" @change="changeDangerTab">
          <el-radio-button label="全部隐患"></el-radio-button>
          <el-radio-button label="已整改"></el-radio-button>
          <el-radio-button label="未整改"></el-radio-button>
        </el-radio-group>
      </div>
      <div style="margin: 5px 0 5px 0">
        <el-table
          :data="dangerTableData"
          border
          v-loading="dangerTableLoading"
          max-height="400"
          style="width: 100%">
          <el-table-column
            type="index"
            align="center"
            width="50"
            fixed
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            prop="checkName"
            label="检查单名称"
            width="150"
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            prop="inspectProject"
            label="检查项目"
            width="150"
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            prop="inspectContent"
            min-width="400"
            label="检查标准内容"
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            prop="inspectResult"
            width="300"
            label="检查结果记录"
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            prop="deptName"
            width="200"
            label="受检单位"
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            min-width="200"
            label="隐患照片"
            label-class-name="inner-header-style">
            <template slot-scope="scope">
              <picture-card :picFileList="scope.row.dangerPics"></picture-card>
            </template>
          </el-table-column>
          <el-table-column
            prop="changeTime"
            width="120"
            label="整改时间"
            :formatter="changeTimeFormat"
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            prop="changeExplain"
            width="180"
            label="整改说明"
            label-class-name="inner-header-style">
          </el-table-column>
        </el-table>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dangerDialogVisible=false">确 定</el-button>
      </div>
    </el-dialog>
    <!--隐患列表结束-->

    <!--刘杰1012 增 起-->
    <!--隐患统计分析饼图点击弹出框-->
    <el-dialog :title="dangerDialogTitle" :visible.sync="pieDialogVisible" width="80%">
      <!--<div style="margin:0">-->
      <!--<el-radio-group v-model="dangerDialogTab" size="small" @change="searchDangerTableData">-->
      <!--<el-radio-button label="全部隐患"></el-radio-button>-->
      <!--<el-radio-button label="已整改"></el-radio-button>-->
      <!--<el-radio-button label="已超期"></el-radio-button>-->
      <!--</el-radio-group>-->
      <!--</div>-->
      <div style="margin: 5px 0 5px 0">
        <el-table
          :data="pieChartDialogData"
          border
          v-loading="dangerTableLoading"
          max-height="400"
          style="width: 100%">
          <el-table-column
            type="index"
            align="center"
            width="50"
            fixed
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            prop="inspectProject"
            label="检查项目"
            width="150"
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            prop="inspectContent"
            min-width="400"
            label="检查标准内容"
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            prop="inspectResult"
            width="300"
            label="检查结果记录"
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            prop="deptName"
            width="200"
            label="受检单位"
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            prop="hiddenDangerLevel"
            width="200"
            label="隐患级别"
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            prop="dangerType"
            width="200"
            label="隐患类型"
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            min-width="200"
            label="隐患照片"
            label-class-name="inner-header-style">
            <template slot-scope="scope">
              <picture-card :picFileList="scope.row.dangerPics"></picture-card>
            </template>
          </el-table-column>
          <el-table-column
            prop="changeTime"
            width="120"
            label="整改时间"
            :formatter="changeTimeFormat"
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            prop="deadline"
            width="120"
            label="整改期限"
            :formatter="changeTimeFormat"
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            prop="changeExplain"
            width="180"
            label="整改说明"
            label-class-name="inner-header-style">
          </el-table-column>
        </el-table>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="pieDialogVisible=false">确 定</el-button>
      </div>
    </el-dialog>
    <!--刘杰1012 增 终-->

  </div>
</template>
<script>
  import safeHeader from '@/components/common/header'
  import PictureCard from '@/components/common/smallComponent/pictureCard.vue'
  import {mapActions} from 'vuex'
  export default {
    name: 'managerView',
    data() {
      return {


        //年度安全概况
        chooseYear:'',//年份选择器对应的值，为date格式
        safeSumYear:'',//搜索的年份，为number格式
        safeIndexData:{
          "dangerIndexInfo": {
            "dangerCount": 36,
            "dangerUnReformCount": 29,
            "dangerInspectCount": 37,
            "dangerReformCount": 7
          },
          "eduInfo": {
            "eduDailyInfoTotal": 0,
            "eduDailyInfoFail": 0,
            "eduEntryTrainingFail": 0,
            "eduEntryTrainingTotal": 0,
            "eduReassignmentFail": 0,
            "eduReassignmentTotal": 11,
            "eduCertificateTotal": 23
          },
          "emgIndexInfo": {
            "emgPlanPublicCount": 8,
            "emgHandlePublicCount": 0
          },
          "safeInfo": {
            "regTotal": 148432.6,
            "budgetRate": '22.66%',
          }
        },
        //0925 刘杰增加,记录预算余额，用作扇形图中投入占的面积
        remainBudget:506609.765,

        //隐患统计分析
        radioType:1,
        radioTypeArray:[
          {value:1,label:'按等级'},
          {value:2,label:'按类型'},
          {value:3,label:'按整改状态'},
        ],
        dangerType:[{'':3},{'个人防护':6},{'安全管理类':1},{'作业场所类':1},{'消防设施':4},{'电气管理':3},{'设备设施类':4},{'警示标牌':1},{'文明施工':1}],
        dangerStatus:[{'超期未整改':30},{'待整改':7},{'已整改':14}],
        dangerLevel:[{'无':1},{'重大(A级)':2},{'一般(B级)':11},{'一般(C级)':37}],
        pieChartData:[],//饼图数据
        pieChartDialogData:[],//饼图对话框数据 刘杰1012 增
        pieDialogVisible:false,//饼图对话框是否显示 刘杰1012 增
        pieIsVisual:false,

        //安全警示趋势数据
        trendMonthArr:['1月','2月','3月','4月','5月','6月', '7月','8月','9月', '10月','11月','12月'],
        trendData:[],
        trendIsVisual:false,

        //0925刘杰 安全月报数据
        monthData: [
          {monthTest: '9月',changeflag:1,inspecflag:1},
          {monthTest: '8月',changeflag:1,inspecflag:1},
          {monthTest: '7月',changeflag:1,inspecflag:0},
          {monthTest: '6月',changeflag:1,inspecflag:1},
          {monthTest: '5月',changeflag:0,inspecflag:1},
          {monthTest: '4月',changeflag:1,inspecflag:1},
          {monthTest: '3月',changeflag:1,inspecflag:1},
          {monthTest: '2月',changeflag:1,inspecflag:1},
          {monthTest: '1月',changeflag:1,inspecflag:1},
        ],
        //隐患对话框
        dangerDialogTitle:'隐患列表',
        dangerDialogVisible:false,
        dangerTableData:[],
        dangerDialogTab:'全部隐患',
        dangerTableLoading:false,
        //隐患分类了的数据
        dangerWholeData:{allDanger:[],doneDanger:[],noDanger:[]}
      }
    },
    components: {
      safeHeader,PictureCard
    },
    mounted:function () {


      //标题条视图按钮
      this.$store.dispatch('changeViewFlag',{ menuFlag:true, managerViewFlag:false, gisFlag:true});
      this.chooseYear=new Date();

      this.searchStaticsClick();

    },
    watch:{
      $route(to, from) {
        if(this.$route.name==='managerView'){
          //标题条视图按钮
          this.$store.dispatch('changeViewFlag',{ menuFlag:true, managerViewFlag:false, gisFlag:true});
//          this.chooseYear=new Date();
//          this.searchStaticsClick();

        }
      }
    },
    methods:{
      pieDataTypeClick:function (val) {
        if(val===1){
          this.pieChartData=this.dangerLevel;
        }else if(val===2){
          this.pieChartData=this.dangerType;
        }else{
          this.pieChartData=this.dangerStatus;
        }
        let pieChartData = this.echarts.init(document.getElementById('pieChart'));
        pieChartData.off('click')
        pieChartData.setOption({
          color:['#C1232B','#E87C25','#27727B','#FBD659','#C15133','green','blue'],
//          color:['#FF9D52','#ED704E','#FFCA6C','#999999','#C15133'],
          title : {show:false},
          tooltip : {trigger: 'item'},
          legend: {},
          series : [
            {
              name: '隐患数量',
              type: 'pie',
              radius : '55%',
              center: ['50%', '40%'],
              data:this.pieChartData,
              itemStyle: {
                emphasis: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }
          ]
        });
        // 处理点击事件  刘杰 1012 增
        pieChartData.on('click', function (params) {
          this.searchDangerPieData(this.radioType,params.name);
          this.pieDialogVisible = true;
//          alert("饼图点击事件： 名称："+(params.name)+"，值："+(params.value));
        }.bind(this));

      },
      //0925刘杰 年度概况饼图应急响应
      pieOfEmerRes: function (val) {
  let pieChartData = this.echarts.init(document.getElementById('pieOfEmerRes'));
  var emerResSum = this.safeIndexData.emgIndexInfo.emgPlanPublicCount;

  pieChartData.setOption({
    series: [
      {
        name: ' 现有公司（家）',
        type: 'pie',
        radius: ['90%', '70%'],
        startAngle: 225,
        color: [new this.echarts.graphic.LinearGradient(0, 0, 0, 1, [{
          offset: 0,
          color: '#f125ff'
        }, {
          offset: 1,
          color: '#2dcbff'
        }]), "transparent"],
        labelLine: {
          show: false // 隐藏引导线
        },
        label: {
          position: 'center',
          formatter: `${emerResSum}\n应急响应`, // 合并数值和文本
          fontSize: 16, // 统一字体大小
          color: '#FF9D52', // 统一字体颜色
          fontWeight: 'bold', // 加粗
          lineHeight: 26 // 调整行高
        },
        data: [
          { value: 75, name: '现有公司' },
          { value: 25, name: '其他' }
        ]
      }
    ]
  });
},

      //0925刘杰 年度概况饼图应急处置
      pieOfEmerHandle: function (val) {
  let pieChartData = this.echarts.init(document.getElementById('pieOfEmerHandle'));
  var emerHandleSum = this.safeIndexData.emgIndexInfo.emgHandlePublicCount;

  pieChartData.setOption({
    series: [
      {
        name: ' 现有公司（家）',
        type: 'pie',
        radius: ['90%', '70%'],
        startAngle: 225,
        color: [new this.echarts.graphic.LinearGradient(0, 0, 0, 1, [{
          offset: 0,
          color: '#f125ff'
        }, {
          offset: 1,
          color: '#2dcbff'
        }]), "transparent"],
        labelLine: {
          show: false // 隐藏引导线
        },
        label: {
          position: 'center',
          formatter: `${emerHandleSum}\n应急处置`, // 合并数值和文本
          fontSize: 16, // 统一字体大小
          color: '#FF9D52', // 统一字体颜色
          fontWeight: 'bold', // 加粗
          lineHeight: 26 // 调整行高
        },
        data: [
          { value: 75, name: '现有公司' },
          { value: 25, name: '其他' }
        ]
      }
    ]
  });
},

      //0925刘杰 年度概况饼图费用投入
      pieOfSafetyInput: function (val) {
  let pieChartData = this.echarts.init(document.getElementById('pieOfSafetyInput'));
  var safetyInputSum = this.safeIndexData.safeInfo.regTotal;
  var safetyInputRate = this.safeIndexData.safeInfo.budgetRate;

  pieChartData.setOption({
    backgroundColor: '#fff',
    series: [{
      name: '费用投入扇形图',
      type: 'pie',
      radius: ['70%', '90%'],
      color: '#FF4500',
      labelLine: {
        show: false // 隐藏引导线
      },
      label: {
        position: 'center',
        formatter: `${safetyInputRate}\n${safetyInputSum}\n安全投入`, // 合并数值和文本
        fontSize: 16, // 统一字体大小
        color: '#FF9D52', // 统一字体颜色
        fontWeight: 'bold', // 加粗
        lineHeight: 26 // 调整行高
      },
      data: [
        { value: safetyInputSum, name: '已发生费用' },
        { value: this.remainBudget, name: '预算余额' }
      ]
    }]
  });
},

      //0925刘杰 安全月报展示
      monthShow:function(){
// app.title = '各城市每个月的雾霾严重程度'
        var hours = ['一月', '二月', '三月', '四月', '五月', '六月', '七月',
          '八月', '九月', '十月', '十一月', '十二月'
        ];
        var days = ['安全教育', '持证管理', '隐患排查',
          '隐患整改', '应急管理'
        ];

        var data = [
          [0, 0, 50],
          [0, 1, 50],
          [0, 2, 50],
          [0, 3, 50],
          [0, 4, 50],
          [0, 5, 50],
          [0, 6, 50],
          [0, 7, 50],
          [0, 8, 50],
          [0, 9, 50],
          [0, 10, 50],
          [0, 11, 50],
          [1, 0, 50],
          [1, 1, 50],
          [1, 2, 50],
          [1, 3, 50],
          [1, 4, 50],
          [1, 5, 50],
          [1, 6, 50],
          [1, 7, 50],
          [1, 8, 50],
          [1, 9, 50],
          [1, 10, 50],
          [1, 11, 50],
          [2, 0, 50],
          [2, 1, 50],
          [2, 2, 50],
          [2, 3, 50],
          [2, 4, 50],
          [2, 5, 50],
          [2, 6, 50],
          [2, 7, 50],
          [2, 8, 50],
          [2, 9, 50],
          [2, 10, 50],
          [2, 11, 50],
          [3, 0, 50],
          [3, 1, 50],
          [3, 2, 50],
          [3, 3, 50],
          [3, 4, 50],
          [3, 5, 50],
          [3, 6, 50],
          [3, 7, 50],
          [3, 8, 50],
          [3, 9, 50],
          [3, 10, 50],
          [3, 11, 50],
          [4, 0, 50],
          [4, 1, 50],
          [4, 2, 50],
          [4, 3, 50],
          [4, 4, 50],
          [4, 5, 50],
          [4, 6, 50],
          [4, 7, 50],
          [4, 8, 50],
          [4, 9, 50],
          [4, 10, 50],
          [4, 11, 50]
        ];

        let pieChartData = this.echarts.init(document.getElementById('monthShow'));
        pieChartData.setOption({

          tooltip: {
            position: 'top',
            formatter: function(params) {
              return params.name + ':' + params.value[1]
            }
          },
          toolbox: {
            show: true,
            feature: {
              saveAsImage: {
                show:true,
                excludeComponents :['toolbox'],
                pixelRatio: 5
              }
            }
          },
          title: [],
          singleAxis: [],
          series: []
        }) ;
//这里应该是遍历的意思，idx是遍历每一根轴的标志
        this.echarts.util.each(days, function(day, idx) {
          pieChartData.option.title.push({
            textBaseline: 'middle',
            top: (idx + 1) * 100 / 5 - 9 + '%', //每个标题的位置
            text: day
          });
          pieChartData.option.singleAxis.push({
            axisLine: false,

            type: 'category',
            boundaryGap: false,
            data: hours,
            top: (idx * 100 / 5 + 10) + '%',
            height: (100 / 5 - 19) + '%', //5是指有五根轴，这里计算的是每根轴离顶端的距离
            left: '14%',
            axisLabel: {
              show:idx>3?true:false,
              margin: 30
            }
          });
          pieChartData.option.series.push({
            singleAxisIndex: idx,

            coordinateSystem: 'singleAxis',
            type: 'scatter',
            itemStyle:{
              color: 'red',
            },

            data: [],
            symbolSize: function(dataItem) {
              return dataItem[1] * 0.5;
            }
          });
        });

        this.echarts.util.each(data, function(dataItem) {
          pieChartData.option.series[dataItem[0]].data.push([dataItem[1], dataItem[2]]);
        });
      },

      searchStaticsClick:function () {
        this.safeSumYear=this.chooseYear.getFullYear();

        // 刘杰1015 增 起
        //先加载缓存，用缓存的数据绘制图表，数据获取到之后再更新
//        window.localStorage.clear();
        let f1=JSON.parse(window.localStorage.getItem('safeIndexData'));
        if(f1 !=null){
          this.safeIndexData = f1;

          let f2=JSON.parse(window.localStorage.getItem('remainBudget'));
          if(f2 !=null){
            this.remainBudget = f2;
          }
        }

        let f3=JSON.parse(window.localStorage.getItem('dangerType'));
        if(f3 !=null){
          this.dangerType = f3;
        }

        let f4=JSON.parse(window.localStorage.getItem('dangerStatus'));
        if(f4 !=null){
          this.dangerStatus = f4;
        }

        let f5=JSON.parse(window.localStorage.getItem('dangerLevel'));
        if(f5 !=null){
          this.dangerLevel = f5;
        }

        this.pieOfEmerRes(1); //应急响应扇形图
        this.pieOfEmerHandle(1);//应急处置扇形图
        this.pieOfSafetyInput(1);//安全投入扇形图
        this.pieDataTypeClick(1);//隐患分析饼图
        //刘杰1015 增 终

        //年度安全概况
        this.$http.get('index/safeIndex/'+this.safeSumYear).then(function (res) {
          if (res.data.success) {
            if(res.data.data.safeInfo){
              this.safeIndexData=res.data.data;

              let rateNumber=Number(this.safeIndexData.safeInfo.budgetRate);
              this.remainBudget=this.safeIndexData.safeInfo.regTotal/rateNumber-this.safeIndexData.safeInfo.regTotal;
              this.safeIndexData.safeInfo.budgetRate=(rateNumber*100).toFixed(2)+'%';

              window.localStorage.setItem('safeIndexData',JSON.stringify(this.safeIndexData));//刘杰1015 增
              window.localStorage.setItem('remainBudget',JSON.stringify(this.remainBudget));//刘杰1015 增


            }else{
              this.safeIndexData={
                "dangerIndexInfo": {
                  "dangerCount": 0,
                  "dangerUnReformCount": 0,
                  "dangerInspectCount": 0,
                  "dangerReformCount": 8
                },
                "eduInfo": {
                  "eduDailyInfoTotal": 0,
                  "eduDailyInfoFail": 0,
                  "eduEntryTrainingFail": 0,
                  "eduEntryTrainingTotal": 0,
                  "eduReassignmentFail": 0,
                  "eduReassignmentTotal": 0,
                  "eduCertificateTotal": 0
                },
                "emgIndexInfo": {
                  "emgPlanPublicCount": 0,
                  "emgHandlePublicCount": 0
                },
                "safeInfo": {
                  "regTotal": 0,
                  "budgetRate": 0
                }
              };
            }
          }
          this.pieOfEmerRes(1);
          this.pieOfEmerHandle(1);
          this.pieOfSafetyInput(1);

        }.bind(this)).catch(function (err) {
          console.log(err);
        });

        //隐患统计分析
        this.$http.get('index/analysisHiddenDangers/'+this.safeSumYear).then(function (res) {
          if (res.data.success) {
            this.dangerType=[];
            this.dangerStatus=[];
            this.dangerLevel=[];
            this.pieIsVisual=false;
            if(res.data.data.dangerType){
              let tempObj=res.data.data;
              for(let typeItem in tempObj.dangerType){
                this.dangerType.push({'name':typeItem?typeItem:'其他','value':tempObj.dangerType[typeItem]});
              }
              for(let statusItem in tempObj.dangerStatus){
                this.dangerStatus.push({'name':statusItem?statusItem:'其他','value':tempObj.dangerStatus[statusItem]});
              }
              for(let levelItem in tempObj.dangerLevel){
                this.dangerLevel.push({'name':levelItem?levelItem:'其他','value':tempObj.dangerLevel[levelItem]});
              }
              window.localStorage.setItem('dangerType',JSON.stringify(this.dangerType));//刘杰1015 增
              window.localStorage.setItem('dangerStatus',JSON.stringify(this.dangerStatus));//刘杰1015 增
              window.localStorage.setItem('dangerLevel',JSON.stringify(this.dangerLevel));//刘杰1015 增

              this.radioType=1;
              this.pieDataTypeClick(1);
            }else {
              this.radioType=1;
              this.pieIsVisual=true;
              this.dangerType=[{value:335, name:'文明施工'},
                {value:310, name:'电气管理'},
                {value:234, name:'消防设施'},
                {value:135, name:'个人防护'},
                {value:1548, name:'其他'}];
              this.dangerStatus=[{value:335, name:'已完成'},
                {value:310, name:'未完成'}];
              this.dangerLevel=[{value:305, name:'其他'},
                {value:548, name:'一般(C级)'},
                {value:234, name:'一般(B级)'},
                {value:135, name:'重大(A级)'}
              ];
              this.pieDataTypeClick(1);
            }
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
        });

        //安全警示趋势
        this.drawSafeWarningTrend();//绘制安全警示趋势图 1015 刘杰 增
//        this.$http.get('index/safeWarningTrend/'+this.safeSumYear).then(function (res) {
//          this.trendIsVisual=false;
//          this.trendData=[];
//          if (res.data.success) {
//            //0927刘杰  本次汇报用模拟数据，将实际数据注释
//            if(res.data.data[String(this.safeSumYear)]){
//              let tempList=[];
//              let tempObj=res.data.data[String(this.safeSumYear)];
//              for(let item in tempObj){
//                tempList.push(tempObj[item]);
//              }
//              for(let i=0;i<tempList.length;i++){
//                this.trendData[i]=tempList[i];
//              }
//            }else
//            {
//
//              this.trendData=[14,15,15,14,16,15,16,16,15,14,14,15];
//              this.trendIsVisual=true;
//            }
//            let trendChartData = this.echarts.init(document.getElementById('trendChart'));
//            trendChartData.setOption({
//              tooltip: {
//                trigger: 'axis',
//              },
//              legend: {
//              },
//              grid: {
//                left: '1%',
//                right: '10%',
//                bottom: '10%',
//                top:'15%',
//                containLabel: true
//              },
//              xAxis: {
//                type: 'category',
//                boundaryGap: false,
//                data: this.trendMonthArr
//              },
//              yAxis: {
//                type: 'value'
//              },
//              series: [{
//                name:'隐患个数',
//                type: 'line',
//                smooth: true,
//                data: this.trendData,
//                label: {
//                  normal: {
//                    show: false,
//                    position: 'top',
//                    color: '#FBD249',
//                  }
//                },
//                areaStyle:{
//                  normal:{
////                    color:'#0F6FC6',
//                    color: new this.echarts.graphic.LinearGradient(0, 0, 0, 1, [{
//                      offset: 0,
//                      color: '#FBD249'
//                    }, {
//                      offset: 1,
//                      color: '#fff'
//                    }])
//
//                  }
//                },
//
//                lineStyle:{
//                  normal:{
//                    color:  '#F6A623',
//                  }
//                }
//              }]
//            });
//          }
//        }.bind(this)).catch(function (err) {
//          console.log(err);
//        });


        //绘制部门安全绩效图
        this.drawDepartmentScore();

        //0925刘杰
        //绘制控股公司安全绩效图
        this.drawCompanyScore();
        //绘制安全月报展示
//        this.monthShow();
      },
      drawDepartmentScore:function () {
        var colors = ['rgba(251,210,73,0.7)'];
//        var colors = ['rgba(20,200,212,0.5)'];


        var aCategorys = ['0','办公室','财务部','审计部','资产部','安全部','-'];
        var topdata = [0,90,96, 96, 97, 95];//峰值data
        var aSeries = [{
          name: '安全绩效',
          type: 'line',
          symbol:'circle',
          symbolSize:12,
          itemStyle: {
            normal: {
              color: '#F6A623',
              borderColor:"#ffffff",
              borderWidth:2
            }
          },
          lineStyle: {
            normal: {
              opacity: 0
            }
          },

          data:[0,90,96, 96, 97, 95]
        },];

        aCategorys.forEach(function(v, i, a) {
          var name = v;
          if (v !== '') {
            var data = [];
            for (var j = 0; j < aCategorys.length; j++) {
              data.push('-');
            }
            data[i - 1] = 0;
            data[i] = topdata[i];
            data[i + 1] = 0;
            aSeries.push({
              name: name,
              type: 'pictorialBar',
              smooth: false,
              legendHoverLink:false,
              symbol: 'path://M0,10 L10,10 C5.5,10 5.5,5 5,0 C4.5,5 4.5,10 0,10 z',
              barCategoryGap: '-130%',
              areaStyle: {
                normal: {
                  color: {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    colorStops: [{
                      offset: 0,
                      color: colors[i - 1] // 0% 处的颜色
                    }, {
                      offset: 1,
                      color: colors[i - 1] // 100% 处的颜色
                    }],
                    globalCoord: false // 缺省为 false
                  }
                }
              },
              data: data,
            });
          }
        });

        let departmentData = this.echarts.init(document.getElementById('departmentScore'));
        departmentData.setOption({
          color: colors,
          tooltip: {
            trigger: 'axis',
            formatter: function(params) {
              var rValue =params[0].name+'<br>';
              params.forEach(function(v, i, a) {
                if (v.data !== 0 && v.data !== "-" && v.seriesType == "line") {
                  rValue+='<span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:' + v.color + '"></span>'+v.seriesName + ':' + v.data +'<br>';
                }
              })
              return rValue;
            }
          },
          legend: {
            icon: 'circle',
            itemWidth: 14,
            itemHeight: 14,
            itemGap: 15,
            data: ['安全绩效'],
            // right: '20%',
            textStyle: {
              fontSize: 14,
              color: '#424242'
            }
          },
          xAxis: [{
            type: 'category',
            boundaryGap: false,
            data: aCategorys,
            axisLabel:{
              textStyle:{
                fontSize:14
              }
            },
            splitLine: {
              show: true,
              lineStyle:{
                color:'#f7f7f7'
              }
            }
          }],
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          yAxis: [{
            type: 'value',
            splitLine: {
              show: true,
              lineStyle:{
                color:'#f7f7f7'
              }
            }
          }],
          series: aSeries
        });
      },

      //绘制安全警示趋势图  1015刘杰 增
      drawSafeWarningTrend: function () {
        {

          this.trendData=[14,15,15,14,16,15,16,16,15,14,14,15];
          this.trendIsVisual=true;
        }
        let trendChartData = this.echarts.init(document.getElementById('trendChart'));
        trendChartData.setOption({
          tooltip: {
            trigger: 'axis',
          },
          legend: {
          },
          grid: {
            left: '1%',
            right: '10%',
            bottom: '10%',
            top:'15%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: this.trendMonthArr
          },
          yAxis: {
            type: 'value'
          },
          series: [{
            name:'隐患个数',
            type: 'line',
            smooth: true,
            data: this.trendData,
            label: {
              normal: {
                show: false,
                position: 'top',
                color: '#FBD249',
              }
            },
            areaStyle:{
              normal:{
//                    color:'#0F6FC6',
                color: new this.echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                  offset: 0,
                  color: '#FBD249'
                }, {
                  offset: 1,
                  color: '#fff'
                }])

              }
            },

            lineStyle:{
              normal:{
                color:  '#F6A623',
              }
            }
          }]
        });
      },

      //0925刘杰
      drawCompanyScore:function () {
        var colors = ['rgba(251,210,73,0.7)'];
        var aCategorys = ['0','交工公司','营运公司','检测公司','-'];
        var topdata = [0,97,96, 95,0];//峰值data
        var aSeries = [{
          name: '安全绩效',
          type: 'line',
          symbol:'circle',
          symbolSize:12,
          itemStyle: {
            normal: {
              color: '#F6A623',
              borderColor:"#ffffff",
              borderWidth:2
            }
          },
          lineStyle: {
            normal: {
              opacity: 0
            }
          },

          data:[0,97,96, 96]
        },];

        aCategorys.forEach(function(v, i, a) {
          var name = v;
          if (v !== '') {
            var data = [];
            for (var j = 0; j < aCategorys.length; j++) {
              data.push('-');
            }
            data[i - 1] = 0;
            data[i] = topdata[i];
            data[i + 1] = 0;
            aSeries.push({
              name: name,
              type: 'pictorialBar',
              smooth: false,
              legendHoverLink:false,
              symbol: 'path://M0,10 L10,10 C5.5,10 5.5,5 5,0 C4.5,5 4.5,10 0,10 z',
              barCategoryGap: '-130%',
              areaStyle: {
                normal: {
                  color: {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    colorStops: [{
                      offset: 0,
                      color: colors[i - 1] // 0% 处的颜色
                    }, {
                      offset: 1,
                      color: colors[i - 1] // 100% 处的颜色
                    }],
                    globalCoord: false // 缺省为 false
                  }
                }
              },
              data: data,
            });
          }
        });

        let departmentData = this.echarts.init(document.getElementById('companyScore'));
        departmentData.setOption({
          color: colors,
          tooltip: {
            trigger: 'axis',
            formatter: function(params) {
              var rValue =params[0].name+'<br>';
              params.forEach(function(v, i, a) {
                if (v.data !== 0 && v.data !== "-" && v.seriesType == "line") {
                  rValue+='<span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:' + v.color + '"></span>'+v.seriesName + ':' + v.data +'<br>';
                }
              })
              return rValue;
            }
          },
          legend: {
            icon: 'circle',
            itemWidth: 14,
            itemHeight: 14,
            itemGap: 15,
            data: ['安全绩效'],
            // right: '20%',
            textStyle: {
              fontSize: 14,
              color: '#424242'
            }
          },
          xAxis: [{
            type: 'category',
            boundaryGap: false,
            data: aCategorys,
            axisLabel:{
              textStyle:{
                fontSize:14
              }
            },
            splitLine: {
              show: true,
              lineStyle:{
                color:'#f7f7f7'
              }
            }
          }],
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          yAxis: [{
            type: 'value',
            splitLine: {
              show: true,
              lineStyle:{
                color:'#f7f7f7'
              }
            }
          }],
          series: aSeries
        });
      },
      //刘杰 1012 增 起
      //隐患分析点击事件对话框
      searchDangerPieData:function (radioType,name) {

        this.pieChartDialogData=[];
        let params={needChange:1,downward:true};
//        {needChange:1,downward:true};
        let startDate = new Date();
        startDate.setFullYear(this.safeSumYear,0,1);
        let endDate = new Date();
        endDate.setFullYear(this.safeSumYear,11,31);
        params.startDate=startDate;params.endDate=endDate;
        switch(radioType){
          case 1:params.hiddenDangerLevel=name;break;
          case 2:params.dangerType=name;break;
          case 3:params.dangerStatus=name;break;

        }
        this.dangerTableLoading=true;
        this.$http.post('index/findAnalysis',params).then(function (res) {
          if (res.data.success) {
            this.dangerTableLoading=false;
            let tempList=[];
            tempList=res.data.data;
            this.pieChartDialogData=tempList;
          }
        }.bind(this)).catch(function (err) {
          this.$message.error('隐患数据获取失败！');
          console.log(err);
        });
      },
      //刘杰 1012 增 终

      //查看隐患对话框
      searchDangerTableData:function () {
        this.dangerWholeData.allDanger=[];
        this.dangerWholeData.doneDanger=[];
        this.dangerWholeData.noDanger=[];
        let params={needChange:1,downward:true};
        let startDate = new Date();
        startDate.setFullYear(this.safeSumYear,0,1);
        let endDate = new Date();
        endDate.setFullYear(this.safeSumYear,11,31);
        params.startDate=startDate;params.endDate=endDate;
        this.dangerTableLoading=true;
        this.$http.post('index/findAnalysis',params).then(function (res) {
          if (res.data.success) {
            this.dangerTableLoading=false;
            let tempList=[];
            tempList=res.data.data;
            for(let i=0;i<tempList.length;i++){
              this.dangerWholeData.allDanger.push(tempList[i]);
              if(tempList[i].changeTime){
                this.dangerWholeData.doneDanger.push(tempList[i]);
              }else{
                this.dangerWholeData.noDanger.push(tempList[i]);
              }
            }
            this.changeDangerTab();
          }
        }.bind(this)).catch(function (err) {
          this.$message.error('隐患数据获取失败！');
          console.log(err);
        });
      },
      //切换隐患类别
      changeDangerTab:function () {
        this.dangerTableData=[];
        if(this.dangerDialogTab!=='全部隐患'){
          if(this.dangerDialogTab==='已整改'){
            this.dangerTableData=[...this.dangerWholeData.doneDanger];
          }else{//未整改
            this.dangerTableData=[...this.dangerWholeData.noDanger];
          }
        }else {//全部
          this.dangerTableData=[...this.dangerWholeData.allDanger];
        }
      },

      //改时间格式
      changeTimeFormat:function (row) {
        return this.transferTime(row.changeTime);
      },

    }
  }
</script>
<style>
  .managerView-main{
    position:absolute;
    top:85px;
    left:0;
    right:0;
    bottom: 0;
    min-width: 1200px;
    background-color: #f2f2f2;
    overflow-y: scroll;
  }
  .managerView-mainImg{
    width: 1200px;margin: 5px auto 0 auto;
    background: url('./../static/imgs/bg.png') no-repeat center bottom;
  }
  .round-gray-text{
    width:100px;
    height:100px;
    border: 2px solid #9f9f9f;
    border-radius: 50px;
    color: #9f9f9f;
    text-align: center;
    line-height: 100px;
    transform:rotate(-45deg);
    -ms-transform:rotate(-45deg); /* Internet Explorer */
    -moz-transform:rotate(-45deg); /* Firefox */
    -webkit-transform:rotate(-45deg); /* Safari 和 Chrome */
    -o-transform:rotate(-45deg); /* Opera */
  }
  .click-text{ cursor: pointer;font-size: 26px;text-align: center;color: red}
  .click-text:hover{color: rgb(246,166,35)}

  .show-text{ font-size: 26px;text-align: center;color: #ED704E;}
</style>
