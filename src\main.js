// The Vue build version to load with the `import` command
// (runtime-only or standalone) has been set in webpack.base.conf with an alias.
import Vue from 'vue'
import App from './App.vue'
import router from './router'
import http from '../src/assets/functions/axiosServer'
import store from './store/index'
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import filter from './assets/functions/filter'
import commonFunction from './assets/functions/commonFunction'
import dateUtil from './assets/safe'
import baseStyle from './assets/style/baseStyle.css'
import tool from '@/components/common/tool.js'
import babelPolyfill from 'babel-polyfill'
import fileHttp from '../src/assets/functions/fileServer'

// 引入Egrid
import Egrid from 'egrid'
// 引入echarts
import echarts from 'echarts'

// table 的样式需要手动引入
import 'element-ui/lib/theme-chalk/icon.css'
import 'element-ui/lib/theme-chalk/table.css'
import 'element-ui/lib/theme-chalk/table-column.css'
// 引入vue-video-player
import VideoPlayer from 'vue-video-player'
require('video.js/dist/video-js.css')
require('vue-video-player/src/custom-theme.css')
Vue.use(VideoPlayer)
import vueEsign from 'vue-esign';

Vue.use(vueEsign)
import Video from 'video.js'
import 'video.js/dist/video-js.css'

Vue.prototype.$video = Video

Vue.use(Egrid);
Vue.use(echarts);

//全局引用
Vue.use(ElementUI);
Vue.use(filter);
Vue.use(dateUtil);
Vue.use(baseStyle);
Vue.use(babelPolyfill);
Vue.use(commonFunction);

// Vue.use(VueQuillEditor)
Vue.config.productionTip = false;

Vue.prototype.$http=http;
Vue.prototype.fileHttp=fileHttp;
Vue.prototype.$tool = tool;
Vue.prototype.commonFunction = commonFunction;
Vue.prototype.echarts=echarts;//引入echarts

/* eslint-disable no-new */
new Vue({
  el: '#app',
  router,
  store,
  components: { App },
  template: '<App/>'
})
