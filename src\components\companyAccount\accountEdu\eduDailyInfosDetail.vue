<template>
  <div class="background-style" style="padding: 10px">
    <el-row style="margin:0">
      <el-col :span="6">
        <el-button type="primary" size="mini" @click="$router.back()">返回</el-button>
      </el-col>
    </el-row>
    <el-row style="margin:10px 0 0 0">
      <el-col :span="24">
        <egrid class="egrid"
               stripe border
               maxHeight="500"
               :data="egrid.data"
               :columns="egrid.columns"
               :columns-handler="columnsHandler"
               :columns-schema="egrid.columnsSchema">
        </egrid>
      </el-col>
    </el-row>
  </div>
</template>

<script>
  // 单元格的组件
  var Editor = {
    template:
      `<div>
          <el-button type="primary" size="mini" @click="viewBtnClickHandler">查看</el-button>
        </div>`,
    props: ['row', 'col'],
    methods:{
      // 查看按钮
      viewBtnClickHandler(){
        this.$emit('row-view', this.row)
      },
    }
  }


  export default {
    data(){
      return {
        // 表格
        egrid : {
          data : [],
          columns : [
            { label: '培训时间', prop: 'trainingDate' },
            { label: '学时', prop: 'trainingHours' },
            { label: '培训内容', prop: 'courses' },
            { label: '参加人员', prop: 'participants' },
            { label: '组织部门', prop: 'department' },
            { label: '安全培训教育效果评价', prop: 'judge' },
          ],
          // columnsProps 用于定义所有 columns 公共的属性
          columnsProps: {
            fit : true,
            sortable: true,
            align : 'center',
          },
          columnsSchema : {
            '培训时间' : {
              width : 120,
            },
            '学时' : {
              width : 50
            },
            '培训内容' : {
              showOverflowTooltip : true,
            },
            '参加人员' : {
              showOverflowTooltip : true,
            },
            '组织部门' : {
              showOverflowTooltip : true,
            },
            '安全培训教育效果评价' : {
              showOverflowTooltip : true,
            },
          },
        }
      }
    },
    created(){
      this.init();
    },
    watch:{
      $route(to,from){
        let data = to.params && to.params.row && to.params.row.data;
        if(to.name === 'eduEntryTrainings') {
          if(data){
            this.searchBtnClickHandle();
          }
        }
      }
    },
    methods:{
      // 初始化
      init(){
        let data = this.$route.params.row.data;
        if(data){
          // 搜索
          this.searchBtnClickHandle();
        }
      },
      // 搜索按钮
      searchBtnClickHandle(){
        let data = this.$route.params.row.data;
        let list = data.map(function(it){
          return {
            trainingDate : (this.$tool.formatDateTime(it.trainingDate)|| '').substring(0, 10),
            trainingHours : it.trainingHours|| '',
            courses : it.courses || '',
            participants : it.participants || '',
            department : it.department || '',
            judge : it.judge || '',
          }
        }.bind(this));
        this.egrid.data = list;
      },

      // egrid---操作列
      columnsHandler (cols) {
        let that = this;
        return cols.concat({
          label: '操作',
          fixed: 'right',
          width: 100,
          component: Editor,
          listeners: {
            'row-view' (row) {
              // implement
              console.log('row', row);
              let name = 'eduDailyInfosItem';
              that.$router.push({
                name : name,
                params : {
                  row : row
                }
              })
            },
          }
        });
      },
    }
  }
</script>

<style>

</style>
