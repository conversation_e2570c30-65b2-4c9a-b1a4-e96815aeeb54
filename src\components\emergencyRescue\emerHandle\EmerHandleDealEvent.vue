<template>
  <div>
    <el-container id="EmerHandleDealEvent">
      <el-main>
        <el-row>
          <el-col :span="24" class="title">应急处置</el-col>
        </el-row>
        <el-row>预案名称：{{data.name}}</el-row>
        <el-row>预案执行清单：</el-row>
        <el-row>
          <el-table
            :data="data.emgHandleListPublics"
            style="width: 100%">
            <el-table-column
              label="序号"
              type="index"
              width="55">
            </el-table-column>
            <el-table-column
              prop="execContent"
              show-overflow-tooltip
              label="内容"
              width="200">
            </el-table-column>
            <el-table-column
              prop="helpInfo"
              show-overflow-tooltip
              label="帮助"
              width="200">
            </el-table-column>
            <el-table-column
              prop="executeUserName"
              label="执行人"
              width="180">
            </el-table-column>
            <el-table-column
              prop="executeTime"
              :formatter="formatDateTime"
              label="执行时间"
              width="180">
            </el-table-column>
            <el-table-column
              label="操作"
              v-if="pageStatus == 'edit'"
              width="200">
              <template slot-scope="scope">
                <el-checkbox
                  @change="handleListChangeHandle(scope.row.id)"
                  v-model="scope.row.status == 1"
                  :label="scope.row.id" border :disabled="scope.row.status == 1">勾选执行</el-checkbox>
                </template>
            </el-table-column>
          </el-table>
        </el-row>
        <el-row>
          <el-col :span="2">备注：</el-col>
          <el-col :span="12">
            <el-input
              :readonly="pageStatus === 'view' || (data.event && data.event.status == 1)"
              v-model="form.remark"
              type="textarea"
              :rows="4"
              placeholder="请输入内容">
            </el-input>
          </el-col>
          <el-col :offset="1" :span="4">
            <el-button v-if="pageStatus === 'edit' && data.event && data.event.status == 0" type="primary" size="small" @click="saveBtnClickHandle">保存备注</el-button>
          </el-col>
          <!--<el-col :span="3" v-if="emgEvent && emgEvent.reported == 1">-->
          <el-col :offset="1" :span="3">
            <el-button type="primary" size="small" @click="reportBtnClickHandle">重大信息上报</el-button>
          </el-col>
        </el-row>
        <el-row>
          <!--文件上传和下载-->
          <el-col :span="24" class="card-shadow-style">
            <div style="width: 100%;padding-top: 10px;padding-bottom:10px;float: left;background-color: #f2f2f2">
              <i class="el-icon-upload" style="color:#049ff1;float: left;margin:3px 10px 0 20px"></i>
              <span style="color:#049ff1;width: 200px;float: left;">其他文件资料</span>
            </div>
            <el-col :span="24" style="padding: 20px">
              <el-upload
                class="upload-demo"
                :action="upload.url"
                multiple
                :with-credentials="upload.cookies"
                :data="upload.params"
                :before-upload="beforeUpload"
                :on-success="uploadSuccess"
                style="width: 300px;margin-bottom: 10px;">
                <el-button size="small" type="primary" v-if="pageStatus === 'edit' && data.event && data.event.status == 0">点击上传</el-button>
              </el-upload>
              <el-col :span="24">
                <el-table
                  :data="upload.fileData"
                  border
                  style="width: 100%">
                  <el-table-column
                    type="index"
                    align="center"
                    label-class-name="inner-header-style"
                    width="50">
                  </el-table-column>
                  <el-table-column
                    prop="fileName"
                    label="文件名称"
                    align="center"
                    label-class-name="inner-header-style"
                    width="320">
                  </el-table-column>
                  <el-table-column
                    prop="uploadTime"
                    :formatter="formatDateTime"
                    label="上传时间"
                    align="center"
                    label-class-name="inner-header-style"
                    min-width="200">
                  </el-table-column>
                  <el-table-column
                    v-if="pageStatus === 'edit' && data.event && data.event.status == 0"
                    label="操作"
                    align="center"
                    fixed="right"
                    width="140"
                    label-class-name="inner-header-style">
                    <template slot-scope="scope">
                      <el-button type="text" size="medium" style="color: #5daf34"
                                 @click="downloadFile(scope.row)">下载
                      </el-button>
                      <el-button type="text" size="medium" style="color: #dd6161"
                                 @click="deleteUploadFile(scope.row,scope.$index)">刪除
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </el-col>
            </el-col>

          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8" class="equalH">当前事件级别：</el-col>
          <el-col :span="3" class="equalH">{{data.eventLevel}}级</el-col>
          <el-col :span="3" class="equalH">死亡{{data.event && data.event.deathNum}}人</el-col>
          <el-col :span="3" class="equalH">受伤{{data.event && data.event.injuriesNum}}人</el-col>
          <el-col :span="3">
            <el-button type="primary" size="small" @click="eventDetailBtnClickHandle">事件详细</el-button>
          </el-col>
        </el-row>
        <el-row>注意事项：</el-row>
        <el-row>
          <el-input
            :readonly="pageStatus === 'view'"
            v-model="data.attentionItem"
            :rows="8"
            type="textarea">
          </el-input>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-table
              :data="data.emgHandlePublicGoodsList"
              style="width: 100%">
              <el-table-column
                prop="name"
                label="物资名称"
                width="180">
              </el-table-column>
              <el-table-column
                prop="count"
                label="数量"
                width="180">
              </el-table-column>
              <el-table-column
                label="操作"
                width="100">
                <template slot-scope="scope">
                  <el-button type="text" size="small" @click="lookStockBtnClickHandle(scope.row)">查看库存</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>
      </el-main>
      <el-footer>
        <el-row  style="margin-bottom:50px;">
          <el-col :offset="8" :span="12">

            <el-button type="primary" size="small" @click="$router.push({ name : 'emerHandle' });">返回首页</el-button>
            <el-button
              type="primary"
              size="small"
              v-if="pageStatus === 'edit' && handleListFinish"
              @click="overBtnClickHandle">结束事件</el-button>
          </el-col>
        </el-row>
      </el-footer>
    </el-container>
    <el-dialog title="查看库存" :visible.sync="lookStockDialogShow">
      <el-table :data="goodsList">
        <el-table-column property="deptName" label="仓库名" width="150"></el-table-column>
        <el-table-column property="count" label="数量"></el-table-column>
        <el-table-column property="manageUser" label="联系人"></el-table-column>
        <el-table-column property="contact" label="联系电话" width="150"></el-table-column>
        <el-table-column property="goodsLocation" label="地址" width="200"></el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
    export default {
      data(){
        let that = this;
        return {
          // 事件的信息
          data : {},
          // 物资库存数据
          goodsList : [],
          lookStockDialogShow : false,  // 查看库存对话框
          // 上传文件
          upload : {
            // 地址
            url : that.$http.defaults.baseURL + 'file/upload',
            // token
            cookies : true,
            // 上传参数
            params : {
              contentId: 0,
              contentType: 3
            },
            // 文件列表
            fileData : [],
          },
          // 要提交的数据---其实只有一个备注
          form : {
            // eventId
            id : '',
            // 备注
            remark : ''
          },
          // 执行清单是否完成
          handleListFinish : false,
          // 页面的状态----查看或者编辑，默认编辑
          pageStatus : 'edit',
        }
      },
      mounted(){
        // 初始化
        this.init();
        // 获取上传文件
        this.loadFile();
      },
      created(){
        if(this.$route.params.status == 'view'){
          this.viewPage();
        } else if(this.$route.params.status == 'edit') {
          this.editPage();
        }
      },
      watch:{
        '$route.params.status'(to, from){
          // 查看
          if(to === 'add'){
            this.init();
          } else if(to === 'view'){
            this.viewPage();
          } else if(to === 'edit') {
            this.editPage();
          }
        }
      },
      computed: {
        // 事件
        emgEvent : function(){
          return this.$store.state.emerHandleModule.emgEvent;
        },
        // 发布
        emgHandlePublic : function(){
          return this.$store.state.emerHandleModule.emgHandlePublic;
        },
      },
      methods:{
        // 初始化
        init(){
          this.clear();
          let params = {
            eventId : this.emgEvent.id
          }
          this.$store.dispatch('emgHandlePublicFindAction', params).then(function(res){
            if(res.success){
              this.data = res.data.list[0];
              this.$store.commit('emgEventMutation', this.data.event);
              this.$store.commit('emgHandlePublicMutation', res.data.list[0])
            }
          }.bind(this));
        },
        // 清空数据
        clear(){
          this.data = {};
          this.goodsList = [];
          this.upload.fileData = [];
          this.form.remark = '';
        },
        // 格式化时间
        formatDateTime(row, column, cellValue){
          let time = row.executeTime ? row.executeTime : row.uploadTime;
          return this.$tool.formatDateTime(time);
        },
        // 查看详情
        viewPage(){
          this.pageStatus = 'view';
          let params = {
            eventId : this.emgEvent.id
          }

          this.$store.dispatch('emgHandlePublicFindAction', params).then(function(res){
            this.data = res.data.list[0];
            this.$store.commit('emgEventMutation', this.data.event);
            this.$store.commit('emgHandlePublicMutation', res.data.list[0])
            this.form.id = this.data.id;
            this.form.remark = this.data.remark;
            // 获取上次的资料
            var params = new URLSearchParams()
            params.append("contentId", this.data.id)
            params.append("contentType", this.upload.params.contentType)
            this.$store.dispatch('fileFindAction', params).then(function(res){
              if (res.success) {
                this.upload.fileData = res.data
              }
            }.bind(this));
          }.bind(this));
        },
        // 修改详情
        editPage(){
          this.pageStatus = 'edit';
          this.form.id = this.emgEvent.id;
          let params = {
            eventId : this.emgEvent.id
          }

          this.$store.dispatch('emgHandlePublicFindAction', params).then(function(res){
            // 发布事件的内容
            this.data = res.data.list[0];
            this.$store.commit('emgEventMutation', this.data.event);
            this.$store.commit('emgHandlePublicMutation', res.data.list[0])
            this.handleListFinish = this.data.emgHandleListPublics.every(function(it){
              return it.status === 1;
            })
            this.data['eventId'] = this.data.eventId;
            this.form.remark = this.data.remark;
            // 获取上次的资料
            var params = new URLSearchParams()
            params.append("contentId", this.data.id)
            params.append("contentType", this.upload.params.contentType)
            this.$store.dispatch('fileFindAction', params).then(function(res){
              if (res.success) {
                this.upload.fileData = res.data
              }
            }.bind(this));
          }.bind(this));
        },
        // 保存备注到事件中
        saveBtnClickHandle(){
          let params = {
            id : this.data.id,
            remark : this.form.remark
          };
          this.$store.dispatch('emgHandlePublicUpdateAction', params).then(function(res){
            if(res.success){
              this.$message({
                type : 'success',
                message : '操作成功'
              })
            }
          }.bind(this));
        },
        // 事件详细信息按钮点击时间处理函数
        eventDetailBtnClickHandle(){
          if(this.emgEvent.status === 1) this.pageStatus = 'view';
          this.$router.push({ name : 'emerHandleEdit', params : { status : this.pageStatus}})
        },
        // 查看重大事件按钮
        reportBtnClickHandle(){

//          this.$router.push({ name : 'emerHandleReport', params : { status : 'view'}})
//          return;
          // view 查看，edit 添加，上报没有修改
          let status = this.$route.params.status;
          // emgEvent.reported 1为已经上报了，0为没有上报过
          let reported = this.emgEvent.reported;
          if(status === 'view' || reported === 1){
            this.$router.push({ name : 'emerHandleReport', params : { status : 'view'}})
          } else {
            this.$router.push({ name : 'emerHandleReport', params : { status : 'edit'}})
          }
        },
        // 查看库存按钮
        lookStockBtnClickHandle(row){
          let params = {
            goodsName : row.name
          }
          this.$store.dispatch('emgGoodsFindGetAction', params).then(function(res){
            if(res.success){
              this.goodsList = res.data.list;
              this.lookStockDialogShow = true;
            }
          }.bind(this))
        },
        // 结束事件
        overBtnClickHandle(){
          let params = {
            status : 1,
            id : this.data.eventId   // eventId
          }
          this.$store.dispatch('emgEventUpdateAction', params).then(function(res){
            if(res.success){
              this.$message({
                type : 'success',
                message : '操作成功'
              })
              this.$router.push({ name : 'emerHandle'})
            } else {
              this.$message({
                type : 'error',
                message : '操作失败！！'
              })
            }
          }.bind(this))
        },
        // 改变执行清单
        handleListChangeHandle(id){
          let params = {
//            handlePublicId : this.emgHandlePublic.id, // handlePublicId
            handlePublicId : this.data.id, // handlePublicId
            id : id,      // handlePublicListId
            status : 1,
          }
//          return;
          this.$store.dispatch('emgHandleListPublicAddOrUpdateAction', params).then(function(res){
            if(res.success){
              this.$message({
                type : 'success',
                message : '操作成功'
              })
              this.handleListFinish = res.data.handleListFinish;
              this.init();
            }
          }.bind(this))
        },
        // 上传之前
        beforeUpload(file){
          this.upload.params.contentId = this.data.id;
        },
        //上传文件
        uploadSuccess: function (response, file, fileList) {
          if (response.success) {
            this.loadFile();
          }
        },
        // 获取上传文件
        loadFile: function () {
          var params = new URLSearchParams()
          params.append("contentId", this.upload.params.contentId)
          params.append("contentType", this.upload.params.contentType)
          this.$store.dispatch('fileFindAction', params).then(function(res){
            if (res.success) {
              this.upload.fileData = res.data
            }
          }.bind(this));
        },
        // 删除上传文件
        deleteUploadFile: function (row, index) {
          this.$confirm('此操作将删除该文件, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(function(){
            var params = new URLSearchParams()
            params.append("fId", row.fId)
            this.$store.dispatch('fileDeleteAction', params).then(function (res) {
              if (res.success) {
                this.loadFile();
              }
            }.bind(this))
          }.bind(this));
        },
        // 下载文件
        downloadFile: function (row) {
          const loading = this.$loading({
            lock: true,
            text: 'Loading',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });
          var params = new URLSearchParams()
          params.append("fId", row.fId)
          this.$store.dispatch('fileDownloadAction', params).then(function(res){ // 处理返回的文件流
            loading.close()
            // 创建a标签
            const elink = document.createElement('a')
            // 文件名
            elink.download = row.fileName
            elink.style.display = 'none'
            const blob = new Blob([res.data])
            elink.href = URL.createObjectURL(blob)
            document.body.appendChild(elink)
            // 触发点击a标签事件
            elink.click();
            document.body.removeChild(elink)
          })
        },
      }
    }
</script>

<style>
  #EmerHandleDealEvent{
    background : #fff;
    padding : 5px;
  }
  .el-row{
    margin:20px;
  }
  .title{
    text-align: center;
    margin-bottom: 20px;
    font-size: large;
    letter-spacing: 2px;
    color:#3576AA;
    border-left:5px solid #049ff1;
    border-radius: 5px;
    background-color: rgb(236,248,255);
    height: 50px;
    line-height: 50px;
  }
  .finish{
    color : #ccc;
  }
  .equalH{
    height:40px;
    line-height:40px;
  }
  .equalH .el-checkbox__label{
    display: inline-block;
    white-space: normal;
  }
</style>
