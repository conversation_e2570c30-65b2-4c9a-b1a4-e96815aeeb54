<template>
  <div id="dataReportTempletList" class="background-style">
    <el-container class="container">
      <el-main>
        <el-form ref="form" :model="form" label-width="5px">
          <el-row>
            <el-col :span="6">
              公司：宁波交通投资集团有限公司
            </el-col>
            <el-col  :span="6">
              <span>发布时间：2018.1.1</span>
            </el-col>

            <el-col :span="4">
              周期：季度
            </el-col>
          </el-row>

          <el-row  class="tableRow" style="margin-top: 20px;">
            <el-col >
              <el-table :data="dataReportTemplet.data" border style="width:80%;" >
                <el-table-column
                  prop="num"
                  label-class-name="header-style"
                  width="70"
                  label="序号"
                  type="index"
                ></el-table-column>
                <el-table-column
                  prop="name"
                  label-class-name="header-style"
                  label="名称"
                  min-width="300"
                ></el-table-column>
                <el-table-column
                  prop="pubTime"
                  label-class-name="header-style"
                  label="发布时间"
                  min-width="200"
                ></el-table-column>
                <el-table-column
                  fixed="right"
                  label-class-name="header-style"
                  label="操作"
                  min-width="100"
                >
                  <template slot-scope="scope">
                    <el-button size="mini" type="success" @click.native="itemViewClick(scope.row)">查看</el-button>
                  </template>
                </el-table-column>

              </el-table>
            </el-col>
            <el-col style="margin-top:20px;">
              <el-pagination
                background
                layout="prev, pager, next"
                :current-page="1"
                :page-size="10"
                :total="1"
                @current-change ="disasterPageChangeHandle">
              </el-pagination></el-col>
            <el-col style="margin-top: 20px;">
              <el-button @click="$router.back()">返回</el-button>
            </el-col>
          </el-row>

        </el-form>

      </el-main>
    </el-container>
  </div>
</template>

<script>
  export default{
    name: 'dataReportTempletList',

    data(){
      return {
        creatDate: '',
        dataReportTemplet: {
          data: [
            {num: '1', name: '2018第二季度', pubTime: '2018.7.1'},
            {num: '2', name: '2018第一季度', pubTime: '2018.4.1'},
            {num: '3', name: '2017第四季度', pubTime: '2018.1.1'},
            {num: '4', name: '2017第三季度', pubTime: '2017.10.1'},
            {num: '5', name: '2017第二季度', pubTime: '2017.7.1'},
            {num: '6', name: '2017第一季度', pubTime: '2017.4.1'},
            {num: '7', name: '2016第四季度', pubTime: '2017.1.1'},
            {num: '8', name: '2016第三季度', pubTime: '2016.10.1'},
            {num: '9', name: '2016第二季度', pubTime: '2016.7.1'},
            {num: '10', name: '2016第一季度', pubTime: '2016.4.1'},
          ],
          // 当前页
          pageCurrent: 1,
          // 页数大小
          pageSize: 10,
        }
        ,

      }
    },
    mounted: function () {

    },
    methods: {
      itemViewClick(row){

      },
    },
  }
</script>

<style>
</style>
