<template>
  <div id="dangerReformSummary">
    <div class="background-style">
      <el-col :span="22" :offset="1" class="success-background-title">{{planTitle}}</el-col>
      <el-col :span="23" :offset="1">
        <el-form :model="form"  label-width="100px">
          <el-col :span="24">
            <el-col :span="12">
              <el-form-item label="填报单位：">
                {{writeAddress}}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="填报时间：">
                {{transferTime(writeDate) }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="填报人：">
                {{writeUser}}
              </el-form-item>
            </el-col>
          </el-col>
        </el-form>
      </el-col>
      <el-col :span="24" style="margin-left:20px;">
       <!-- <el-table
          :data="tableContent"
          border
          fit
          style="width: 100%">
          <el-table-column
            width="50"
            type="index"
            align="center">
          </el-table-column>
          <el-table-column
            prop="inspectContent"
            label="隐患名称或存在部位"
            width="120"
            align="center">
          </el-table-column>
          <el-table-column
            prop="inspectResult"
            label="待整改"
            width="120"
            align="center">
          </el-table-column>
          <el-table-column
            prop="hiddenDangerLevel"
            label="隐患等级(A/B/C)"
            width="100"
            align="center">
          </el-table-column>
          <el-table-column
            prop="checkNum"
            label="检查表编号"
            width="100"
            align="center">
          </el-table-column>
          <el-table-column
            prop="memberName"
            label="检查人员"
            width="80"
            align="center">
          </el-table-column>
          <el-table-column
            label="整改期限"
            width="100"
            align="center">
            <template slot-scope="scope">
              <span>{{transferTime(scope.row.deadline)}}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="applyUserName"
            label="整改责任人"
            width="100"
            align="center">
          </el-table-column>
          <el-table-column
            prop="finishName"
            label="整改状态"
            width="80"
            align="center">
          </el-table-column>
          <el-table-column
            label="整改完成时间	"
            width="120"
            align="center">
            <template slot-scope="scope">
              <span>{{transferTime(scope.row.changeTime)}}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="deptName"
            label="备注"
            width="120"
            show-overflow-tooltip
            align="center">
          </el-table-column>
        </el-table>-->
        <el-table
          :data="tableContent"
          border
          fit
          style="width: 100%">
          <el-table-column
            type="index"
            align="center">
          </el-table-column>
          <el-table-column
            prop="inspectContent"
            label="隐患名称或存在部位"
            align="center">
          </el-table-column>
          <el-table-column
            prop="inspectResult"
            label="待整改"
            align="center">
          </el-table-column>
          <el-table-column
            prop="hiddenDangerLevel"
            label="隐患等级(A/B/C)"
            align="center">
          </el-table-column>
          <el-table-column
            prop="checkNum"
            label="检查表编号"
            align="center">
          </el-table-column>
          <el-table-column
            prop="memberName"
            label="检查人员"
            align="center">
          </el-table-column>
          <el-table-column
            label="整改期限"
            align="center">
            <template slot-scope="scope">
              <span>{{transferTime(scope.row.deadline)}}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="applyUserName"
            label="整改责任人"
            align="center">
          </el-table-column>
          <el-table-column
            prop="finishName"
            label="整改状态"
            align="center">
          </el-table-column>
          <el-table-column
            label="整改完成时间	"
            align="center">
            <template slot-scope="scope">
              <span>{{transferTime(scope.row.changeTime)}}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="deptName"
            label="备注"
            show-overflow-tooltip
            align="center">
          </el-table-column>
        </el-table>
      </el-col>
      <el-button style="float: right;margin: 20px" @click="returnClick">返回</el-button>
      <el-button type="primary" style="float: right;margin: 20px" @click="download">下载</el-button>
      <el-button type="success" style="float: right;margin: 20px" @click="print">预览</el-button>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'dangerReformSummary',
    data() {
      return {
        planTitle:'',
        tableHead:[
          {index:'num',title:'序号'},
          {index:'inspectContent',title:'隐患名称或存在部位'},
          {index:'inspectResult',title:'待整改'},
          {index:'hiddenDangerLevel',title:'隐患等级(A/B/C)'},
          {index:'checkNum',title:'检查表编号'},
          {index:'inspectMember',title:'检查人员'},
          {index:'deadline',title:'整改期限'},
          {index:'applyUserName',title:'整改责任人'},
          {index:'finish',title:'整改状态'},
          {index:'changeTime',title:'整改完成时间'},
          {index:'deptName',title:'备注'},
        ],
        writeAddress:'',
        writeDate:0,
        writeUser:'',
        dangerSafePlanId:0,
        tableContent:[],
        form:{
          num01:'',
          num02:''
        },
        year:0,
        startDate:0,
        endDate:0,
        timer : null,
      }
    },
    created:function(){
      this.year=parseInt(this.$tool.formatDateTime(this.$route.params.year).substring(0,4));
      this.planTitle=this.year+"年市属企业生产安全事故隐患排查治理情况一览表";
      this.searchTable();
    },
    watch:{
      $route(to, from){
        if(from.name==='accountDangerIndex') {
          this.year=parseInt(this.$tool.formatDateTime(this.$route.params.year).substring(0,4));
          this.planTitle=this.year+"年市属企业生产安全事故隐患排查治理情况一览表";
          this.searchTable();
        }
      }
    },
    methods:{
      searchTable:function () {
        // this.$route.params
        /*let year = this.year;
        let startDate = this.$route.params.startDate;
        let endDate = this.$route.params.endDate;
        let params = {};
        if(startDate && endDate){
          params = {
            year : year,
            startDate : startDate,
            endDate : endDate
          }
        } else {
          params = {
            year : year
          }
        }*/


        let params = new URLSearchParams;
        let year = this.year;
        let startDate = this.$route.params.startDate;
        let endDate = this.$route.params.endDate;
        params.append("year", this.year);
        if(startDate && endDate){
          params.append("startDate", startDate);
          params.append("endDate", endDate);
        }

//        let url = `/danger/inspectPublic/reformSummary?year=${year}&startDate=${startDate}&endDate=${endDate}`;
        this.$store.dispatch("dangerInspectPublicReformSummary",params).then(function (res) {
          if(res.success){
            this.tableContent=res.data.dangerInspectListPublicList;
            this.writeAddress=res.data.writeAddress;
            this.writeUser = res.data.writeUser;
            this.writeDate =res.data.writeDate;
            this.tableContent.forEach(function (item,index) {
              item.num=index+1;
            })
          }else{
            console.log('danger/inspectPublic/reformSummary'+'数据申请失败');
          }
        }.bind(this)).catch(function (err) {
          console.log('检查计划列表查找:'+err);
        }.bind(this));
      },
      download:function () {
        const loading = this.$loading({
          lock: true,
          text: 'Loading',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });

        let params = new URLSearchParams;
        let year = this.year;
        let startDate = this.$route.params.startDate;
        let endDate = this.$route.params.endDate;
        params.append("year", this.year);
        if(startDate && endDate){
          params.append("startDate", startDate);
          params.append("endDate", endDate);
        }


        this.$http({ // 用axios发送post请求
          method: 'post',
          url: 'report/getDangerInspectListPublicExcel/', // 请求地址
          data : params,
          responseType: 'blob' // 表明返回服务器返回的数据类型
        }).then((res) => { // 处理返回的文件流
          //console.info(res)
          loading.close()
        const content = res
        const elink = document.createElement('a') // 创建a标签
        elink.download = this.planTitle + ".xlsx" // 文件名
        elink.style.display = 'none'
        const blob = new Blob([res.data])
        elink.href = URL.createObjectURL(blob)
        document.body.appendChild(elink)
        elink.click() // 触发点击a标签事件
        document.body.removeChild(elink)
      })


      /*  return;
        this.$http({ // 用axios发送post请求
          method: 'get',
          url: 'report/getDangerInspectListPublicExcel/' + this.year, // 请求地址
          responseType: 'blob' // 表明返回服务器返回的数据类型
        }).then((res) => { // 处理返回的文件流
          //console.info(res)
          loading.close()
          const content = res
          const elink = document.createElement('a') // 创建a标签
          elink.download = this.planTitle + ".xlsx" // 文件名
          elink.style.display = 'none'
          const blob = new Blob([res.data])
          elink.href = URL.createObjectURL(blob)
          document.body.appendChild(elink)
          elink.click() // 触发点击a标签事件
          document.body.removeChild(elink)
        })*/
      },
      print:function(){
        let that = this;
      /*  let setting = {
          url : `/report/getDangerInspectListPublicHtml/${this.year}`,
        };*/
        let params = new URLSearchParams;
        let year = this.year;
        let startDate = this.$route.params.startDate;
        let endDate = this.$route.params.endDate;
        params.append("year", this.year);
        if(startDate && endDate){
          params.append("startDate", startDate);
          params.append("endDate", endDate);
        }


        that.loginLoading=that.$loading({
          lock: true,
          text: '数据加载中',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.5)'
        });
        that.$store.dispatch('printOnLinePost', params).then(function(res){
          that.loginLoading.close();//关闭登陆加载
          if(res.success){
            let myWindow=window.open("".href, "_blank");
            myWindow.document.write(res.data);
            window.clearTimeout(this.timer);
            this.timer = window.setTimeout(function(){
              myWindow.print();
            }, 300)
//                    myWindow.print()
          } else {
            that.$message({
              type : 'error',
              message : res.message || '调用打印接口失败！！'
            })
          }
        }.bind(this))
      },
      returnClick:function () {
        this.planTitle='';
        this.tableContent=[];
        this.writeAddress='';
        this.writeUser = '';
        this.writeDate = 0;
        this.$router.go(-1);
      },
    }
  }
</script>
<style>
  .el-tag + .el-tag {
    margin-left: 10px;
  }
  .button-new-tag {
    margin-left: 10px;
    height: 32px;
    line-height: 30px;
    padding-top: 0;
    padding-bottom: 0;
  }
  .input-new-tag {
    width: 90px;
    margin-left: 10px;
    vertical-align: bottom;
  }
</style>
