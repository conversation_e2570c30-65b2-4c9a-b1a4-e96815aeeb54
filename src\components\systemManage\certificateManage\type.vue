<template>
  <div id="trainingPlanIndex">
    <div class="background-style">

      <!--搜索区-->
      <div style="margin:10px;float: left">
        <el-button
          @click="addBtnClickHandle"
          type="success" icon="el-icon-plus">新增</el-button>
      </div>

      <!--表格区-->
      <div style="width: 100%;">
        <div style="padding: 10px">
          <el-table
            border
            :data="tableData.list"
            style="width: 100%">
            <el-table-column
              type="index"
              label="编号"
              width="100"
              align="center"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="certName"
              label="名称"
              min-width="150"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="remark"
              label="备注"
              width="150"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              fixed="right" label="操作"
              label-class-name="header-style"
              align="center" width="300">
              <template slot-scope="scope">
                <template>
                  <el-button size="mini" type="primary" @click="itemUpdateClick(scope.row)">修改</el-button>
                  <el-button size="mini" type="danger" @click="itemDeleteClick(scope.row)">删除</el-button>
                </template>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div>
          <el-pagination
            background
            layout="prev, pager, next"
            :current-page="tableData.pageNum"
            :page-size="form.pageSize"
            :total="tableData.total"
            @current-change ="disasterPageChangeHandle">
          </el-pagination>
        </div>
      </div>


      <!--对话框-->
      <el-dialog
        title="新增证书类型"
        :visible.sync="dialog.isShow"
        width="60%"
        :before-close="handleClose">
        <el-form label-width="100px">
          <el-row  class="row">
            <el-col :span="24">
              <el-form-item label="名称：">
                <el-input v-model="dialog.form.certName"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row  class="row">
            <el-col :span="24">
              <el-form-item label="备注：">
                <el-input  v-model="dialog.form.remark" type="textarea"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row  class="row">
            <el-col :span="2">
              <el-form-item>
                <el-button
                  type="danger"  size="mini"
                  @click="dialogOkBtnClickHandle">确定</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-dialog>
    </div>
  </div>
</template>
<script>
  export default {
    data() {
      return {
        form : {
          // 当前页
          pageCurrent : 1,
          // 页数大小
          pageSize : 5,
        },
        tableData : {},
        // 对话框
        dialog : {
          // 是否显示
          isShow : false,
          form : {
            id : '',
            // 名称
            certName : '',
            // 情况说明
            remark : '',
          },
        },
      }
    },
    mounted(){
      this.init();
    },
    watch:{
      $route(to,from){
        if(to.name === 'certificateManageType') {
          this.init();
        }
      }
    },
    methods:{
      // 初始化
      init(){
        // 搜索
        this.searchBtnClickHandle();
      },
      clear(){
        this.dialog.form = this.$tool.clearObj({}, this.dialog.form);
      },
      // 分页
      disasterPageChangeHandle(page){
        this.form.pageCurrent = page;
        this.searchBtnClickHandle();
      },
      // 搜索按钮
      searchBtnClickHandle(){
        this.clear();
        this.$store.dispatch('eduCertTypeFind', this.form).then(function(res){
          if(res.success){
            this.tableData = res.data;
          } else {
            this.$message({
              type : 'error',
              message : res.message || '错误'
            })
          }
        }.bind(this));
      },
      // 查看
      itemViewClick(row){
        let name = 'costBudgetAdd';
        let params = {
          id : row.id,
          status : 'view'
        }
        this.$router.push({ name : name, params : params})
      },
      // 修改
      itemUpdateClick(row){
        this.$tool.cloneObj(this.dialog.form, row);
        this.dialog.isShow = true;
      },
      // 删除按钮
      itemDeleteClick(row){
        this.$confirm('此操作将永久删除, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
          .then(function(){
            this.$store.dispatch('eduCertTypeDelete', {
              id : row.id
            }).then(function(res){
              if(res.success){
                this.$message({
                  type : 'success',
                  message : '删除成功'
                })
                this.searchBtnClickHandle();
              } else {
                this.$message({
                  type : 'error',
                  message : res.message || '删除失败！！'
                })
              }
            }.bind(this))
          }.bind(this))
      },
      // 新增按钮
      addBtnClickHandle(){
        this.dialog.form.id = '';
        this.dialog.isShow = true;
      },
      // 对话框---确定按钮
      dialogOkBtnClickHandle(){
        let form = this.dialog.form;
        if(form.certName == ''){
          this.$message({
            type : 'error',
            message : '名称不得为空！！'
          })
          return;
        }
        this.$store.dispatch('eduCertTypeAddOrUpdate', this.dialog.form).then(function(res){
          if(res.success){
            this.$message({
              type : 'success',
              message : '操作成功'
            })
            this.searchBtnClickHandle();
            this.dialog.isShow = false;
          } else {
            this.$message({
              type : 'error',
              message :  res.message || '操作失败'
            })
          }
        }.bind(this));
      },
      // 对话框--关闭
      handleClose(){
        this.dialog.form = this.$tool.clearObj({}, this.dialog.form);
        this.dialog.isShow = false;
      }
    }
  }
</script>
<style>
</style>
