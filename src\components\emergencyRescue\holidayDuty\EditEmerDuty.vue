<template>
  <div id="EditEmerDuty">
    <div class="background-style">
      <el-button @click="$router.push({name:'emerDuty'})" icon="el-icon-d-arrow-left" type="primary" plain size="medium" style="margin: 10px 0 0 10px">返回</el-button>
      <el-tabs v-model="activeTabName" style="padding: 5px 10px 0 10px"  type="card" @tab-click="tabClick" v-if="routePage==='view'">
        <el-tab-pane name="view">
          <span slot="label" style="font-size: large;font-weight: 600">集团值班表</span>
          <el-col :span="8">
            <el-tree ref="companyTree"
                     :data="companyTreeData"
                     :props="companyTreeProps"
                     @check="getCheckCompanyIds"
                     check-strictly
                     node-key="id"
                     show-checkbox
                     default-expand-all>
            </el-tree>
          </el-col>
          <el-col :span="16">
            <el-table
              :data="conmpanyDutyTable"
              border
              highlight-current-row
              style="width: 100%;margin-top: 10px">
              <el-table-column
                prop="dutyDate"
                label="值班日期"
                width="120"
                align="center"
                :formatter="dutyDateFormat"
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                label="值班周期"
                align="center"
                label-class-name="header-style"
                width="150">
                <template slot-scope="scope">
                  {{scope.row.startHour}}-{{scope.row.endHour}}
                </template>
              </el-table-column>
              <el-table-column
                prop="username"
                label="值班人员"
                width="100"
                align="center"
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="dutyJob"
                label="值班职务"
                min-width="120"
                align="center"
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="phone"
                label="联系电话"
                min-width="120"
                align="center"
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="companyName"
                label="所属公司"
                min-width="120"
                align="center"
                label-class-name="header-style">
              </el-table-column>
            </el-table>
            <div style="margin: 10px 0 0 0">
              <el-pagination
                background
                layout="prev, pager, next"
                :page-size="viewPageSize"
                :current-page="viewCurrentPage"
                :total="viewTotal"
                @current-change="viewPageClick">
              </el-pagination>
            </div>
          </el-col>
        </el-tab-pane>
        <el-tab-pane name="record">
          <span slot="label" style="font-size: large;font-weight: 600">值班上报记录</span>
          公司名称：
          <el-input placeholder="请输入公司名称" v-model="searchCompanyName" style="width: 300px;margin-right: 10px">
          </el-input>
          <el-button icon="el-icon-search" @click="editPageClick(1)" type="primary">搜 索</el-button>
          <el-table
            :data="dutyRecordTable"
            :span-method="objectSpanMethod"
            border
            highlight-current-row
            style="width: 100%;margin-top: 10px">
            <el-table-column
              prop="dutyDate"
              label="值班日期"
              width="120"
              align="center"
              :formatter="dutyDateFormat"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              label="值班周期"
              align="center"
              label-class-name="header-style"
              width="150">
              <template slot-scope="scope">
                {{scope.row.startHour}}-{{scope.row.endHour}}
              </template>
            </el-table-column>
            <el-table-column
              prop="username"
              label="值班人员"
              width="100"
              align="center"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="dutyJob"
              label="值班职务"
              min-width="120"
              align="center"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="phone"
              label="联系电话"
              min-width="120"
              align="center"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="companyName"
              label="所属公司"
              min-width="120"
              align="center"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="dutyAddress"
              label="值班地点"
              min-width="120"
              align="center"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              show-overflow-tooltip
              width="250"
              prop="dutyContent"
              label="值班内容"
              min-width="120"
              align="center"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="writeTime"
              label="上报时间"
              min-width="130"
              align="center"
              :formatter="writeTimeFormat"
              label-class-name="header-style">
            </el-table-column>
          </el-table>
          <div style="margin: 10px 0 0 0">
            <el-pagination
              background
              layout="prev, pager, next"
              :page-size="editPageSize"
              :current-page="editCurrentPage"
              :total="editTotal"
              @current-change="editPageClick">
            </el-pagination>
          </div>


        </el-tab-pane>
      </el-tabs>
      <el-tabs v-model="editTabName" style="padding: 5px 10px 0 10px"  type="card"  v-else>
        <el-tab-pane name="edit">
          <span slot="label" style="font-size: large;font-weight: 600">编辑</span>
          <el-col :span="16" :offset="4" class="primary-background-title" style="margin-top: 0">编辑值班表</el-col>
          <el-col :span="16" :offset="4">
            <el-form :model="form" class="demo-ruleForm" label-width="100px">
              <el-col :span="24">
                <el-form-item label="节假日类型：">
                  <el-input v-model="form.title" readonly="readonly"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-col :span="12">
                  <el-form-item label="年份：">
                    <el-input v-model="form.year" readonly="readonly"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="值班天数：">
                    <el-input v-model="form.dutyDayCount" readonly="readonly"></el-input>
                  </el-form-item>
                </el-col>
              </el-col>
              <el-col :span="24">
                <!--<el-button type="success" @click="updateClick">修改基本信息</el-button>-->
                <el-button type="primary" @click="dialogTitle='添加值班人员';addPerson = true">新增本公司值班人员</el-button>
              </el-col>
              <el-col :span="24" style="margin-top: 10px">
                <el-table
                  :data="dutyTable"
                  border
                  highlight-current-row
                  style="width: 100%;margin-top: 10px">
                  <el-table-column
                    type="index"
                    width="50"
                    align="center"
                    label-class-name="header-style">
                  </el-table-column>
                  <el-table-column
                    prop="dutyDate"
                    label="值班日期"
                    width="120"
                    align="center"
                    :formatter="dutyDateFormat"
                    label-class-name="header-style">
                  </el-table-column>
                  <el-table-column
                    label="值班周期"
                    align="center"
                    label-class-name="header-style"
                    width="150">
                    <template slot-scope="scope">
                      {{scope.row.startHour}}-{{scope.row.endHour}}
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="username"
                    label="值班人"
                    width="100"
                    align="center"
                    label-class-name="header-style">
                  </el-table-column>
                  <el-table-column
                    prop="phone"
                    label="联系电话"
                    min-width="120"
                    align="center"
                    label-class-name="header-style">
                  </el-table-column>
                  <el-table-column
                    prop="dutyJob"
                    label="值班职务"
                    align="center"
                    label-class-name="header-style">
                  </el-table-column>
                  <el-table-column
                    label="操作"
                    width="170"
                    align="center"
                    label-class-name="header-style">
                    <template slot-scope="scope">
                      <el-button type="primary" size="mini" @click="editDutyPerson(scope.row,scope.$index)">修改</el-button>
                      <el-button type="danger" size="mini" @click="delDutyPerson(scope.row,scope.$index)">删除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </el-col>
            </el-form>
          </el-col>
        </el-tab-pane>
      </el-tabs>
    </div>
    <!--新增值班人员对话框-->
    <el-dialog :title="dialogTitle" :visible.sync="addPerson">
      <el-form :model="personForm" :rules="personRules" ref="personForm" label-position="right" class="demo-ruleForm">
        <el-form-item label="值班日期:" label-width="120px" prop="dutyDate" style="margin-bottom: 10px">
          <el-date-picker
            v-model="personForm.dutyDate"
            type="date"
            placeholder="选择日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="值班周期" label-width="120px" prop="dutyCycleId" style="margin-bottom: 10px">
          <el-select v-model="personForm.dutyCycleId" placeholder="请选择">
            <el-option
              v-for="item in cycleOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="姓名:" label-width="120px" prop="name" style="margin-bottom: 10px">
          <el-select
            v-model="personForm.name"
            filterable
            remote
            reserve-keyword
            clearable
            placeholder="请输入姓名后选择"
            @change="handlePersonClick"
            :remote-method="remotePersonDuty"
            :loading="personLoading"
            style="width: 220px">
            <el-option
              v-for="item in personOptions"
              :key="item.value"
              :label="item.label"
              :value="item">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="手机长号:" label-width="120px" prop="phoneNumber" style="margin-bottom: 10px">
          <el-input v-model="personForm.phoneNumber" style="width: 220px"></el-input>
        </el-form-item>

        <el-form-item label="值班职务:" label-width="120px" prop="dutyJob" style="margin-bottom: 10px">
          <el-select clearable v-model="personForm.dutyJob" placeholder="请选择">
            <el-option label="值班领导" value="值班领导"></el-option>
            <el-option label="值班员" value="值班员"></el-option>
          </el-select>
          <!--<el-input v-model="personForm.dutyJob" style="width: 220px"></el-input>-->
        </el-form-item>
        <!--<el-form-item label="手机短号:" label-width="120px" prop="shortNumber" style="margin-bottom: 10px">-->
        <!--<el-input v-model="personForm.shortNumber" style="width: 400px"></el-input>-->
        <!--</el-form-item>-->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="addPerson = false">取 消</el-button>
        <el-button type="primary" @click="determineAddPerson">确 定</el-button>
      </div>
    </el-dialog>
    <!--新增值班人员对话框结束-->
  </div>
</template>

<script>
    export default {
        name: "EditEmerDuty",
      data() {
        return {
          activeTabName:'view',
          //--------------------查看数据---------------------------

          form:{
            id:'',//节假日ID
            title:'',//节假日类型
            year:'',//年份
            dutyDayCount:'',//值班天数
            dutyTypCode:''//节假日缩写，作为唯一识别码
          },
          //本公司的值班表
          dutyTable:[],

          //---------------------加人员对话框----------------------
          addPerson:false,//对话框flag
          dialogTitle:'添加值班人员',//对话框名字，新建或是修改
          personForm:{
            id:'',
            index:'',
            name:'',
            dutyDate:'',
            phoneNumber:'',
            dutyJob : '',
            shortNumber:'',
            dutyCycleId:''
          },
          personRules:{
            dutyDate:[
              { required: true, message: '请选择值班日期', trigger: 'change' }
            ],
            name: [
              { required: true, message: '请选择人员', trigger: 'change' }
            ]
          },
          cycleOptions:[],
          //查找人员的数据
          personLoading:false,
          personOptions:[],

          //--------------------------集团值班表-------------------------------------
          companyTreeData:[],
          companyTreeProps:{
            children: 'subDept',
            label: 'name'
          },
          checkedIds:[],//勾选公司ID列表
          conmpanyDutyTable:[],
          viewPageSize:10,
          viewCurrentPage:1,
          viewTotal:0,

          //--------------------------值班上报记录------------------------------------
          searchCompanyName:'',
          dutyRecordTable:[],
          editPageSize:10,
          editCurrentPage:1,
          editTotal:0,

          //-------------------------单独编辑页面---------------------------------
          routePage:'view',//view为查看所有信息页面，edit为单独编辑页面
          editTabName:'edit',
        }
      },
      mounted:function () {
        if(this.$route.params.activeTabName){
          this.routePage=this.$route.params.activeTabName;
          this.activeTabName='view';
          this.form.id=this.$route.params.id;
          this.form.title=this.$route.params.title;
          this.form.year=this.$route.params.year;
          this.form.dutyDayCount=this.$route.params.dutyDayCount;
          this.form.dutyTypCode=this.$route.params.dutyTypCode;

          this.getCompanyTree();
          this.cycleTabelFind();
        }
      },
      watch:{
        $route(to, from) {
          if (this.$route.name === 'editEmerDuty'&&from.name === 'emerDuty') {
            if(this.$route.params.activeTabName){
              this.routePage=this.$route.params.activeTabName;
              this.activeTabName='view';
              this.form.id=this.$route.params.id;
              this.form.title=this.$route.params.title;
              this.form.year=this.$route.params.year;
              this.form.dutyDayCount=this.$route.params.dutyDayCount;
              this.form.dutyTypCode=this.$route.params.dutyTypCode;

              this.getCompanyTree();
              this.cycleTabelFind();
            }
          }
        }
      },
      methods:{
        //-------------------------------切换页面---------------------------------
        tabClick:function(tab){
          if(tab.name==='view'){
            this.viewPageClick(1);
          }else if(tab.name==='record'){
            this.editPageClick(1);
          }else{

          }
        },
        //-------------------------------集团值班表-------------------------------
        //获取公司结构树
        getCompanyTree:function(){
          this.$http.get('dept/getAllCompany').then(function (res) {
            if(res.data.success){
              this.companyTreeData.splice(0);
              this.companyTreeData=res.data.data;
              //获取本公司值班列表
              this.getCurrentCompanyDutyTable();
              //获取全部公司的ID
              this.checkedIds=[this.$tool.getStorage('LOGIN_USER').companyId];
              //勾选所有公司
              this.$refs.companyTree.setCheckedKeys(this.checkedIds);
              //查找所有公司值班列表
              this.viewPageClick(1);

            }
          }.bind(this)).catch(function (err) {
            console.log(err);
          });
        },
        //获取全部公司的ID
        getCompanyTreeIds:function(list){
          let ids=[];
          for(let i=0;i<list.length;i++){
            ids.push(list[i].id);
            if(list[i].subDept.length){
              ids=ids.concat(this.getCompanyTreeIds(list[i].subDept));
            }
          }
          return ids;
        },
        //勾选公司响应事件
        getCheckCompanyIds:function(){
          this.checkedIds=this.$refs.companyTree.getCheckedKeys();
          this.viewPageClick(1);
        },
        //获取勾选公司的值班列表
        getCompanyDutyTable:function(){
          let params = new URLSearchParams;
          params.append("dutyTypeCode",this.form.dutyTypCode);
          params.append("dutyYear",this.form.year);
          params.append("companyName","");
          params.append("companyIds",this.checkedIds);
          params.append("pageCurrent",this.viewCurrentPage);
          params.append("pageSize",this.viewPageSize);
          this.$http.post('sysEmgDuty/getGroupDuty', params).then(function (res) {
            if (res.data.success) {
              this.conmpanyDutyTable.splice(0);
              if(res.data.data.list.length){
                this.conmpanyDutyTable=res.data.data.list;
                this.viewTotal=res.data.data.total;
              }
            }
          }.bind(this)).catch(function (err) {
            console.log(err);
          }.bind(this));
        },

        //翻页，获取勾选公司的值班列表
        viewPageClick:function(page){
          this.viewCurrentPage=page;
          this.getCompanyDutyTable();
        },
        //-------------------------------值班记录------------------------------
        //获取本级和本级以下公司的值班记录表
        getDutyRecordTable:function(){
          let params = new URLSearchParams;
          params.append("dutyTypeCode",this.form.dutyTypCode);
          params.append("dutyYear",this.form.year);
          params.append("companyName",this.searchCompanyName);
          params.append("pageCurrent",this.editCurrentPage);
          params.append("pageSize",this.editPageSize);
          this.$http.post('sysEmgDuty/getCompanyDuty', params).then(function (res) {
            if (res.data.success) {
              this.dutyRecordTable.splice(0);
              if(res.data.data.list.length){
                let tempList=res.data.data.list;
                for(let i=0;i<tempList.length;i++){
                  if(tempList[i].length===1){
                    let tempObj=tempList[i][0];
                    tempObj.rowFlag=1;
                    this.dutyRecordTable.push(tempObj);
                  }else{
                    tempList[i].forEach(function (item,index) {
                      item.rowFlag=index===0?tempList[i].length:0;
                      this.dutyRecordTable.push(item);
                    }.bind(this));
                  }
                }
                // this.dutyRecordTable=res.data.data.list;
                this.editTotal=res.data.data.total;
              }
            }
          }.bind(this)).catch(function (err) {
            console.log(err);
          }.bind(this));
        },
        //翻页，获取本级和本级以下公司的值班记录表
        editPageClick:function(page){
          this.editCurrentPage=page;
          this.getDutyRecordTable();
        },
        //表格行合并
        objectSpanMethod({ row, column, rowIndex, columnIndex }) {
          if (columnIndex === 5||columnIndex === 6||columnIndex === 7||columnIndex === 8) {
            let rowNum=row.rowFlag;
            let colNum=rowNum>0?1:0;
            return {
              rowspan: rowNum,
              colspan: colNum
            };
          }
        },

        //-------------------------------表格功能------------------------------
        //获取值班周期Options
        cycleTabelFind:function(){
          this.cycleOptions.splice(0);
          let params=new URLSearchParams;
          params.append("pageSize",50);
          this.$http.post('sysEmgDutyCycle/find ',params).then(function (res) {
            if(res.data.success){
              res.data.data.list.forEach(function (item) {
                this.cycleOptions.push({value:item.id,label:item.startHour+'-'+item.endHour});
              }.bind(this));
            }
          }.bind(this)).catch(function (err) {
            console.log('sysEmgDutyCycle/find ',err);
            this.$message.error('请尝试重登录或检查网络连接');
          }.bind(this));
        },
        //获取本公司值班列表
        getCurrentCompanyDutyTable:function(){
          let params = new URLSearchParams;
          params.append("companyId",this.$tool.getStorage('LOGIN_USER').companyId);
          params.append("dutyTypeCode",this.form.dutyTypCode);
          params.append("year",this.form.year);
          this.$http.post('sysEmgDuty/getDutyByType', params).then(function (res) {
            if (res.data.success) {
              this.dutyTable.splice(0);
              this.dutyTable=res.data.data;
            }
          }.bind(this)).catch(function (err) {
            console.log(err);
          }.bind(this));
        },
        //修改人员
        editDutyPerson:function (row,index) {
          this.dialogTitle='修改值班人员';
          this.personForm.name={value:row.userId,label:row.username};
          this.personOptions=[];
          this.personOptions.push({value:row.userId,label:row.username});
          this.personForm.dutyDate=row.dutyDate;
          this.personForm.phoneNumber=row.phone;
          this.personForm.dutyJob=row.dutyJob;
          this.personForm.dutyCycleId=row.dutyCycleId;
          this.personForm.id=row.id;
          this.personForm.index=index;
          this.addPerson=true;
        },
        //删除人员
        delDutyPerson:function (row,index) {
          this.$confirm('此操作将删除该记录, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.$http.get('sysEmgDuty/delete/'+row.id).then(function (res) {
              if (res.data.success) {
                this.dutyTable.splice(index, 1);
              }
            }.bind(this)).catch(function (err) {
              console.log(err);
            }.bind(this));
          }).catch(() => {
            this.$message({
              type: 'info',
              message: '已取消删除'
            });
          });

        },
        //值班日期转化
        dutyDateFormat:function (row) {
          return this.transferTime(row.dutyDate);
        },
        //上报时间转化
        writeTimeFormat:function (row) {
          return this.transferTime(row.writeTime,null,true);
        },
        //-------------------------------对话框中的人员选择-----------------------
        //搜索人员
        remotePersonDuty:function (val) {
          this.personLoading = true;
          this.$http.get('user/find?username='+val+'&companyId='+this.$tool.getStorage('LOGIN_USER').companyId).then(function (res) {
            if(res.data.success){
              this.personOptions=[];
              for (let i = 0; i < res.data.data.list.length; i++) {
                this.personOptions.push({value:res.data.data.list[i].userId,label:res.data.data.list[i].username,phone:res.data.data.list[i].mobile,shortPhone:res.data.data.list[i].shortPhone});
              }
              this.personLoading = false;
            }
          }.bind(this)).catch(function (err) {
            console.log(err);
          });
        },
        handlePersonClick:function (val) {
          this.personForm.phoneNumber=val.phone;
//        this.personForm.shortNumber=val.shortPhone;
        },
        //-----------------------------添加,修改人员---------------------------------------
        determineAddPerson:function () {
          this.$refs['personForm'].validate((valid) => {
            if (valid) {
              let params = new URLSearchParams;
              if(typeof (this.personForm.dutyDate)==='number'){
                let tempDate=new Date(this.personForm.dutyDate);
                params.append("dutyDate", tempDate);
              }else{
                params.append("dutyDate", this.personForm.dutyDate);
              }
              params.append("dutyTypeCode",this.form.dutyTypCode);//节假日标识码
              params.append("companyId", this.$tool.getStorage('LOGIN_USER').companyId);
              params.append("companyName", this.$tool.getStorage('LOGIN_USER').companyName);
              params.append("username",this.personForm.name.label);
              params.append("userId", this.personForm.name.value);
              params.append("phone", this.personForm.phoneNumber);
              params.append("dutyJob", this.personForm.dutyJob);
              params.append("dutyCycleId",this.personForm.dutyCycleId);
//            params.append("shortPhone",this.personForm.shortNumber);
              if(this.dialogTitle==='添加值班人员'){
                this.$http.post('sysEmgDuty/add', params).then(function (res) {
                  if (res.data.success) {
                    this.getCurrentCompanyDutyTable();
                    this.$refs['personForm'].resetFields();
                    this.addPerson = false;
                  }else{
                    this.addPerson = false;
                  }
                }.bind(this)).catch(function (err) {
                  console.log(err);
                }.bind(this));
              }else{//修改值班人员
                params.append("id", this.personForm.id);
                this.$http.post('sysEmgDuty/update', params).then(function (res) {
                  if (res.data.success) {
                    this.getCurrentCompanyDutyTable();
                    this.$refs['personForm'].resetFields();
                    this.addPerson = false;
                  }else{
                    this.addPerson = false;
                  }
                }.bind(this)).catch(function (err) {
                  console.log(err);
                }.bind(this));
              }
            } else {
              console.log('error submit!!');
              return false;
            }
          });
        },


      }

    }
</script>

<style scoped>

</style>
