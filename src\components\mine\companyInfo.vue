<template>
  <div id="trainingPlanIndex">
    <div class="background-style">
      <!--搜索区-->
      <div class="search-bar">
        <el-row>
          <el-col :span="2">
            <el-button
              @click="addBtnClickHandle"
              type="success"
              style="margin-left: 20px"
              >新增</el-button
            >
          </el-col>
        </el-row>
      </div>
      <!--表格区-->
      <div style="width: 100%">
        <div style="padding: 20px 10px 20px 10px">
          <el-table
            border
            @row-click="rowclick"
            :data="tableData.list"
            style="width: 100%"
          >
            <el-table-column
              type="index"
              label="编号"
              width="50"
              align="center"
              label-class-name="header-style"
            >
            </el-table-column>
            <el-table-column
              prop="entName"
              align="center"
              width="180"
              label="企业名称"
              label-class-name="header-style"
            >
            </el-table-column>
            <el-table-column
              prop="entNumber"
              width="70"
              align="center"
              label="人数"
              show-overflow-tooltip
              label-class-name="header-style"
            >
            </el-table-column>
            <el-table-column
              prop="entAddress"
              label="地址"
              show-overflow-tooltip
              label-class-name="header-style"
            />
            <el-table-column
              prop="entDirectorTel"
              label="主要负责人手机"
              align="center"
              show-overflow-tooltip
              label-class-name="header-style"
            />

            <el-table-column
              prop="entDirectorName"
              align="center"
              label="主要负责人姓名"
              label-class-name="header-style"
            />
            <el-table-column
              prop="safeDirectorTel"
              align="center"
              label="安全负责人姓名"
              label-class-name="header-style"
            />
            <el-table-column
              prop="undertakePartName"
              width="180"
              align="center"
              label="上一级企业名称"
              label-class-name="header-style"
            />
            <el-table-column
              label="操作"
              label-class-name="header-style"
              align="left"
              width="160"
            >
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="warning"
                  @click="itemEditClick(scope.row)"
                  >修改</el-button
                >
                <el-button
                  size="mini"
                  type="danger"
                  @click="itemDeleteClick(scope.row)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div style="margin-top: 10px">
          <el-pagination
            background
            layout="prev, pager, next"
            :current-page="tableData.pageNum"
            :page-size="form.pageSize"
            :total="tableData.total"
            @current-change="disasterPageChangeHandle"
          >
          </el-pagination>
        </div>
      </div>
      <!--新增对话框-->
      <el-dialog
        title="对话框"
        :visible.sync="dialog.isShow"
        width="70%"
        :destroy-on-close="true"
      >
        <el-form label-width="120px" :model="dialog.form">
          <el-row class="row">
            <el-col :span="12">
              <el-form-item label="企业名称" prop="entName">
                <el-input clearable v-model="dialog.form.entName"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="人数" prop="entNumber">
                <el-input clearable v-model="dialog.form.entNumber"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row class="row">
            <el-col :span="12">
              <el-form-item label="地址" prop="entAddress">
                <el-input clearable v-model="dialog.form.entAddress"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="主要负责人手机" prop="entDirectorTel">
                <el-input
                  clearable
                  v-model="dialog.form.entDirectorTel"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row class="row">
            <el-col :span="12">
              <el-form-item label="主要负责人姓名" prop="entDirectorName">
                <el-input
                  clearable
                  v-model="dialog.form.entDirectorName"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="安全负责人姓名" prop="safeDirectorTel">
                <el-input
                  clearable
                  v-model="dialog.form.safeDirectorTel"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row class="row">
            <el-col :span="12">
              <el-form-item label="上一级企业名称" prop="undertakePartName">
                <el-input
                  clearable
                  v-model="dialog.form.undertakePartName"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="danger" size="mini" @click="dialogOkBtnClickHandle"
            >确定</el-button
          >
        </div>
      </el-dialog>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      form: {
        // 标题
        name: "",
        // 当前页
        pageCurrent: 1,
        // 页数大小
        pageSize: 10,
      },
      tableData: {},
      // 专题类型
      fileType: [],
      // 对话框
      dialog: {
        // 是否显示
        isShow: false,
        form: {
          id: "",
          entName: "",
          entNumber: "",
          entAddress: "",
          entDirectorTel: "",
          entDirectorName: "",
          safeDirectorTel: "",
          undertakePartName: "",
          sort: "",
        },
      },
    };
  },
  activated() {
    this.init();
  },
  methods: {
    // 初始化
    init() {
      // 搜索
      this.searchBtnClickHandle();
    },
    // 格式化时间
    formatDateTime(row, column, cellValue) {
      let pro = column.property;
      let num = 10;
      // 年份4位 1999
      if (pro === "createYear") num = 4;
      let str = this.$tool.formatDateTime(row[pro] || 0);
      return str ? str.substring(0, num) : str;
    },
    // 分页
    disasterPageChangeHandle(page) {
      this.form.pageCurrent = page;
      this.searchBtnClickHandle();
    },
    // 搜索按钮
    searchBtnClickHandle() {
      let params = {};
      this.$http.post("/sys/sysMine/findEntInfo", params).then((res) => {
        if (res.data.success) {
          this.tableData = res.data.data;
        } else {
          this.$message({
            type: "error",
            message: res.message || "错误",
          });
        }
      });
    },
    // 删除按钮
    itemDeleteClick(row) {
      this.$confirm("此操作将永久删除, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.$http
          .post("/sys/sysMine/delEntInfo", { id: row.id })
          .then((res) => {
            if (res.data.success) {
              this.$message({
                type: "success",
                message: "删除成功",
              });
              this.searchBtnClickHandle();
            } else {
              this.$message({
                type: "error",
                message: res.message || "删除失败！！",
              });
            }
          });
      });
    },
    rowclick(row) {
      console.log(row);
    },
    // 修改按钮
    itemEditClick(row) {
      this.dialog.form = row;
      this.dialog.isShow = true;
    },
    // 添加按钮
    addBtnClickHandle() {
      this.dialog.form = {
        id: "",
        entName: "",
        entNumber: "",
        entAddress: "",
        entDirectorTel: "",
        entDirectorName: "",
        safeDirectorTel: "",
        undertakePartName: "",
        sort: "",
      };
      this.dialog.isShow = true;
    },
    // 对话框---确定按钮
    dialogOkBtnClickHandle() {
      let params = this.dialog.form;
      this.$http.post("/sys/sysMine/addOrUpdateEntInfo", params).then((res) => {
        if (res.data.success) {
          this.$message({
            type: "success",
            message: "操作成功",
          });
          this.dialog.isShow = false;
          this.searchBtnClickHandle();
        } else {
          this.$message({
            type: "error",
            message: res.message || "错误",
          });
        }
      });
    },
  },
};
</script>
<style>
</style>
