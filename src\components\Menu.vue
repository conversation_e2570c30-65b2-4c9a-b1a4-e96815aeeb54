<template>
  <div id="menuPage">
    <safeHeader></safeHeader>

    <main class="menu-main">
      <div class="menu-img">
        <div class="top-box" style="overflow: hidden">
          <div class="left-box">
            <div class="left-top-box">
              <Title title="岗位目标" />
              <div v-for="(item, index) in jobList" :key="index" class="left-top-Purpose box-s">
                <img src="../static/icon/icon_item.png" style="margin: 0 20px" />
                <span>{{ item.text }}</span>
                <div @click="() => item.func(item.params)" style="
                    cursor: pointer;
                    display: flex;
                    position: absolute;
                    right: 20px;
                    align-items: center;
                  ">
                  <span style="
                      margin-right: 10px;
                      font-size: 14px;
                      line-height: 0;
                      color: #666;
                    ">查看</span>
                  <div class="triangle-right"></div>
                </div>
              </div>
            </div>
            <div class="left-bot-box">
              <Title title="安全专题" />
              <div class="card-box2 box-s" style="background: #ffffff">
                <Safecard></Safecard>
              </div>
            </div>
          </div>
          <div class="middle-box">
            <div class="middle-top-box">
              <Title title="待办事项" />
              <div class="card-box box-s">
                <div style="margin-top: 10px; height: 330px">
                  <div class="card-box-img" v-if="taskList.length == 0">
                    <img src="./image/nodata.png" style="width: 50%" />
                  </div>
                  <div class="middle-top-work" v-for="(taskItem, index) in taskList" :key="index">
                    <div style="margin-top: 10px">
                      <div class="middle-div-work">
                        <span :style="{
                          color: '#3197f9',
                        }" class="margin-title">{{ taskItem.taskType }}</span>
                        <span class="margin-createTime">{{
                          taskItem.createTime
                        }}</span>
                      </div>
                      <el-tooltip :content="taskItem.name" placement="top-start">
                        <div class="middle-top-title" @click="taskClick(taskItem)">
                          {{ truncatedString(taskItem.name) }}
                        </div>
                      </el-tooltip>
                    </div>
                    <div style="display: flex">
                      <div v-for="item in 30" :key="item">
                        <div class="middle-top-border"></div>
                      </div>
                    </div>
                  </div>
                </div>
                <div @click="$router.push({ name: 'taskNotice' })" class="middle-top-view" v-if="taskList.length !== 0">
                  <span style="font-size: 16px;">查看更多</span>
                  <div class="triangle-right1"></div>
                </div>
              </div>
            </div>
            <div class="middle-bot-box">
              <Title title="工作绩效" />
              <div class="card-box2">
                <div class="middle-bot-performance box-s">
                  <div>
                    <span class="middle-bot-performance-title">培训学时</span>
                    <span class="text-box" style="color: #3396fb">
                      <span class="text-box-1">{{
                        this.educationData.done
                      }}</span>学时
                    </span>
                  </div>
                  <el-progress id="histogram01" :percentage="studyPercentage" :stroke-width="20"></el-progress>
                </div>
                <div class="middle-bot-performance box-s">
                  <div>
                    <span class="middle-bot-performance-title">自查次数</span>
                    <span class="text-box text-box-2" style="color: rgb(20, 192, 80)"><span class="text-box-1">{{ inspectData.done }}</span>次</span>
                  </div>
                  <el-progress class="elProgress" :percentage="inspectPercentage" :stroke-width="20"></el-progress>
                </div>
                <div class="middle-bot-performance box-s">
                  <div>
                    <span class="middle-bot-performance-title">安全积分</span>
                    <span class="text-box text-box-color3" style="color: rgb(255, 141, 0)"><span class="text-box-1">{{
                      this.safetyScoreData.done
                    }}</span>分</span>
                  </div>
                  <el-progress class="elProgress2" :percentage="scorePercentage" :stroke-width="20"></el-progress>
                </div>
              </div>
            </div>
          </div>
          <div class="right-box">
            <Title title="通知要求" />
            <div class="card-box3">
              <div class="card-box3-div-img" v-if="noticeList.length == 0">
                <img src="./image/nodata.png" style="width: 50%" />
              </div>
              <div class="card-box3-div">
                <div class="right-box-notice box-s" v-for="(taskItem, index) in noticeList" :key="index">
                  <div class="card-box3-sdiv">
                    <div style="display: flex; justify-content: space-between;">
                      <p :style="{ color: taskItem.color }" class="margin-title">
                      {{ taskItem.idType }}
                    </p>
                      <p class="margin-createTime">{{ taskItem.createTime }}</p>
                    </div>


                    <el-tooltip :content="taskItem.title" placement="top-start">
                      <div class="middle-top-title" @click="noticeClick(taskItem, index)">
                        {{ truncatedString(taskItem.title) }}
                      </div>
                    </el-tooltip>
                  </div>
                  <!-- <div style="display: flex;margin:-10px 0 0 10px">
                    <div v-for="item in 30" :key="item">
                      <div class="middle-top-border"></div>
                    </div>
                  </div> -->
                </div>
              </div>
              <div @click="
                $router.push({
                  name: 'workflowNotifyIndex',
                  params: {
                    taskFlag: true,
                    componentName: 'workflowNotifyIndex',
                  },
                })
                " class="right-box-view box-s" v-if="noticeList.length !== 0" style="font-size: 16px;">
                查看更多
                <div class="triangle-right1"></div>
              </div>

            </div>
          </div>
        </div>

        <div class="bot-box">
          <Title title="常用模块" />
          <div class="card-box4 box-s">
            <div style="display: flex">
              <router-link :key="item.id" :to="item.url" v-for="item in mainMenu">
                <div class="card-box4-div">
                  <img alt="图片错误" :src="item.icon" style="display: block" />
                  <span>{{ item.permissionName }}</span>
                </div>
              </router-link>
            </div>
          </div>
        </div>

        <el-col :span="24" style="height: 50px; background-color: #3396fb">
          <div style="width: 1200px; margin: 0 auto; text-align: center">
            <span style="line-height: 50px; color: #fff; font-size: 14px">版权所有@宁波市交通建设工程试验检测中心有限公司
              <a href="https://beian.miit.gov.cn/" target="_blank" style="color: #ffffff;text-decoration: underline">浙ICP备12022514号-2</a>
            </span>
          </div>
        </el-col>
      </div>
    </main>

    <!--通知消息查看-->
    <el-dialog :title="notice.title" center append-to-body :visible.sync="noticeDialogVisible">
      <!--安全生产目标结束-->
      <el-dialog :title="dialog.title" :modal-append-to-body="false" width="100%" top="0vh" :center="true" :visible.sync="dialog.isShow">
      </el-dialog>

      <el-col :span="24">
        <span style="color: #8c939d">创建人：{{ notice.writeUserName }} | 部门：{{ notice.deptName }} |
          时间 {{ notice.createDate }}</span>
      </el-col>
      <el-col :span="24">
        <!-- <el-button v-if="notice.filePath" type="text" @click="showPdfDialog">{{
          notice.contentLink
        }}</el-button> -->
      </el-col>
      <el-col :span="24" style="margin-top: 20px; margin-bottom: 10px">
      </el-col>
      <span @click="toPdf(notice.filePath)" style="
          margin-right: 10px;
          font-size: 14px;
          line-height: 0;
          color: #3396fb;
          cursor: pointer;
        ">{{ notice.content }}</span>
      <!-- <el-link>{{notice.content}}</el-link> -->
      <div slot="footer" class="dialog-footer" style="margin-top: 10px">
        <el-button type="primary" @click="noticeDialogVisible = false">返回主页</el-button>
      </div>
    </el-dialog>
    <!--通知消息查看结束-->

    <!--安全生产目标-->
    <el-dialog :title="safeTitle" :visible.sync="safeGoalDialogVisible">
      <el-table :data="safeGoalList" border style="width: 100%">
        <el-table-column type="index" align="center" label-class-name="inner-header-style" width="50">
        </el-table-column>
        <el-table-column label="内容" prop="content" label-class-name="inner-header-style">
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="safeGoalDialogVisible = false">确 定</el-button>
      </div>
    </el-dialog>
    <!--安全生产目标结束-->

    <el-dialog :title="pdfDialogTitle" width="100%" top="0vh" :center="true" :visible.sync="viewSafeDangerDialogVisible">
      <iframe :src="pdfUrl + '#toolbar=0'" width="100%" height="810"></iframe>
    </el-dialog>
  </div>
</template>
<script>
import safeHeader from "@/components/common/header";
import { VueEditor } from "vue2-editor";
import Title from "./card/title.vue";
import Safecard from "./card/safecard.vue";
export default {
  name: "menuPage",
  data() {
    return {
      //培训学时的百分比值
      train: {
        total: 200,
        value: 50,
      },
      userName: "登陆",
      noticeCount: 0,
      mainMenu: [], //有菜单内容的菜单项
      blockList: [], //为了显得好看的空block
      // 岗位目标列表
      jobList: [
        { text: "安全生产承诺书", func: this.showPdf, params: 2 },
        { text: "安全风险告知书", func: this.showPdf, params: 1 },
        { text: "岗位责任清单", func: this.showSafeGoalClick, params: 0 },
        { text: "年度工作目标", func: this.showSafeGoalClick, params: 2 },
        { text: "年度工作任务", func: this.showSafeGoalClick, params: 1 },
      ],
      // blockList: [], //为了显得好看的空block
      //个人主页显示数据
      taskList: [],
      //检查类型
      inpectTypeArr: ["自查", "检查", "排查"],
      picData: [],
      noticeList: [],
      typeList: {
        101: "【文件】",
        102: "【活动】",
        103: "【会议】",
      },
      colorList: {
        101: "#3396fb",
        102: "#3197f9",
        103: "#3197f9",
      },
      //通知对话框
      notice: {
        title: "",
        content: "",
        contentLink: "",
        filePath: "",
        createDate: "",
        writeUserName: "",
        deptName: "",
        noticeType: "",
        relateId: "",
      },
      noticeDialogVisible: false,

      // pdf查看对话框
      dialog: {
        //查看安全风险告知书
        isShow: false,
        pdfUrl: "http://www.safe360.vip/safeFile.pdf",
        title: "查看安全风险告知书",
      },

      //安全生产目标对话框
      safeGoalDialogVisible: false,
      safeGoalList: [],

      //任务数量统计
      dangerTaskNum: 0,
      emreTaskNum: 0,
      safetyFileNum: 0,
      editEmerTaskNum: 0,
      demandTaskNum: 0,
      holidaysTaskNum: 0,
      //图表数据
      //        safetyScoreData: {done:90,sum:100},
      safetyScoreData: { done: 90, sum: 100 },
      inspectData: { done: 50, sum: 100 },
      educationData: { done: 80, sum: 100 },

      //查看安全风险告知书
      viewSafeDangerDialogVisible: false,
      pdfUrl: "http://www.safe360.vip/safeFile.pdf",
      pdfDialogTitle: "查看安全风险告知书",
      safeTitle: "安全工作目标",

      //各个公司安全员信息
      safePersonTitle: "今日日期",
      safePersonName: "",
      safePersonContent: "",
      weekArray: ["周日", "周一", "周二", "周三", "周四", "周五", "周六"],
      safePersonList: {
        1: { name: "朱海燕", phone: "13605885947", shortphone: "645947" },
        2: { name: "李喜燕", phone: "13906619041", shortphone: "" },
      },
    };
  },
  components: {
    safeHeader,
    VueEditor,
    Title,
    Safecard,
  },
  computed: {
    //培训学时的百分比值
    studyPercentage() {
      return Math.round(
        (this.educationData.done / this.educationData.sum) * 100
      );
    },
    //自查次数的百分比值
    inspectPercentage() {
      return Math.round((this.inspectData.done / this.inspectData.sum) * 100);
    },
    //安全积分的百分比值
    scorePercentage() {
      return Math.round(
        (this.safetyScoreData.done / this.safetyScoreData.sum) * 100
      );
    },
  },
  created: function () {
    // console.log()
    if(window.location.hash.replace('#/menu?','')){
      
    }
    if (localStorage.SAFE_PLATFORM_USERNAME) {
      this.userName = localStorage.SAFE_PLATFORM_USERNAME;
      this.noticeCount = localStorage.NOTICE_COUNT;
    }
    this.searchHolidays();
    this.searchDangerTask();
    // this.$nextTick(
    //   function () {
    //     //按菜单个数，规定最低高度，以保证底部条永远在底部
    //     if (this.$tool.getStorage("SAFE_PLATFORM_MENU").mainMenu.length > 6) {
    //       this.$refs.mainContent.style.minHeight = "890px";
    //     } else {
    //       this.$refs.mainContent.style.minHeight = "740px";
    //     }
    //   }.bind(this)
    // );
  },
  mounted: function () {
    if (document.body.clientWidth < 1600) {
      document.getElementById('menuPage').style.zoom = 0.85
    }
    console.log(document.getElementById('menuPage').style.zoom)
    this.searchNoticeList();
    this.mainMenu = this.$tool
      .getStorage("SAFE_PLATFORM_MENU")
      .mainMenu.map((i) => {
        if (i.permissionName == "应急救援") {
          i.permissionName = "应急管理";
        } else if (i.permissionName == "公司台账") {
          i.permissionName = "台账管理";
        }
        return i;
      });
    const img1 = require("./../static/imgs/icon_cy_01.png");
    const img2 = require("./../static/imgs/icon_cy_02.png");
    const img3 = require("./../static/imgs/icon_cy_03.png");
    const img4 = require("./../static/imgs/icon_cy_04.png");
    const img5 = require("./../static/imgs/icon_cy_05.png");
    const img6 = require("./../static/imgs/icon_cy_06.png");
    this.mainMenu = this.mainMenu.map((item) => {
      if (item.permissionName == "安全投入") {
        item.icon = img1;
      } else if (item.permissionName == "教育培训") {
        item.icon = img2;
      } else if (item.permissionName == "隐患排查") {
        item.icon = img3;
      } else if (item.permissionName == "应急管理") {
        item.icon = img4;
      } else if (item.permissionName == "台账管理") {
        item.icon = img5;
      } else if (item.permissionName == "系统管理") {
        item.icon = img6;
      }
      return item;
    });
    // this.taskList = [];
    let f1 = JSON.parse(window.localStorage.getItem("inspectData"));
    if (f1 != null) {
      this.inspectData = f1;
      this.showEduTimeScore();
    }
    this.searchSafetyFileNum();

    // this.searchEmerTask();
    // this.searchDemandTask();
    this.searchNoticeList();
    this.getEditEmerTaskNum();
    this.getSelfInspectNum();
    this.getSafePerson();
    //标题条视图按钮
    this.$store.dispatch("changeViewFlag", {
      menuFlag: false,
      managerViewFlag: true,
      gisFlag: true,
    });
  },
  watch: {
    $route(to, from) {
      if (this.$route.name === "menu") {
        this.noticeCount = localStorage.NOTICE_COUNT;
        // this.taskList = [];
        this.searchDangerTask();
        this.searchSafetyFileNum();
        // this.searchEmerTask();
        //this.searchNoticeList();
        this.getEditEmerTaskNum();
        this.getSelfInspectNum();
        //          this.drawHistogram();//1129刘杰增加
        //标题条视图按钮
        this.$store.dispatch("changeViewFlag", {
          menuFlag: false,
          managerViewFlag: true,
          gisFlag: true,
        });
      }
    },
  },
  methods: {
    // 获取培训学时和安全积分的接口
    showEduTimeScore() {
      /*
          * safetyScoreData: {done:90,sum:100},
           inspectData: {done:50,sum:100},
           educationData: {done:80,sum:100},
          * */
      this.$store.dispatch("eduUserStudyGetUserStudyProgress", {}).then(
        function (res) {
          if (res.success) {
            //            this.tableData = res.data;
            this.safetyScoreData.sum = res.data.totalIntegral || 100;
            this.safetyScoreData.done = res.data.userIntegral || 0;
            this.educationData.sum = res.data.totalCourseTime || 100;
            this.educationData.done = res.data.userCourseTime || 0;

            // this.drawHistogram();
          } else {
            this.$message({
              type: "error",
              message: res.message || "错误",
            });
          }
        }.bind(this)
      );
    },

    // 文章查看
    showPdfDialog: function () {
      //        this.dialog.title = this.info.name;
      this.dialog.pdfUrl = this.notice.filePath;
      this.dialog.title = this.notice.contentLink;
      this.dialog.isShow = true;
    },

    showPdf: function (type) {
      let posts = this.$tool.getStorage("LOGIN_USER").posts;
      if (posts.length) {
        if (type === 1) {
          this.$http
            .post(
              "postResponsible/findFile?postId=" +
              posts[0].postId +
              "&responsibleType=3"
            )
            .then(
              function (res) {
                if (res.data.success) {
                  if (res.data.data.length) {
                    this.pdfDialogTitle = "安全风险告知书";
                    this.pdfUrl = res.data.data[0].filePath;
                    this.viewSafeDangerDialogVisible = true;
                  } else {
                    this.$message.info("没有对应文件");
                  }
                }
              }.bind(this)
            )
            .catch(
              function (err) {
                this.$message.info("查找失败，请检查网络");
              }.bind(this)
            );
        } else {
          this.$http
            .post(
              "postResponsible/findFile?postId=" +
              posts[0].postId +
              "&responsibleType=4"
            )
            .then(
              function (res) {
                if (res.data.success) {
                  if (res.data.data.length) {
                    this.pdfDialogTitle = "安全生产承诺书";
                    this.pdfUrl = res.data.data[0].filePath;
                    this.viewSafeDangerDialogVisible = true;
                  } else {
                    this.$message.info("没有对应文件");
                  }
                }
              }.bind(this)
            )
            .catch(
              function (err) {
                this.$message.info("查找失败，请检查网络");
              }.bind(this)
            );
        }
      } else {
        this.$message.info("您还没有岗位设置");
      }
    },
    outClick: function () {
      this.$router.push("/");
    },
    myNotice: function () {
      this.$router.push("/my-notice");
    },
    searchSafetyFileNum: function () {
      let params = {};
      params["pageCurrent"] = 1;
      params["pageSize"] = 10;
      this.$http
        .post("sys/sysManageFile/find", params)
        .then(
          function (res) {
            if (res.data.success) {
              this.safetyFileNum = res.data.data.total;
            } else {
              this.safetyFileNum = 0;
            }
          }.bind(this)
        )
        .catch(function (err) { });
    },
    //时间戳转换
    formattedtime: function (params) {
      const timestamp = params;
      const date = new Date(timestamp);
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      const day = date.getDate();
      const formattedDate = `${year}-${month < 10 ? "0" + month : month}-${day < 10 ? "0" + day : day
        }`;
      return formattedDate;
    },
    //holidays值班
    searchHolidays() {
      this.$http.post("/sysDuty/findUndoEmgAndDuty").then((res) => {
        // this.holidaysTaskNum= res.data.data.page.total;
        const list = res.data.data.list;
        list.forEach((i, index) => {
          let j = {}; // 在每次循环开始时创建一个新对象
          j.taskId = i.id;
          j.taskType = i.bizType == 1 ? "【节假日值班】" : "【应急值班】";
          j.name = i.dutyCodeName;
          j.createTime = this.formattedtime(i.dutyDate);
          // i.operateId =
          // i.statusName =
          if (index < 5) {
            this.taskList.push(j);
          }
        });
        this.taskList.sort((a, b) => {
          const createTimeA = new Date(a.createTime);
          const createTimeB = new Date(b.createTime);
          if (createTimeA < createTimeB) {
            return 1;
          } else if (createTimeA > createTimeB) {
            return -1;
          } else {
            return 0;
          }
        });
      });
    },
    //应急值班
    //任务的搜索和点击
    searchDangerTask: function () {
      this.$http
        .get("dangerFlow/getUndoTask?pageCurrent=1&pageSize=5")
        .then(
          function (res) {
            if (res.data.success) {
              this.dangerTaskNum = res.data.data.page.total;
              let list = res.data.data.data;
              for (let i = 0; i < list.length; i++) {
                let tempObj = list[i].dangerInspectPublic
                  ? list[i].dangerInspectPublic
                  : {};
                tempObj.taskId = list[i].id;
                tempObj.statusName = list[i].name;
                if (list[i].extActNode) {
                  tempObj.statusColor = list[i].extActNode.statusColor;
                  tempObj.operateUrl = list[i].extActNode.url;
                  let strTOJson = JSON.parse(list[i].extActNode.config);
                  tempObj.nodeData = strTOJson.types[tempObj.type];
                  tempObj.currentStatus = list[i].extActNode.status;
                }
                tempObj.processInstanceId = list[i].processInstanceId;
                tempObj.operateId = list[i].userId;
                tempObj.subProcessStartUserId = list[i].subProcessStartUserId;
                tempObj.hasReassign = list[i].hasReassign;
                if (tempObj.hasReassign) {
                  tempObj.fromUserId = list[i].extActTaskReassign.fromUserId;
                  tempObj.ressignNodeId = list[i].extActTaskReassign.id;
                }

                tempObj.typeName = this.inpectTypeArr[tempObj.type];
                tempObj.taskType = "【隐患整改】";
                tempObj.createTime = this.formattedtime(list[i].createTime);
                if (this.taskList.length < 5) {
                  this.taskList.push(tempObj);
                }
                this.taskList.sort((a, b) => {
                  const createTimeA = new Date(a.createTime);
                  const createTimeB = new Date(b.createTime);
                  if (createTimeA < createTimeB) {
                    return 1;
                  } else if (createTimeA > createTimeB) {
                    return -1;
                  } else {
                    return 0;
                  }
                });
              }
            } else {
              this.dangerTaskNum = 0;
            }
          }.bind(this)
        )
        .catch(function (err) { });
    },
    // searchEmerTask: function () {
    //   this.$http
    //     .post("emgFlow/getMyTask?pageCurrent=1&pageSize=5")
    //     .then(
    //       function (res) {
    //         if (res.data.success) {
    //           this.emreTaskNum = res.data.data.page.total;
    //           let list = res.data.data.data;
    //           for (let i = 0; i < list.length; i++) {
    //             let tempObj = list[i].emgPlanPublic
    //               ? list[i].emgPlanPublic
    //               : {};
    //             tempObj.taskId = list[i].id;
    //             tempObj.statusName = list[i].name;
    //             if (list[i].extActNode) {
    //               tempObj.statusColor = list[i].extActNode.statusColor;
    //               tempObj.operateUrl = list[i].extActNode.url;
    //             }
    //             tempObj.processInstanceId = list[i].processInstanceId;
    //             tempObj.operateId = list[i].userId;

    //             tempObj.taskType = "【应急】";
    //             tempObj.createTime = this.formattedtime(list[i].createTime);
    //             if (this.taskList.length < 5) {
    //               this.taskList.push(tempObj);
    //             }
    //           }
    //         } else {
    //           this.emreTaskNum = 0;
    //         }
    //       }.bind(this)
    //     )
    //     .catch(function (err) {});
    // },

    searchDemandTask: function () {
      let params = {};
      params["pageCurrent"] = 1;
      params["pageSize"] = 5;
      params["userId"] = this.$tool.getStorage("LOGIN_USER").userId;
      params["status"] = 0;
      this.$store
        .dispatch("eduReqUserRltFind", params)
        .then(
          function (res) {
            if (res.success) {
              this.demandTaskNum = res.total;
              let list = res.data.list;
              for (let i = 0; i < list.length; i++) {
                let tempObj = list[i];
                tempObj.taskId = list[i].id;
                tempObj.operateUrl = "demandSurveyStaffAdd";
                // tempObj.operateUrl="demandSurveyStaffIndex";
                tempObj.operateId = list[i].id;
                tempObj.name = list[i].eduRequirementInvestigation.title;
                tempObj.taskType = "【需求】";
                tempObj.createTime = this.formattedtime(list[i].createTime);
                if (this.taskList.length < 5) {
                  this.taskList.push(tempObj);
                }
              }

            } else {
              this.emreTaskNum = 0;
            }
          }.bind(this)
        )
        .catch(function (err) { });
    },
    taskClick: function (rowData) {
      if (rowData.taskType === "【隐患整改】") {
        if (rowData.operateUrl) {
          this.$router.push({
            name: rowData.operateUrl,
            params: {
              dangerData: rowData,
              taskFlag: true,
              componentName: "hideDangerWorkflow",
            },
          });
        } else {
          this.$message.warning("节点数据获取失败");
        }
      } else if (rowData.taskType === "【应急值班】") {
        this.$router.push({
          name: "emerDuty",
          params: {
            taskFlag: true,
            componentName: "emerDuty",
          },
        });
      } else {
        //需求类型
        this.$router.push({
          name: "holidayDuty",
          params: {
            taskFlag: true,
            componentName: "holidayDuty",
          },
        });
      }
    },
    //获取记录应急数量
    getEditEmerTaskNum: function () {
      let params = new URLSearchParams();
      //不是被调整过的应急
      params.append("history", 0);
      //上级发布的应急列表
      params.append(
        "companyId",
        this.$tool.getStorage("LOGIN_USER").parentCompanyId
      );
      params.append("status", 3); //待写工作简报
      this.$http
        .post("planPublic/findMotherCompanyEmg", params)
        .then(
          function (res) {
            if (res.data.success) {
              this.editEmerTaskNum = res.data.data.total;
              let params = new URLSearchParams();
              //不是被调整过的应急
              params.append("history", 0);
              //上级发布的应急列表
              params.append(
                "companyId",
                this.$tool.getStorage("LOGIN_USER").parentCompanyId
              );
              params.append("status", 11); //待写工作总结
              this.$http
                .post("planPublic/findMotherCompanyEmg", params)
                .then(
                  function (res) {
                    if (res.data.success) {
                      this.editEmerTaskNum += res.data.data.total;
                    }
                  }.bind(this)
                )
                .catch(function (err) { }.bind(this));
            } else {
              this.editEmerTaskNum = 0;
            }
          }.bind(this)
        )
        .catch(function (err) { }.bind(this));
    },

    //通知的搜索
    searchNoticeList: function () {
      this.$http
        .post("/sys/notice/findFileNotice", {
          pageCurrent: 1,
          pageSize: 8,
          idType: "",
        })
        .then((res) => {
          if (res.data.success) {
            // this.noticeList = res.data.data.list;
            const list = res.data.data.list;
            // console.log(list);
            this.noticeList = list.map((item) => {
              return {
                title: item.title,
                createTime: this.formattedtime(item.createTime),
                idType: this.typeList[item.idType],
                color: this.colorList[item.idType],
                writeUserName: item.writeUserName,
                deptName: item.deptName,
                content: item.content,
                filePath: item.filePath,
              };
            });
            // console.log(this.noticeList);
          }
        })
        .catch(function (err) { });
    },
    toPdf(filePath) {
      window.open(filePath);
    },
    //-----------------------------查看通知对话框事件----------------------------------
    noticeClick: function (row, index) {
      this.notice.title = row.title;
      this.notice.content = row.content;
      // this.notice.contentLink = row.sysNotice.content;
      this.notice.filePath = row.filePath;
      this.notice.writeUserName = row.writeUserName;
      this.notice.deptName = row.deptName;
      this.notice.createDate = (
        this.$tool.formatDateTime(row.createTime) || ""
      ).substring(0, 16);
      // this.notice.noticeType = row.sysNotice.idType;
      // this.notice.relateId = row.sysNotice.relateId;
      // if (!row.hasRead) {
      //   this.readNoticeRequest(row, index);
      // }
      this.noticeDialogVisible = true;
    },
    // 过长的字符串截断
    truncatedString(originalString) {
      if (originalString.length > 20) {
        return originalString.substring(0, 20) + "...";
      }
      return originalString;
    },
    readNoticeRequest: function (row, index) {
      // let params = new URLSearchParams();
      // params.append("noticeId", row.sysNotice.nId);
      // params.append("sysNoticeTargetId", row.id);
      // this.$http
      //   .post("/sys/notice/readNotice", params)
      //   .then(
      //     function (res) {
      //       if (res.data.success) {
      //         localStorage.setItem(
      //           "NOTICE_COUNT",
      //           localStorage.NOTICE_COUNT - 1
      //         ); //存储未阅读通知数量
      //         row.hasRead = true;
      //         this.noticeList.splice(index, 1, row);
      //         //            this.searchNoticeList();
      //       }
      //     }.bind(this)
      //   )
      //   .catch(
      //     function (err) {
      //       this.$message.error("网络错误或者其他：" + err);
      //     }.bind(this)
      //   );
    },

    //----------------------------三个柱形图的数据获取-----------------------------------
    //获取教育时长
    getEducationHours: function () {
      this.$http
        .post("eduDailyInfo/getUserTrainningHours")
        .then(
          function (res) {
            if (res.data.success) {
            }
          }.bind(this)
        )
        .catch(function (err) { }.bind(this));
    },
    //获取自查次数
    getSelfInspectNum: function () {
      this.$http
        .get("index/userSelfInspectInfo/")
        .then(
          function (res) {
            if (res.data.success) {
              let finishNum = Number(res.data.data.finishCount);
              let yearGoalNum = Number(res.data.data.yearCount);
              yearGoalNum = finishNum > yearGoalNum ? finishNum : yearGoalNum;
              this.inspectData.done = finishNum;
              this.inspectData.sum = yearGoalNum;
              window.localStorage.setItem(
                "inspectData",
                JSON.stringify(this.inspectData)
              ); //刘杰1123 增
              this.showEduTimeScore();
            }
          }.bind(this)
        )
        .catch(function (err) { }.bind(this));
    },
    //---------------------------------查看安全生产目标-----------------------
    showSafeGoalClick: function (type) {
      this.safeGoalList = [];
      if (type == 0) {
        this.safeTitle = "责任清单";
      } else if (type == 1) {
        this.safeTitle = "任务清单";
      } else if (type == 2) {
        this.safeTitle = "安全工作目标";
      }
      let posts = this.$tool.getStorage("LOGIN_USER").posts;
      if (posts.length) {
        this.$http
          .post(
            "postResponsible/find?postId=" +
            posts[0].postId +
            "&responsibleType=" +
            type
          )
          .then(
            function (res) {
              if (res.data.success) {
                this.safeGoalList = res.data.data;
              }
            }.bind(this)
          )
          .catch(function (err) { }.bind(this));
        this.safeGoalDialogVisible = true;
      } else {
        this.$message.info("您还没有岗位设置");
      }
    },
    //查看通知对话框的查看应急
    viewEmergencyClick: function () {
      this.$router.push({
        name: "viewEmergency",
        params: {
          emergencyId: this.notice.relateId,
          taskFlag: true,
          editTaskFlag: "view",
        },
      });
    },
    //本公司安全员信息获取
    getSafePerson: function () {
      this.safePersonTitle = "今日日期";
      let currentDate = new Date();
      this.safePersonName =
        currentDate.getMonth() + 1 + "月" + currentDate.getDate() + "日";
      this.safePersonContent =
        currentDate.getFullYear() +
        "年" +
        (currentDate.getMonth() + 1) +
        "月" +
        currentDate.getDate() +
        "日  " +
        this.weekArray[currentDate.getDay()];
      this.$http
        .get("sysDuty/getTodayDuty")
        .then(
          function (res) {
            if (res.data.data) {
              this.safePersonTitle = "今日值班";
              this.safePersonName = res.data.data.username;
              this.safePersonContent = "联系电话：" + res.data.data.mobile;
            }
          }.bind(this)
        )
        .catch(function (err) { }.bind(this));
    },
  },
};
</script>
<style scoped>
.middle-box /deep/ .el-progress__text {
  display: none !important;
}

.middle-box /deep/ .el-progress-bar {
  padding-right: 20px !important;
}

.box-s {
  /* 下面有两像素阴影 */
  box-sizing: border-box;
  box-shadow: 1px 0 #0000000f, 0 1px #0000000f, 1px 1px #0000000f,
    1px 0 #0000000f inset, 0 1px #0000000f inset;
  border-radius: 4px;
}

/*任务统计*/
.task-block {
  width: 254px;
  height: 112px;
  background-color: #fff;
  float: left;
}

.task-block:hover {
  box-shadow: 0 0 10px #999;
  cursor: pointer;
}

.block-text {
  font-size: 16px;
  margin: 27px 0 0 20px;
  color: #666666;
  font-family: "Microsoft YaHei";
  font-weight: 500;
}

.block-sub-text {
  font-size: 32px;
  margin: 10px 0 0 20px;
  color: #282828;
  font-family: Arial;
}

/*任务和通知清单*/
.task-item {
  height: 38px;
  line-height: 38px;
  margin: 0 20px 0 20px;
  padding-top: 8px;
  overflow: hidden;
  font-family: "Microsoft YaHei";
  font-size: 16px;
  color: #9f9f9f;
}

.task-item:hover {
  cursor: pointer;
  background-color: #8cc5ff;
}

.more-block {
  float: right;
  margin-right: 20px;
  font-size: 16px;
  color: #999999;
  font-family: "Microsoft YaHei";
}

.more-block:hover {
  cursor: pointer;
}

.unRead {
  color: #4a90e2;
}

#menuPage {

  /* width: 100vw; */
  /* height: 100vh; */
  /* height: 1100px; */
  /* position: relative; */
  /* background-color: rgb(248, 249, 254); */
}

span {
  font-size: 18px;
}

.menu-main {

  /* position: relative;
    width: 1200px;
    height: 1050px;
    margin: 100px auto;
    margin-bottom: 0;
    box-sizing: border-box; */
  position: absolute;
  inset: 85px 0px 0px;
  top: 80px;
  right: 0px;
  bottom: 0px;
  left: 0px;
  min-width: 1000px;
  background-color: rgb(248, 249, 254);
  overflow-y: scroll;
}

.menu-img {
  background: url("./../static/imgs/bg.png") no-repeat center bottom;
  /* transform: scale(0.77); */

}

.top-box {
  margin: 0 auto;
  width: 1200px;
}

.left-box,
.middle-box,
.right-box {
  float: left;
  box-sizing: border-box;
  width: 390px;
  margin-right: 15px;
}

.right-box {
  width: 390px;
  /* background-color: rgb(248, 249, 254); */
  margin-right: 0px;
}

/* 设置 bot-box 的样式和宽度 */
.bot-box {
  margin: 0 auto;
  width: 1200px;
  height: 203px;
}

/* 设置子盒子的样式 */
.left-top-box,
.left-bot-box,
.middle-top-box,
.middle-bot-box {
  box-sizing: border-box;
  width: 100%;
}

/* 设置子盒子的高度 */
.left-top-box,
.middle-top-box {}

.card-box {
  width: 390px;
  height: 390px;
  overflow: hidden;
  background-color: white;
}

.card-box2 {
  width: 390px;
  height: 340px;
  overflow: hidden;
}

.card-box3 {
  width: 390px;
  height: 789px;
  overflow: hidden;
}

a {
  text-decoration: none;
}

.card-box4 {
  padding: 20px;
  width: 1200px;
  height: 140px;
  margin-bottom: 10px;
  overflow: hidden;
  background: #ffffff;
  box-sizing: border-box;
}

.card-box4 .card-box4-div {
  background-color: #eaf4fe;
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  margin-right: 20px;
  width: 100px;
  height: 100px;
}

.card-box4 .card-box4-div span {
  color: #666;
  font-size: 14px;
  margin-top: 5px;
}

.card-box3-div-img {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 789px;
  background-color: white;
}

.left-bot-box,
.middle-bot-box {}

.left-top-Purpose,
.middle-bot-performance,
.right-box-notice {
  background-color: white;
}

.card-box-img {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 390px;
}

.left-top-Purpose {
  height: 70px;
  margin-top: 10px;
  display: flex;
  flex-direction: row;
  align-items: center;
  position: relative;
}

.triangle-right {
  width: 0;
  height: 0;
  border-top: 7px solid transparent;
  border-bottom: 7px solid transparent;
  border-left: 9px solid #3596f7;
  display: inline-block;
}

.triangle-right1 {
  margin-left: 5px;
  width: 0;
  height: 0;
  border-left: 7px solid transparent;
  border-right: 7px solid transparent;
  border-top: 9px solid #1689e9;
  display: inline-block;
}

.middle-top-work {
  height: 65px;
  margin-left: 25px;
  margin-right: 25px;
  /* line-height: 25px; */
  /* border-bottom: 2px dashed #3396fb; */
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.middle-div-work {
  display: flex;
  justify-content: space-between;
}

.right-box-view,
.middle-top-view {
  color: #3396fb;
  text-align: center;
  width: 390px;
  cursor: pointer;
  font-size: 16px;
  font-family: "Microsoft YaHei";
}

.middle-top-view {
  line-height: 50px;
  font-size: 16px;
}

.right-box-view {
  height: 60px;
  line-height: 60px;
  width: 389px;
  background-color: white;

}

.middle-bot-performance {
  height: 106px;
  width: 389px;
  margin-bottom: 10px;
  display: flex;
  justify-content: center;
  flex-direction: column;
  /* align-items: center; */
  padding-left: 20px;
}

.middle-bot-performance>div:first-child {
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
}

.text-box {
  margin-right: 20px;
  letter-spacing: 2px;
}

.middle-bot-performance-title {
  font-size: 16px;
  color: #666;
  font-family: "Microsoft YaHei";
  display: flex;
  flex-direction: column-reverse;
}

.text-box-1 {
  font-size: 30px;
  font-weight: 700;
  font-family: "DIN Medium";
}

.elProgress /deep/.el-progress-bar__inner {
  background-color: rgb(20, 192, 80) !important;
}

.elProgress2 /deep/.el-progress-bar__inner {
  background-color: rgb(255, 141, 0) !important;
}

.card-box3-div {
  height: 720px;
  overflow: hidden;
}

.middle-top-border {
  width: 11px;
  height: 5px;
  background-color: #cfe4ff;
  margin-left: 2px;
  margin-top: 7px;
}

.card-box3-sdiv {
  padding-top: 8px;
  margin-left: 20px;
  line-height: 25px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.right-box-notice {
  height: 80px;
  margin-bottom: 10px;
  width: 389px;
}

.margin-createTime {
  color: #666;
  font-size: 14px;
  font-family: "Microsoft YaHei";
  margin-right: 20px;
}

.middle-top-title {
  font-family: "Microsoft YaHei";
  color: #666666;
  font-size: 16px;
  cursor: pointer;
  margin-top: 5px;
}

.margin-title {
  font-size: 16px;
  margin-left: -8px;
  font-family: "Microsoft YaHei";
  /* margin-top: 3px; */
}
</style>
