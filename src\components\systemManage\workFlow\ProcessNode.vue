<template>
  <div id="processNode" class="background-style">
    <div
      style="width: 100%;padding:10px 0 0 10px">
      <!--表格区-->
      <div style="width:60%;float:left;margin-top: 20px;margin-bottom: 10px">
        <div style="width: 100%;margin: auto">
          <div style="padding-left: 19px;padding-right: 19px">
            <el-table
              v-loading="loading"
              element-loading-text="同步数据中"
              element-loading-spinner="el-icon-loading"
              element-loading-background="rgba(255, 255, 255, 0.9)"
              :data="processNodeData"
              highlight-current-row
              style="width: 100%;"
              stripe
              border>
              <el-table-column
                type="index"
                width="50"
                align="center"
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="name"
                label="节点名字"
                width="150"
                align="center"
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="nodeId"
                label="节点id"
                width="120"
                align="center"
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="url"
                label="跳转"
                width="120"
                align="center"
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="statusColor"
                label="状态颜色"
                width="120"
                align="center"
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="status"
                label="状态值"
                width="120"
                align="center"
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                label="操作"
                align="center"
                width="200"
                fixed="right"
                label-class-name="header-style">
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="primary"
                    @click="getNodeDetail(scope.row,scope.$index)"
                  >编辑</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>

      </div>
      <div style="width: 40%;float: left;margin-top: 20px;margin-bottom: 10px;">
        <el-form ref="form" :model="form" label-width="80px" style="width: 80%">
          <el-form-item label="节点名称">
            <el-input v-model="form.name" disabled></el-input>
          </el-form-item>
          <el-form-item label="节点id">
            <el-input v-model="form.nodeId" disabled></el-input>
          </el-form-item>
          <el-form-item label="流程定义id">
            <el-input v-model="procdefId" disabled></el-input>
          </el-form-item>
          <el-form-item label="状态值">
            <el-input v-model="form.status" type="number"></el-input>
          </el-form-item>
          <el-form-item label="状态颜色">
            <el-input v-model="form.statusColor" ></el-input>
          </el-form-item>
          <el-form-item label="跳转">
            <el-switch
              v-model="form.jump"
              active-text="是"
              inactive-color="否">
            </el-switch>
          </el-form-item>
          <el-form-item label="跳转值">
            <el-input v-model="form.url" ></el-input>
          </el-form-item>
          <el-form-item label="发布通知">
            <el-switch
              v-model="form.publicNotice"
              active-text="是"
              inactive-color="否">
            </el-switch>
          </el-form-item>
          <el-form-item label="配置">
            <el-input type="textarea" :rows="3"
                      placeholder="请输入内容" v-model="form.config" ></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="safeProcessNode">保存</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div style="width: 100%">
        <img :src="imageUrl" style="width: 80%;margin: auto;padding-bottom: 20px"/>
      </div>
    </div>
  </div>
</template>

<script>
    export default {
        name: "processNode",
      data() {
        return {
          userName:'',
          procdefId:'',
          loading: false,
          processNodeData:[],
          form:{
            id:0,
            status:0,
            name:'',
            url:'',
            statusColor:'',
            jump:false,
            nodeId:'',
            publicNotice:false,
            config:""
          },
          imageUrl:"",
          index:0,
        }
      },
      created:function () {
        this.procdefId=this.$route.params.procdefId
        this.imageUrl= this.$http.defaults.baseURL+"/activiti/getProcessPic/"+this.procdefId
        this.loadProcessNode()
      },
      watch:{
        $route(to, from) {
          if ((from.path === '/manage-menu/workflow-manage')&&this.$route.name==='processNode') {
            this.procdefId=this.$route.params.procdefId
            this.imageUrl=this.$http.defaults.baseURL+"/activiti/getProcessPic/"+this.procdefId
            this.loadProcessNode();
          }
        }
      },
      mounted:function () {
        this.procdefId=this.$route.params.procdefId
        this.imageUrl=this.$http.defaults.baseURL+"/activiti/getProcessPic/"+this.procdefId
        this.loadProcessNode()
      },
      methods:{
        loadProcessNode:function(){
          this.$http.get("/activiti/getProcessNode/"+this.procdefId).then(function (res) {
            if(res.data.success){
              this.processNodeData=res.data.data
            }
          }.bind(this)).catch(function (err) {
            console.log(err)
          })
        },
        getNodeDetail:function (node,index) {
          this.form=node
          this.index=index
        },
        safeProcessNode:function () {
          var params=new URLSearchParams()
          params.append("id",this.form.id)
          params.append("url",this.form.url)
          params.append("publicNotice",this.form.publicNotice)
          params.append("jump",this.form.jump)
          params.append("status",this.form.status)
          params.append("statusColor",this.form.statusColor)
          params.append("config",this.form.config)
          this.$http.post("/activiti/updateProcessNode",params).then(function (res) {
            if(res.data.success){
              this.$message.success("保存成功")
              this.processNodeData.splice(this.index,1,this.data.data)
            }
          }.bind(this)).catch(function (err) {
            console.log(err)
          })
        }
      }
    }
</script>

<style scoped>

</style>
