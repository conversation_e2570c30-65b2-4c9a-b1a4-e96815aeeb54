<template>
  <div id="">
    <el-container class="container">
      <el-main>
        <el-form ref="form" :model="form" label-width="5px">
          <el-row>
            <el-col :span="3">
              <el-form-item>
                <el-input
                  clearable
                  placeholder="组织部门"
                  v-model="form.department"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="2">
              <el-form-item>
                <el-input
                  clearable
                  placeholder="效果评价"
                  v-model="form.judge"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item>
                <el-input
                  clearable
                  placeholder="培训内容"
                  v-model="form.courses"
                ></el-input>
              </el-form-item>
            </el-col>
            <!--</el-row>-->
            <!--<el-row>-->
            <el-col :span="2">
              <el-form-item>
                <el-select v-model="form.status" placeholder="状态" clearable>
                  <el-option
                    v-for="item in assist.status"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item>
                <el-date-picker
                  style="width: 100%"
                  start-placeholder="培训时间范围"
                  @change="trainingDateChange"
                  v-model="assist.trainingDate"
                  type="daterange"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :offset="1" :span="1">
              <el-button type="primary" @click="searchBtnClickHandle"
                >搜索</el-button
              >
            </el-col>
            <el-col :offset="1" :span="1">
              <!--公司/组织者-->
              <el-button
                type="success"
                v-if="role === 4"
                icon="el-icon-plus"
                @click="$router.push({ name: 'dailyTrainingAdd' })"
                >日常培训</el-button
              >
            </el-col>
          </el-row>
          <el-row>
            <el-table :data="tableData.list" border>
              <el-table-column
                label-class-name="header-style"
                label="序号"
                width="100"
                type="index"
              >
              </el-table-column>
              <el-table-column
                label-class-name="header-style"
                prop="status"
                label="状态"
                width="120"
              >
                <template slot-scope="scope">
                  <el-tag v-if="scope.row.status == 0" type="info"
                    >未发布</el-tag
                  >
                  <el-tag v-if="scope.row.status == 1" type="primary"
                    >已发布</el-tag
                  >
                  <el-tag v-if="scope.row.status == 2" type="danger"
                    >进行中</el-tag
                  >
                  <el-tag v-if="scope.row.status == 3" type="warning"
                    >待评价</el-tag
                  >
                  <el-tag v-if="scope.row.status == 4" type="success"
                    >已完结</el-tag
                  >
                </template>
              </el-table-column>
              <el-table-column
                label-class-name="header-style"
                prop="department"
                label="组织部门"
                show-overflow-tooltip
                width="120"
              >
              </el-table-column>
              <!--              <el-table-column-->
              <!--                prop="companyName"-->
              <!--                label="公司"-->
              <!--                show-overflow-tooltip-->
              <!--                width="120"-->
              <!--                label-class-name="header-style">-->
              <!--              </el-table-column>-->
              <el-table-column
                label-class-name="header-style"
                :formatter="formatDateTime"
                prop="trainingDate"
                label="培训时间"
                width="120"
              >
              </el-table-column>
              <el-table-column
                label-class-name="header-style"
                prop="location"
                label="地点"
                width="120"
              >
              </el-table-column>
              <el-table-column
                label-class-name="header-style"
                prop="trainingHours"
                label="学时"
                width="120"
              >
              </el-table-column>
              <el-table-column
                label-class-name="header-style"
                prop="courses"
                label="培训内容"
                width="120"
              >
              </el-table-column>
              <el-table-column
                label-class-name="header-style"
                prop="participants"
                label="参加人员"
                width="120"
              >
              </el-table-column>

              <el-table-column
                label-class-name="header-style"
                prop="judge"
                label="安全培训教育效果评价"
              >
              </el-table-column>
              <el-table-column
                fixed="right"
                width="350"
                label="操作"
                label-class-name="header-style"
                align="left"
              >
                <template slot-scope="scope">
                  <el-button
                    v-if="scope.row.status >= 1"
                    size="mini"
                    type="success"
                    @click="itemViewClick(scope.row)"
                    >查看</el-button
                  >
                  <el-button
                    v-if="scope.row.isPunch"
                    size="mini"
                    type="primary"
                    @click="createQRcode(scope.row)"
                    >签到码</el-button
                  >
                  <el-button
                    v-if="scope.row.isPunch"
                    size="mini"
                    type="success"
                    @click="signin(scope.row.id)"
                    >签到统计</el-button
                  >
                  <!--组织者修改 或者 员工待评价-->
                  <!-- <el-button v-if="(role == 4 && scope.row.status != 4 && scope.row.status != 3)" size="mini" type="primary" @click="itemUpdateClick(scope.row)">修改</el-button> -->
                  <!--不是管理者的话员工待评价-->
                  <el-button
                    v-if="
                      scope.row.status == 3 &&
                      scope.row.eduDailyParticipateUserIds.includes(
                        $tool.getStorage('LOGIN_USER').userId
                      ) &&
                      !scope.row.eduDailyEvaluateUserIds.includes(
                        $tool.getStorage('LOGIN_USER').userId
                      )
                    "
                    size="mini"
                    type="warning"
                    @click="itemUpdateClick(scope.row)"
                    >评价</el-button
                  >
                  <el-button
                    v-if="role == 4"
                    size="mini"
                    type="danger"
                    @click="itemDeleteClick(scope.row)"
                    >删除</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
            <el-pagination
              background
              layout="prev, pager, next"
              :current-page="tableData.pageNum"
              :page-size="form.pageSize"
              :total="tableData.total"
              @current-change="disasterPageChangeHandle"
            >
            </el-pagination>
          </el-row>
        </el-form>
      </el-main>
    </el-container>
    <el-dialog :title="qrcodeTitle" :visible.sync="qrcodeVisible" @closed="handleDialogClosed" width="30%">
      <div style="margin-left: 50%">
        <div id="qrcode" ref="qrCodeUrl" style="margin-left: -90px"></div>
      </div>
    </el-dialog>
    <el-dialog :title="qrcodeTitle" :visible.sync="showStatistics" width="45%">
      <chooseStaffPage
        :staffData.sync="eduDailyParticipants"
        ref="chooseStaffPage"
      ></chooseStaffPage>
    </el-dialog>
  </div>
</template>
<script>
import QRCode from 'qrcodejs2'
import chooseStaffPage from '@/components/common/chooseStaffPage'
export default {
  name: '',
  components: { chooseStaffPage },
  data() {
    return {
      qrcode: '',//签名二维码扫码图
      qrcodeTitle: '签到二维码',
      qrcodeVisible: false,
      showStatistics: false,
      eduDailyParticipants: [],//签到参与人员
      // 搜索
      form: {
        // 培训时间---开始时间
        startDate: '',
        // 培训时间---结束时间
        endDate: '',
        // 组织部门
        department: '',
        // 培训内容
        courses: '',
        // 效果评价
        judge: '',
        // 状态 0 未发布    1 已发布    2 进行中    3 待评级    4 已完结
        status: '',
        // 当前页
        pageCurrent: 1,
        // 页数大小
        pageSize: 10,
      },
      assist: {
        // 培训时间
        trainingDate: '',
        // 状态
        status: [
          { value: '0', label: '未发布' },
          { value: '1', label: '已发布' },
          { value: '2', label: '进行中' },
          { value: '3', label: '待评价' },
          { value: '4', label: '已完结' },
        ],
      },
      tableData: {},
      // 角色 0 组织者或公司      1 部门        2  班组
      role: 0,
    }
  },
  mounted() {
    this.init();
  },
  watch: {
    $route(to, from) {
      if (to.name === 'dailyTrainingIndex') {
        this.init();
      }
    }
  },
  methods: {
    // 初始化
    init() {
      this.judgeUserRole();
      // 搜索
      this.searchBtnClickHandle();
    },
    // 生成签到码
    createQRcode(row) {
      let id = row.id;
      this.qrcodeTitle = row.eduDailyNotify[0].title
      this.qrcodeVisible = true;

      // 清空已有二维码
      if (this.qrcode) {
        this.qrcode.clear();
        this.$refs.qrCodeUrl.innerHTML = '';
      }

      // 延迟到DOM更新后生成
      this.$nextTick(() => {
        this.qrcode = new QRCode(this.$refs.qrCodeUrl, {
          text: `https://www.safe360.vip/safe/qrcode/inspectQrCode?codePrefix=checkin&id=${id}`,
          width: 200,  // 适当增加尺寸
          height: 200,
          colorDark: '#000000',
          colorLight: '#ffffff',
          correctLevel: QRCode.CorrectLevel.H
        });
      });
    },
    handleDialogClosed() {
      // 对话框关闭时销毁二维码实例
      if (this.qrcode) {
        this.qrcode.clear();
        this.$refs.qrCodeUrl.innerHTML = ''; // 清空容器内容
        this.qrcode = null;
      }
    },

    signin(id) {
      this.showStatistics = true;
      this.$store.dispatch('eduDailyInfoShow', { id: id }).then(res => {
        if (res.success) {
          let list = res.data.list[0];
          this.eduDailyParticipants = list.eduDailyParticipants.map(it => {
            return {
              participate: it.participate,
              userId: it.userId,
              companyName: it.eduUser.companyName,
              deptName: it.eduUser.deptName,
              username: it.eduUser.username,
            }
          });
          // 执行培训的人员列表
          this.$refs['chooseStaffPage'].isShowDeleteHandle(false);
          this.$refs['chooseStaffPage'].isShowSelectionHandle(false);
          this.$refs['chooseStaffPage'].isShowParticipateHandle(true);
          this.$refs['chooseStaffPage'].isEditDisableHandle(true);
        }
      })
    },

    judgeUserRole() {
      // 获取权限按钮
      let btns = this.$tool.getPowerBtns('eduTrainingMenu', this.$route.path);
      //        console.log('btns', btns)
      // 公司
      if (btns.includes('addBtn')) {
        this.role = 4;
      }
    },
    // 清空数据
    clear() {

    },
    // 格式化时间
    formatDateTime(row, column, cellValue) {
      let pro = column.property;
      let num = 10;
      let str = this.$tool.formatDateTime(row[pro]) || '';
      return str ? str.substring(0, num) : str;
    },
    // 分页
    disasterPageChangeHandle(page) {
      this.form.pageCurrent = page;
      if (this.isMore) {
        this.saveScoreBtnClickHandle();
      } else {
        this.searchBtnClickHandle();
      }
    },
    // 搜索按钮
    searchBtnClickHandle() {
      this.clear();
      let userId = this.$tool.getStorage('LOGIN_USER').userId;
      let params = this.$tool.filterObj({}, this.$tool.filterObj({}, this.form));
      if (this.role !== 4) {
        // 如果是员工，只显示和自己有关的主表
        params['userId'] = userId
      }
      let url = this.role == 4 ? 'eduDailyInfoManagerFindSimple' : 'eduDailyInfoStaffFindSimple';
      this.$store.dispatch(url, params).then(function (res) {
        if (res.success) {
          let data = res.data;
          // 员工--只有实参人员才可以看见
          if (data != null && data.list != null && data.list.length > 0) {
            data.list.map(function (it) {
              if (it.eduDailyParticipants && it.eduDailyParticipants.length > 0) {
                it.eduDailyParticipateUserIds = it.eduDailyParticipants.map(function (ip) {
                  return ip.userId;
                });
              } else {
                it.eduDailyParticipateUserIds = [];
              }

            });
          }
          this.tableData = data;
        } else {
          this.$message({
            type: 'error',
            message: res.message || '错误'
          })
        }
      }.bind(this));
    },
    // 查看
    itemViewClick(row) {
      let name = 'dailyTrainingView';
      let params = {
        id: row.id,
        progress: row.status,
        status: 'view'
      }
      this.$router.push({ name: name, params: params })
    },
    // 修改
    itemUpdateClick(row) {
      let name = '';
      let params = {
        id: row.id,
        progress: row.status,
        status: 'edit'
      }
      switch (row.status) {
        case 0:
          name = 'dailyTrainingAdd';
          break;
        case 1:
          name = 'dailyTrainingProcessPublished';
          break;
        case 2:
          name = 'dailyTrainingProcessHaveInHand';
          break;
        case 3:
          name = 'dailyTrainingProcessToBeEvaluated';
          break;

      }
      //        console.log(111, params);
      //        return;
      this.$router.push({ name: name, params: params })
    },
    // 删除按钮
    itemDeleteClick(row) {
      this.$confirm('此操作将永久删除, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(function () {
          this.$store.dispatch('eduDailyInfoDelete', {
            id: row.id
          }).then(function (res) {
            if (res.success) {
              this.$message({
                type: 'success',
                message: '删除成功'
              })
              this.searchBtnClickHandle();
            } else {
              this.$message({
                type: 'error',
                message: res.message || '删除失败！！'
              })
            }
          }.bind(this))
        }.bind(this))
    },
    // 批量保存分数
    saveScoreBtnClickHandle() {
      this.$store.dispatch('eduEntryTrainingBatchInputScores', this.more).then(function (res) {

        if (res.success) {
          this.$message({
            type: 'success',
            message: '打分成功'
          })
          this.searchBtnClickHandle();
        } else {
          this.$message({
            type: 'error',
            message: res.message || '打分失败！！'
          })
        }

      }.bind(this));
    },
    // 培训时间
    trainingDateChange(val) {
      this.form.startDate = val ? val[0] : '';
      this.form.endDate = val ? val[1] : '';
    },
  }
}
</script>
<style>
.container {
  background: #fff;
  padding: 0 20px;
}

.row {
  margin-top: 10px;
}
</style>
