<template>
  <el-container class="container">
    <el-main>
      <!--发布-->
      <el-form ref="info" label-width="100px" :model="info">
        <el-row>
          <el-col :span="24">
            <el-form-item label="文件上传">
              <fileUpload
                @fileData="fileDataFn"
                :data="upload"></fileUpload>
              <!-- <fileUpload
                 @fileData="fileDataFn"></fileUpload>-->
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex">
          <el-col :span="12">
            <el-form-item label="文件名称">
              <el-input v-model="info.name"></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="文件大小">
              <el-input v-model="info.size" disabled></el-input>
            </el-form-item>
          </el-col>

        </el-row>
        <el-row type="flex">
         <!-- <el-col :span="12">
            <el-form-item label="文件类型" >
              <el-radio v-model="info.type" label="0" border disabled>视频</el-radio>
              <el-radio v-model="info.type" label="1" border disabled>文章</el-radio>
            </el-form-item>
          </el-col>-->
          <el-col :span="8">
            <el-form-item label="是否共享">
              <el-radio v-model="info.isShare" label="0" border>不共享</el-radio>
              <el-radio v-model="info.isShare" label="1" border>共享</el-radio>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <div style="color:red;text-align:left;vertical-align: middle;padding-top:10px;">共享：全公司可见；不共享，本公司可见。</div>
          </el-col>
        </el-row>
        <el-row type="flex">
          <el-col :span="24">
            <el-form-item label="备注">
              <el-input type="textarea" :rows="2" v-model="info.remark"></el-input>
            </el-form-item>
          </el-col>
        </el-row>


        <el-row type="flex" class="row" justify="center">
          <el-button
            @click="saveBtnClickHandle({ status : '0' })"
            size="small" :span="2" type="success" >保存</el-button>
         <!-- <el-button
            @click="saveBtnClickHandle({ status : '1' })"
            size="small" :span="2" type="primary">提交</el-button>-->
          <el-button size="small" :span="2"  @click="$router.back();">返回</el-button>
        </el-row>
      </el-form>
    </el-main>
  </el-container>
</template>

<script>
  import { VueEditor } from 'vue2-editor'
  import fileUpload from '@/components/common/fileUploadFileServer'
  export default {
    components: {
      VueEditor,
      fileUpload
    },
    data(){
      return {
        // info表
        info : {
          /*
          *
           name， 文件名称
           size， 文件大小
           fileId， 前端文件上传之后返回的Id
           remark 描述
           type    0:视频/1:文章
           isShare   是否共享，默认不共享0；
          * */

          // 知识库
          name : '',
          size : '',
          fileId : -1,
          remark : '',
          type : '',
          isShare : '0',


        },
        // 上传文件的个数
        uploadFileLength : 0,
        // 参与人员对话框数据
        staffDialog : {
          isShow : false,
        },

        upload:{
          tip : "只能上传pdf文章或者MP4视频(大小限制500MB)",
//          limit : 1,
          params:{
            contentId : this.$tool.getStorage('LOGIN_USER').userId,
            contentType:19
          },
          btns : {
            // 上传按钮
            upload : {
              isShow : true,
            },
            // 下载按钮
            download : {
              isShow : false,
            },
            // 删除按钮
            delete : {
              isShow : true,
            },
          },
          uploadUrl:'',
          uploadCookies:true,
          fileData : [],
        },

        viewOrEdit : false,
        // 权限按钮
        powerBtns : [],
      }
    },
    watch:{
      $route(to,from){
        // 如果来至列表页
        if(from.name === 'safetyEducationKnowledgeIndex'&&this.$route.name==='safetyEducationKnowledgeAdd'){
          this.init();
        }
      },


    },
    mounted(){
      this.init();
    },
    methods:{
      // 初始化
      init(){
        if(this.$route.params.status == 'edit'){
          this.searchBtnClickHandle();
          //刘杰1130 终
        } else {
          this.clear();
        }
        this.judgeUserRole();
      },
      fileDataFn(data){
        console.log('父组件获取子组件数据：',data);
        this.uploadFileLength = data.length;
        if(data){
          if(data.length == 1){
            this.info.fileId = data[0].fileId;
            this.info.name = data[0].fileName;
            let size = (data[0].fileData.file.size / 1024 / 1024).toFixed(2) + "M";
            this.info.size = size;
            let type = data[0].fileData.file.type;
            if(type == 'application/pdf'){
              this.info.type = '1';
            } else {
              this.info.type = '0';
            }
          } else if(data.length == 0){
            this.clear();
          } else {
            this.$message({
              type : 'error',
              message : '只能上传一篇文章或者一个视频'
            })
          }


        } else {
          this.clear();
        }
      },

      judgeUserRole(){
        // 获取权限按钮
        let url = '/edu-training-menu/daily-training-index';
        this.powerBtns = this.$tool.getPowerBtns('eduTrainingMenu', url);
      },
      // 清空数据
      clear(){
        this.info.name = '';
        this.info.size = '';
        this.info.remark = '';
        this.info.fileId = -1;
        this.info.isShare = '0';
        this.info.type = '';

        this.upload.fileData = [];
        this.viewOrEdit = false;
      },
      // 根据id搜索信息
      searchBtnClickHandle(){
        this.clear();
        let id = this.$route.params.id;
        this.viewOrEdit = true;
        this.$store.dispatch('eduNewsFindById', { id : id }).then(function(res){
          if(res.success){
//            let list = res.data.list[0];
            let list = res.data;
            // 发布培训信息
            Object.entries(list).forEach(function(it){
              if(it[1] && this.info.hasOwnProperty(it[0])){
                this.info[it[0]] = it[1];
              }
            }.bind(this));
            this.upload.fileData = list.listNewsFile;
//            this.$store.commit('articleDataMut', this.info)
//            console.log('子数据',this.$store.state.articleData)

          } else {
            this.$message({
              type : 'error',
              message : res.message || '错误'
            })
          }
        }.bind(this));
      },
      // 未发布/已发布/进行中【开始按钮】--培训发布--保存按钮
      saveBtnClickHandle(options){
        let params = this.info;
        console.log(params);

        if(this.uploadFileLength == 0){
          this.$message({
            type : 'warning',
            message : '还未上传文件，请上传'
          })
          return;
        } else if(this.uploadFileLength > 1){
          this.$message({
            type : 'warning',
            message : '一次性只能上传一个文件，请删除多余的文件'
          })
          return;
        } else if(this.uploadFileLength == 1) {
          if(params.fileId == '' || params.fileId == -1){
            this.$message({
              type : 'warning',
              message : '请上传文件'
            })
            return;
          }

//          return;

          this.$store.dispatch('safeEduMediumLibraryAddMedium', params).then(function (res) {
            if(res.success){
              this.$message({
                type : 'success',
                message : '操作成功'
              })
              this.$router.push({ name : 'safetyEducationKnowledgeIndex' })
            }  else {
              this.$message({
                type : 'error',
                dangerouslyUseHTMLString: true,
                message : res.message || '错误'
              })
            }
          }.bind(this))
        }

      },


    }
  }
</script>

<style>
  .container{
    background:#fff;
    padding:0px 20px 20px;
  }
  .title{
    background:rgba(64,158,255,.1);
    color:#0f6fc6;
    border: 1px solid rgba(64,158,255,.2);
    border-radius:5px;
  }
  .row{
    margin-top:10px;
  }
</style>
