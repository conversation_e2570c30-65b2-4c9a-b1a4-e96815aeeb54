<template>
  <div id="investigation">
    <div class="background-style">

      <!--表格区-->
      <div style="width: 100%;">
        <!--粗分类-->
        <div style="float: left;margin: 10px 0 0 10px;">
          <el-radio-group v-model="radioResponseType" @change="responseTypeChange">
            <!--            <el-radio-button  v-for="item in radioResponseButtons"   v-if="powerBtns.includes('item.buttonName')" :id="item.value" :label="item.value" :key="item.value">{{item.name}}</el-radio-button>-->
            <el-radio-button   v-if="powerBtns.includes('myInspectBtn')" :id="radioResponseButtons[1].value" :label="radioResponseButtons[1].value" :key="radioResponseButtons[1].value">{{radioResponseButtons[1].name}}</el-radio-button>
            <el-radio-button   v-if="powerBtns.includes('allInspectBtn')"  :id="radioResponseButtons[0].value" :label="radioResponseButtons[0].value" :key="radioResponseButtons[0].value">{{radioResponseButtons[0].name}}</el-radio-button>
            <el-radio-button   v-if="this.$tool.getStorage('LOGIN_USER').parentCompanyId != 0"  :id="radioResponseButtons[2].value" :label="radioResponseButtons[2].value" :key="radioResponseButtons[2].value">{{radioResponseButtons[2].name}}</el-radio-button>
          </el-radio-group>
        </div>
        <!--搜索条件选取-->
        <div style="float: left;margin: 10px 0 0 10px;">
          <!--<el-select v-model="publicUnit" placeholder="发布单位" clearable style="width:120px">-->
          <!--<el-option-->
          <!--v-for="item in unitOptions"-->
          <!--:key="item.value"-->
          <!--:label="item.label"-->
          <!--:value="item.value">-->
          <!--</el-option>-->
          <!--</el-select>-->
          <el-cascader
            change-on-select
            :options="unitOptions"
            v-model="targetCompanyArray"
            placeholder="受检单位" clearable style="width:130px"
            @change="handlePickCompany">
          </el-cascader>
          <el-select v-model="status" placeholder="状态选择" clearable multiple collapse-tags style="width: 130px">
            <el-option
              v-for="item in statusTable"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
          <el-date-picker
            id="investigationDateRange"
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期">
          </el-date-picker>
          <el-input style="width: 220px" v-model="searchInput" placeholder="请输入检查单名称" clearable></el-input>
          <el-select v-model="check" placeholder="检查类型" clearable collapse-tags style="width: 130px" v-if="!(this.radioResponseType=='check')">
            <el-option
              v-for="item in checkType"
              :label="item.label"
              :key="item.type"
              :value="item.type">
            </el-option>
          </el-select>
          <el-button type="primary" icon="el-icon-search" @click="searchClick">搜索</el-button>
        </div>
        <!--搜索条件选取结束-->
        <!--类别标签-->
        <div style="float: left;margin: 10px 0 0 10px;width: 100%;height: 30px">
          <span style="float: left;line-height: 30px">类别标签：</span>
          <el-tag
            :key="tag"
            v-for="tag in dynamicTags"
            closable
            :disable-transitions="false"
            @close="handleClose(tag)"
            style="float: left">
            {{tag}}
          </el-tag>
          <div v-if="inputVisible" style="float: left">
            <el-select
              v-model="search.searchTag"
              filterable
              remote
              reserve-keyword
              clearable
              placeholder="请输入标签名后选择"
              :remote-method="remoteTag"
              :loading="search.tagLoading"
              @change="labelSelectChange"
              size="small"
              style="width: 150px;margin-left: 10px">
              <el-option
                v-for="item in tableTagOption"
                :key="item.label"
                :label="item.label"
                :value="item.label">
              </el-option>
            </el-select>
            <el-button type="info" size="small" style="margin-left: 10px" @click="search.searchTag='';inputVisible=false">取消</el-button>
          </div>
          <div v-else style="float: left">
            <el-button v-if="!viewRole" type="warning" size="small" @click="inputVisible = true;" icon="el-icon-plus" style="margin-left: 10px">添加类别</el-button>
          </div>
        </div>
        <!--类别标签结束-->
        <!--列表-->
        <div style="width: 100%;float: left;">
          <div style="padding: 10px">
            <el-table
              :data="tableData"
              border
              highlight-current-row
              @row-dblclick="itemViewClick"
              style="width: 100%">
              <el-table-column
                prop="num"
                width="60"
                align="center"
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                label="状态"
                width="120"
                align="center"
                label-class-name="header-style">
                <template slot-scope="scope">
                  <el-tag :type="scope.row.statusTagType" size="small">{{scope.row.statusTag}}</el-tag>
                </template>
              </el-table-column>
              <el-table-column
                prop="checkNum"
                label="检查单编号"
                width="200"
                align="center"
                show-overflow-tooltip
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="name"
                label="检查单名称"
                width="200"
                align="center"
                show-overflow-tooltip
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="createTime"
                label="创建日期"
                width="120"
                align="center"
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="targetDeptName"
                label="受检单位"
                min-width="300"
                show-overflow-tooltip
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                label="分类标签"
                min-width="300"
                show-overflow-tooltip
                label-class-name="header-style">
                <template slot-scope="scope">
                  <el-tag v-for="item in scope.row.labels" :key="item" size="small">{{item }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column fixed="right" label="操作" label-class-name="header-style" width="170">
                <template slot-scope="scope">
                  <el-button size="mini" type="success" @click="itemViewClick(scope.row)">查看</el-button>
                  <el-button  size="mini" type="danger" @click="itemDeleteClick(scope.row)" v-if="parentCheck()">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div>
            <el-pagination
              background
              layout="prev, pager, next"
              :current-page="currentPage"
              :total="totalItem"
              @current-change="currentPageClick">
            </el-pagination>
          </div>
        </div>
        <!--列表结束-->
      </div>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'investigation',
    data() {
      return {
        //粗分类
        radioResponseButtons:[{value:'all',name:'全部检查'},{value:'createList',name:'我的创建'},{value:'check',name:'督办整改'}],
        radioResponseType:'createList',
        //搜索类别选择
        publicUnit:'',
        unit:'',
        status:[],
        check:'',
        //受检公司选择为数组，需要从数组转字符
        targetCompanyArray:[],
        //标签对应表
        statusOptions:[
          {value:0,label:'未发布',type:'primary'},
          {value:1,label:'发布审核',type:'warning'},
          {value:2,label:'修改检查单',type:'danger'},
          {value:3,label:'待检查',type:'success'},
          {value:4,label:'发布检查单',type:'primary'},
          {value:5,label:'隐患评估',type:'primary'},
          {value:6,label:'整改单审核',type:'warning'},
          {value:7,label:'待整改',type:'success'},
          {value:8,label:'整改审核',type:'warning'},
          {value:9,label:'验收',type:'warning'},
          {value:10,label:'督办整改',type:'primary'},
          {value:11,label:'督办审核',type:'warning'},
          {value:12,label:'督办验收',type:'warning'},
          {value:13,label:'督办整改审核',type:'warning'},
          {value:14,label:'验收审核',type:'warning'},
          {value:15,label:'已完成',type:'info'},
        ],
        //状态选择项目
        statusTable:[
          {value:0,label:'未发布',type:'primary'},
          {value:1,label:'发布审核',type:'warning'},
          {value:2,label:'修改检查单',type:'danger'},
          {value:3,label:'待检查',type:'success'},
          {value:4,label:'发布检查单',type:'primary'},
          {value:5,label:'隐患评估',type:'primary'},
          {value:6,label:'整改单审核',type:'warning'},
          {value:7,label:'待整改',type:'success'},
          {value:8,label:'整改审核',type:'warning'},
          {value:9,label:'验收',type:'warning'},
          {value:10,label:'督办整改',type:'primary'},
          {value:11,label:'督办审核',type:'warning'},
          {value:12,label:'督办验收',type:'warning'},
          {value:13,label:'督办整改审核',type:'warning'},
          {value:14,label:'验收审核',type:'warning'},
          {value:15,label:'已完成',type:'info'},
        ],
        //检查类型项目       add by pdn
        checkType:[
          {type:0,label:'自查'},
          {type:1,label:'督查'},
          {type:2,label:'排查'},
        ],
        dateRange:'',
        searchInput:'',

        //表格数据
        tableData:[],
        currentPage:0,
        totalItem:0,

        //筛选标签
        dynamicTags: [],
        inputVisible: false,
        search: {
          searchTag: '',
          tagLoading: false,
        },
        // 权限按钮
        powerBtns : [],
        //浏览角色模式
        viewRole : false,
      }
    },
    computed:{
      unitOptions:function () {
        if(this.$store.state.hideDangerData.targetDept.length){
          return this.$store.state.hideDangerData.targetDept;
        }else{
          return [];
        }
      },
      tableTagOption:function () {
        return this.$store.state.hideDangerData.tableLabels;
      },
    },
    mounted:function () {
      this.init();
      this.$store.dispatch('getTargetDept');
      this.searchClick();
    },
    watch:{
      $route(to, from){
        if((from.name==='investigationForm'||from.name==='editInvestigation'||from.name==='newInvestigation'||from.name==='updateInvestigation')&&this.$route.name==='investigation') {
          this.init();
          this.searchClick();
        }
      }
    },
    methods:{
      init(){
        this.viewRole = this.$tool.judgeViewRole();
        if(this.viewRole){
          this.radioResponseType = 'all'
        }

        this.powerBtns = this.$tool.getPowerBtns('hideDangerMenu', this.$route.path);

      },
      responseTypeChange:function (val) {
        this.searchClick();
      },
      searchClick:function () {
        this.currentPage=1;
        let params={"pageCurrent":1};
        this.sendRequest(params);
      },
      currentPageClick:function (val) {
        if(val){
          this.currentPage=val;
          let params={"pageCurrent":Number(val)};
          this.sendRequest(params);
        }
      },
      sendRequest:function (params) {
        if(this.radioResponseType==='createList'){
          params.createUserId=this.$tool.getStorage('LOGIN_USER').userId;
        }else if(this.radioResponseType==='superviseList'){
          params.superviseId=this.$tool.getStorage('LOGIN_USER').userId;
        }
        if(this.radioResponseType=='check') {
          if (this.unit == null || this.unit == '') {
            params.targetDeptId = this.$tool.getStorage('LOGIN_USER').companyId;
          } else {
            params.targetDeptId = this.unit;
          }
          params.type=1;
        }else{
          params.publicCompanyId = this.$tool.getStorage('LOGIN_USER').companyId;//只能看当前公司发布的隐患
          params.type=this.check;
          params.targetDeptId=this.unit;
        }
        params.statuses=this.status;

        if(this.dateRange){
          params.startDate=this.dateRange[0];
          params.endDate=this.dateRange[1];
        }
        params.name=this.searchInput;
        params.label={labels:this.dynamicTags};
        this.$http.post('danger/inspectPublic/find', params).then(function (res) {
          if (res.data.success) {
            this.tableData=res.data.data.list;
            this.totalItem=res.data.data.total;
            if(this.tableData){
              for(let i=0;i< this.tableData.length;i++){
                this.tableData[i].num=(this.currentPage-1)*10+i+1;
                this.tableData[i].createTime=this.transferTime(this.tableData[i].createTime);
                this.tableData[i].statusTagType=this.statusOptions[this.tableData[i].status].type;
                this.tableData[i].statusTag=this.statusOptions[this.tableData[i].status].label;
              }
            }else{
              this.tableData=[];
            }
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
        });
      },

      //----------------------------------检查表的查看，修改，记录和删除---------------------------------------
      itemViewClick:function (row) {
        this.$router.push({name:'viewInvestigation',params:{id:row.id,name:row.name}})
      },
      itemDeleteClick:function (row) {
        this.$confirm('此操作将删除该检查单, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$http.post('danger/inspectPublic/delete',{id:row.id}).then(function (res) {
            if(res.data.success){
              this.$message({
                showClose: true,
                message: '删除成功',
                type: 'success'
              });
              this.searchClick();
            }else{
              console.log('danger/safePlan/delete'+'数据申请失败');
            }
          }.bind(this)).catch(function (err) {
            console.log(err);
            this.$message({
              showClose: true,
              message: '删除失败',
              type: 'error'
            });
          }.bind(this));
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });
        });
      },

      //-----------------------------------筛选标签-----------------------------------------------
      remoteTag: function (val) {
        let params = new URLSearchParams;
        if (val !== null&&val!=='') {
          this.search.tagLoading = true
          params.append("label", val);
          params.append("pageSize", 20);
          params.append("pageCurrent", 1);
          params.append("companyId",this.$tool.getStorage('LOGIN_USER').companyId);
          this.$http.post('label/find', params).then(function (res) {
            if (res.data.success) {
              this.editTag(res.data.data.list);
            } else {
              this.tableTagOption = [];
            }
            this.search.tagLoading = false
          }.bind(this)).catch(function (err) {
            console.log(err);
          });
        }else {
          this.$store.dispatch("getTableLabels",this.dynamicTags)
        }
      },
      //选择标签
      labelSelectChange: function (label) {
        this.search.searchTag = label
        if(this.dynamicTags.length<5){
          if (label == '') {
            this.inputVisible=false
          }else{
            var index = this.dynamicTags.indexOf(label)
            if (index == -1) {
              this.dynamicTags.splice(this.dynamicTags.length, 0, label)
              this.search.searchTag=''
              this.inputVisible=false
            }else{
              this.search.searchTag=''
              this.inputVisible=false
            }
          }
        }else{
          this.$message.warning('标签数量已达上限');
          this.search.searchTag=''
          this.inputVisible=false
        }
      },
      //关闭标签
      handleClose(tag) {
        this.dynamicTags.splice(this.dynamicTags.indexOf(tag), 1);
      },
      //选择受检单位
      handlePickCompany:function () {
        this.unit=this.targetCompanyArray[this.targetCompanyArray.length-1];
      },

      parentCheck(){
        if(this.radioResponseType =='check' || this.viewRole){
          return false;
        }else{
          return true;
        }

      }

    }
  }
</script>
<style>
  .el-tag + .el-tag {
    margin-left: 10px;
  }
  .button-new-tag {
    margin-left: 10px;
    height: 32px;
    line-height: 30px;
    padding-top: 0;
    padding-bottom: 0;
  }
  .input-new-tag {
    width: 90px;
    margin-left: 10px;
    vertical-align: bottom;
  }
</style>
