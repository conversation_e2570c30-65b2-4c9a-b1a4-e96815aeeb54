<template>
  <div id="viewRelieve">
    <div class="background-style">
      <el-col :span="16" :offset="4" style="text-align: center;margin-bottom: 20px;margin-top:30px;letter-spacing: 2px;height: 50px;line-height: 50px">
        <h2>{{title}}</h2>
      </el-col>
      <el-col :span="16" :offset="4">
        <el-col :span="24" style="margin-top: 40px">
          <vue-editor v-model="content"></vue-editor>
        </el-col>
        <el-col :span="24" style="margin-top: 40px">
          <div style="float: right">
            <!--<el-button type="primary" v-show="isLeader" @click="releaseRelieve">签发</el-button>-->
            <el-button @click="$router.go(-1)">返回</el-button>
          </div>
        </el-col>
      </el-col>
    </div>
  </div>
</template>
<script>
  import { VueEditor } from 'vue2-editor'
  export default {
    name: 'viewRelieve',
    data() {
      return {
        title:'',
        content:'',
        isLeader:false,

        currentEmerId:'',//当前应急ID
      }
    },
    components: {
      VueEditor
    },
    created:function () {
      if(this.$route.params.title&&this.$route.params.content){
        this.title=this.$route.params.title;
        this.content=this.$route.params.content;
      }
    },
    watch:{
      $route(to, from){
        if((from.name==='emerResponse'||from.name==='emergencyProcess')&&this.$route.name==='viewRelieve'){
          this.clearContent();
          if(this.$route.params.title&&this.$route.params.content){
            this.title=this.$route.params.title;
            this.content=this.$route.params.content;
          }
        }
      },
    },
    methods:{
      releaseRelieve:function () {

      },
      clearContent:function () {
        this.title='';
        this.content='';
        this.isLeader=false;
      },
    }
  }
</script>
<style>
</style>
