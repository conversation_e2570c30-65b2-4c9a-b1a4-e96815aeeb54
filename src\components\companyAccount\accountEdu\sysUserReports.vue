<template>
  <div class="background-style" style="padding: 10px">
    <el-row style="margin:0">
      <el-col :span="6">
        <el-button type="primary" size="mini" @click="$router.back()">返回</el-button>
      </el-col>
    </el-row>
    <el-row style="margin:10px 0 0 0">
      <el-col :span="24">
        <egrid class="egrid"
               stripe border
               maxHeight="500"
               :data="egrid.data"
               :columns="egrid.columns"
               :columns-schema="egrid.columnsSchema">
        </egrid>
      </el-col>
    </el-row>
  </div>
</template>

<script>


  export default {
    data(){
      return {
        // 表格
        egrid : {
          data : [],
          columns : [
            { label: '姓名', prop: 'username' },
            { label: '性别', prop: 'gender' },
            { label: '出生年月', prop: 'birthday' },
            { label: '文化程度', prop: 'degreeOfEducation' },
            { label: '入职时间', prop: 'entryDate' },
            { label: '所在部门', prop: 'deptCode' },
            { label: '岗位(职务)', prop: 'duty' },
            { label: '备注', prop: 'remark' },
          ],
          // columnsProps 用于定义所有 columns 公共的属性
          columnsProps: {
            fit : true,
            sortable: true,
            align : 'center',
          },
          columnsSchema : {
            '姓名' : {
              width : 120
            },
            '性别' : {
              width : 50
            },
          },
        }
      }
    },
    created(){
      this.init();
    },
    watch:{
      $route(to,from){
        let data = to.params && to.params.row;
        if(to.name === 'sysUserReports') {
          if(data){
            this.searchBtnClickHandle();
          }
        }
      }
    },
    methods:{
      // 初始化
      init(){
        let data = this.$route.params.row;
        if(data){
          // 搜索
          this.searchBtnClickHandle();
        }
      },
      // 搜索按钮
      searchBtnClickHandle(){
        let data = this.$route.params.row.data;
        let list = data.map(function(it){
          return {
            username : it.username || '',
            gender : it.gender ? '男' : '女',
            birthday : (this.$tool.formatDateTime(it.birthday) || '').substring(0, 10),
            degreeOfEducation : it.degreeOfEducation || '',
            entryDate : (this.$tool.formatDateTime(it.entryDate) || '').substring(0, 10),
            deptCode : it.deptCode || '',
            duty : it.duty || '',
            remark : it.remark || '',
          }
        }.bind(this));
        this.egrid.data = list;
      },
    }
  }
</script>

<style>

</style>
