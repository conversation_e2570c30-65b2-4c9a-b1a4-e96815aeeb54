<template>
  <div id="EmerDuty">
    <div class="background-style">
      <!--日期范围和名称搜索-->
      <div style="width: 100%;padding: 10px 10px 0 10px;height: 40px">
        <div style="float: left;display: inline-block;">
          年份：
          <el-date-picker
            v-model="searchDate"
            type="year"
            style="width: 150px"
            @change="searchEmerDutyTable"
            placeholder="请选择年份">
          </el-date-picker>
          <el-button @click="searchEmerDutyTable" icon="el-icon-refresh">刷新</el-button>
        </div>
        <div style="float:right;margin-right: 30px">
          <el-button  type="primary" @click="findMyDutyTable">我的值班</el-button>
          <el-button  type="success" v-if="powerBtns.includes('emerCycleManage')" @click="openCycleManageDialog">周期管理</el-button>
          <el-button  type="warning" v-if="powerBtns.includes('emerTypeManage')" @click="openEmerTypeDialog">应急类型</el-button>
        </div>

      </div>
      <!--搜索结束-->
      <!--值班单列表-->
      <div style="padding: 10px">
        <el-table
          :data="emerDutyTable"
          border
          highlight-current-row
          style="width: 100%">
          <el-table-column
            type="index"
            width="100"
            align="center"
            label-class-name="header-style">
          </el-table-column>
          <el-table-column
            prop="name"
            label="应急值班类型"
            min-width="350"
            align="center"
            label-class-name="header-style">
          </el-table-column>
          <el-table-column
            prop="dayCount"
            label="值班天数"
            width="100"
            align="center"
            label-class-name="header-style">
          </el-table-column>
          <el-table-column
            prop="year"
            label="年份"
            width="100"
            align="center"
            label-class-name="header-style">
          </el-table-column>
          <el-table-column
            label="操作"
            width="170"
            align="center"
            label-class-name="header-style">
            <template slot-scope="scope">
              <el-button type="success" size="mini" @click="viewEmerDuty(scope.row)">详情</el-button>
              <el-button type="primary" size="mini" v-if="powerBtns.includes('emerDutyEdit')" @click="editEmerDuty(scope.row)">编辑</el-button>
            </template>
          </el-table-column>
        </el-table>

        <el-pagination
          background
          layout="prev, pager, next"
          @current-change="emerDutyPageClick"
          :total="emerDutyTotal">
        </el-pagination>
      </div>
      <!--值班单列表结束-->
      <!--应急类型管理对话框-->
      <el-dialog title="应急类型管理" :visible.sync="emerTypeVisible" append-to-body>
        <el-row>
          年份：
          <el-date-picker
            v-model="dutyTypeDate"
            type="year"
            style="width: 150px"
            @change="searchdutyTypeTable"
            placeholder="请选择年份">
          </el-date-picker>
          <el-button style="float: right;margin-right: 20px" type="primary" size="small" @click="addFlag=true">添加应急类型</el-button>
        </el-row>

        <el-dialog width="30%" title="添加应急类型" :visible.sync="addFlag" append-to-body>
          <el-form :model="addEmerDutyForm" label-width="100px" :rules="addEmerDutyRule" ref="addEmerDutyForm">
            <el-form-item label="年份" prop="year">
              <el-date-picker
                v-model="addEmerDutyForm.year"
                type="year"
                style="width: 200px"
                placeholder="请选择年份">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="类型名称" prop="addEmerDutyName">
              <el-input style="width: 200px;"  v-model="addEmerDutyForm.addEmerDutyName" placeholder="请输入类型名称"></el-input>
            </el-form-item>
            <el-form-item label="拼音缩写" prop="addEmerDutyTypeCode">
              <el-input style="width: 200px;"  v-model="addEmerDutyForm.addEmerDutyTypeCode" placeholder="请输入拼音缩写"></el-input>
            </el-form-item>
            <el-form-item label="值班天数" prop="addEmerDutyTypeDay">
              <el-input style="width: 200px;"  v-model="addEmerDutyForm.addEmerDutyTypeDay" placeholder="请输入天数"></el-input>
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button type="success" size="small" @click="dutyTypeAdd">确认</el-button>
            <el-button type="info" size="small" @click="addFlag=false">取消</el-button>
          </div>
        </el-dialog>

        <el-dialog width="30%" title="修改应急类型" :visible.sync="editFlag" append-to-body>
          <el-form :model="editEmerDutyForm" label-width="100px" :rules="editEmerDutyRule" ref="editEmerDutyForm">
            <el-form-item label="类型名称" prop="dutyTypeName">
              <el-input style="width: 200px;"  v-model="editEmerDutyForm.dutyTypeName" placeholder="请输入类型名称"></el-input>
            </el-form-item>
            <el-form-item label="值班天数" prop="dutyTypeDay">
              <el-input style="width: 200px;"  v-model="editEmerDutyForm.dutyTypeDay" placeholder="请输入天数"></el-input>
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button type="success" size="small" @click="dutyTypeUpdate">确认</el-button>
            <el-button type="info" size="small" @click="editFlag=false">取消</el-button>
          </div>
        </el-dialog>
        <el-row style="margin-top: 5px">
          <el-table :data="emerDutyTypeTable">
            <el-table-column type="index" label="序号" width="80"></el-table-column>
            <el-table-column label="类型" width="150" prop="name"></el-table-column>
            <el-table-column label="天数" width="100" prop="dayCount"></el-table-column>
            <el-table-column label="缩写" prop="codeNum" ></el-table-column>
            <el-table-column label="操作" width="170" fixed="right">
              <template slot-scope="scope">
                <el-button size="mini" type="primary" @click="editEmerDutyForm.id=scope.row.id;editEmerDutyForm.dutyTypeName=scope.row.name;editEmerDutyForm.dutyTypeDay=scope.row.dayCount;editFlag=true">修改</el-button>
                <el-button size="mini" type="danger" @click="dutyTypeDelete(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            background
            layout="prev, pager, next"
            @current-change="dutyTypePageClick"
            :total="dutyTypeTotal">
          </el-pagination>
        </el-row>
        <!--<el-row style="margin-top: 5px">-->
        <!--<el-pagination-->
        <!--background-->
        <!--layout="prev, pager, next"-->
        <!--:current-page="holidayTypeCurrentPage"-->
        <!--:total="holidayTypeTotal"-->
        <!--@current-change="holidayTypePageClick">-->
        <!--</el-pagination>-->
        <!--</el-row>-->
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="closeDutyTypeDialog">确 定</el-button>
        </div>
      </el-dialog>
      <!--值班类型管理对话框-->

      <!--填写值班记录-->
      <el-dialog title="我的值班" width="70%" :visible.sync="myDutyVisible">
        <el-table
          :data="myDutyTable"
          border
          style="width: 100%">
          <el-table-column
            type="index"
            align="center"
            label-class-name="inner-header-style"
            width="50">
          </el-table-column>
          <el-table-column
            label="应急类型"
            prop="dutyCodeName"
            label-class-name="inner-header-style"
            width="150">
          </el-table-column>
          <el-table-column
            label="值班日期"
            prop="dutyDate"
            :formatter="responseTimeFormat"
            label-class-name="inner-header-style"
            width="120">
          </el-table-column>
          <el-table-column
            label="值班周期"
            align="center"
            label-class-name="inner-header-style"
            width="150">
            <template slot-scope="scope">
              {{scope.row.startHour}}-{{scope.row.endHour}}
            </template>
          </el-table-column>
          <el-table-column
            label="值班记录"
            prop="dutyContent"
            label-class-name="inner-header-style"
            min-width="350">
          </el-table-column>
          <el-table-column
            label="值班地点"
            prop="dutyAddress"
            label-class-name="inner-header-style"
            min-width="350">
          </el-table-column>
          <el-table-column
            label="上报时间"
            prop="writeTime"
            :formatter="writeTimeFormat"
            label-class-name="inner-header-style"
            min-width="350">
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            label-class-name="inner-header-style"
            fixed="right"
            width="80">
            <template slot-scope="scope">
              <el-button type="text" size="small" v-show="!scope.row.hasReport" @click="editDutyForm.rowData=scope.row;editDutyForm.index=scope.$index;editDutyForm.dutyContent=scope.row.dutyContent;editDutyForm.dutyAddress=scope.row.dutyAddress || $tool.getStorage('LOGIN_USER').deptName;editDutyContentVisible=true;">填写记录</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-dialog title="填写值班记录" :visible.sync="editDutyContentVisible" append-to-body>
          <el-form :model="editDutyForm" label-width="100px" ref="editDutyForm">
            <el-form-item label="值班记录" prop="dutyContent">
              <el-input type="textarea" v-model="editDutyForm.dutyContent" placeholder="请输入值班记录"></el-input>
            </el-form-item>
            <el-form-item label="值班地点" prop="dutyAddress">
              <el-input type="text" clearable v-model="editDutyForm.dutyAddress" placeholder="请输入值班地点"></el-input>
            </el-form-item>
            <!--<el-form-item label="值班周期" prop="dutyCycleId">-->
              <!--<el-select v-model="editDutyForm.dutyCycleId" placeholder="请选择">-->
                <!--<el-option-->
                  <!--v-for="item in cycleOptions"-->
                  <!--:key="item.value"-->
                  <!--:label="item.label"-->
                  <!--:value="item.value">-->
                <!--</el-option>-->
              <!--</el-select>-->
            <!--</el-form-item>-->
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button type="success" size="small" @click="editDutyContentClick">确认</el-button>
            <el-button type="info" size="small" @click="editDutyContentVisible=false">取消</el-button>
          </div>
        </el-dialog>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="myDutyVisible = false">确 定</el-button>
        </div>
      </el-dialog>
      <!--填写值班记录结束-->

      <!--周期管理对话框-->
      <el-dialog title="周期管理" :visible.sync="cycleManageVisible" append-to-body>
        <el-row>
          <el-button type="primary" size="small" @click="addCycleFlag=true">添加周期</el-button>
        </el-row>

        <el-dialog width="50%" title="添加周期" :visible.sync="addCycleFlag" append-to-body>
          <el-form :model="addCycleForm" label-width="100px" ref="addCycleForm">
            <el-form-item label="开始时间" prop="startHour">
              <el-time-picker
                arrow-control
                v-model="addCycleForm.startHour"
                placeholder="选择时间">
              </el-time-picker>
            </el-form-item>
            <el-form-item label="结束时间" prop="endHour">
              <el-time-picker
                arrow-control
                v-model="addCycleForm.endHour"
                placeholder="选择时间">
              </el-time-picker>
            </el-form-item>
            <el-form-item label="备注" prop="remark">
              <el-input style="width: 350px;"  v-model="addCycleForm.remark"></el-input>
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button type="success" size="small" @click="cycleAdd">确认</el-button>
            <el-button type="info" size="small" @click="addCycleFlag=false">取消</el-button>
          </div>
        </el-dialog>

        <el-row style="margin-top: 5px">
          <el-table :data="cycleTable">
            <el-table-column type="index" label="序号" width="80"></el-table-column>
            <el-table-column label="开始时间" width="100" prop="startHour"></el-table-column>
            <el-table-column label="结束时间" width="100" prop="endHour"></el-table-column>
            <el-table-column label="备注" prop="remark"></el-table-column>
            <el-table-column label="操作" width="170" fixed="right">
              <template slot-scope="scope">
                <el-button size="mini" type="danger" @click="cycleDelete(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-row>
        <!--<el-row style="margin-top: 5px">-->
        <!--<el-pagination-->
        <!--background-->
        <!--layout="prev, pager, next"-->
        <!--:current-page="holidayTypeCurrentPage"-->
        <!--:total="holidayTypeTotal"-->
        <!--@current-change="holidayTypePageClick">-->
        <!--</el-pagination>-->
        <!--</el-row>-->
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="cycleManageVisible = false">确 定</el-button>
        </div>
      </el-dialog>
      <!--周期管理对话框结束-->

    </div>
  </div>
</template>

<script>
    export default {
        name: "EmerDuty",//该文件中的周期就是时间段
      data(){
        return {
          //搜索按钮部分
          powerBtns:[],//权限按钮
          searchDate:'',

          //列表部分
          emerDutyTable:[],
          emerDutyTotal:0,//列表总数
          currentPage:1,

          //应急类型对话框
          emerTypeVisible:false,//应急值班类型
          dutyTypeDate:'',
          addFlag:false,
          editFlag:false,
          addEmerDutyForm:{
            year:'',
            addEmerDutyName:'',
            addEmerDutyTypeCode:'',
            addEmerDutyTypeDay:''
          },
          addEmerDutyRule:{
            addEmerDutyTypeName:[{ required: true, message: '请输入值班类型名称', trigger: 'change'}],
            addEmerDutyTypeCode:[{ required: true, message: '请输入拼音缩写', trigger: 'change'}],
            addEmerDutyTypeDay:[{ required: true, message: '请输入天数', trigger: 'change'}],
          },
          editEmerDutyForm:{
            dutyTypeName:'',
            dutyTypeDay:''
          },
          editEmerDutyRule:{
            dutyTypeName:[{ required: true, message: '请输入值班类型名称', trigger: 'change'}],
            dutyTypeDay:[{ required: true, message: '请输入天数', trigger: 'change'}],
          },
          emerDutyTypeTable:[],
          dutyTypeTotal:0,
          dutyTypeCurrentPage:1,

          //我的值班对话框
          myDutyVisible:false,
          myDutyTable:[],
          editDutyContentVisible:false,
          editDutyForm:{
            index:'',
            rowData:{},
            dutyContent:'',
            dutyAddress : ''
          },
          cycleOptions:[],

          //周期管理对话框
          cycleManageVisible:false,//周期管理
          cycleTable:[],
          addCycleFlag:false,
          addCycleForm:{
            startHour:'',
            endHour:'',
            remark:''
          },

        }
      },
      mounted:function (){
        this.powerBtns = this.$tool.getPowerBtns2URL('emerMenu', '/emer-menu/holiday-duty',this.$route.path);
        this.searchDate=new Date();
        this.dutyTypeDate=new Date();
        this.addEmerDutyForm.year=new Date();
        this.searchEmerDutyTable();
        this.cycleTabelFind();
      },
      watch:{
        $route(to, from) {
          if (this.$route.name === 'emerDuty') {
            this.searchEmerDutyTable();
            this.cycleTabelFind();
          }
        }
      },
      methods:{
        searchEmerDutyTable:function () {
          this.emerDutyTable=[];
          let params=new URLSearchParams;
          params.append("year",this.searchDate.getFullYear());
          params.append("pageCurrent",this.currentPage);
          this.$http.post('sysEmgDutyType/find',params).then(function (res) {
            if(res.data.success){
              this.emerDutyTable=res.data.data.list;
              this.emerDutyTotal=res.data.data.total;
            }
          }.bind(this)).catch(function (err) {
            console.log('sysEmgDutyType/find',err);
            this.$message.error('请尝试重登录或检查网络连接');
          }.bind(this));
        },


        //列表功能
        emerDutyPageClick:function(val){
          this.currentPage=val;
          this.searchEmerDutyTable();
        },
        responseTimeFormat:function (row) {
          return this.transferTime(row.dutyDate);
        },
        viewEmerDuty:function (row) {
          this.$router.push({name:'editEmerDuty',params:{id:row.id,title:row.name,dutyDayCount:row.dayCount,year:row.year,dutyTypCode:row.codeNum,activeTabName:'view'}});

        },
        editEmerDuty:function (row) {
          this.$router.push({name:'editEmerDuty',params:{id:row.id,title:row.name,dutyDayCount:row.dayCount,year:row.year,dutyTypCode:row.codeNum,activeTabName:'edit'}});

        },

        //应急类型对话框
        openEmerTypeDialog:function(){
          this.searchdutyTypeTable();
          this.emerTypeVisible=true;
        },
        searchdutyTypeTable:function () {
          this.emerDutyTypeTable=[];
          let params=new URLSearchParams;
          params.append("year",this.dutyTypeDate.getFullYear());
          params.append("pageCurrent",this.dutyTypeCurrentPage);
          this.$http.post('sysEmgDutyType/find',params).then(function (res) {
            if(res.data.success){
              this.emerDutyTypeTable=res.data.data.list;
              this.emerDutyTypeTable.forEach(function (item) {
                item.nameTemp=item.name;
                item.dayCountTemp=item.dayCount;
                item.viewFlag=true;
              });
              this.dutyTypeTotal=res.data.data.total;
            }
          }.bind(this)).catch(function (err) {
            console.log('sysEmgDutyType/find',err);
            this.$message.error('请尝试重登录或检查网络连接');
          }.bind(this));
        },
        dutyTypePageClick:function(val){
          this.dutyTypeCurrentPage=val;
          this.searchdutyTypeTable();
        },
        dutyTypeAdd:function () {
          let params=new URLSearchParams;
          params.append("year",this.addEmerDutyForm.year.getFullYear());
          params.append("name",this.addEmerDutyForm.addEmerDutyName);
          params.append("codeNum",this.addEmerDutyForm.addEmerDutyTypeCode);
          params.append("dayCount",this.addEmerDutyForm.addEmerDutyTypeDay);
          this.$http.post('sysEmgDutyType/add',params).then(function (res) {
            if(res.data.success){
              this.$message.success('添加成功！');
              this.dutyTypePageClick(1);
              this.searchEmerDutyTable();
              this.addFlag=false;
              this.addEmerDutyForm.year=new Date();
            }
          }.bind(this)).catch(function (err) {
            console.log('sysEmgDutyType/add',err);
            this.$message.error('请尝试重登录或检查网络连接');
          }.bind(this));
        },
        dutyTypeUpdate:function () {
          let params=new URLSearchParams;
          params.append("id",this.editEmerDutyForm.id);
          params.append("name",this.editEmerDutyForm.dutyTypeName);
          params.append("dayCount",this.editEmerDutyForm.dutyTypeDay);
          this.$http.post('sysEmgDutyType/update',params).then(function (res) {
            if(res.data.success){
              this.$message.success('修改成功！');
              this.dutyTypePageClick(1);
              this.searchEmerDutyTable();
              this.editFlag=false;
            }
          }.bind(this)).catch(function (err) {
            console.log('sysEmgDutyType/update',err);
            this.$message.error('请尝试重登录或检查网络连接');
          }.bind(this));
        },
        dutyTypeDelete:function (row) {
          this.$confirm('此操作将删除应急值班类型, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.$http.post('sysEmgDutyType/delete?id='+row.id).then(function (res) {
              if(res.data.success){
                this.$message.success('删除成功！');
                this.dutyTypePageClick(1);
                this.searchEmerDutyTable();
              }
            }.bind(this)).catch(function (err) {
              console.log('sysDutyPublic/delete',err);
              this.$message.error('删除失败');
            }.bind(this));
          }).catch(() => {
            this.$message({
              type: 'info',
              message: '已取消删除'
            });
          });
        },
        closeDutyTypeDialog:function(){
          //this.searchEmerDutyTable();
          this.emerTypeVisible = false;
        },

        //我的值班对话框
        findMyDutyTable:function () {
          this.myDutyTable=[];
          let params=new URLSearchParams;
          let currentDate=new Date();
          params.append("year",currentDate.getFullYear());
          params.append("pageSize",50);
          this.$http.post('sysEmgDuty/findMyDuty',params).then(function (res) {
            if(res.data.success){
              this.myDutyTable=res.data.data.list;
              this.myDutyVisible=true;
            }
          }.bind(this)).catch(function (err) {
            console.log('sysEmgDuty/findMyDuty',err);
            this.$message.error('请尝试重登录或检查网络连接');
          }.bind(this));
        },
        writeTimeFormat:function (row) {
          return this.transferTime(row.writeTime,null,true);
        },
        editDutyContentClick:function () {
          let params = new URLSearchParams;
          params.append("id",this.editDutyForm.rowData.id);
          params.append("dutyContent",this.editDutyForm.dutyContent);
          params.append("dutyAddress",this.editDutyForm.dutyAddress);
          this.$http.post('sysEmgDuty/update', params).then(function (res) {
            if (res.data.success) {
              this.findMyDutyTable();
              this.editDutyContentVisible=false;
            }
          }.bind(this)).catch(function (err) {
            console.log(err);
          }.bind(this));
        },

        //周期管理对话框
        cycleTabelFind:function(){
          this.cycleOptions.splice(0);
          let params=new URLSearchParams;
          params.append("pageSize",50);
          this.$http.post('sysEmgDutyCycle/find ',params).then(function (res) {
            if(res.data.success){
              this.cycleTable=res.data.data.list;
              this.cycleTable.forEach(function (item) {
                this.cycleOptions.push({value:item.id,label:item.startHour+'-'+item.endHour});
              }.bind(this));
            }
          }.bind(this)).catch(function (err) {
            console.log('sysEmgDutyCycle/find ',err);
            this.$message.error('请尝试重登录或检查网络连接');
          }.bind(this));
        },
        openCycleManageDialog:function(){
          this.cycleTabelFind();
          this.cycleManageVisible=true;
        },
        cycleAdd:function () {
          let params=new URLSearchParams;
          params.append("startHour",this.addCycleForm.startHour.getHours()+':'+this.timeAddZero(this.addCycleForm.startHour.getMinutes()));
          params.append("endHour",this.addCycleForm.endHour.getHours()+':'+this.timeAddZero(this.addCycleForm.endHour.getMinutes()));
          params.append("remark",this.addCycleForm.remark);
          this.$http.post('sysEmgDutyCycle/add ',params).then(function (res) {
            if(res.data.success){
              this.cycleTabelFind();
              this.addCycleFlag=false;
            }
          }.bind(this)).catch(function (err) {
            console.log('sysEmgDutyCycle/find ',err);
            this.$message.error('请尝试重登录或检查网络连接');
          }.bind(this));
        },
        cycleDelete:function (row) {
          let params=new URLSearchParams;
          params.append("id",row.id);
          this.$http.post('sysEmgDutyCycle/checkInUse',params).then(function (res) {
            if(res.data.success){
              if(res.data.data){
                this.$confirm('该值班周期正在被使用, 是否确认删除?', '提示', {
                  confirmButtonText: '确定',
                  cancelButtonText: '取消',
                  type: 'warning'
                }).then(() => {
                  this.doCycleDelete(row);
                }).catch(() => {
                  this.$message({
                    type: 'info',
                    message: '已取消删除'
                  });
                });
              }else{
                this.doCycleDelete(row);
              }
            }
          }.bind(this)).catch(function (err) {
            console.log('sysEmgDutyCycle/checkInUse',err);
            this.$message.error('请尝试重登录或检查网络连接');
          }.bind(this));


        },
        doCycleDelete:function (row) {
          this.$confirm('此操作将删除该值班周期, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.$http.post('sysEmgDutyCycle/delete?id='+row.id).then(function (res) {
              if(res.data.success){
                this.$message.success('删除成功！');
                this.cycleTabelFind();
              }
            }.bind(this)).catch(function (err) {
              console.log('sysEmgDutyCycle/delete',err);
              this.$message.error('删除失败');
            }.bind(this));
          }).catch(() => {
            this.$message({
              type: 'info',
              message: '已取消删除'
            });
          });
        }

      }

    }
</script>

<style scoped>

</style>
