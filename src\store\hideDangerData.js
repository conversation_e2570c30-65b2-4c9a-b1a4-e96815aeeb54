/*
  created by m<PERSON><PERSON><PERSON> on 2018-4-12
  common data in hide danger
*/
import http from '../../src/assets/functions/axiosServer'
import tool from '../../src/components/common/tool'
import dealData from '../../src/assets/functions/dealData'
// 应急救援
export default {
  state: {
    //检查表的标签
    tableLabels:[],
    //受检单位（只有子公司,树形）
    targetDept:[],

    //检查表的标签，树形的
    tableTreeLabels:[],
    //隐患类型
    dangerType:[],
  },
  getters: {},
  mutations: {
    getTableLabels(state,msg){
      state.tableLabels=msg;
    },
    getTargetDept(state,msg){
      state.targetDept=msg;
    },
    getTableTreeLabels(state,msg){
      state.tableTreeLabels=msg;
    },
    getChildrenCompanyList(state,msg) {
      state.childrenCompanyList=msg;
    },
    getDangerTypeByCompanyId(state,msg){
      state.dangerType=msg;
    },
    },
  actions : {
    getTableLabels:function ({commit},label) {
      let paramsStr='label/inspectLabels?searchLabels='+label;
      if(label.length){//标签搜索个数超过1，label不能为空
        paramsStr+='&label='+label[label.length-1];
      }
      paramsStr+='&companyId='+tool.getStorage('LOGIN_USER').companyId;
      http.get(paramsStr).then(function (res) {
        if(res.data.success){
          let tempList=[];
          for(let i=0;i<res.data.data.length;i++){
            tempList.push({value:res.data.data[i].label,label:res.data.data[i].label});
          }
          commit('getTableLabels',tempList);
        }
      }.bind(this)).catch(function (err) {
        console.log('获取检查表标签时的错误:'+err);
      }.bind(this));
    },
    // START YANG
    // 添加检测表
    dangerInspectAdd({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('danger/inspect/add', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 更新检测表
    dangerInspectUpdate({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('danger/inspect/update', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 删除检测表
    dangerInspectDelete({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('danger/inspect/delete', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 获取检测表
    dangerInspectFind({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('danger/inspect/find', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 获取检测表的知识点---获取检测表ID来获取
    dangerInspectListFind({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('danger/inspectList/find', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 删除检测表的知识点---通过ID来删除
    dangerInspectListDelete({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('danger/inspectList/delete', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 添加检测表的知识点
    dangerInspectListAdd({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('danger/inspectList/add', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // EDN YANG
    //fan edit
    getTargetDept({commit}){//获取当前公司的所有子公司（列表第一级是当前公司）
      http.post('dept/getOrgDept').then(function (res) {
        if(res.data.success){
          // console.log(dealData.editChildrenCompany(res.data.data))
          commit('getTargetDept',dealData.editChildrenCompany(res.data.data));
        }
      }.bind(this)).catch(function (err) {
        console.log('获取受检单位时的错误:'+err);
      }.bind(this));
    },
    //检查表的标签，树形的
    getTableTreeLabels:function ({commit},companyId) {
      http.get('danger/inspectTableLabel/getLabelStructure?companyId='+companyId).then(function (res) {
        if (res.data.success) {
          commit('getTableTreeLabels',res.data.data);
        }
      }.bind(this)).catch(function (err) {
        console.log('获取树形检查表标签时的错误:'+err);
      }.bind(this));
    },
    //获取本公司的隐患类型
    getDangerTypeByCompanyId:function ({commit},companyId) {
      http.post('danger/inspectListType/find?companyId='+companyId).then(function (res) {
        if (res.data.success) {
          commit('getDangerTypeByCompanyId',res.data.data);
        }
      }.bind(this)).catch(function (err) {
        console.log('danger/inspectListType/find:'+err);
      }.bind(this));
    }
  }
};
