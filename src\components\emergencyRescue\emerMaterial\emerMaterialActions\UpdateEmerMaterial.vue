<template>
  <div id="updateEmerMaterial">
    <div class="background-style">
      <el-col :span="16" :offset="4" class="warning-background-title">修改物资</el-col>
      <el-col :span="16" :offset="4">
        <el-form :model="form" :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm">
          <el-col :span="24">
            <el-col :span="12">
              <el-form-item label="所属部门：" prop="department">
                <el-select v-model="form.department" placeholder="请选择" style="width: 100%">
                  <el-option
                    v-for="item in departmentOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="存放地点：" prop="place">
                <el-select v-model="form.place" placeholder="请选择" style="width: 100%">
                  <el-option
                    v-for="item in placeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-col>
          <el-col :span="24">
            <el-col :span="12">
              <el-form-item label="物资名称：" prop="name">
                <el-input v-model="form.name"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="物资类型：" prop="type">
                <el-select v-model="form.type" placeholder="请选择" style="width: 100%">
                  <el-option
                    v-for="item in typeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-col>
          <el-col :span="24">
            <el-col :span="12">
              <el-form-item label="数量：" prop="num">
                <el-input v-model="form.num" type="number"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="单位：" prop="unit">
                <el-input v-model="form.unit"></el-input>
              </el-form-item>
            </el-col>
          </el-col>
          <el-col :span="24">
            <el-col :span="12">
              <el-form-item label="管理员：" prop="manager">
                <el-input v-model="form.manager"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="联系方式：" prop="contactInfo">
                <el-input v-model="form.contactInfo"></el-input>
              </el-form-item>
            </el-col>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注：" prop="other">
              <el-input type="textarea" :autosize="{ minRows: 3}" v-model="form.other"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-button style="float: right;margin-left: 20px" @click="returnClick()">返回</el-button>
            <el-button type="primary" style="float: right" @click="saveClick()">保存</el-button>
          </el-col>
        </el-form>
      </el-col>
    </div>
    <new-emer-goods></new-emer-goods>
  </div>
</template>
<script>
  import newEmerGoods from './NewEmerMaterial'
  export default {
    name: 'updateEmerMaterial',
    data() {
      return {
        form:{
          department:'',
          place:'',
          name:'',
          type:'',
          num:'',
          unit:'',
          manager:'',
          contactInfo:'',
          other:''
        },
        rules:{
          department:[{required:true,message:'请选择所属部门',trigger:'change'}],
          place:[{required:true,message:'请选择存放地点',trigger:'change'}],
          name:[{required:true,message:'请选择输入物品名称',trigger:'change'}],
          type:[{required:true,message:'请选择选择物资类型',trigger:'change'}],
          num:[{required:true,message:'请选择输入物资数量',trigger:'change'}],
          manager:[{required:true,message:'请选择输入管理员姓名',trigger:'change'}],
          contactInfo:[{required:true,message:'请选择输入联系方式',trigger:'change'}]
        },
        departmentOptions:[
          {value:'',label:''}
        ],
        placeOptions:[
          {value:'',label:''}
        ],
        typeOptions:[
          {value:'',label:''}
        ],
      }
    },
    components:{newEmerGoods},
    methods:{
      returnClick:function () {
        this.$refs['ruleForm'].resetFields();
        this.$router.go(-1);
      },
      saveClick:function () {
        this.$refs['ruleForm'].validate((valid) => {
          if (valid) {

            alert('submit!');
          } else {
            console.log('error submit!!');
            return false;
          }
        });
      }
    }
  }
</script>
<style>
</style>
