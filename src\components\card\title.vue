<template>
  <div class="title-box">
    <span class="bullet"></span>
    {{ title }}
  </div>
</template>

<script>
  export default {
    props: {
      title: {
        type: String,
        required: true,
      },
    },
  };
</script>
<style   scoped>
  .title-box {
    margin-top: 30px;
    margin-bottom: 15px;
    font-size: 20px;
    font-weight: 600;
    line-height: 1;
    font-family: "micorsfot yahei";
    padding: 0;
    color: #333333;
    display: flex;
    align-items: center;
  }
  .bullet {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #3197fb;
    margin-right: 12px;
    font-size: 20px;
    font-weight: 600;
  }
</style>
