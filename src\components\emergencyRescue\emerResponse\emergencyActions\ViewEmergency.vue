<template>
  <div id="viewEmergency">
    <div class="background-style">
      <el-col :span="16" :offset="4" class="success-background-title">查看应急响应</el-col>
      <el-col :span="16" :offset="4">
        <el-form :model="form" ref="ruleForm" label-width="120px" class="demo-ruleForm">
        <el-col :span="24">
          <el-form-item label="响应名称：" prop="emergencyName">
            <el-input v-model="form.emergencyName"></el-input>
          </el-form-item>
        </el-col>
          <el-col :span="24">
            <el-form-item label="应急启动：" prop="beginMessage">
              <el-input type="textarea" :autosize="{ minRows: 3}" v-model="form.beginMessage"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="预警信号：" prop="emergencyFlag">
              <el-input v-model="form.emergencyFlag" readonly="readonly"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="预警信息：" prop="emerSituation">
              <el-input type="textarea" :autosize="{ minRows: 3}" v-model="form.emerSituation"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="应急响应要求：" prop="emerRequire">
              <el-input type="textarea" :autosize="{ minRows: 3}" v-model="form.emerRequire"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="值班安排：">
              <el-table
                :data="form.dutyTable"
                border
                style="width: 100%">
                <el-table-column
                  prop="dutyDate"
                  label="值班日期"
                  align="center"
                  label-class-name="inner-header-style"
                  width="120">
                </el-table-column>
                <el-table-column
                  prop="dutyPerson"
                  label="值班人"
                  align="center"
                  label-class-name="inner-header-style"
                  width="120">
                </el-table-column>
                <el-table-column
                  prop="company"
                  label="公司"
                  label-class-name="inner-header-style"
                  min-width="300">
                </el-table-column>
                <el-table-column
                  prop="phoneNumber"
                  label="手机长号"
                  align="center"
                  label-class-name="inner-header-style"
                  width="130">
                </el-table-column>
                <el-table-column
                  prop="shortNumber"
                  label="手机短号"
                  align="center"
                  label-class-name="inner-header-style"
                  width="120">
                </el-table-column>
              </el-table>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-col :span="12">
              <el-form-item label="审核人：" prop="signerUserName">
                <el-input v-model="form.signerUserName" readonly="readonly"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="发布人：" prop="createUserName">
                <el-input v-model="form.createUserName" readonly="readonly"></el-input>
              </el-form-item>
            </el-col>
          </el-col>
          <el-col :span="24">
            <el-form-item label="审核意见：" prop="examine">
              <div  v-if="isLeader">
                <el-dropdown @command="editExamine">
                  <el-button type="primary" size="small">
                    审核参考<i class="el-icon-arrow-down el-icon--right"></i>
                  </el-button>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item v-for="item in selectOptions" :key="item.id" :command="item.content">{{item.name}}</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
                <el-input type="textarea" :autosize="{ minRows: 3}" v-model="form.examine"></el-input>
              </div>
              <div v-else>
                <el-input type="textarea" :autosize="{ minRows: 3}" v-model="form.examine" readonly="readonly"></el-input>
              </div>
             </el-form-item>
          </el-col>
          <el-col :span="24" style="margin-top: 10px">
            <el-form-item>
              <el-button style="float: right;margin-left: 20px" @click="returnClick()">返回</el-button>
            </el-form-item>
          </el-col>
        </el-form>
      </el-col>
    </div>
  </div>
</template>
<script>
  import {mapGetters} from 'vuex'
  export default {
    name: 'viewEmergency',
    data() {
      return {
        //表单数据
        form:{
          emergencyName:'',
          beginMessage:'',
          emergencyFlag:'',
          emerSituation:'',
          emerRequire:'',
          dutyTable:[],
          examine:''
        },
        //参考审核数据
        selectOptions:[
          {id:'examine01',name:'同意签发',content:'经审核，同意签发该应急响应。'},
          {id:'examine02',name:'退回修改',content:'经审核，该应急响应内容有待修改，修改意见如下：'},
          {id:'examine03',name:'建议删除',content:'经审核，该应急响应必要性较低，建议删除。'}
        ],
        //暂存数据
        currentEmerId:'',
        currentStatus:'',
        startPlanPublicId:'',
        //身份判断
        isLeader:false,
      }
    },
    created:function () {
      if(this.$route.params.emergencyId){
        this.currentEmerId=this.$route.params.emergencyId;
        this.searchEmerById();
      }
    },
    watch:{
      $route(to, from){
        if(this.$route.name==='viewEmergency'){
          if(this.$route.params.emergencyId){
            this.currentEmerId=this.$route.params.emergencyId;
            this.searchEmerById();
          }
        }
      }
    },
    methods:{
      searchEmerById:function () {
        let params=new URLSearchParams;
        params.append("id",this.currentEmerId);
        this.$http.post('planPublic/find',params).then(function (res) {
          if(res.data.data){
            this.editEmerForm(res.data.data.list[0]);
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message({
            showClose: true,
            message: '网络错误，请尝试重登录',
            type: 'error'
          });
        }.bind(this));
      },
      editEmerForm:function (val) {
        this.form.emergencyName=val.name;
        this.form.beginMessage=val.startInfo;
        this.form.emergencyFlag=val.warnSignal;
        this.form.emerSituation=val.warnSituation;
        this.form.emerRequire=val.startupRequire;
        this.form.examine=val.auditOpinion;
        this.form.signerUserName=val.signerUserName;
        this.form.createUserName=val.createUserName;
        this.startPlanPublicId=val.startPlanId;
        this.currentStatus=Number(val.status);
        this.isLeader=false;//不进行身份判断，只是显示
//        if(this.currentStatus===1||this.currentStatus===5||this.currentStatus===9){
//          if(val.signerUserId===this.$tool.getStorage('LOGIN_USER').userId){
//            this.isLeader=true;
//          }
//        }
        this.form.dutyTable=[];
        if(val.emgDuties.length){
          for(let i=0;i<val.emgDuties.length;i++){
            this.form.dutyTable.push({dutyDate:this.transferTime(val.emgDuties[i].dutyDate),dutyDateTemp:val.emgDuties[i].dutyDate,dutyPerson:val.emgDuties[i].name,phoneNumber:val.emgDuties[i].phone,shortNumber:val.emgDuties[i].shortPhone});
          }
        }
      },
      //填写审核意见
      editExamine:function (content) {
        this.form.examine=content;
      },


      //发出通知

      //返回
      returnClick:function () {
        this.$refs['ruleForm'].resetFields();
        this.$router.go(-1);
      },
    }
  }
</script>
<style>
</style>
