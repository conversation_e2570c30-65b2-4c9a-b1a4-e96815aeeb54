<template>
  <div id="">
    <el-container class="container">
      <el-main>
        <el-form ref="form" :model="form" label-width="5px">
          <el-row>
            <el-col :span="24">
              <h3>员工积分排行榜</h3>
            </el-col>
          </el-row>
          <el-row>
            <el-table
              border
              :data="tableData.list"
              style="width: 100%">
              <el-table-column
                type="index"
                label="编号"
                width="100"
                align="center"
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="userName"
                label="姓名"
                min-width="150"
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="integral"
                label="积分"
                show-overflow-tooltip
                width="200"
                label-class-name="header-style">
              </el-table-column>
            </el-table>
            <el-pagination
              background
              layout="prev, pager, next"
              :current-page="tableData.pageNum"
              :page-size="form.pageSize"
              :total="tableData.total"
              @current-change ="disasterPageChangeHandle">
            </el-pagination>
          </el-row>
        </el-form>
      </el-main>
    </el-container>
  </div>
</template>
<script>
  export default {
    name: '',
    data() {
      return {
        // 搜索
        form : {
          // 当前页
          pageCurrent : 1,
          // 页数大小
          pageSize : 10,
        },
        assist:{
          // 类型
          newsType : [
            { value : '党建巡礼', label : '党建巡礼' },
            { value : '廉洁教育', label : '廉洁教育' },
            { value : '理论学习', label : '理论学习' },
            { value : '党务学习', label : '党务学习' },
          ],
        },
        tableData : {},
        // 角色 0 组织者或公司      1 部门        2  班组
        role : 0,
      }
    },
    mounted(){
      this.init();
    },
    watch:{
      $route(to,from){
        if(to.name === 'safetyEducationScoreSortIndex') {
          this.init();
        }
      }
    },
    methods:{
      // 初始化
      init(){
        this.judgeUserRole();
        // 搜索
        this.searchBtnClickHandle();
      },
      judgeUserRole(){
        // 获取权限按钮
        let btns = this.$tool.getPowerBtns('eduTrainingMenu', this.$route.path);
//        console.log('btns', btns)
        // 公司
        if(btns.includes('addBtn')){
          this.role = 4;
        }
      },
      // 清空数据
      clear(){

      },
      // 格式化时间
      formatDateTime(row, column, cellValue){
        let pro = column.property;
        let num = 10;
        let str = this.$tool.formatDateTime(row[pro]) || '';
        return str ? str.substring(0, num) : str;
      },
      // 分页
      disasterPageChangeHandle(page){
        this.form.pageCurrent = page;
        if(this.isMore){
          this.saveScoreBtnClickHandle();
        } else {
          this.searchBtnClickHandle();
        }
      },
      // 搜索按钮
      searchBtnClickHandle(){
        let params = Object.assign({}, this.form);
        params = this.$tool.filterObj({}, params)
        this.$store.dispatch('eduUserStudyGetUserRankScore', params).then(function(res){
          if(res.success){
            this.tableData = res.data;
          } else {
            this.$message({
              type : 'error',
              message : res.message || '错误'
            })
          }
        }.bind(this));
      },
    }
  }
</script>
<style>
  .container{
    background:#fff;
    padding:0 20px;
  }
  .row{
    margin-top:10px;
  }
</style>
