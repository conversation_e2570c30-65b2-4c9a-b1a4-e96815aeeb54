<template>
  <div id="costBudgetPrintOne">
    <div class="background-style">
      <!--表格区-->
      <div style="width: 100%;">
        <div style="padding: 10px 10px 10px 10px">
          <el-table
            row-key="id"
            border
            default-expand-all
            show-summary
            :data="tableData">
           <!-- :default-expand-all="true"
            :expand-row-keys="expandRowKeys"
            :tree-props="{children: 'children'}"-->

           <!-- <el-table-column type="expand">
              <template slot-scope="props">
                <el-table
                  :show-header="false"
                  :data="props.row.children">
                  <el-table-column
                    prop="item"
                    min-width="240"
                    show-overflow-tooltip
                    align="left"
                    label-class-name="header-style">
                  </el-table-column>
                  <el-table-column
                    prop="itemCurrentBudget"
                    width="100"
                    align="center"
                    label-class-name="header-style">
                  </el-table-column>
                  <el-table-column
                    prop="deptName"
                    width="100"
                    align="center"
                    label-class-name="header-style">
                  </el-table-column>
                </el-table>
                &lt;!&ndash;<el-form label-position="left" inline class="demo-table-expand">
                  <el-form-item label="商品名称：">
                    <span>{{ props.row.item }}</span>
                  </el-form-item>
                </el-form>&ndash;&gt;
              </template>
            </el-table-column>-->
            <el-table-column
              prop="item"
              label="预算项目类别"
              min-width="240"
              show-overflow-tooltip
              align="left"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="itemCurrentBudget"
              label="金额"
              width="100"
              align="center"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="deptName"
              label="部门"
              width="100"
              align="center"
              label-class-name="header-style">
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
    export default {
      data(){
        let that = this;
        return {
          // 需要展开的key数组
          expandRowKeys : [],
          // 路由信息
          routeInfo : {},
          // 表格数据
          tableData : [],
        }
      },

      mounted(){
        this.init();
      },
      watch:{
        // 监听路由
        $route(to,from){
          if(to.name === 'costBudgetPrintOne') {
            this.init();
          }
          if (from.name == 'costBudgetIndex'){

          }
        },
      },
      methods:{

        // 初始化
        init(){
          // 获取路由信息
          this.routeInfo = this.$route.params;
          console.log("routeInfo = ", this.routeInfo)
          // 搜索
          this.searchBtnClickHandle();
        },
        clear(){
//          this.dialog.form = this.$tool.clearObj({}, this.dialog.form);
        },
        // 获取九大类和子类的信息
        filterNineTypeAndSubTypeInfo2(parentArr){
          let result = [];
          for(var i = 0; i < parentArr.length; i++){
            let row = {};
            row['id'] = parentArr[i].id;
            row['item'] = parentArr[i].item;
            row['itemCurrentBudget'] = parentArr[i].itemCurrentBudget;
            row['deptName'] = parentArr[i].deptName || '';
            //row['children'] = [];
            result.push(row)
            let costBudgetSubItems = parentArr[i].costBudgetSubItems;
            for(var j = 0; j < costBudgetSubItems.length;j++){
              let subRow = {}
              subRow['id'] = costBudgetSubItems[j].id;
              subRow['item'] = costBudgetSubItems[j].item;
              subRow['itemCurrentBudget'] = costBudgetSubItems.itemCurrentBudget;
              subRow['deptName'] = costBudgetSubItems[j].deptName;
//              row['children'].push(subRow)
              result.push(row)
            }
            //result.push(row)
          }
          return result || [];
        },
        filterNineTypeAndSubTypeInfo(parentArr){
          let result = [];
          this.expandRowKeys = [];
          for(var i = 0; i < parentArr.length; i++){
            let row = {};
            row['id'] = parentArr[i].id;
            this.expandRowKeys.push(parentArr[i].id)
            row['item'] = parentArr[i].item;
            row['itemCurrentBudget'] = parentArr[i].itemCurrentBudget;
            row['deptName'] = parentArr[i].deptName || '';
            row['children'] = [];
            let costBudgetSubItems = parentArr[i].costBudgetSubItems;
            for(var j = 0; j < costBudgetSubItems.length;j++){
              let subRow = {}
              subRow['id'] = costBudgetSubItems[j].id;
              subRow['item'] = costBudgetSubItems[j].item;
              subRow['itemCurrentBudget'] = costBudgetSubItems.itemCurrentBudget;
              subRow['deptName'] = costBudgetSubItems[j].deptName;
              row['children'].push(subRow)
            }
            result.push(row)
          }
          return result || [];
        },
        // 搜索按钮
        searchBtnClickHandle(){
//          this.clear();
          let id = this.routeInfo.data.id
          this.$store.dispatch('costBudgetPlanFindDetail', {
            id : id
          }).then(function(res){
            if(res.success){
              let row = res.data.list[0].costBudgetItems || [];
              if(row.length > 0) {
                this.tableData = this.filterNineTypeAndSubTypeInfo(row);
              }
            } else {
              this.$message({
                type : 'error',
                message : res.message || '错误'
              })
            }
          }.bind(this));
        },
      }

    }
</script>

<style>

</style>
