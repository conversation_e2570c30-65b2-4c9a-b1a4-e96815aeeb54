<template>
  <el-container class="container">
    <el-main>
      <!--发布-->
      <el-form ref="info" label-width="100px" :model="info">
        <el-row type="flex">
          <el-col :span="8">
            <el-form-item label="文章标题">
              <template v-if="!viewOrEdit">
                <el-input v-model="info.newsName"></el-input>
              </template>
              <template v-if="viewOrEdit">
                <span>{{info.newsName}}</span>
              </template>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="文章学时">
              <el-input-number v-model="info.newsTime"></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex">
          <el-col :span="8">
            <el-form-item label="停留时间(s)">
              <el-input-number v-model="info.stayTime"></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="文章积分">
              <el-input-number v-model="info.newsScore"></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>
        <!--<el-row>
          <el-col :span="24">
            <el-form-item label="文章上传">
              <fileUpload
                @fileData="fileDataFn"
                :data="upload"></fileUpload>
            </el-form-item>
          </el-col>
        </el-row>-->

        <el-row>
          <el-col :span="24">
            <el-form-item label="文章选择">
              <el-select
                v-model="assist.selectedArticleList"
                multiple
                filterable
                remote
                @change="articleChangeFn"
                reserve-keyword
                :remote-method="remoteMethod"
                placeholder="请输入关键词">
                <el-option
                  v-for="item in assist.articleList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex">
          <el-col :span="24">
            <chooseStaff
              ref="chooseStaff"
              @selectedRows="selectedRows"></chooseStaff>
          </el-col>
        </el-row>
        <el-row type="flex">
          <el-col :span="24">
            <el-form-item label="文章内容">
              <vue-editor v-model="info.newsContent"></vue-editor>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" class="row" justify="center">
          <el-button
            @click="saveBtnClickHandle({ status : '0' })"
            size="small" :span="2" type="success" >保存</el-button>
          <el-button size="small" :span="2"  @click="$router.back();">返回</el-button>
        </el-row>
      </el-form>
    </el-main>
  </el-container>
</template>

<script>
  import { VueEditor } from 'vue2-editor'
  import chooseStaff from '@/components/common/chooseStaff'
  import fileUpload from '@/components/common/fileUploadFileServer'
  export default {
    components: {
      chooseStaff,
      VueEditor,
      fileUpload
    },
    data(){
      return {
        // info表
        info : {
          // ID
          id : '',
          // 文章名称
          newsName : '',
          // 文章类型
//          newsType : '',
          // 文章内容
          newsContent : '',
          // 文章作者
//          newsAuthor : '',
          // 文章出处
//          newsSource : '',
          // 停留时间(s)
          stayTime : 0,
          // 所属主题
          // 是否必修
          // 文章积分
          newsScore : 0,

          // 文章学时
          newsTime : 0,
          // 上传列表
          listNewsFile :[],
          // 用户id
          userIds : [],
          // 用户

        },
        // 辅助字段
        assist:{
          // 文章列表
          articleList : [],
          // 文章
          selectedArticleList : [],

        },


        upload:{
          params:{
            contentId : this.$tool.getStorage('LOGIN_USER').userId,
            contentType:19
          },
          btns : {
            // 上传按钮
            upload : {
              isShow : true,
            },
            // 下载按钮
            download : {
              isShow : false,
            },
            // 删除按钮
            delete : {
              isShow : true,
            },
          },
          uploadUrl:'',
          uploadCookies:true,
          fileData : [],
        },

        viewOrEdit : false,
        // 权限按钮
        powerBtns : [],
      }
    },
    watch:{
      $route(to,from){
        // 如果来至列表页
        if(from.name === 'safetyEducationArticleIndex'&&this.$route.name==='safetyEducationArticleAdd'){
          this.init();
        }
      },
    },
    mounted(){
      this.init();
    },
    methods:{
      // 初始化
      init(){
        // 获取知识库列表
        this.getArticleList('');
        if(this.$route.params.status == 'edit'){
          this.searchBtnClickHandle();
          //刘杰1130 终
        } else {
          this.clear();
        }
        this.judgeUserRole();
      },
      // 获取知识库文章类型列表
      getArticleList(name){
        let params = {
          type : '1',
          name : name,
        }
        this.$store.dispatch('safeEduMediumLibraryQueryMedium', params).then(function(res){
          if(res.success){
            this.assist.articleList = res.data.list.map(function(item){
              return {
                label : item.name,
                value : item.fid
              }
            })
          } else {
            this.$message({
              type : 'error',
              message : res.message || '错误'
            })
          }
        }.bind(this));
      },
      // 知识库远程搜索
      remoteMethod(query) {
       this.getArticleList(query);
      },
      articleChangeFn(item){
//        console.log(111,item);
//        console.log(222,this.assist.articleList);
        this.info.listNewsFile = [];
        for(var i = 0; i < item.length; i++ ){
          for(var j = 0; j < this.assist.articleList.length; j++){
            if(this.assist.articleList[j].value == item[i]){
              this.info.listNewsFile.push({
                fileId : item[i],
                fileName : this.assist.articleList[j].label
              })
              break;
            }
          }
        }
//        console.log(111,this.info.listNewsFile)
      },
      // 人员列表选择组件处理函数
      selectedRows(rows){
        // 参与人员列表----用户userId列表
        let userIds = rows.map(function(it){
          return it.userId;
        })
        this.info.userIds = userIds;
      },

      fileDataFn(data){
        console.log('父组件获取子组件数据：',data);
        this.info.listNewsFile = data.map(function(e){
          return {
            id : '',
            eduNewsId : '',
            fileId : e.fileId,
            fileName : e.fileName
          }
        })
//        console.log(888, this.info.listNewsFile)
      },

      judgeUserRole(){
        // 获取权限按钮
        let url = '/edu-training-menu/daily-training-index';
        this.powerBtns = this.$tool.getPowerBtns('eduTrainingMenu', url);
      },
      // 清空数据
      clear(){
        this.info = this.$tool.clearObj({}, this.info);
        this.upload.fileData = [];
        this.viewOrEdit = false;
        this.assist.selectedArticleList = [];
        // 清空人员列表
        if(this.$refs['chooseStaff'].changeTableDataHandle){
          this.$refs['chooseStaff'].changeTableDataHandle([]);
        }
//        this.assist = this.$tool.clearObj({}, this.assist);
      },
      // 根据id搜索信息
      searchBtnClickHandle(){
        this.clear();
        let id = this.$route.params.id;
        this.viewOrEdit = true;
        this.$store.dispatch('eduNewsFindById', { id : id }).then(function(res){
          if(res.success){
//            let list = res.data.list[0];
            let list = res.data;
            // 发布培训信息
            Object.entries(list).forEach(function(it){
              if(it[1] && this.info.hasOwnProperty(it[0])){
                this.info[it[0]] = it[1];
              }
            }.bind(this));


            if(this.info.listNewsFile &&  this.info.listNewsFile.length > 0){
              this.assist.selectedArticleList = this.info.listNewsFile.map(function(item){
                return item.fileId;
              }.bind(this))
            }

//            this.upload.fileData = list.listNewsFile;
//            this.$store.commit('articleDataMut', this.info)
//            console.log('子数据',this.$store.state.articleData)

            // 人员列表
            let userList = list.userList.map(function(it){
              return {
                userId : it.userId,
                companyId : it.companyId,
                parentCompanyId : it.parentCompanyId,
                companyName : it.companyName,
                deptName : it.deptName,
                username : it.username,
              }
            }.bind(this));
            if(this.$refs['chooseStaff'].changeTableDataHandle){
              this.$refs['chooseStaff'].changeTableDataHandle(userList);
            }

            return;
            // edit 添加和修改的时候，按钮显示；view 查看的时候，按钮隐藏
            if(this.$refs['chooseStaff'].isShowBtnHandle){
              this.$refs['chooseStaff'].isShowBtnHandle(this.pageStatus === 'view' ? false : true);
            }

            if(this.$refs['chooseStaff'].isShowJoinHandle){
//                  console.log('sdlfjsdfkjdls')
              this.$refs['chooseStaff'].isShowJoinHandle(true);
            }


          } else {
            this.$message({
              type : 'error',
              message : res.message || '错误'
            })
          }
        }.bind(this));
      },
      // 未发布/已发布/进行中【开始按钮】--培训发布--保存按钮
      saveBtnClickHandle(options){
        let params = this.info;
        if(params.stayTime <= 0 || params.newsScore <= 0){
          this.$message({
            type : 'error',
            message : "停留时间或积分必须大于0"
          })
          return;
        }
        this.$store.dispatch('eduNewsAddOrUpdate', params).then(function (res) {
          if(res.success){
            this.$message({
              type : 'success',
              message : '操作成功'
            })
            this.$router.push({ name : 'safetyEducationArticleIndex' })
          }  else {
            this.$message({
              type : 'error',
              dangerouslyUseHTMLString: true,
              message : res.message || '错误'
            })
          }
        }.bind(this))
      },


    }
  }
</script>

<style>
  .container{
    background:#fff;
    padding:0px 20px 20px;
  }
  .title{
    background:rgba(64,158,255,.1);
    color:#0f6fc6;
    border: 1px solid rgba(64,158,255,.2);
    border-radius:5px;
  }
  .row{
    margin-top:10px;
  }
</style>
