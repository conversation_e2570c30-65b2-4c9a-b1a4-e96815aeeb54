<template>
  <div id="trainingPlanIndex">
    <div class="background-style">
      <!--搜索区-->
      <div style="padding:10px 0 10px 0;float: left">
        <el-button
          v-if="!viewRole"
          @click="addBtnClickHandle"
          type="success" icon="el-icon-plus" style="margin-left: 20px">费用投入表</el-button>
      </div>
      <!--表格区-->
      <div style="width: 100%;">
        <div style="padding: 10px 10px 20px 10px">
          <el-table
            border
            :data="tableData.list"
            style="width: 100%">
            <el-table-column
              type="index"
              label="编号"
              width="100"
              align="center"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="title"
              label="名称"
              show-overflow-tooltip
              min-width="150"
              label-class-name="header-style">
              <template slot-scope="scope">
                <router-link :to="{ path : '/safety-input-menu/cost-input-item', query : { reg : scope.row } }">{{scope.row.title}}</router-link>
              </template>
            </el-table-column>
            <el-table-column
              prop="companyName"
              label="公司"
              show-overflow-tooltip
              width="200"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="year"
              :formatter="formatDateTime"
              label="年份"
              width="150"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="totalAccount"
              label="投入总额（元）"
              width="150"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              fixed="right" label="操作"
              label-class-name="header-style"
              align="center" width="200">
              <template slot-scope="scope">
                <template>
                  <el-button v-if="!viewRole" size="mini" type="primary" @click="itemUpdateClick(scope.row)">修改</el-button>
                  <el-button v-if="!viewRole" size="mini" type="danger" @click="itemDeleteClick(scope.row)">删除</el-button>
                </template>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div style="margin-top: 10px">
          <el-pagination
            background
            layout="prev, pager, next"
            :current-page="tableData.pageNum"
            :page-size="form.pageSize"
            :total="tableData.total"
            @current-change ="disasterPageChangeHandle">
          </el-pagination>
        </div>
      </div>
    </div>
    <!--对话框-->
    <el-dialog
      :visible.sync="dialog.isShow"
      width="50%"
      :before-close="handleClose">
      <el-form label-width="100px">
        <el-row  class="row">
          <el-col :span="24">
            <el-form-item label="名称">
              <el-input v-model="dialog.form.title"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row  class="row">
          <el-col :span="24">
            <el-form-item label="年份">
              <span v-if="dialog.form.id">{{dialog.form.year && $tool.formatDateTime(dialog.form.year).substring(0,4)}}</span>
              <el-date-picker
                v-if="!dialog.form.id"
                v-model="dialog.form.year"
                type="year"
                @change="yearChange"
                placeholder="选择日期">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row  class="row">
          <el-col :span="24">
            <el-form-item label="预算方案：">
              <el-select v-model="dialog.form.budgetPlanId" placeholder="请选择">
                <el-option
                  v-for="item in dialog.assist.planList"
                  :key="item.id"
                  :label="item.title"
                  :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <!--<el-row  class="row">-->
        <!--<el-col :span="2">-->
        <!--<el-form-item>-->

        <!--</el-form-item>-->
        <!--</el-col>-->
        <!--</el-row>-->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          type="danger"  size="mini"
          @click="dialogOkBtnClickHandle">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
  export default {
    data() {
      return {
        form : {
          // 当前页
          pageCurrent : 1,
          // 页数大小
          pageSize : 10,
        },
        tableData : [],
        // 对话框
        dialog : {
          // 是否显示
          isShow : false,
          form : {
            id : '',
            // 预算id
            budgetPlanId : '',
            // 项目
            title : '',
            // 年份
            year : '',
          },
          assist : {
            planList : [],
          }
        },
        //浏览角色模式
        viewRole : false,
      }
    },
    mounted(){
      this.init();
    },
    watch:{
      $route(to,from){
        if(to.name === 'costInputIndex') {
          this.init();
        }
      }
    },
    methods:{
      // 初始化
      init(){
        this.viewRole = this.$tool.judgeViewRole();
        // 搜索
        this.searchBtnClickHandle();
      },
      // 格式化时间
      formatDateTime(row, column, cellValue){
        let pro = column.property;
        let num = 10;
        // 年份4位 1999
        if(pro === 'year') num = 4;
        let str = this.$tool.formatDateTime(row[pro] || 0);
        return str ? str.substring(0, num) : str;
      },
      // 分页
      disasterPageChangeHandle(page){
        this.form.pageCurrent = page;
        this.searchBtnClickHandle();
      },
      // 搜索按钮
      searchBtnClickHandle(){
        this.$store.dispatch('costAccountRegFind', this.form).then(function(res){
          if(res.success){
            this.tableData = res.data;
          } else {
            this.$message({
              type : 'error',
              message : res.message || '错误'
            })
          }
        }.bind(this));
      },
      // 添加按钮
      addBtnClickHandle(){
        this.dialog.isShow = true;
      },
      // 修改按钮
      itemUpdateClick(row){

        this.$tool.cloneObj(this.dialog.form, row);
        this.yearChange(row.year);
        this.dialog.isShow = true;

      },
      // 删除按钮
      itemDeleteClick(row){
        this.$confirm('此操作将永久删除, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
          .then(function(){
            this.$store.dispatch('costAccountRegDelete', {
              id : row.id
            }).then(function(res){
              if(res.success){
                this.$message({
                  type : 'success',
                  message : '删除成功'
                })
                this.searchBtnClickHandle();
              } else {
                this.$message({
                  type : 'error',
                  message : res.message || '删除失败！！'
                })
              }
            }.bind(this))
          }.bind(this))
      },
      // 对话框--选择年份
      yearChange(val){
        let params = {
          year : val,
          budgetType : '3',
          deptId : this.$tool.getLoginer().companyId,
        }

        // 根据年份查找预算费用
        this.$store.dispatch('costBudgetPlanFind', params).then(function(res){
          if(res.success){
            let list = res.data.list;
            this.dialog.assist.planList = list;
          } else {
            this.$message({
              type : 'error',
              message : res.message || '错误'
            })
          }
        }.bind(this));
      },
      // 对话框---确定按钮
      dialogOkBtnClickHandle(){
        let form = this.dialog.form;
        if(form.title == '' || form.year == '' || form.budgetPlanId == ''){
          this.$message({
            type : 'error',
            message : '名称、年份或者预算不得为空！！'
          })
          return;
        }
        let params = {
          title : form.title,
          year : form.year,
          budgetPlanId : form.budgetPlanId,
        }
        if(this.dialog.form.id) {
          params['id'] = this.dialog.form.id;
        }
        this.$store.dispatch('costAccountRegAddOrUpdate', params).then(function(res){
          if(res.success){
            this.$message({
              type : 'success',
              message : '操作成功'
            })
            this.handleClose();
            this.searchBtnClickHandle();
            this.dialog.isShow = false;
          }  else {
            this.$message({
              type : 'error',
              message : res.message || '错误'
            })
          }
        }.bind(this));
      },
      // 对话框--关闭
      handleClose(){
        this.dialog.form = this.$tool.clearObj({}, this.dialog.form);
//        this.dialog.assist.planList = [];
        this.dialog.isShow = false;
      }
    }
  }
</script>
<style>
</style>
