<template>
  <div id="viewPlanForm">
    <div class="background-style">
      <el-col :span="22" :offset="1" class="success-background-title">{{planTitle}}</el-col>
      <el-col :span="16" :offset="4">
        <el-form :model="form"  label-width="120px">
          <el-col :span="24">
            <el-col :span="12">
              <el-form-item label="文件编号：">
                {{docNum}}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="记录编号：">
                {{recordNum}}
              </el-form-item>
            </el-col>
          </el-col>
        </el-form>
      </el-col>
      <el-col :span="22" :offset="1" style="margin-top: 10px">
        <table class="simple-table">
          <tr><td v-for="itemHead in tableHead">{{itemHead.title}}</td></tr>
          <tr v-for="row in tableContent"><td v-for="item in tableHead">{{row[item.index]}}</td></tr>
        </table>
        <el-button style="float: right;margin: 20px" @click="returnClick">返回</el-button>
      </el-col>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'viewPlanForm',
    data() {
      return {
        planTitle:'',
        tableHead:[
          {index:'num',title:'序号'},
          {index:'inspectType',title:'检查类型'},
          {index:'inspectDeptName',title:'检查单位'},
          {index:'content',title:'检查内容'},
          {index:'inspectMemeber',title:'检查人员'},
          {index:'inspectDate',title:'检查日期'},
          {index:'remark',title:'备注'},
        ],
        tableContent:[],
        docNum:'',
        recordNum:'',
      }
    },
    created:function(){
      this.currentPlanId=this.$route.params.planId;
      this.planTitle=this.$route.params.planName;
      this.docNum=this.$route.params.docNum;
      this.recordNum=this.$route.params.recordNum;
      this.searchTable();
    },
    watch:{
      $route(to, from){
        this.currentPlanId=this.$route.params.planId;
        this.planTitle=this.$route.params.planName;
        this.docNum=this.$route.params.docNum;
        this.recordNum=this.$route.params.recordNum;
        this.searchTable();
      }
    },
    methods:{
      searchTable:function () {
        this.$http.post('danger/safePlanList/find',{safePlanId:this.currentPlanId}).then(function (res) {
          if(res.data.success){
            this.tableContent=res.data.data.list;
            this.tableContent.forEach(function (item,index) {
              item.num=index+1;
            })
          }else{
            console.log('danger/safePlanList/find'+'数据申请失败');
          }
        }.bind(this)).catch(function (err) {
          console.log('检查计划列表查找:'+err);
        }.bind(this));
      },
      returnClick:function () {
        this.planTitle='';
        this.docNum='';
        this.recordNum='';
        this.tableContent=[];
        this.$router.go(-1);
      },
    }
  }
</script>
<style>
</style>
