<template>
  <div id="editSummary">
    <div class="background-style">
      <el-col :span="16" :offset="4" class="primary-background-title">应急工作总结评估表</el-col>
      <el-col :span="16" :offset="4">
        <el-form :model="form" :rules="rules" ref="ruleForm" class="demo-ruleForm" label-width="140px">
          <el-col :span="24">
            <el-col :span="12">
              <el-form-item label="编制公司：">
                {{form.deptName}}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="显示上报情况：">
                <el-button type="primary" @click="showSignedMessage">显示上报情况</el-button>
              </el-form-item>
            </el-col>
          </el-col>
          <el-col :span="24">
            <el-form-item label="应急响应起止时间：">
              <el-input v-model="form.dateRange"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="应急响应类型等级：">
              <el-input v-model="form.typeLevel"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <div style="color:rgb(99,149,208);font-weight: bold;margin-bottom: 20px;margin-left: 20px">PS：点击“显示上报情况”，即可显示各子公司的上报情况</div>
          </el-col>
          <el-col :span="24">
            <el-form-item label="各主要控股企业应急响应情况：" prop="response">
              <el-input type="textarea" :autosize="{ minRows: 3}" v-model="form.response"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="应急效果评估：" prop="effect">
              <el-input type="textarea" :autosize="{ minRows: 3}" v-model="form.effect"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="受损单位：" prop="unit">
              <el-input type="textarea" :autosize="{ minRows: 3}" v-model="form.unit"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="人员伤亡情况：" prop="people">
              <el-input type="textarea" :autosize="{ minRows: 3}" v-model="form.people"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="财产损失情况：" prop="property">
              <el-input type="textarea" :autosize="{ minRows: 3}" v-model="form.property"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="经济损失预估：" prop="lost">
              <el-input type="textarea" :autosize="{ minRows: 3}" v-model="form.lost"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <div style="float: right">
              <el-button type="primary" v-show="isCreater" @click="sendRequest()">提交</el-button>
              <el-button type="success" @click="saveSummary('总结保存成功！')">保存</el-button>
              <el-button @click="$refs['ruleForm'].resetFields();$router.push({name:'emerResponseWorkflow'})">返回</el-button>
            </div>
          </el-col>
        </el-form>
      </el-col>
    </div>

    <search-people-dialog @determineClick="selectPersonClick" :data="selectPersonData" :defaultPersonId="selectPersonId"></search-people-dialog>

  </div>
</template>
<script>
  import SearchPeopleDialog from '../../../common/smallComponent/searchSinglePeople.vue'
  import {mapGetters} from 'vuex'
  export default {
    name: 'editSummary',
    data() {
      return {
        form:{
          id:'',
          deptName:'',
          dateRange:'',//应急响应起止时间
          typeLevel:'',//应急响应类型等级
          response:'',//各主要控股企业应急响应情况
          effect:'',//应急效果评估
          unit:'',//受损单位
          people:'',//人员伤亡情况
          property:'',//财产损失情况
          lost:'',//经济损失预估
        },
        rules:{
          response: [{required: true, message: '请填写应急响应情况', trigger: 'change'}],
          effect: [{required: true, message: '请填写应急效果评估', trigger: 'change'}],
          unit: [{required: true, message: '请填写受损单位', trigger: 'change'}],
          people: [{required: true, message: '请填写人员伤亡情况', trigger: 'change'}],
          property: [{required: true, message: '请填写财产损失情况', trigger: 'change'}],
          lost: [{required: true, message: '请填写经济损失预估', trigger: 'change'}],
        },
        //缓存数据
        emerId:'',
        planId:'',
        startId:'',
        status:'',
        publicCompanyId:'',
        addFlag:true,//是否为新增
        isCreater:false,
        signPerson:{},
        taskId:'',
        doTaskLoading:'',//走流程时的缓冲图
        //------------------选择审核人的对话框-----------------------
        selectPersonData:{title:'请选择审核人',isShow:false,defaultPerson:{value:0,label:''}},
        selectPersonId:0,
      }
    },
    components : {
      'search-people-dialog' : SearchPeopleDialog
    },
    created:function () {
      if(this.$route.params.emerData){
        this.form.deptName=this.$route.params.emerData.companyName;
        this.form.typeLevel=this.$route.params.emerData.typeName+this.$route.params.emerData.respLevel;
        this.startId=this.$route.params.emerData.startPlanId;
        this.emerId=this.$route.params.emerData.id;
        this.planId=this.$route.params.emerData.planId;
        this.status=this.$route.params.emerData.status;
        this.publicCompanyId=this.$route.params.emerData.companyId;
        this.taskId=this.$route.params.emerData.taskId;
        this.signPerson={value:this.$route.params.emerData.signerUserId,label:this.$route.params.emerData.signerUserName};
        this.isCreater=false;
        if(Number(this.$route.params.emerData.operateId)===this.$tool.getStorage('LOGIN_USER').userId){
          this.isCreater=true;
        }else{
          this.isCreater=false;
        }
        this.searchSummary();
      }
    },
    watch:{
      $route(to, from){
        if((from.name==='emerResponseWorkflow'||from.name==='emergencyProcessWorkflow'||from.name==='taskNotice')&&this.$route.name==='editSummaryWorkflow'){
          if(this.$route.params.emerData){
            this.form.deptName=this.$tool.getStorage('LOGIN_USER').companyName;
            this.form.typeLevel=this.$route.params.emerData.typeName+this.$route.params.emerData.respLevel;
            this.startId=this.$route.params.emerData.startPlanId;
            this.emerId=this.$route.params.emerData.id;
            this.planId=this.$route.params.emerData.planId;
            this.status=this.$route.params.emerData.status;
            this.publicCompanyId=this.$route.params.emerData.companyId;
            this.taskId=this.$route.params.emerData.taskId;
            this.signPerson={value:this.$route.params.emerData.signerUserId,label:this.$route.params.emerData.signerUserName};
            this.isCreater=false;
            if(Number(this.$route.params.emerData.operateId)===this.$tool.getStorage('LOGIN_USER').userId){
              this.isCreater=true;
            }else{
              this.isCreater=false;
            }
            this.searchSummary();
          }
        }
      },
    },
    methods:{
      searchSummary:function () {
        let params=new URLSearchParams;
        params.append("companyId",this.$tool.getStorage('LOGIN_USER').companyId);
        params.append("planPublicId", this.emerId);
        this.$http.post('emgSummary/find',params).then(function (res) {
          if(res.data.success){
            if(res.data.data.list.length){
              this.form.id=res.data.data.list[0].id;
              this.form.dateRange=res.data.data.list[0].timeRange;
              this.form.lost=res.data.data.list[0].econLossEstimate;
              this.form.effect=res.data.data.list[0].effectEvaluation;
              this.form.people=res.data.data.list[0].deathSituation;
              this.form.property=res.data.data.list[0].econLossSituation;
              this.form.response=res.data.data.list[0].responseSituation;
              this.form.unit=res.data.data.list[0].damagedUnit;
              this.addFlag=false;
            }else{
              this.getTimeRange(this.startId,this.emerId);
              this.addFlag=true;
            }
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message({
            showClose: true,
            message: '网络错误，请尝试重登录',
            type: 'error'
          });
        }.bind(this));
      },
      showSignedMessage:function () {
       let params=new URLSearchParams;
       params.append("planPublicId",this.emerId);
        this.$http.post('emgSummary/findLowerDeptSummary', params).then(function (res) {
          if (res.data.success) {
            if(res.data.data){
              this.form.lost+=this.editToStr(res.data.data.econLossEstimate);
              this.form.effect+=this.editToStr(res.data.data.effectEvaluation);
              this.form.people+=this.editToStr(res.data.data.deathSituation);
              this.form.property+=this.editToStr(res.data.data.econLossSituation);
              this.form.response+=this.editToStr(res.data.data.responseSituation);
              this.form.unit+=this.editToStr(res.data.data.damagedUnit);
            }else{
              this.$message({
                showClose: true,
                message: '暂无上报数据',
                type: 'warning'
              });
            }
          }else{
            this.$message({
              showClose: true,
              message: res.data.message,
              type: 'warning'
            });
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
        }.bind(this));
      },
//     //将列表数据转为字符串
      editToStr:function (arr) {
        let strTemp='\n';
        for(let i=0;i<arr.length;i++){
          strTemp+=arr[i].date+' '+arr[i].companyName+': '+arr[i].content+'\n';
        }
        return strTemp;
      },
      getTimeRange:function (startId,currentId) {
        let params=new URLSearchParams;
        params.append("planPublicId",currentId);
        params.append("startPlanPublicId",startId);
        this.$http.post('emgSummary/getSummaryInitInfo', params).then(function (res) {
          if (res.data.success) {
            this.form.dateRange= res.data.data.timeRange;
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message({
            showClose: true,
            message: '网络错误，请尝试重登录',
            type: 'error'
          });
        }.bind(this));
      },
      sendRequest:function () {
        if(this.isCreater){//创建者可以改变应急响应的状态
          this.selectPersonData.defaultPerson=this.signPerson;
          this.selectPersonId=this.signPerson.value;
          this.selectPersonData.isShow=true;
        }else {//非创建者只是保存
          this.saveSummary('总结保存成功！');
        }
      },
      selectPersonClick:function (val) {
        if(val){
          this.selectPersonData.isShow=false;
          let flowParams=new URLSearchParams;
          flowParams.append("taskId",this.taskId);
          flowParams.append("result",1);
          flowParams.append("applyUserId",val);
          this.doTaskLoading=this.$loading({
            lock: true,
            text: 'Loading',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });
          this.$http.post('emgFlow/doTask',flowParams).then(function (res) {
            if(res.data.success){
              this.doTaskLoading.close();
              this.saveSummary('总结提交成功！');
            }
          }.bind(this)).catch(function (err) {
            console.log(err);
            this.$message.error('签发失败')
          }.bind(this));
        }else{
          this.$message.warning('请选择审核人');
        }
      },
      //新建或者存储总结
      saveSummary:function (str) {
        let params = new URLSearchParams;
        params.append("deptId",this.$tool.getStorage('LOGIN_USER').deptId);
        params.append("companyId",this.$tool.getStorage('LOGIN_USER').companyId);
        params.append("timeRange",this.form.dateRange);
        params.append("responseLevel",this.form.typeLevel);
        params.append("responseSituation",this.form.response);
        params.append("effectEvaluation",this.form.effect);
        params.append("damagedUnit",this.form.unit);
        params.append("deathSituation",this.form.people);
        params.append("econLossSituation",this.form.property);
        params.append("econLossEstimate",this.form.lost);
        params.append("planPublicId",this.emerId);
        let urlStr='';
        if(this.addFlag){
          urlStr='emgSummary/add';
        }else{
          urlStr='emgSummary/update';
          params.append("id",this.form.id);
        }
        this.$http.post(urlStr, params).then(function (res) {
          if (res.data.success) {
            this.$message.success(str);
            this.$router.push({name:'emerResponseWorkflow'});
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message({
            showClose: true,
            message: '总结保存失败',
            type: 'error'
          });
        }.bind(this));
      },

    }
  }
</script>
<style>
</style>
