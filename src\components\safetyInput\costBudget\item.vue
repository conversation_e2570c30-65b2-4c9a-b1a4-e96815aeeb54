<template>
  <div id="costBudgetItem" class="background-style">
    <el-form label-width="100px">
      <el-row  class="row" span="8" style="margin-top: 20px">
<!--        <el-col :span="8" style="margin-top: 20px">-->
<!--          <el-form-item label="审批人：" style="margin: 2px;" >-->
<!--            <el-autocomplete-->
<!--              class="inline-input"-->
<!--              @select="selectUsernameFn"-->
<!--              v-model="baseInfo.otherData.username"-->
<!--              :fetch-suggestions="querySearch"-->
<!--              placeholder="请输入内容"-->
<!--            ></el-autocomplete>-->
<!--          </el-form-item>-->
<!--        </el-col>-->
<!--        <el-col :span="12" style="margin-top: 24px">-->
<!--          &lt;!&ndash;status 0 未发布，1 提交中，2已结算&ndash;&gt;-->
<!--          &lt;!&ndash;status == 1 && nowPersonId == userInfo.userId(登录人)&ndash;&gt;-->
<!--          <el-button-->
<!--            v-if="baseInfo.form.status == 1 && baseInfo.form.nowPersonId == userInfo.userId"-->
<!--            size="small" type="success"-->
<!--            @click="itemRejectClick">审批不同意</el-button>-->
<!--          &lt;!&ndash;status != 2 && nowPersonId == userInfo.userId(登录人)&ndash;&gt;-->
<!--          <el-button-->
<!--            v-if="baseInfo.form.status != 2 && baseInfo.form.nowPersonId == userInfo.userId"-->
<!--            size="small"-->
<!--            type="success" @click="submitBtnHandle">提交审批</el-button>-->

<!--          &lt;!&ndash;status != 1 && nowPersonId == userInfo.userId(登录人)&ndash;&gt;-->
<!--          <el-button-->
<!--            v-if="baseInfo.form.status == 1 && baseInfo.form.nowPersonId == userInfo.userId"-->
<!--            size="small"-->
<!--            type="success" @click="finishBtnHandle">流程完成</el-button>-->
<!--          <el-button-->
<!--            size="small" @click="$router.back()">返回</el-button>-->
<!--        </el-col>-->
      </el-row>

    </el-form>
    <el-tabs v-model="activeName" @tab-click="handleClick" style="margin-left: 10px">
      <el-tab-pane label="基本信息" name="zero">
        <el-form label-width="180px">
          <el-row  class="row">
            <el-col :span="24">
              <el-form-item label="名称：" style="margin: 2px;">
                <span>{{baseInfo.form.title}}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row  class="row">
            <el-col :span="24">
              <el-form-item label="预算类型：" style="margin: 2px;">
                <span>
                  <template v-if="baseInfo.form.budgetType == 1">月度预算</template>
                  <template v-if="baseInfo.form.budgetType == 2">季度预算</template>
                  <template v-if="baseInfo.form.budgetType == 3">年度预算</template>
                </span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row  class="row">
            <!--年度、季度显示年-->
            <el-col :span="8"  v-if="baseInfo.form.id && baseInfo.form.budgetType == '1'">
              <el-form-item label="年：" style="margin: 2px;">
                <span >{{baseInfo.form.year && $tool.formatDateTime(baseInfo.form.year).substring(0,4)}}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8"  v-if="baseInfo.form.budgetType != '1'">
              <el-form-item label="年：" style="margin: 2px;">
                <span>{{baseInfo.form.year && $tool.formatDateTime(baseInfo.form.year).substring(0,4)}}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8"  v-if="baseInfo.form.budgetType == '2'">
              <el-form-item label="季度：" style="margin: 2px;">
                <span>第{{baseInfo.form.budgetType}}季度</span>
              </el-form-item>
            </el-col>
            <el-col :span="8"  v-if="baseInfo.form.budgetType == '1'">
              <el-form-item label="月：" style="margin: 2px;">
                <span>{{baseInfo.form.budgetType}}月</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row  class="row">
            <el-col :span="24">
              <el-form-item label="预算部门：" style="margin: 2px;">
                <span>{{baseInfo.form.deptName}}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row  class="row">
            <el-col :span="24">
              <el-form-item label="情况说明：" style="margin: 2px;" >
                <span>{{baseInfo.form.remark}}</span>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-tab-pane>
      <el-tab-pane label="本部预算" name="first">
       <!-- <el-row style="margin-top: 30px">
          {{ year }}年度安全生产投入预算计划表（单位：元）
        </el-row>
        <el-row style="margin-top:10px;">
          <el-table
            border
            :data="[form.costBudgetMonth]">
            <el-table-column
              prop="jan"
              label="1月"
              width="94"
              align="center">
            </el-table-column>
            <el-table-column
              prop="feb"
              label="2月"
              width="94"
              align="center">
            </el-table-column>
            <el-table-column
              prop="mar"
              label="3月"
              width="94"
              align="center">
            </el-table-column>
            <el-table-column
              prop="apr"
              label="4月"
              width="94"
              align="center">
            </el-table-column>
            <el-table-column
              prop="may"
              label="5月"
              width="94"
              align="center">
            </el-table-column>
            <el-table-column
              prop="jun"
              label="6月"
              width="94"
              align="center">
            </el-table-column>
            <el-table-column
              prop="jul"
              label="7月"
              width="94"
              align="center">
            </el-table-column>
            <el-table-column
              prop="aug"
              label="8月"
              width="94"
              align="center">
            </el-table-column>
            <el-table-column
              prop="sep"
              label="9月"
              width="94"
              align="center">
            </el-table-column>
            <el-table-column
              prop="oct"
              label="10月"
              width="94"
              align="center">
            </el-table-column>
            <el-table-column
              prop="nov"
              label="11月"
              width="94"
              align="center">
            </el-table-column>
            <el-table-column
              prop="dece"
              label="12月"
              width="94"
              align="center">
            </el-table-column>
          </el-table>
        </el-row>-->
        <!--明细表和费用小类表-->
        <el-row style="margin-top: 20px">
          <el-col :span="12" style="padding: 10px">
            <el-col :span="24" style="border-bottom: 1px solid #d3d4d6;padding-bottom: 5px">
              <div style="border-left: 5px solid #0168B7;color: #0168B7">&nbsp&nbsp分项明细表</div>
            </el-col>
            <el-col :span="24" style="margin-top: 20px" v-show="!isOnlyView">
              <div style="min-width: 500px">
                <el-input v-model="bigItem" placeholder="费用类别" size="small" style="width: 300px;float: left"></el-input>
                <el-button v-if="!viewRole" size="small" icon="el-icon-plus" type="primary" @click="addBigItem" style="float: left;margin-left: 10px">添加类别</el-button>
              </div>
            </el-col>
            <el-col :span="24" style="margin-top: 10px">
              <el-table
                ref="tableOne"
                border
                highlight-current-row
                @current-change="loadDetailTable"
                :data="form.costBudgetItems">
                <el-table-column
                  type="index"
                  width="100"
                  align="center"
                  label-class-name="header-style">
                </el-table-column>
                <el-table-column
                  prop="item"
                  label="费用大类"
                  min-width="240"
                  show-overflow-tooltip
                  align="left"
                  label-class-name="header-style">
                </el-table-column>
                <el-table-column
                  prop="itemTotalBudget"
                  label="金额"
                  width="100"
                  align="center"
                  label-class-name="header-style">
                  <!--<template slot-scope="scope">
                    <el-input-number :controls="false"  :min="0" :step="10000" v-model="scope.row.itemTotalBudget"></el-input-number>
                  </template>-->
                </el-table-column>
                <el-table-column
                  fixed="right" label="操作"
                  label-class-name="header-style"
                  align="center" width="100">
                  <template slot-scope="scope">
                    <template>
                      <el-button
                        v-if="scope.row.itemTempId == -1"
                        size="mini" type="danger" @click="itemDeleteBigClick(scope.row)">删除</el-button>
                    </template>
                  </template>
                </el-table-column>
                <!--<template slot="append">-->
                <!--<el-row>-->
                <!--<el-col :span="18">-->
                <!--<el-input v-model="bigItem" placeholder="费用类别" size="mini"></el-input>-->
                <!--</el-col>-->
                <!--<el-col :offset="1" :span="4">-->
                <!--<el-button size="mini" icon="el-icon-plus" type="danger" @click="addBigItem">类别</el-button>-->
                <!--</el-col>-->
                <!--</el-row>-->
                <!--</template>-->
              </el-table>
            </el-col>
          </el-col>
          <el-col :span="12" style="padding: 10px">
            <el-col :span="24" style="border-bottom: 1px solid #d3d4d6;padding-bottom: 5px">
              <div style="border-left: 5px solid #0168B7;color: #0168B7">&nbsp&nbsp子项目明细表</div>
            </el-col>
            <el-col :span="24" style="margin-top: 20px"  v-show="!isOnlyView">
              <div style="min-width: 500px">
                <el-input v-model="dialog.form.item" placeholder="子项目" size="small" style="width: 250px;float: left"></el-input>
                <el-button v-if="!viewRole" size="small" icon="el-icon-plus" type="primary" @click="dialogOkBtnClickHandle" style="float: left;margin-left: 10px">添加子项目</el-button>
                <el-button  v-if="!viewRole" size="small" icon="el-icon-delete" type="danger" style="margin-left: 10px;float: left" @click="multiDelete">删除</el-button>
              </div>
            </el-col>
            <el-col :span="24" style="margin: 10px 0 10px 0">

              <!-- @current-change="loadMonthTable"-->
              <el-table
                ref="tableTwo"
                height="250px"
                v-if="selectedIndex.type > -1"
                border
                highlight-current-row
                @selection-change="deleteSelectionClick"
                :data="form.costBudgetItems[this.selectedIndex.type]['costBudgetSubItems']||[]">
                <el-table-column
                  label-class-name="header-style"
                  type="selection"
                  width="55">
                </el-table-column>
                <el-table-column
                  type="index"
                  width="40"
                  align="center"
                  label-class-name="header-style">
                </el-table-column>
                <el-table-column
                  prop="item"
                  label="使用范围"
                  min-width="200"
                  show-overflow-tooltip
                  align="left"
                  label-class-name="header-style">
                </el-table-column>
                <el-table-column
                  prop="itemBudget"
                  label="金额"
                  min-width="100"
                  align="center"
                  label-class-name="header-style">
                  <template slot-scope="scope">
                  <!--  <span>{{scope.row.itemBudget}}</span>-->
                    <el-input-number
                      @change="changeHandle"
                      v-model="scope.row.itemBudget"
                      :controls="false" :min="0" :step="100" ></el-input-number>
                  </template>
                </el-table-column>
                <el-table-column
                  fixed="right"  label="操作"
                  label-class-name="header-style"
                  align="center"
                  v-if="!isOnlyView"
                  width="100">
                  <template slot-scope="scope">
                    <el-button v-if="!viewRole" size="mini" type="danger" @click="itemDeleteClick(scope.row)">删除</el-button>
                  </template>
                </el-table-column>

              </el-table>
            </el-col>
           <!-- <el-col :span="24" v-if="selectedIndex.detail > -1">
              <el-col
                v-for="(item, index) in monthField"
                :span="4" style="margin-bottom:10px;">
                {{monthChinese[index]}}
                <el-input-number
                  @change="changeHandle"
                  v-model="form.costBudgetItems[selectedIndex.type]['costBudgetSubItems'][selectedIndex.detail]['costBudgetMonth'][item]"
                  :controls="false" :min="0" :step="100" ></el-input-number>
              </el-col>
            </el-col>-->
            <el-col :span="24" style="margin-top: 20px">
              <el-button
                v-if="!viewRole"
                v-show="!isOnlyView"
                size="small"
                type="success" @click="saveBtnHandle">保存</el-button>
              <el-button
                size="small" @click="$router.back()">返回</el-button>
            </el-col>

          </el-col>
        </el-row>
      </el-tab-pane>
      <!--<el-tab-pane label="集团预算" name="second">
        <el-row>
          {{ ($tool.formatDateTime(planItem.year) || '').substring(0,4) }}年度安全生产投入预算计划表（单位：元）
        </el-row>
        <el-row style="margin-top:10px;">
         &lt;!&ndash; <el-table
            :data="groupTable"
            :summary-method="getSummaries"
            show-summary
            border>
            <el-table-column
              width="50"
              type="index"
              align="center">
            </el-table-column>
            <el-table-column
              prop="deptName"
              show-overflow-tooltip
              label="公司"
              width="100"
              align="center">
            </el-table-column>
            <el-table-column
              prop="totalBudget"
              label="全年"
              width="100"
              align="center">
            </el-table-column>
            <el-table-column
              prop="costBudgetMonth.jan"
              label="1月"
              width="100"
              align="center">
            </el-table-column>
            <el-table-column
              prop="costBudgetMonth.feb"
              label="2月"
              width="100"
              align="center">
            </el-table-column>
            <el-table-column
              prop="costBudgetMonth.mar"
              label="3月"
              width="100"
              align="center">
            </el-table-column>
            <el-table-column
              prop="costBudgetMonth.apr"
              label="4月"
              width="100"
              align="center">
            </el-table-column>
            <el-table-column
              prop="costBudgetMonth.may"
              label="5月"
              width="100"
              align="center">
            </el-table-column>
            <el-table-column
              prop="costBudgetMonth.jun"
              label="6月"
              width="100"
              align="center">
            </el-table-column>
            <el-table-column
              prop="costBudgetMonth.jul"
              label="7月"
              width="100"
              align="center">
            </el-table-column>
            <el-table-column
              prop="costBudgetMonth.aug"
              label="8月"
              width="100"
              align="center">
            </el-table-column>
            <el-table-column
              prop="costBudgetMonth.sep"
              label="9月"
              width="100"
              align="center">
            </el-table-column>
            <el-table-column
              prop="costBudgetMonth.oct"
              label="10月"
              width="100"
              align="center">
            </el-table-column>
            <el-table-column
              prop="costBudgetMonth.nov"
              label="11月"
              width="100"
              align="center">
            </el-table-column>
            <el-table-column
              prop="costBudgetMonth.dece"
              label="12月"
              width="100"
              align="center">
            </el-table-column>
          </el-table>&ndash;&gt;
          <el-collapse accordion>
            <el-collapse-item :title="item.deptName" :name="index" v-for="(item,index) in groupTable">
              <el-table
                :data="item.costBudgetItems"
                show-summary
                border>
                <el-table-column
                  width="100"
                  type="index"
                  align="center">
                </el-table-column>
                <el-table-column
                  prop="item"
                  show-overflow-tooltip
                  label="类别"
                  width="400"
                  align="center">
                </el-table-column>
                <el-table-column
                  prop="itemTotalBudget"
                  label="预算费用"
                  width="100"
                  align="center">
                </el-table-column>
              </el-table>
            </el-collapse-item>
          </el-collapse>
        </el-row>
      </el-tab-pane>-->
    </el-tabs>

    <!--对话框-->
    <el-dialog
      title="新增一级项目"
      :visible.sync="dialog.isShow"
      width="60%">
      <el-form label-width="100px">
        <el-row  class="row">
          <el-col :span="24">
            <el-form-item label="费用类别：">
              <el-input v-model="dialog.form.item"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row  class="row">
          <el-col :span="2">
            <el-form-item>
              <el-button
                type="danger"  size="mini"
                @click="dialogOkBtnClickHandle">确定</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-dialog>
    <!--对话框-->
    <el-dialog
      title="新增二级项目"
      :visible.sync="dialog.isShow"
      width="60%">
      <el-form label-width="150px">
        <el-row  class="row">
          <el-col :span="24">
            <el-form-item label="资金使用范围：">
              <el-input v-model="dialog.form.item"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row  class="row">
          <el-col :span="2">
            <el-form-item>
              <el-button
                type="danger"  size="mini"
                @click="dialogOkBtnClickHandle">确定</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-dialog>
  </div>
</template>
<script>
  export default {
    data() {
      let that = this;
      let userInfo = this.$tool.getStorage("LOGIN_USER");
      return {
        // 登录人信息
        userInfo : userInfo,
        // 选项卡
        activeName: 'zero',
        // 年
        year: '',
        form: {
          id: '',
          cancel: 0,
          parentId: 0,
          // 大类---子项---月份信息
          costBudgetItems: [],
          // 每月统计情况
          costBudgetMonth: {},
        },
        // 当前选中表格的index
        selectedIndex: {
          // 总类别
          type: -1,
          // 明细
          detail: -1,
        },
        // 当前选中的项目，包括总类别和明细
        selectedItem: {
          // 总类别
          type: '',
          // 明细
          detail: '',
        },
        // 1到12月对应的字段
        monthField: [
          'jan', 'feb', 'mar', 'apr',
          'may', 'jun', 'jul', 'aug',
          'sep', 'oct', 'nov', 'dece'
        ],
        // 1到12月对应的汉字
        monthChinese: [
          '1月', '2月', '3月', '4月',
          '5月', '6月', '7月', '8月',
          '9月', '10月', '11月', '12月',
        ],
        // 父级--预算项目
        planItem: '',
        // 选项卡----集团预算，上面的字段都是本部预算
        groupTable: [],
        // 上传文件
        upload: {
          // 地址
          url: that.$http.defaults.baseURL + 'costBudgetPlan/addBatch',
          // token
          cookies: true,
          params: {
            id: -1,
          },
        },
        // 对话框
        dialog: {
          // 是否显示
          isShow: false,
          form: {
            // 一级项目的ID
            budgetItemId: -1,
            // 使用范围
            item: '',
            // 金额
            //itemBudget : '',
          },
        },


        // 添加九大类的类别
        bigItem: '',

        //批量删除预算小类
        multiDeleteClassArr: [],


        // 基本信息

        // 对话框
        baseInfo : {
          // 额外数据
          otherData : {
            // 预算类型
            typeList : [
              { value: '1', label: '月度预算' },
              { value: '2', label: '季度预算' },
              { value: '3', label: '年度预算' },
            ],
            // 季度
            quarterList : [
              { value: '1', label: '第一季度' },
              { value: '2', label: '第二季度' },
              { value: '3', label: '第三季度' },
              { value: '4', label: '第四季度' },
            ],
            // 部门列表
            deptSubcomIds : [],
            deptSubcomNames : [],
            // 季度、月
            quarter : '',
            month : '',
            // 公司、公司id
            companyName : '',
            companyId : '',



            // 选择的人员
            username : '',
          },
          form : {
            id : '',
            // 项目
            title : '',
            // 类型
            budgetType : "1",
            // 年份
            year : '',
            // 第几季度，或者第几月
            budgetNo : '',
            // 公司id
            deptId : '',
            // 公司名称
            deptName : '',
            // 部门ID
            deptSubcomIds : [],
            // 部门名称
            deptSubcomNames : [],
            // 情况说明
            remark : '',

            // 状态,当前编写人，前一个阶段的编写人
            // 0 未发布 1 已经提交给部门负责人 2 负责人审批，发给分管领导 3 分管领导审批完毕，已经完成
            status : 0,
            prePersonId : -1,
            prePersonName : "",
            nowPersonId : -1,
//            nowPersonNo : -1,
            nowPersonName : "",
          },
        },

        // 是否只能查看
        isOnlyView : false,
        //浏览角色模式
        viewRole : false,
      }
    },
    created(){
      this.init();
    },
    watch:{
      $route(to,from){
        if(to.name === 'costBudgetItem') {
          console.log('load')
          this.init();
        }
      }
    },
    methods:{
      init(){
        this.viewRole = this.$tool.judgeViewRole();
        this.upload.params.id = this.$route.query.reg.id || -1;
        if(this.$route.query.isOnlyView){
          this.isOnlyView = this.$route.query.isOnlyView
        } else {
          this.isOnlyView = false
        }
        this.selectedIndex.type = -1;
        this.selectedIndex.detail = -1;
        this.planItem = this.$route.query.reg;
        this.form.id = this.$route.query.reg.id;

        this.searchBtnClickHandle();
        this.activeName = 'first'
//        this.baseInfoFn()
//        this.activeName = 'zero'
      },
      addItem(){
        this.dialog.isShow = true;
      },
      // 添加九大类
      addBigItem(){
        this.$store.dispatch('costBudgetItemAddOrUpdate', {
          item : this.bigItem,
          budgetPlanId : this.$route.query.reg.id
        }).then(function(res){
          if(res.success){
            this.$message({
              type : 'success',
              message : '添加成功'
            })
            this.searchBtnClickHandle();
          } else {
            this.$message({
              type : 'error',
              message : res.message || '错误'
            })
          }
        }.bind(this))

      },
      // 添加二级项目
      dialogOkBtnClickHandle(){
        this.dialog.form.budgetItemId = this.selectedItem.type.id;
        this.$store.dispatch('costBudgetSubItemAddOrUpdate', this.dialog.form).then(function(res){
          if(res.success){
            this.$message({
              type : 'success',
              message : '添加成功'
            })
            this.dialog.isShow = false;
            this.searchBtnClickHandle();
          } else {
            this.$message({
              type : 'error',
              message : res.message || '错误'
            })
          }
        }.bind(this))
      },
      // 删除一级项目
      itemDeleteBigClick(row){
        this.$store.dispatch('costBudgetItemDelete', {
          id : row.id,
          itemTempId : row.itemTempId
        }).then(function(res){
          if(res.success){
            this.$message({
              type : 'success',
              message : '删除成功'
            })
            this.selectedIndex.type = -1;
            this.selectedIndex.detail = -1;
            this.searchBtnClickHandle();
          } else {
            this.$message({
              type : 'error',
              message : res.message || '错误'
            })
          }
        }.bind(this))
      },
      // 删除二级项目
      itemDeleteClick(row){
        this.$store.dispatch('costBudgetSubItemDelete', {
          id : row.id
        }).then(function(res){
          if(res.success){
            this.$message({
              type : 'success',
              message : '删除成功'
            })
            this.selectedIndex.type = -1;
            this.selectedIndex.detail = -1;
            this.searchBtnClickHandle();
          } else {
            this.$message({
              type : 'error',
              message : res.message || '错误'
            })
          }
        }.bind(this))
      },
      // 选项卡点击
      handleClick(tab, event) {
        switch (tab.name){
          // 本部预算
          case 'zero':
            this.baseInfoFn();
                break;
          // 本部预算
          case 'first':
            this.searchBtnClickHandle();
                break;
          // 集团预算
          case 'second':
            this.getGroupFee();
                break;
        }
      },
      // 基本信息
      baseInfoFn(){
        let obj = this.$tool.clearObj({}, this.baseInfo.form);
        this.baseInfo.form = obj;
        this.baseInfo.otherData.username = '';
        this.$store.dispatch('costBudgetPlanFind', this.form).then(function(res){
          if(res.success){
            let row = res.data.list[0]
            this.$tool.cloneObj(this.baseInfo.form, row || {});

          } else {
            this.$message({
              type : 'error',
              message : res.message || '错误'
            })
          }
        }.bind(this));
      },
      // 获取人员列表
      getUserList(params){
        let userList = [];
        let data = new URLSearchParams();
        data.set("username", params.username);
        data.set("pageSize", 100);
        return new Promise(function(resolve, project){
          this.$store.dispatch('userFind', data).then(function(res){
            if(res.success){
              if(res.data.list.length > 0){
                res.data.list.forEach(function(it){
                  userList.push({
                    value : it.username,
                    label : it.userId
                  })
                }.bind(this))
                resolve(userList)
              }
            } else {
              this.$message({
                type : 'error',
                message : res.message || '错误'
              })
            }
          }.bind(this));
        }.bind(this))
      },
      // 模糊搜索人员
      querySearch(queryString, cb) {
        if(queryString == '') {
          return
        } else {
          let timer = null;
          clearTimeout(timer);
          timer = setTimeout(function(){
            this.getUserList({
              username : queryString
            }).then(function(data){
              if(data && data.length > 0){
                cb(data)
              }
            })
          }.bind(this), 300)
        }

      },
      // 选项卡----流程信息----选择人员
      selectUsernameFn(item){
        this.baseInfo.form.nowPersonId = item.label;
//        this.baseInfo.form.nowPersonNo = item.label;
        this.baseInfo.form.nowPersonName = item.value;
        this.baseInfo.form.prePersonId = this.userInfo.userId;
        this.baseInfo.form.prePersonName = this.userInfo.username;
        this.baseInfo.form.status = 1;
      },
      // 选项卡---流程信息---同意
      itemRejectClick(){
        this.baseInfo.form.status = 0;
        this.submitBtnHandle();
      },
      // 选项卡----流程信息----提交按钮
      submitBtnHandle(){
        this.$store.dispatch("costBudgetPlanUpdatePlanMain", this.baseInfo.form).then(function(res){
          if(res.success){
            this.$message({
              type : 'success',
              message : '操作成功'
            })
            this.baseInfoFn();
          } else {
            this.$message({
              type : 'error',
              message :  res.message || '操作失败'
            })
          }
        }.bind(this));
      },
      // 选项卡----流程信息----完成按钮
      finishBtnHandle(){
        this.baseInfo.form.status = 2;
        this.submitBtnHandle();
      },
      // 获取集团预算列表
      getGroupFee(){
        let date = new Date();
        date.setFullYear(this.year);
        const loading = this.$loading({
          lock: true,
          text: 'Loading',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        this.$store.dispatch('costBudgetPlanFindSubcompanyItems', {
          deptId : this.planItem.deptId,
          id : this.form.id,
          year : date
        }).then(function(res){
          if(res.success){
            loading.close()
//            this.groupTable = res.data.list;
            this.groupTable = res.data.list;

          } else {
            this.$message({
              type : 'error',
              message : res.message || '查找失败',
            })
          }
        }.bind(this));
      },
      // 搜索按钮
      searchBtnClickHandle(){
        const loading = this.$loading({
          lock: true,
          text: 'Loading',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        this.$store.dispatch('costBudgetPlanFindDetail', {
          id : this.form.id
        }).then(function(res){
          if(res.success){
            loading.close()
            let list = res.data.list[0];
            this.form.costBudgetItems = list.costBudgetItems;
            this.form.costBudgetMonth = list.costBudgetMonth;
            this.form.parentId = list.parentId;
            this.year = (this.$tool.formatDateTime(list.year) || '').substring(0, 4);
//            //刘杰 1120 新增 起 刷新时，如果缓存里有表格选择结果，则取出赋值
              let tableOneChoose = JSON.parse(window.sessionStorage.getItem('costBudgetItemTableOneCurrent'));
              if(tableOneChoose!=null){
//                this.selectedItem.type=tableOneChoose;
                this.$refs.tableOne.setCurrentRow(this.form.costBudgetItems[tableOneChoose]);
              }
//            //刘杰 1120 新增 终

          } else {
            this.$message({
              type : 'error',
              message : res.message || '错误'
            })
          }
        }.bind(this));
      },
      // 加载类别明细表格
      loadDetailTable(row){
        if(row){
          this.selectedItem.type = row;
          let typeList = this.form.costBudgetItems;
          for(var i = 0; i < typeList.length; i++){
            if(typeList[i].id === row.id){
              this.selectedIndex.type = i;
              break;
            }
          }

          //刘杰1120 增加 将当前选择加入缓存，刷新本页后记住所选项
          window.sessionStorage.setItem('costBudgetItemTableOneCurrent',JSON.stringify(this.selectedIndex.type));

          // 还原
          this.selectedItem.detail = "";
          this.selectedIndex.detail = -1;
        }
      },
      // 加载类别明细月份表格
      loadMonthTable(row){
        this.selectedItem.detail = row;

        let typeList = this.form.costBudgetItems[this.selectedIndex.type]['costBudgetSubItems'];
        for(var i = 0; i < typeList.length; i++){
          if(typeList[i].id === row.id){
            this.selectedIndex.detail = i;
            break;
          }
        }
        //刘杰1120 增加 将当前选择加入缓存，刷新本页后记住所选项
        window.sessionStorage.setItem('costBudgetItemTableTwoCurrent',JSON.stringify(this.selectedIndex.detail));
     },
      // 月份费用发生改变
      changeHandle(val){
        this.$nextTick(function () {//消除异步
          // 类别
          let type = this.selectedItem.type;
          // 明细
          //let detail = this.selectedItem.detail;
          //let typeList = this.form.costBudgetItems[this.selectedIndex.type]['costBudgetSubItems'][this.selectedIndex.detail]['costBudgetMonth'];
          // 明细总额
         /* let dTotal = 0;
          this.monthField.forEach(function(it){
            dTotal += detail['costBudgetMonth'][it]
          })
          detail['itemBudget'] = dTotal;*/

          // 类别总额
          let tTotal = 0;
          type['costBudgetSubItems'].forEach(function(it){
            tTotal += it['itemBudget'];
          })
          type['itemTotalBudget'] = tTotal;

        }.bind(this));
      },
      // 一键保存
      saveBtnHandle(){
        const loading = this.$loading({
          lock: true,
          text: 'Loading',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });

        this.$store.dispatch('costBudgetPlanUpdatePlanItems', this.form).then(function(res){
          if(res.success){
            loading.close()
            this.searchBtnClickHandle();
            this.$message({
              type : 'success',
              message : '操作成功'
            })
            //刘杰11.20 删除  原因：保存后不跳转上页，留在本页继续录入数据
//            this.$router.push({ name : 'costBudgetIndex' })
          } else {
            this.$message({
              type : 'error',
              message :  res.message || '跟新失败'
            })
          }
        }.bind(this));
      },
      // 导入
      uploadSuccess: function (response, file, fileList) {
        if (response.success) {
          this.$message({
            type : 'success',
            message : '导入成功'
          })
          // 获取项目列表
          this.searchBtnClickHandle();
        } else {
          this.$message({
            type : 'error',
            message : response.message || '导入失败'
          })
        }
      },
      // 集团预算---统计
      getSummaries(param) {
        const { columns, data } = param;
        const sums = [];
        columns.forEach(function (column, index) {
          let pro = (column.property || '').split('.');
          if (index === 0) {
            sums[index] = '总价';
            return;
          } else if(index === 1){
            sums[index] = '——';
            return;
          }
          let values = '';
          if(pro.length === 1){
            values = data.map(function(item){ return Number(item[pro[0]]) });
          } else {
            values = data.map(function(item){ return Number(item[pro[0]][pro[1]]) });
          }
          if (!values.every(function(value){ return isNaN(value) })) {
            sums[index] = values.reduce(function(prev, curr){
              const value = Number(curr);
              if (!isNaN(value)) {
                return prev + curr;
              } else {
                return prev;
              }
            }, 0);
          }else {
            sums[index] = 'N/A';
          }
        });
        return sums;
      },
      //批量删除安全投入预算小类
      deleteSelectionClick:function (val) {//多选
        this.multiDeleteClassArr.splice(0)
        if(val.length){
          val.forEach(function (item) {
            this.multiDeleteClassArr.push({id:item.id})
          }.bind(this))
        }
      },
      multiDelete:function () {//批量删除
        if(this.multiDeleteClassArr.length){
          this.$http.post('costBudgetSubItem/deleteMultiple', {costBudgetSubItems:this.multiDeleteClassArr}).then(function (res) {
            if (res.data.success) {
              this.searchBtnClickHandle();
              this.$message.success('删除成功');
            }
          }.bind(this)).catch(function (err) {
            this.$message.error('删除失败');
          }.bind(this));
        }else {
          this.$message.warning('请勾选待删除项');
        }
      },

    }
  }
</script>
<style>
  #costBudgetItem .el-input-number{
    width : 70px
  }
  #costBudgetItem  .el-footer{
    padding:0;
  }
</style>
