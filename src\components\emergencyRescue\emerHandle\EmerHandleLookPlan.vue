<template>
    <div id="emerHandleLookPlan">
      <el-row class="row">
        <el-col :span="24" class="title">查看应急预案</el-col>
      </el-row>
      <el-form label-width="120px" class="demo-ruleForm">
        <el-col :span="24">
          <el-col :span="18">
            <el-form-item label="预案名称：">
              <el-input v-model="data.name" readonly></el-input>
            </el-form-item>
          </el-col>
        </el-col>
        <el-col :span="24">
          <el-col :span="16">
            <el-form-item label="分类：">
              <el-input v-model="typeName" readonly></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="级别：">
              <el-input v-model="data.eventLevel" readonly></el-input>
            </el-form-item>
          </el-col>
        </el-col>
        <el-col :span="24">
          <el-col :span="16">
            <el-form-item label="执行清单：">
              <el-col :span="24">执行操作</el-col>
              <el-col :span="24">
                <el-input
                  readonly
                  type="textarea"
                  :rows="4"
                  placeholder="请输入内容"
                  v-model="operateList">
                </el-input>
              </el-col>
              <el-col :span="24">帮助</el-col>
              <el-col :span="24">
                <el-input
                  readonly
                  type="textarea"
                  :rows="4"
                  placeholder="请输入内容"
                  v-model="helpList">
                </el-input>
              </el-col>
            </el-form-item>
          </el-col>
        </el-col>
        <el-col :span="24">
          <el-col :span="16">
            <el-form-item label="注意事项：">
              <el-col :span="24">
                <el-input
                  readonly
                  type="textarea"
                  :rows="4"
                  placeholder="请输入内容"
                  v-model="data.remark"
                  >
                </el-input>
              </el-col>
            </el-form-item>
          </el-col>
        </el-col>
        <el-col :span="24">
          <el-col :span="16">
            <el-form-item label="应急物资：">
              <el-col :span="24">
                <el-table
                  :data="data.emgHandleGoodsList"
                  style="width: 300px;">
                  <el-table-column
                    type="index"
                    width="50">
                  </el-table-column>

                  <el-table-column
                    prop="name"
                    label="物资名称"
                    width="180">
                  </el-table-column>
                </el-table>
              </el-col>
            </el-form-item>
          </el-col>
        </el-col>

        <el-col :offset="8" :span="12">
          <el-button type="primary" @click="$router.back()">返回</el-button>
        </el-col>
      </el-form>
    </div>
</template>

<script>
    export default {
      data(){
        return {

          data : {},

          // 分类名称
          typeName : '',

          // 执行操作
          operateList : '',

          // 帮助
          helpList : '',

        }
      },
      computed:{

      },
      create(){
        this.getInfoHandle();
      },
      mounted(){
        this.getInfoHandle();
      },
      watch:{
        $route(to, from){
          if( from.name === 'emerPlan' && this.$route.name==='emerHandleLookPlan' ){
            this.getInfoHandle();
          }
        },
        // 监听路由的参数变化
        '$route.params.planId':function(newVal, oldVal){
          this.getInfoHandle();
        }
      },
      methods:{

        // 根据 id 获取数据
        getInfoHandle(){
          let that = this;
          let type = [];
          let params = {
            id : this.$route.params.planId
          }
          this.$store.dispatch('postDisasterTreatmentOneAction', params).then(function(res){
            this.data = res;
            this.operateList = '';
            this.helpList = '';
            this.typeName = '';
            res.emgHandleLists && res.emgHandleLists.forEach(function(it, index){
              that.operateList += `${index + 1}、${it.execContent}\n`;
              that.helpList += `${index + 1}、${it.helpInfo}\n`;
            })
            if(res.topTypeName) this.typeName = res.topTypeName
            if(res.typeName) this.typeName += ' / ' + res.typeName
          }.bind(this));

        },
      }
    }
</script>

<style>
  #emerHandleLookPlan{
    background : #fff;
    padding:10px 50px;
    height:900px;
  }
  .title{
    text-align: center;
    margin-bottom: 20px;
    margin-top:30px;
    font-size: large;
    letter-spacing: 2px;
    color:#3576AA;
    border-left:5px solid #049ff1;
    border-radius: 5px;
    background-color: rgb(236,248,255);
    height: 50px;
    line-height: 50px;
  }
</style>
