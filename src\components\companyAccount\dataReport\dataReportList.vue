<template>
  <div id="dataReportList" class="background-style">
    <el-container class="container">
      <el-main>
        <el-form ref="form" :model="form" label-width="5px">
          <el-row>
            <el-col :span="3">
              <el-input placeholder="名称" ></el-input>
            </el-col>
            <el-col :span="8">
              <el-date-picker
                clearable
                v-model="creatDate"
                type="daterange"
                style="margin-left:5px"
                start-placeholder="发布时间范围">
              </el-date-picker>
            </el-col>
            <el-col :span="2">
              <el-button type="primary"
              >搜索</el-button>
            </el-col>
            <el-col :span="2">
              <el-button type="success"
                         icon="el-icon-plus">新增</el-button>
            </el-col >

          </el-row>

          <el-row  class="tableRow" style="margin-top: 20px;">
            <el-col >
              <el-table :data="dataReport.data" border >
                <el-table-column
                  prop="num"
                  label-class-name="header-style"
                  width="70"
                  label="序号"
                  type="index"
                ></el-table-column>
                <el-table-column
                  prop="title"
                  label-class-name="header-style"
                  label="名称"
                  min-width="400"
                ></el-table-column>
                <el-table-column
                  prop="companyName"
                  label-class-name="header-style"
                  label="发布单位"
                  min-width="300"
                ></el-table-column>
                <el-table-column
                  prop="circle"
                  label-class-name="header-style"
                  label="周期"
                  align="center"
                  min-width="95"
                ></el-table-column>
                <el-table-column
                  prop="pdate"
                  label-class-name="header-style"
                  label="发布日期"
                  align="center"
                  min-width="120"
                ></el-table-column>
                <el-table-column
                  fixed="right" label="操作"
                  label-class-name="header-style"
                  align="center" min-width="130"
                >
                  <template slot-scope="scope">
                    <el-button size="mini" type="success" @click.native="itemViewClick(scope.row)">查看</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
            <el-col style="margin-top:20px;">
              <el-pagination
              background
              layout="prev, pager, next"
              :current-page="1"
              :page-size="10"
              :total="2"
              @current-change ="disasterPageChangeHandle">
            </el-pagination></el-col>

          </el-row>
          <!--<el-row>-->
            <!--<el-button type="success" @click.native="templetListClick()">报告模板列表</el-button>-->
            <!--<el-button type="success" @click.native="templetViewClick()">查看报告模板</el-button>-->
            <!--<el-button type="success" @click.native="templetEditClick()">修改报告模板</el-button>-->
            <!--<el-button type="success" @click.native="dataEditClick()">数据填报</el-button>-->
            <!--<el-button type="success" @click.native="systemDataClick()">系统数据</el-button>-->
            <!--<el-button type="success" @click.native="subCompanyClick()">查看下级报告</el-button>-->
          <!--</el-row>-->
        </el-form>
      </el-main>
      </el-container>
  </div>
</template>

<script>
  export default{
    name:'dataReportList',
    data(){
      return {
        creatDate: '',
        form : {
          // 年份
          createYear : '',
          // 状态
          status : '',
          // 当前页
          pageCurrent : 1,
          // 页数大小
          pageSize : 10,
        },
        dataReport: {
          data:[
            {num: '1', title: '宁波市国资委安全生产工作履职报告书2018第二季度', companyName: '宁波交通投资集团有限公司', pdate: '2018-07-1', circle: '季度'},
            {num: '2', title: '宁波市国资委安全生产工作履职报告书2018第一季度', companyName: '宁波交通投资集团有限公司', pdate: '2018-04-1', circle: '季度'},
//            {num: '3', title: '宁波市国资委安全生产工作履职报告书3', companyName: '交投', pdate: '2018-09-30', circle: '半年度'},
//            {num: '4', title: '宁波市国资委安全生产工作履职报告书4', companyName: '交投', pdate: '2018-09-30', circle: '年度'},
//            {num: '5', title: '宁波市国资委安全生产工作履职报告书5', companyName: '交投', pdate: '2018-09-30', circle: '无'},
//            {num: '6', title: '宁波市国资委安全生产工作履职报告书6', companyName: '交投', pdate: '2018-09-30', circle: '无'},
//            {num: '7', title: '宁波市国资委安全生产工作履职报告书7', companyName: '交投', pdate: '2018-09-30', circle: '无'},
//            {num: '8', title: '宁波市国资委安全生产工作履职报告书8', companyName: '交投', pdate: '2018-09-30', circle: '无'},
//            {num: '9', title: '宁波市国资委安全生产工作履职报告书9', companyName: '交投', pdate: '2018-09-30', circle: '无'},
//            {num: '10', title: '宁波市国资委安全生产工作履职报告书10', companyName: '交投', pdate: '2018-09-30', circle: '无'}
          ]
        }
        ,

//        egrid:{
//          data:[],
//          columns:[
//            { label: '序号', prop: 'num' },
//          ],
//          columnsProps:{
//            fit : true,
//            sortable: true,
//            align : 'center',},
//          columnsSchema:{},
//          columnType:'index'
//        }
      }
    },
    mounted:function(){

    },
    methods:{
      // 查看
      itemViewClick(row){
        let name = 'dataReportDetail';
        let params={
          num: row.num,
        }
        this.$router.push({ name : name,params:params});
      },

      templetViewClick(){
        let name = 'dataReportTempletView';

        this.$router.push({ name : name});
      },
      templetListClick(){
        let name = 'dataReportTempletList';

        this.$router.push({ name : name});
      },

      templetEditClick(){
        let name = 'dataReportTempletEdit';

        this.$router.push({ name : name});
      },
      dataEditClick(){
        let name = 'dataReportDetailEdit';

        this.$router.push({ name : name});
      },
      systemDataClick(){
        let name = 'dataReportSystemData';

        this.$router.push({ name : name});
      },
      subCompanyClick(){
        let name = 'dataReportDetailSubCompany';

        this.$router.push({ name : name});
      },

    }
  }
</script>

<style>
</style>
