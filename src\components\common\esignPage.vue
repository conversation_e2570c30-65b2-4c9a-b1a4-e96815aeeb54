<template>
  <div>
    <vue-esign
      ref="esign"
      style="border: 1px dashed #c2c1c1"
      :width="width"
      :height="height"
      :isCrop="isCrop"
    />
    <div :class="isRoll ? 'btn' : ''">
      <div v-if="isRoll" style="color: grey">请横屏书写</div>
      <el-button type="danger" @click="handleReset">重置</el-button>
      <el-button type="success" @click="handleGenerate">确定</el-button>
    </div>
  </div>
</template>

<script>
import dealData from '../../assets/functions/dealData'
export default {
  name: "esignPage",
  data() {
    return {
      userId: -1,
      contentId: -1,
      isCrop: false,
      timer: '',
      isRoll: false,
    }
  },
  computed: {
    width: function () {
      if (window.innerWidth < window.innerHeight) {
        this.isRoll = true;
        return window.innerWidth * .85;
      }
    },
    height: function () {
      if (window.innerWidth < window.innerHeight) {
        this.isRoll = true;
        return window.innerHeight * .6;
      }
      // return window.innerHeight*;
    }
  },
  mounted() {
    let params = this.$route.query;
    console.log(params);

    /*if(!params){
      params = this.$route.query;
    }*/
    this.userId = params.userId;
    this.contentId = params.contentId;
    console.log(this.userId, this.contentId);

  },
  methods: {
    handleReset() { // 清除
      this.$refs.esign.reset()
    },
    handleGenerate() {
      var _this = this;
      _this.$refs.esign.generate().then(res => {
        // 将 base64 转换为 Blob
        var blob = _this.dataURLtoBlob(res);
        var tofile = _this.blobToFile(blob, this.userId + '签名.jpg');

        clearTimeout(this.timer);
        this.timer = setTimeout(async () => {
          // 创建一个新的 Image 对象
          var img = new Image();
          img.src = URL.createObjectURL(tofile);

          img.onload = function () {
            // 创建一个 canvas 元素
            var canvas = document.createElement('canvas');
            var ctx = canvas.getContext('2d');

            // 设置 canvas 的尺寸与图片原始尺寸一致
            canvas.width = img.naturalWidth; // 使用图片的原始宽度
            canvas.height = img.naturalHeight; // 使用图片的原始高度

            // 填充白色背景（避免黑色背景）
            ctx.fillStyle = '#FFFFFF'; // 白色
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // 绘制图片（保持原始尺寸）
            ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

            // 设置水印样式
            ctx.font = '18px serif';
            ctx.fillStyle = '#ddd'; // 黑色，50% 透明度
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';

            // 保存当前画布状态
            ctx.save();

            // 旋转画布并重复绘制水印
            ctx.translate(canvas.width / 2, canvas.height / 2); // 将原点移动到画布中心
            ctx.rotate((-45 * Math.PI) / 180); // 旋转 -45 度

            // 重复绘制水印
            // var text = '安全系统'+时间
            var text = '安全系统' + new Date().getFullYear() + '年' + (new Date().getMonth() + 1) + '月' + new Date().getDate() + '日';
            var spacing = 250; // 水印之间的间距

            for (var x = -canvas.width; x < canvas.width * 2; x += spacing) {
              for (var y = -canvas.height; y < canvas.height * 2; y += spacing) {
                ctx.fillText(text, x, y);
              }
            }

            // 恢复画布状态
            ctx.restore();

            // 将 canvas 转换回 Blob
            canvas.toBlob(function (blob) {
              var fileInfo = blob.slice(0, blob.size, 'image/jpeg'); // 或者使用 'image/png'
              var watermarkedFile = _this.blobToFile(fileInfo, _this.userId + '签名_水印.jpg');

              // 进行旋转操作
              if (_this.isRoll) {
                // 创建一个新的 Image 对象来加载水印后的图片
                var imgWatermarked = new Image();
                imgWatermarked.src = URL.createObjectURL(watermarkedFile);

                imgWatermarked.onload = function () {
                  var rotateCanvas = document.createElement('canvas');
                  var rotateCtx = rotateCanvas.getContext('2d');

                  // 设置旋转后的尺寸
                  rotateCanvas.width = imgWatermarked.height; // 水平旋转，宽度变为原图高度
                  rotateCanvas.height = imgWatermarked.width; // 高度变为原图宽度

                  rotateCtx.save();
                  rotateCtx.translate(rotateCanvas.width / 2, rotateCanvas.height / 2);
                  // rotateCtx.rotate(Math.PI / 2); // 顺时针旋转90度
                  // 改成旋转-90度
                  rotateCtx.rotate(-Math.PI / 2);
                  rotateCtx.drawImage(imgWatermarked, -imgWatermarked.width / 2, -imgWatermarked.height / 2);
                  rotateCtx.restore();

                  // 将旋转后的 canvas 转换回 Blob
                  rotateCanvas.toBlob(function (rotateBlob) {
                    var rotatedFile = _this.blobToFile(rotateBlob, _this.userId + '签名_水印_旋转.jpg');

                    // 将带水印且旋转的文件上传
                    const formData = new FormData();
                    formData.append('file', rotatedFile, rotatedFile.name);
                    formData.append('fileType', 9);
                    _this.ossUploadRequest(rotatedFile);
                  }, 'image/jpeg', 0.9); // 0.9 是图片质量
                };
              } else {
                // 将带水印的文件直接上传
                const formData = new FormData();
                formData.append('file', watermarkedFile, watermarkedFile.name);
                formData.append('fileType', 9);
                _this.ossUploadRequest(watermarkedFile);
              }

            }, 'image/jpeg', 0.9); // 0.9 是图片质量
          };

          // 如果图片加载失败，处理错误
          img.onerror = function () {
            console.error('图片加载失败');
          };
        });
      }).catch(err => {
        console.log('没有签字');
        // _this.$toast(err) // 画布没有签字时会执行这里 'Not Signned'
      });
    },
    // 将base64转换为blob
    dataURLtoBlob(dataurl) {
      var arr = dataurl.split(',')
      var mime = arr[0].match(/:(.*?);/)[1]
      var bstr = atob(arr[1])
      var n = bstr.length
      var u8arr = new Uint8Array(n)
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n)
      }
      return new Blob([u8arr], { type: mime })
    },
    // 将blob转换为file
    blobToFile(theBlob, fileName) {
      theBlob.lastModifiedDate = new Date()
      theBlob.name = fileName
      return theBlob
    },

    //签名直传公司logo
    ossUploadRequest(file) {
      // return;
      //获取该文件对应的sign
      let url = `sys/oss/sign?contentId=${this.contentId}&userId=${this.userId}&contentType=23&realName=${this.userId}`;

      this.$http.get(url).then(function (res) {
        if (res.data) {
          let params = new FormData();
          params.append("name", file.name);
          params.append("key", res.data.dir + file.name);
          params.append("policy", res.data.policy);
          params.append("OSSAccessKeyId", res.data.accessid);
          params.append('success_action_status', '200');
          params.append("callback", res.data.callback);
          params.append("signature", res.data.signature);
          params.append("file", file);
          this.fileHttp.post('', params, { headers: { 'Content-Type': 'multipart/form-data' } }).then(function (res) {
            if (res.data.file) {
              let resultStr = dealData.decode(res.data.file);
              let resultJson = JSON.parse(resultStr);
              // return;
              let updateParams = new URLSearchParams;
              updateParams.append("path", resultJson.path);
              updateParams.append("contentId", this.userId);
              updateParams.append("contentType", 22);
              updateParams.append("userId", this.userId);
              updateParams.append("fileName", this.userId);
              this.$http.post("user/updateUserSignFile", updateParams).then(function (res) {
                this.$message.success('上传成功');
                //这个可以关闭i微信的页面
                if (WeixinJSBridge) {
                  WeixinJSBridge.call('closeWindow');
                } else {
                  window.location.replace("about:blank");
                }
              }.bind(this)).catch(() => {
                // this.$message.error('操作失败');
              })
            } else {
              this.$message.error('上传图片失败');
            }

          }.bind(this))
        }
      }.bind(this)).catch(function (err) {
        console.log(err);
        this.$message.error('获取唯一标识失败');
      }.bind(this));
    },
    // 接口
    postApi() {
      let _this = this;
      let url = "sys/oss/sign";
      let params = {
        userId: _this.userId,
        contentId: _this.contentId,
        contentType: 23,
        realName: _this.userId
      }

      _this.$tool.postAjax({
        url: url,
        //          params : formData,
        params: params,
        type: 'post'
      }).then(function (res) {
        if (res.success) {
          _this.$message({
            showClose: true,
            message: '操作成功！',
            type: 'success'
          });
          _this.ToTestPersonDialog.isShow = false;
          _this.searchBtnClickHandle();
        } else {
          _this.$message({
            showClose: true,
            message: '操作失败！',
            type: 'error'
          });
        }
      })
    },
  }
}
</script>

<style scoped>
.btn {
  transform: rotate(90deg) translateY(-100%);
  transform-origin: top left;
}
</style>
