<template>
    <div class="background-style" style="padding: 10px">
      <el-container>
        <el-aside width="400px">
          <!--搜索区-->
            <el-col :span="24" style="margin-bottom:10px;">
              <el-date-picker
                v-model="form.year"
                type="year"
                @change="yearChangeHandle"
                placeholder="选择年">
              </el-date-picker>
            </el-col>
            <el-col :span="24">
            <egrid class="egrid"
                   stripe border
                   :data="egrid.data"
                   :columns="egrid.columns"
                   :columns-schema="egrid.columnsSchema"
                   :column-type="egrid.columnType"
                   @current-change="selectionChange">
            </egrid>
          </el-col>
         <!-- <el-col :span="24">
            <div style="margin-top: 10px">
              <el-pagination
                background
                layout="prev, pager, next"
                :current-page="tableData.pageNum"
                :page-size="form.pageSize"
                :total="tableData.total"
                @current-change ="disasterPageChangeHandle">
              </el-pagination>
            </div>
          </el-col>-->
        </el-aside>
        <el-main v-if="isClickPlanTable" style="padding: 0 0 0 10px">
          <el-col :span="24">
            <egrid class="egrid"
                   stripe border
                   maxHeight="500"
                   :data="item.data"
                   :columns="item.columns"
                   :columns-schema="item.columnsSchema"
                   :columns-handler="columnsHandler"
                   :column-type="item.columnType">
            </egrid>
          </el-col>
        </el-main>
      </el-container>

      <!--对话框-->
      <el-dialog
        :visible.sync="dialog.isShow"
        width="50%"
        :title="dialog.form.title"
        :before-close="dialogCancelBtnClickHandle">
        <el-form label-width="100px">
          <el-row  class="row">
            <el-col :span="12">
              <el-form-item label="文件编号">
                <el-input v-model="dialog.form.fileNumber"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="记录编号">
                <el-input v-model="dialog.form.recordNumber"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogCancelBtnClickHandle">取 消</el-button>
          <el-button
            type="danger"  size="mini"
            @click="dialogOkBtnClickHandle">确定</el-button>
        </div>
      </el-dialog>
      <!--节假日值班日期的对话框-->
      <el-dialog
        :visible.sync="holiday.isShow"
        width="50%"
        :title="holiday.form.title"
        :before-close="holidayCancelBtnClickHandle">

        <el-table
          border
          :data="holiday.form.list"
          style="width: 100%">
          <el-table-column
            type="index"
            label="序号"
            width="100"
            align="center"
            label-class-name="header-style">
          </el-table-column>
          <el-table-column
            prop="date"
            label="名称"
            min-width="150"
            label-class-name="header-style">
            <template slot-scope="scope">
              <span>{{transferTime(scope.row.date)}}</span>
            </template>
          </el-table-column>
          <el-table-column
            fixed="right" label="操作"
            label-class-name="header-style"
            align="left" width="200">
            <template slot-scope="scope">
              <el-button size="mini" type="primary" @click="holidayRowDownload(scope.row)">下载</el-button>
              <el-button size="mini" type="danger" @click="holidayRowPrint(scope.row)">预览</el-button>
            </template>
          </el-table-column>
        </el-table>

      </el-dialog>
    </div>

</template>

<script>
    // 单元格的组件
    var Editor = {
      // 查看不需要了，下载和预览合并了
//      <el-button type="primary" size="mini" @click="viewBtnClickHandler">查看</el-button>
//      <el-button type="danger" size="mini" @click="downloadBtnClickHandler">下载</el-button>
//      <el-button type="primary" size="mini" @click="printBtnClickHandler">预览</el-button>


      template:
        `<div style="width:280px;">
          <el-button type="success" size="mini" @click="editBtnClickHandler">修改</el-button>
          <el-button type="primary" size="mini" @click="printOrDownload">下载预览</el-button>
        </div>`,
      props: ['row', 'col'],
      methods:{
        // 查看按钮
        viewBtnClickHandler(){
          this.$emit('row-view', this.row)
        },
        // 修改按钮
        editBtnClickHandler(){
          let viewRole = this.$tool.judgeViewRole();
          if(!viewRole){
            this.$emit('row-edit', this.row)
          }
        },
        // 下载按钮
        downloadBtnClickHandler(){
          this.$emit('row-download', this.row)
        },

        // 打印按钮
        printBtnClickHandler(){
          this.$emit('row-print', this.row)
        },
        // 预览和下载合并的按钮
        printOrDownload(){
          this.$emit('row-print-download', this.row)
        }
      }
    }

    export default {
      data(){
        return {
          // 点击打印加载
          loginLoading : '',
          form : {
            // 当前页
            pageCurrent : 1,
            // 页数大小
            pageSize : 5,
            // 预算类型为年度预算
            budgetType: 3,
            year : this.$tool.formatDateTime(new Date()).substring(0,4),
          },
          // 左边的分页信心
          tableData : {},
          // 表格---左边计划
          egrid : {
            data : [],
            columns : [
              { label: '节假日类型', prop: 'title' },
              { label: '年份', prop: 'year' },
            ],
            // columnsProps 用于定义所有 columns 公共的属性
            columnsProps: {
              fit : true,
              sortable: true,
              align : 'center',
            },
            columnsSchema : {
              '年份':{
                width : 100,
              }
            },
            columnType : 'index'
          },
          // 表格---右边明细
          item : {
            data : [
              { title : '年度安全生产投入预算计划表', name : 'plan', year : '' },
              { title : '年度安全生产费用投入登记表', name : 'input', year : '' },
              { title : '年度安全生产投入预算执行情况表', name : 'implement', year : '' },
              { title : '受伤员工获赔记录表', name : 'injury', year : '' },
            ],
            columns : [
              { label: '文件', prop: 'title' },
              { label: '年份', prop: 'year' },
              { label: '文件编号', prop: 'fileNumber' },
              { label: '记录编号', prop: 'recordNumber' },
            ],
            // columnsProps 用于定义所有 columns 公共的属性
            columnsProps: {
              fit : true,
              sortable: true,
              align : 'center',
            },
          },
          // 是否点击了左边的计划
          isClickPlanTable : false,


          // 文件编号-对话框
          dialog : {
            // 是否显示
            isShow : false,
            form : {
              id : '',
              title : '',
              fileNumber : '',
              recordNumber : '',
            },
          },

          // 节假日-对话框
          holiday : {
            // 是否显示
            isShow : false,
            title : '节假日值班日期列表',
            form : {
              list : []
            },
          },

        }
      },
      mounted(){
        this.init();
      },
      watch:{
        $route(to,from){
          if(to.name === 'holidayWorkIndex') {
            this.init();
          }
        }
      },
      methods:{
        // 初始化
        init(){
          this.isClickPlanTable = false;
          // 搜索
          this.searchBtnClickHandle();
        },
        // 根据年份搜索左侧
        yearChangeHandle(e){
          this.form.year = this.$tool.formatDateTime(e).substring(0,4);
          this.searchBtnClickHandle();
          // this.item.data.forEach(function(it){
          //   it.year = this.$tool.formatDateTime(this.form.year).substring(0,4);
          // }.bind(this))
        },
        // 分页
        disasterPageChangeHandle(page){
          this.form.pageCurrent = page;
          this.searchBtnClickHandle();
        },
        // 左边表格数据-----搜索按钮
        searchBtnClickHandle(){
          this.$store.dispatch('sysDutyTypeFind', this.form).then(function(res){
            if(res.success){
              this.tableData = res.data;
              let list = res.data.map(function(it){
                let year = this.form.year;
                return {
                  title : it.name,
                  year : year,
                  codeNum : it.codeNum,
                  holidayName : it.name
                }
              }.bind(this))
//              console.log(111,list)
              this.egrid.data = list;
            }
          }.bind(this));
        },
        // 左边表格数据-----单行选择
        selectionChange (row) {
//          console.log('左边：', row);
          this.isClickPlanTable = true;
          // 获取文件接口
          this.$store.dispatch('reportCostGetDutyReportTableInfo', {year:row.year,code:row.codeNum}).then(function(res){
            if(res.success){
              this.item.data = res.data.list.map(function(it){
                return {
                  id : it.id,
                  title : row.holidayName + it.title,
                  codeNum : row.codeNum,
                  holidayName : row.holidayName,
                  name : it.name,
                  fileNumber : it.fileNumber,
                  recordNumber : it.recordNumber,
                  year : row.year
                }
              }.bind(this))
            }
          }.bind(this));

//          this.item.data.forEach(function(it){
//            it['year'] = row.year;
//          }.bind(this))
        },
        // egrid---操作列
        columnsHandler (cols) {
          let that = this;
          let timer = null;
          return cols.concat({
            label: '操作',
            fixed: 'right',
            width: 250,
            component: Editor,
            listeners: {
              'row-edit' (row) {
//                console.log(row);
                that.dialog.isShow = true;
                that.dialog.form.id = row.id;
                that.dialog.form.title = row.title;
                that.dialog.form.name = row.name;
                that.dialog.form.fileNumber = row.fileNumber;
                that.dialog.form.recordNumber = row.recordNumber;
              },
              'row-download'(row){

                let setting = {
                  url : `/report/getHolidayRecordExcel/${row.year}/${row.codeNum}/${row.fileNumber}/${row.recordNumber}`,
                  filename : `${row.year}${row.holidayName}值班表.xlsx`,
                };

                that.$http({ // 用axios发送post请求
                  method: 'get',
                  url: setting.url, // 请求地址
                  responseType: 'blob' // 表明返回服务器返回的数据类型
                }).then(function(res){ // 处理返回的文件流
                  if (res.request.responseType == 'blob'){
                    var reader = new FileReader();
                    reader.readAsText(res.data, 'utf-8');
                    reader.onload = function () {
                      try{
                        // 如果有报错信息，那么blob转json就有值，否则没有
                        let data = JSON.parse(reader.result);
//                        console.log("blob:", data)
                        that.$message({
                          type : 'error',
                          message : data.message || '错误'
                        })
                      }catch (e){
                        const content = res;
                        const elink = document.createElement('a') // 创建a标签
                        elink.download =  setting.filename // 文件名
                        elink.style.display = 'none'
                        const blob = new Blob([res.data])
                        elink.href = URL.createObjectURL(blob)
                        document.body.appendChild(elink)
                        elink.click() // 触发点击a标签事件
                        document.body.removeChild(elink)
                      }
                    }
                  }
                })


               // that.$tool.download(that, setting);


                return;

              },
              'row-print'(row){
                let setting = {
                  url : `/report/getHolidayRecordHtml/${row.year}/${row.codeNum}/${row.fileNumber}/${row.recordNumber}`,
                  filename : `${row.year}${row.holidayName}值班表.xlsx`,
                };

                that.loginLoading=that.$loading({
                  lock: true,
                  text: '数据加载中',
                  spinner: 'el-icon-loading',
                  background: 'rgba(0, 0, 0, 0.5)'
                });
                that.$store.dispatch('printOnLine', {
                  url : setting.url
                }).then(function(res){
                  that.loginLoading.close();//关闭登陆加载
                  if(res.success){
                    let myWindow=window.open("".href, "_blank");
                    myWindow.document.write(res.data);
                    window.clearTimeout(timer);
                    timer = window.setTimeout(function(){
                      myWindow.print();
                    }, 300)
//                    myWindow.print()
                  } else {
                    that.$message({
                      type : 'error',
                      message : res.message || '调用打印接口失败！！'
                    })
                  }
                }.bind(this))

              },

              'row-print-download'(row){

                console.log(row);
                // 打开节假日值班的对话框
                that.holiday.isShow = true;
//                this.holiday.title = row.title;
                // ajax
                that.$store.dispatch('reportGetHolidayDayList', {
                  year : row.year,
                  codeNum : row.codeNum,

                }).then(function(res){
                  if(res.success){
                    if(res.data.length > 0){
                      that.holiday.form.list = res.data.map(function(it){
                        return {
                          date : it,
                          data : row
                        }
                      })
                    }else{
                      that.holiday.form.list = []
                    }
                  } else {
                    that.$message({
                      type : 'error',
                      message : res.message || '调用打印接口失败！！'
                    })
                  }
                }.bind(this))


                return;


                let setting = {
                  url : `/report/getHolidayRecordHtml/${row.year}/${row.codeNum}/${row.fileNumber}/${row.recordNumber}`,
                  filename : `${row.year}${row.holidayName}值班表.xlsx`,
                };

                that.loginLoading=that.$loading({
                  lock: true,
                  text: '数据加载中',
                  spinner: 'el-icon-loading',
                  background: 'rgba(0, 0, 0, 0.5)'
                });
                that.$store.dispatch('printOnLine', {
                  url : setting.url
                }).then(function(res){
                  that.loginLoading.close();//关闭登陆加载
                  if(res.success){
                    let myWindow=window.open("".href, "_blank");
                    myWindow.document.write(res.data);
                    window.clearTimeout(timer);
                    timer = window.setTimeout(function(){
                      myWindow.print();
                    }, 300)
//                    myWindow.print()
                  } else {
                    that.$message({
                      type : 'error',
                      message : res.message || '调用打印接口失败！！'
                    })
                  }
                }.bind(this))

              }
            }
          });
        },

        // 文件编号对话框--取消按钮
        dialogCancelBtnClickHandle(){
          // 关闭对话框
          this.dialog.isShow = false;
          // 情况对话框
          this.dialog.form.id = '';
          this.dialog.form.title = '';
          this.dialog.form.fileNumber = '';
          this.dialog.form.recordNumber = '';
        },
        // 文件编号对话框--确定按钮
        dialogOkBtnClickHandle(){
          let params = {
            id : this.dialog.form.id,
            fileNumber : this.dialog.form.fileNumber,
            recordNumber : this.dialog.form.recordNumber,
          }
          this.$store.dispatch('reportCostUpdateCostReportTableInfo', params).then(function(res){
            if(res.success){
              // 关闭对话框，情况数据
              this.dialogCancelBtnClickHandle();
              // 执行点击坐标，刷新右边
              this.item.data.forEach(function(it){
                if(it.id == params.id){
                  it.fileNumber = params.fileNumber;
                  it.recordNumber = params.recordNumber;
                }
              }.bind(this))
            } else {
              this.$message({
                type : 'error',
                message : res.message || '错误'
              })
            }
          }.bind(this));
        },

        holidayRowDownload(row){
          var that = this;
          let dutyDate = this.$formatDate(row.date);
          let setting = {
            url : `/report/getHolidayRecordExcel/${row.data.year}/${row.data.codeNum}/${row.data.fileNumber}/${row.data.recordNumber}/${dutyDate}`,
            filename : `${dutyDate}${row.data.holidayName}值班表.xlsx`,
          };

          that.$http({ // 用axios发送post请求
            method: 'get',
            url: setting.url, // 请求地址
            responseType: 'blob' // 表明返回服务器返回的数据类型
          }).then(function(res){ // 处理返回的文件流
            if (res.request.responseType == 'blob'){
              var reader = new FileReader();
              reader.readAsText(res.data, 'utf-8');
              reader.onload = function () {
                try{
                  // 如果有报错信息，那么blob转json就有值，否则没有
                  let data = JSON.parse(reader.result);
//                        console.log("blob:", data)
                  that.$message({
                    type : 'error',
                    message : data.message || '错误'
                  })
                }catch (e){
                  const content = res;
                  const elink = document.createElement('a') // 创建a标签
                  elink.download =  setting.filename // 文件名
                  elink.style.display = 'none'
                  const blob = new Blob([res.data])
                  elink.href = URL.createObjectURL(blob)
                  document.body.appendChild(elink)
                  elink.click() // 触发点击a标签事件
                  document.body.removeChild(elink)
                }
              }
            }
          })


          // that.$tool.download(that, setting);


          return;

        },
        holidayRowPrint(row){
          let timer = null;
          var that = this;
          let dutyDate = this.$formatDate(row.date);
          let setting = {
            url : `/report/getHolidayRecordHtml/${row.data.year}/${row.data.codeNum}/${row.data.fileNumber}/${row.data.recordNumber}/${dutyDate}`,
            filename : `${dutyDate}${row.holidayName}值班表.xlsx`,
          };

          that.loginLoading=that.$loading({
            lock: true,
            text: '数据加载中',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.5)'
          });
          that.$store.dispatch('printOnLine', {
            url : setting.url
          }).then(function(res){
            that.loginLoading.close();//关闭登陆加载
            if(res.success){
              let myWindow=window.open("".href, "_blank");
              myWindow.document.write(res.data);
              window.clearTimeout(timer);
              timer = window.setTimeout(function(){
                myWindow.print();
              }, 300)
//                    myWindow.print()
            } else {
              that.$message({
                type : 'error',
                message : res.message || '调用打印接口失败！！'
              })
            }
          }.bind(this))

        },

        // 节假日对话框--取消按钮
        holidayCancelBtnClickHandle(){
          // 关闭对话框
          this.holiday.isShow = false;
          // 情况对话框
//          this.dialog.form.id = '';
//          this.dialog.form.title = '';
//          this.dialog.form.fileNumber = '';
//          this.dialog.form.recordNumber = '';
        },
        // 节假日对话框--确定按钮
        holidayOkBtnClickHandle(){


          this.dialogCancelBtnClickHandle();

          return;


          let params = {
            id : this.dialog.form.id,
            fileNumber : this.dialog.form.fileNumber,
            recordNumber : this.dialog.form.recordNumber,
          }
          this.$store.dispatch('reportCostUpdateCostReportTableInfo', params).then(function(res){
            if(res.success){
              // 关闭对话框，情况数据
              this.dialogCancelBtnClickHandle();
              // 执行点击坐标，刷新右边
              this.item.data.forEach(function(it){
                if(it.id == params.id){
                  it.fileNumber = params.fileNumber;
                  it.recordNumber = params.recordNumber;
                }
              }.bind(this))
            } else {
              this.$message({
                type : 'error',
                message : res.message || '错误'
              })
            }
          }.bind(this));
        }
      }
    }
</script>

<style>
</style>
