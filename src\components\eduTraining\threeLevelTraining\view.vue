<template>
    <el-container class="container">
      <el-main>
        <el-form ref="form" label-width="100px">
          <el-row type="flex">
            <el-col :span="8">
              <el-form-item class="formItem" label="姓名">
                <span>{{form.eduUser.username}}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item class="formItem" label="性别">
                <span>{{form.eduUser.gender ? '男' : '女'}}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item class="formItem" label="出身年月">
                <span>{{$tool.formatDateTime(form.eduUser.birthday)}}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex">
            <el-col :span="8">
              <el-form-item class="formItem" label="文化程度">
                <span>{{form.eduUser.degreeOfEducation}}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item class="formItem" label="入职时间">
                <span>{{$tool.formatDateTime(form.eduUser.birthday)}}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item class="formItem" label="部门">
                <span>{{form.eduUser.deptName}}</span>
                <span></span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="公司培训：" class="title">
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex">
            <el-col :offset="2" :span="7">
              <el-form-item class="formItem" label="培训时间">
                <span>{{$tool.formatDateTime(form.eduEntryTrainingCompany.trainingDate)}}</span>
              </el-form-item>
            </el-col>
            <el-col :span="7">
              <el-form-item class="formItem" label="学时">
                <span>{{form.eduEntryTrainingCompany.trainingHours}}</span>
              </el-form-item>
            </el-col>
            <el-col :span="7">
              <el-form-item class="formItem" label="教育者">
                <span>{{form.eduEntryTrainingCompany.teacher}}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex">
            <el-col :offset="2" :span="22">
              <el-form-item class="formItem" label="培训内容">
                <span>{{form.eduEntryTrainingCompany.courses}}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex">
            <el-col :offset="2" :span="7">
              <el-form-item class="formItem" label="公司培训成绩">
                <span>{{form.eduEntryTrainingCompany.score}}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item  label="部门培训：" class="title">
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex">
            <el-col :offset="2" :span="7">
              <el-form-item class="formItem" label="培训时间">
                <span>{{$tool.formatDateTime(form.eduEntryTrainingDepartment.trainingDate)}}</span>
              </el-form-item>
            </el-col>
            <el-col :span="7">
              <el-form-item class="formItem" label="学时">
                <span>{{form.eduEntryTrainingDepartment.trainingHours}}</span>
              </el-form-item>
            </el-col>
            <el-col :span="7">
              <el-form-item class="formItem" label="教育者">
                <span>{{form.eduEntryTrainingDepartment.teacher}}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex">
            <el-col :offset="2" :span="22">
              <el-form-item class="formItem" label="培训内容">
                <span>{{form.eduEntryTrainingDepartment.courses}}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex">
            <el-col :offset="2" :span="7">
              <el-form-item class="formItem" label="部门培训成绩">
                <span>{{form.eduEntryTrainingDepartment.score}}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="班组培训：" class="title">
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex">
            <el-col :offset="2" :span="7">
              <el-form-item class="formItem" label="培训时间">
                <span>{{$tool.formatDateTime(form.eduEntryTrainingTeam.trainingDate)}}</span>
              </el-form-item>
            </el-col>
            <el-col :span="7">
              <el-form-item class="formItem" label="学时">
                <span>{{form.eduEntryTrainingTeam.trainingHours}}</span>
              </el-form-item>
            </el-col>
            <el-col :span="7">
              <el-form-item class="formItem" label="教育者">
                <span>{{form.eduEntryTrainingTeam.teacher}}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex">
            <el-col :offset="2" :span="22">
              <el-form-item class="formItem" label="培训内容">
                <span>{{form.eduEntryTrainingTeam.courses}}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex">
            <el-col :offset="2" :span="7">
              <el-form-item class="formItem" label="班组培训成绩">
                <span>{{form.eduEntryTrainingTeam.score}}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex">
            <el-col :span="6">
              <el-form-item class="formItem" label="考试成绩">
                <span>{{form.score}}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex" class="row" justify="center">
            <el-button size="small" :span="2" @click="$router.back();">返回</el-button>
          </el-row>
        </el-form>
      </el-main>
    </el-container>
</template>

<script>

    export default {
      data(){
        return {
          form : {

          }
        }
      },
      watch:{
        $route(to,from){
          // 如果来至列表页
          if(from.name === 'threeLevelTrainingIndex'){
            this.init();
          }
        }
      },
      created(){
        this.init();
      },
      mounted(){
        this.init();
      },
      methods:{
        // 初始化
        init(){
          this.searchBtnClickHandle();
        },
        // 根据id搜索信息
        searchBtnClickHandle(){
          let id = this.$route.params.id;
          this.$store.dispatch('eduEntryTrainingFind', { id : id }).then(function(res){
            if(res.success){
              let list = res.data.list[0];
              this.form = list;
            } else {
              this.$message({
                type : 'error',
                message : res.message || '错误'
              })
            }
          }.bind(this));
        },
      }
    }
</script>

<style>
  .container{
    background:#fff;
    padding:0px 20px 20px;
  }
  .title{
    background:rgba(64,158,255,.1);
    color:#0f6fc6;
    border: 1px solid rgba(64,158,255,.2);
    border-radius:5px;
  }
  .row{
    margin-top:10px;
  }
  .formItem{
    margin:2px;
  }
</style>
