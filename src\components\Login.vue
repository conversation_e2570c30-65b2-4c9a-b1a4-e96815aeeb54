<template>
  <div id="loginPage" style="width: 100%;height: 100%;background-color: #e6e8ec;position: absolute">
    <!--日历-->
    <div style="width: 300px;height: 325px;left: 18%;bottom:37%;position: absolute;background: url('../static/imgs/public/日历.png');z-index: 3">
      <div style="width: 151px;height: 30px;top:85px;left: 85px;position: absolute;font-family: Arial;font-size: 22px;color: #282828;text-align: center">{{currentMonth}}</div>
      <div style="width: 151px;height: 120px;top:110px;left: 83px;position: absolute;font-family: Arial;font-size: 110px;color: #00489E;text-align: center;">{{currentDay}}</div>
      <div style="width: 151px;height: 40px;top:245px;left: 85px;position: absolute;font-family: Arial;font-size: 18px;color: #FFF;text-align: center">{{currentWeek}}</div>
    </div>
    <!--登陆框-->
    <div style="width: 400px;height: 380px;left: 55%;bottom:30%;position: absolute;z-index: 3;background-color: rgba(255,255,255,0.1);border-radius: 20px">
      <div style="width: 20px;height: 360px;background: url('../static/imgs/public/左边线.png');background-size:100% 100%;top:0;left: -20px;position: absolute"></div>
      <div style="width:100px;height: 114px;background: url('../static/imgs/public/logo-login.png');top:10px;left: 150px;position: absolute"></div>
      <div style="width: 20px;height: 360px;background: url('../static/imgs/public/右边线.png');background-size:100% 100%;top:0;left: 400px;position: absolute"></div>
      <div style="width: 400px;height: 20px;background: url('../static/imgs/public/下边线.png');background-size:100% 100%;top:380px;left: 0;position: absolute"></div>

      <div style="width: 280px;height: 200px;top:160px;left:60px;position: absolute;">
        <input style="border-radius: 30px;width: 230px;height: 30px;border: 1px solid #C2C2C2;padding-left: 50px;" class="form-control input-img-1" type="text" v-model="user.name" placeholder="登录名"/>
        <input style="border-radius: 30px;width: 230px;height: 30px;border: 1px solid #C2C2C2;padding-left: 50px;margin-top: 20px;" class="form-control input-img-2" type="password" v-model="user.password" placeholder="密码"/>
        <el-button type="primary" round style="width: 295px;margin-top: 30px;height: 40px;background-color: #00489e;border: 1px solid #00489e" @click="loginClick">登  录</el-button>
      </div>
    </div>
    <!--背景图-->
    <div style="bottom: 0;position: absolute;width:100%;height: 36%;background: url('../static/imgs/public/login-background.png') center no-repeat;background-size:100% 100%">
      <div style="color: #ffffff;bottom: 10px;right: 10px;position:absolute">版权所有©宁波市交通建设工程试验检测中心有限公司 <a href="https://beian.miit.gov.cn/" target="_blank" style="color: #ffffff">浙ICP备12022514号-2</a></div>
    </div>
  </div>
</template>
<script>
  import {mapActions} from 'vuex'
  export default {
    name: 'login',
    data() {
      return {
        user: {name: '', password: ''},
        fname:'',
        fcodeid:'',
        loginPage:true,
        menuPage:false,

        //当前时间
        currentMonth:'',
        currentDay:'',
        currentWeek:'',
        monthArray:['JANUARY','FEBRUARY','MARCH','APRIL','MAY','JUNE','JULY','AUGUST','SEPTEMBER','OCTOBER','NOVEMBER','DECEMBER'],
        weekArray:['SUNDAY','MONDAY','TUESDAY','WEDNESDAY','THURSDAY','FRIDAY','SATURDAY'],

        //登陆加载提示
        loginLoading:''
      }
    },
    mounted:function () {
      let currentTime=new Date();
      this.currentMonth=this.monthArray[currentTime.getMonth()];
      this.currentDay=currentTime.getDate();
      this.currentWeek=this.weekArray[currentTime.getDay()];

      // 单点登录
      var tokenId = this.$tool.getQueryString("tokenId");
      var loginName2 = this.$tool.getQueryString("loginName2");
      //矿山安全平台单点登录
      if(loginName2){
        this.login2()
      }
      if(tokenId){
        this.ssoValidate();
      }
    },
    methods:{
      // login2 单点登录矿山
      login2:function(){
        var start=new Date().getTime();
        this.loginLoading=this.$loading({
          lock: true,
          text: '登陆中',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.5)'
        });
        // URL http://localhost:8084/#/?tokenId=242c1a3f-2b52-43dc-8d2b-84e4ab332417&uid=admin&loginName=%E7%AE%A1%E7%90%86%E5%91%98
        // 从BPM站点中获取对应的参数信息
        // 其中getQueryString方法是封住的方法
        // 用于获取URL参数的
        var loginName2 = this.$tool.getQueryString("loginName2");
        // 将数据传递给后台，后台获取参数信息，进行验证之后，返回信息
        this.$http.post('/login2','loginName='+loginName2).then(function (resp){
          if(resp.data.success){
            var step1=new Date().getTime();
            console.log(step1-start)
            localStorage.setItem('SAFE_PLATFORM_USERNAME', resp.data.user.username);//浏览器端存储用户名字
            // localStorage.setItem('NOTICE_COUNT', resp.data.user.sysNoticePageInfo.total);//存储未阅读通知数量
            localStorage.setItem('NOTICE_COUNT', resp.data.user.unreadNoticeCount);//存储未阅读通知数量
            localStorage.setItem('LOGO_FILE_ID',resp.data.user.logoFileId);
            this.$tool.setStorage('LOGIN_USER', resp.data.user);
            this.$store.dispatch('sendCurrentUser',resp.data.user);
            this.$store.dispatch('sendHeadLogoPath',resp.data.user.headPath);

            this.findMenu(true);
          }else {
            this.loginLoading.close();//关闭登陆加载
            this.$message({
              showClose: true,
              message: resp.data.message || '免登陆失败',
              type: 'warning'
            });
          }
        }.bind(this)).catch(function (err) {
          console.log(err)
        }.bind(this));
      },
      // 单点登录验证
      ssoValidate:function(){
        var start=new Date().getTime();
        this.loginLoading=this.$loading({
          lock: true,
          text: '登陆中',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.5)'
        });
        // URL http://localhost:8084/#/?tokenId=242c1a3f-2b52-43dc-8d2b-84e4ab332417&uid=admin&loginName=%E7%AE%A1%E7%90%86%E5%91%98
        // 从BPM站点中获取对应的参数信息
        // 其中getQueryString方法是封住的方法
        // 用于获取URL参数的
        var tokenId = this.$tool.getQueryString("tokenId");
        var userName = this.$tool.getQueryString("loginName");
        var uid = this.$tool.getQueryString("uid");

        // 姓名+身份证后四位
        var ssoLoginName = userName + uid.substr(-4)
        if(tokenId == ''){
          this.loginLoading.close();//关闭登陆加载
          return
        }
        // 将数据传递给后台，后台获取参数信息，进行验证之后，返回信息
        this.$http.post('/SSOlogin','userName='+ssoLoginName+'&tokenId='+tokenId).then(function (resp){
          if(resp.data.success){
            var step1=new Date().getTime();
            console.log(step1-start)
            localStorage.setItem('SAFE_PLATFORM_USERNAME', resp.data.user.username);//浏览器端存储用户名字
            // localStorage.setItem('NOTICE_COUNT', resp.data.user.sysNoticePageInfo.total);//存储未阅读通知数量
            localStorage.setItem('NOTICE_COUNT', resp.data.user.unreadNoticeCount);//存储未阅读通知数量
            localStorage.setItem('LOGO_FILE_ID',resp.data.user.logoFileId);
            this.$tool.setStorage('LOGIN_USER', resp.data.user);
            this.$store.dispatch('sendCurrentUser',resp.data.user);
            this.$store.dispatch('sendHeadLogoPath',resp.data.user.headPath);

            this.findMenu();
          }else {
            this.loginLoading.close();//关闭登陆加载
            this.$message({
              showClose: true,
              message: resp.data.message || '免登陆失败',
              type: 'warning'
            });
          }
        }.bind(this)).catch(function (err) {
          console.log(err)
        }.bind(this));
      },
      loginClick:function () {
        var start=new Date().getTime();
        this.loginLoading=this.$loading({
          lock: true,
          text: '登陆中',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.5)'
        });
        this.$http.post('login','loginName='+this.user.name.trim()+'&password='+this.user.password).then(function (resp){
          if(resp.data.success){
            var step1=new Date().getTime();
            console.log(step1-start)
            localStorage.setItem('SAFE_PLATFORM_USERNAME', resp.data.user.username);//浏览器端存储用户名字
            // localStorage.setItem('NOTICE_COUNT', resp.data.user.sysNoticePageInfo.total);//存储未阅读通知数量
            localStorage.setItem('NOTICE_COUNT', resp.data.user.unreadNoticeCount);//存储未阅读通知数量
            localStorage.setItem('LOGO_PATH',resp.data.user.companyLogPath);
            this.$tool.setStorage('LOGIN_USER', resp.data.user);
            this.$store.dispatch('sendCurrentUser',resp.data.user);
            this.$store.dispatch('sendHeadLogoPath',resp.data.user.headPath);
            this.findMenu();
          }else {
            this.loginLoading.close();//关闭登陆加载
            this.$message({
              showClose: true,
              message: '用户名或者密码错误！',
              type: 'warning'
            });
          }
        }.bind(this)).catch(function (err) {
          this.loginLoading.close();//关闭登陆加载
          console.log(err);
          this.$message({
            showClose: true,
            message: '网络错误',
            type: 'error'
          });
        }.bind(this));
      },
      findMenu:function (mine) {
        var start=new Date().getTime();
        this.$http.post('permission/menu').then(function (resp){
          if(resp.data.success){
            var step1=new Date().getTime();
            console.log(step1-start)
            let tempData=resp.data.data;
            let resultData={mainMenu:[],subMenu:{}};
            for (let i=0;i<tempData.length;i++){
              resultData.mainMenu.push({id:tempData[i].id,icon:tempData[i].icon,permissionName:tempData[i].permissionName,url:tempData[i].url});
              switch (tempData[i].url){
                case '/edu-training-menu':
                  resultData.subMenu.eduTrainingMenu=tempData[i].list;
                  break;
                case '/hide-danger-menu':
                  resultData.subMenu.hideDangerMenu=tempData[i].list;
                  break;
                case '/emer-menu':
                  resultData.subMenu.emerMenu=tempData[i].list;
                  break;
                case '/company-account':
                  resultData.subMenu.companyAccount=tempData[i].list;
                  break;
                case '/manage-menu':
                  resultData.subMenu.manageMenu=tempData[i].list;
                  break;
                case '/safety-input-menu':
                  resultData.subMenu.safetyInputMenu=tempData[i].list;
                  break;
                default:
                  break;
              }
            }
            this.loginLoading.close();//关闭登陆加载
            this.$tool.setStorage('SAFE_PLATFORM_MENU', resultData);
            if(mine){
            this.$router.push('/manage-menu/minehome');
            }else{
            this.$router.push('/menu');
            }
          }
        }.bind(this)).catch(function (err) {
          this.loginLoading.close();//关闭登陆加载
          console.log(err);
          this.$message({
            showClose: true,
            message: '获取目录失败',
            type: 'error'
          });
        }.bind(this));
      },
    }

  }
</script>
<style>
  .form-control {
    display: block;
    width: 100%;
    height: 34px;
    padding: 6px 12px;
    font-size: 14px;
    line-height: 1.42857143;
    color: #555;
    background-color: #fff;
    background-image: none;
    border: 1px solid #ccc;
    border-radius: 4px;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
    -webkit-transition: border-color ease-in-out .15s, -webkit-box-shadow ease-in-out .15s;
    -o-transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
    transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
  }
  .form-control:focus {
    border-color: #66afe9;
    outline: 0;
    -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(102, 175, 233, .6);
    box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(102, 175, 233, .6);
  }
  .input-img-1{background: url('../static/imgs/public/账号.png') left no-repeat;background-position-x: 16px}
  .input-img-2{background: url('../static/imgs/public/密码.png') left no-repeat;background-position-x: 16px}
</style>
