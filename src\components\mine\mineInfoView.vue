<template>
  <el-container class="container">
    <el-main>
      <!--发布-->
      <el-form
        ref="info"
        :disabled="disabled"
        label-width="250px"
        :model="info"
      >
        <div class="form">
          <div class="form-left">
            <el-form-item label="矿山编码" prop="mineId">
              <el-input v-model="info.mineId"></el-input>
            </el-form-item>

            <el-form-item label="矿山名称" prop="mineName">
              <el-input v-model="info.mineName"></el-input>
            </el-form-item>

            <el-form-item label="所在市" prop="cityName">
              <el-input v-model="info.cityName"></el-input>
            </el-form-item>

            <el-form-item label="所在县" prop="countyName">
              <el-input v-model="info.countyName"></el-input>
            </el-form-item>

            <el-form-item label="所在乡镇" prop="townName">
              <el-input v-model="info.townName"></el-input>
            </el-form-item>

            <el-form-item label="详细地址" prop="detailAddr">
              <el-input v-model="info.detailAddr"></el-input>
            </el-form-item>

            <el-form-item label="经度" prop="longitude">
              <el-input v-model="info.longitude"></el-input>
            </el-form-item>

            <el-form-item label="纬度" prop="latitude">
              <el-input v-model="info.latitude"></el-input>
            </el-form-item>

            <el-form-item label="日常安全监管主体" prop="supeMainBody">
              <el-input v-model="info.supeMainBody"></el-input>
            </el-form-item>

            <el-form-item label="生产状态" prop="procStatus">
              <el-input v-model="info.procStatus"></el-input>
            </el-form-item>

            <el-form-item label="主要矿种" prop="mineKind">
              <el-input v-model="info.mineKind"></el-input>
            </el-form-item>

            <el-form-item label="其他矿种名称" prop="otherMineKind">
              <el-input v-model="info.otherMineKind"></el-input>
            </el-form-item>

            <el-form-item label="开采方式" prop="digMode">
              <el-input v-model="info.digMode"></el-input>
            </el-form-item>
            <el-form-item label="开拓方式" prop="exMode">
              <el-input v-model="info.exMode"></el-input>
            </el-form-item>

            <el-form-item label="标准化等级" prop="standardBuild">
              <el-input v-model="info.standardBuild"></el-input>
            </el-form-item>

            <el-form-item label="双重预防机制" prop="doublePrevent">
              <el-input v-model="info.doublePrevent"></el-input>
            </el-form-item>

            <el-form-item label="风险分级管控" prop="riskCtrlClass">
              <el-input v-model="info.riskCtrlClass"></el-input>
            </el-form-item>

            <el-form-item label="固有风险等级" prop="fixRiskLevel">
              <el-input v-model="info.fixRiskLevel"></el-input>
            </el-form-item>

            <el-form-item label="风险管控水平" prop="riskCtrlLevel">
              <el-input v-model="info.riskCtrlLevel"></el-input>
            </el-form-item>

            <el-form-item label="监管类别" prop="supeKind">
              <el-input v-model="info.supeKind"></el-input>
            </el-form-item>

            <el-form-item label="开采存储(万吨)" prop="digStor">
              <el-input v-model="info.digStor"></el-input>
            </el-form-item>
            <el-form-item label="设计生产规模(万吨/年)" prop="designScale">
              <el-input v-model="info.designScale"></el-input>
            </el-form-item>

            <el-form-item label="矿山规模" prop="mineScale">
              <el-input v-model="info.mineScale"></el-input>
            </el-form-item>

            <el-form-item
              label="地下矿山井下单班最大作业人数"
              prop="maxWorkerNum"
            >
              <el-input v-model="info.maxWorkerNum"></el-input>
            </el-form-item>

            <el-form-item label="主要负责人姓名" prop="directorName">
              <el-input v-model="info.directorName"></el-input>
            </el-form-item>

            <el-form-item label="主要负责人手机号" prop="directorTel">
              <el-input v-model="info.directorTel"></el-input>
            </el-form-item>

            <el-form-item label="最大开采深度(设计)" prop="designDigDepth">
              <el-input v-model="info.designDigDepth"></el-input>
            </el-form-item>
            <el-form-item label="最大开采深度(现状)" prop="realDigDepth">
              <el-input v-model="info.realDigDepth"></el-input>
            </el-form-item>

            <el-form-item label="最大开采高度(设计)" prop="designDigHeight">
              <el-input v-model="info.designDigHeight"></el-input>
            </el-form-item>
          </div>
          <div class="form-right">


            <el-form-item label="最大开采高度(现状)" prop="realDigHeight">
              <el-input v-model="info.realDigHeight"></el-input>
            </el-form-item>

            <el-form-item label="边坡稳定性专业分析评估" prop="slopeAnaly">
              <el-input v-model="info.slopeAnaly"></el-input>
            </el-form-item>

            <el-form-item label="边坡稳定性分析完成时间" prop="slopeAnalyDate">
              <el-input v-model="info.slopeAnalyDate"></el-input>
            </el-form-item>

            <el-form-item label="边坡最高安全监测等级" prop="slopSafeGrade">
              <el-input v-model="info.slopSafeGrade"></el-input>
            </el-form-item>

            <el-form-item label="边坡位移监测" prop="slopeMoveWatch">
              <el-input v-model="info.slopeMoveWatch"></el-input>
            </el-form-item>

            <el-form-item
              label="停建矿山-是否与企业取得联系"
              prop="stopProcContactEnt"
            >
              <el-input v-model="info.stopProcContactEnt"></el-input>
            </el-form-item>

            <el-form-item label="停建矿山-停产时间(月)" prop="stopProcDate">
              <el-input v-model="info.stopProcDate"></el-input>
            </el-form-item>

            <el-form-item
              label="停建矿山-主要井口是否封闭"
              prop="stopProcCloseWell"
            >
              <el-input v-model="info.stopProcCloseWell"></el-input>
            </el-form-item>

            <el-form-item
              label="停建矿山-是否正常排水"
              prop="stopProcDrainWater"
            >
              <el-input v-model="info.stopProcDrainWater"></el-input>
            </el-form-item>

            <el-form-item
              label="停建矿山-是否正常通风"
              prop="stopProcVentilate"
            >
              <el-input v-model="info.stopProcVentilate"></el-input>
            </el-form-item>

            <el-form-item label="停产停建原因" prop="stopProcCause">
              <el-input v-model="info.stopProcCause"></el-input>
            </el-form-item>

            <el-form-item label="复工复产状况" prop="resumeProc">
              <el-input v-model="info.resumeProc"></el-input>
            </el-form-item>

            <el-form-item label="所属企业名称" prop="entName">
              <el-input v-model="info.entName"></el-input>
            </el-form-item>

            <el-form-item label="上一级公司" prop="parent01Company">
              <el-input v-model="info.parent01Company"></el-input>
            </el-form-item>

            <el-form-item label="承托方名称" prop="undertakePartName">
              <el-input v-model="info.undertakePartName"></el-input>
            </el-form-item>

            <el-form-item label="委托方名称" prop="entrustPartName">
              <el-input v-model="info.entrustPartName"></el-input>
            </el-form-item>

            <el-form-item
              label="所属企业统一社会信用代码"
              prop="entUnifiedCode"
            >
              <el-input v-model="info.entUnifiedCode"></el-input>
            </el-form-item>

            <el-form-item label="采矿许可证编号" prop="mineLicenseNo">
              <el-input v-model="info.mineLicenseNo"></el-input>
            </el-form-item>

            <el-form-item
              label="采矿许可证生效日期"
              prop="mineLicenseEffectDate"
            >
              <el-input v-model="info.mineLicenseEffectDate"></el-input>
            </el-form-item>

            <el-form-item label="采矿许可证失效日期" prop="mineLicenseValiDate">
              <el-input v-model="info.mineLicenseValiDate"></el-input>
            </el-form-item>

            <el-form-item label="安全生产许可证编号" prop="safeLicenseNo">
              <el-input v-model="info.safeLicenseNo"></el-input>
            </el-form-item>

            <el-form-item
              label="安全生产许可证生效日期"
              prop="safeLicenseSeffectDate"
            >
              <el-input v-model="info.safeLicenseSeffectDate"></el-input>
            </el-form-item>

            <el-form-item
              label="安全生产许可证失效日期"
              prop="safeLicenseValidDate"
            >
              <el-input v-model="info.safeLicenseValidDate"></el-input>
            </el-form-item>

            <el-form-item label="备注" prop="remark">
              <el-input v-model="info.remark"></el-input>
            </el-form-item>

            <el-form-item label="填报完成时间" prop="fillTime">
              <el-input v-model="info.fillTime"></el-input>
            </el-form-item>

            <el-form-item label="填报人标识" prop="fillPsnId">
              <el-input v-model="info.fillPsnId"></el-input>
            </el-form-item>

            <el-form-item label="填报人姓名" prop="fillPsnName">
              <el-input v-model="info.fillPsnName"></el-input>
            </el-form-item>

            <el-form-item label="填报单位标识" prop="fillDeptId">
              <el-input v-model="info.fillDeptId"></el-input>
            </el-form-item>

            <el-form-item label="填报单位名称" prop="fillDeptName">
              <el-input v-model="info.fillDeptName"></el-input>
            </el-form-item>
          </div>
        </div>
      </el-form>

      <el-row type="flex" class="row" justify="center">
        <el-button
          v-if="!disabled"
          @click="saveBtnClickHandle({ status: 1 })"
          size="small"
          :span="2"
          type="success"
          >提交</el-button
        >
        <el-button size="small" :span="2" @click="$router.back()"
          >返回</el-button
        >
      </el-row>
    </el-main>
  </el-container>
</template>

<script>
import { nextTick } from "vue";
export default {
  data() {
    return {
      // info表
      info: {
        // ID
        id: "",
        mineId: "",
        mineName: "",
        cityName: "",
        countyName: "",
        townName: "",
        detailAddr: "",
        longitude: "",
        latitude: "",
        supeMainBody: "",
        procStatus: "",
        mineKind: "",
        otherMineKind: "",
        digMode: "",
        exMode: "",
        standardBuild: "",
        doublePrevent: "",
        riskCtrlClass: "",
        fixRiskLevel: "",
        riskCtrlLevel: "",
        supeKind: "",
        digStor: "",
        designScale: "",
        mineScale: "",
        maxWorkerNum: "",
        directorName: "",
        directorTel: "",
        designDigDepth: "",
        realDigDepth: "",
        designDigHeight: "",
        realDigHeight: "",
        slopeAnaly: "",
        slopeAnalyDate: "",
        slopSafeGrade: "",
        slopeMoveWatch: "",
        stopProcContactEnt: "",
        stopProcDate: "",
        stopProcCloseWell: "",
        stopProcDrainWater: "",
        stopProcVentilate: "",
        stopProcCause: "",
        resumeProc: "",
        entName: "",
        parent01Company: "",
        undertakePartName: "",
        entrustPartName: "",
        entUnifiedCode: "",
        mineLicenseNo: "",
        mineLicenseEffectDate: "",
        mineLicenseValiDate: "",
        safeLicenseNo: "",
        safeLicenseSeffectDate: "",
        safeLicenseValidDate: "",
        remark: "",
        fillTime: "",
        fillPsnId: "",
        fillPsnName: "",
        fillDeptId: "",
        fillDeptName: "",
      },
      disabled: false,
    };
  },
  mounted() { },
  created() {
    // this.init();
  },
  activated() {
    this.init();
  },
  methods: {
    // 初始化
    init() {
      console.log(this.$route.params);
      if(this.$route.params.type!=="add") {
        this.disabled=false;
        this.info=this.$route.params.row;
      }
      if(this.$route.params.type==="view") {
        this.disabled=true;
      }
      if(this.$route.params.type==="add") {
        this.info.id=null
        this.$nextTick(() => {
          this.$refs.info.resetFields();
          console.log("add");
          this.disabled=false;
        });
      }
    },
    timeChange(e) {
      // console.log(e);
      if(e) {
        this.info.startTime=e[0];
        this.info.endTime=e[1];
      }
    },

    // 未发布/已发布/进行中【开始按钮】--培训发布--保存按钮
    saveBtnClickHandle(options) {
      // this.$refs.info.resetFields();
      //sys/sysMine/addOrUpdateMineInfo
      let params=this.info;
      console.log(params);
      this.$http
        .post("/sys/sysMine/addOrUpdateMineInfo",params)
        .then((res) => {
          console.log(res);
          if(res.data.success) {
            this.$message({
              message: "保存成功",
              type: "success",
            });
            setTimeout(() => {
              this.$router.back();
            },500);
          } else {
            this.$message({
              message: res.msg,
              type: "error",
            });
          }
        });
    },
  },
};
</script>

<style scoped>
.form {
  display: flex;
  /* flex-wrap: wrap; */
}
.form .form-left {
  width: 50%;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 1rem;
}
.form .form-right {
  width: 50%;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 1rem;
}
.container {
  background: #fff;
  padding: 0px 20px 20px;
}

.title {
  background: rgba(64, 158, 255, 0.1);
  color: #0f6fc6;
  border: 1px solid rgba(64, 158, 255, 0.2);
  border-radius: 5px;
}

.row {
  margin-top: 10px;
}
</style>
