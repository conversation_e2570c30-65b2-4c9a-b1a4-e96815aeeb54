/*
  created by m<PERSON><PERSON><PERSON> on 2018-3-28
  common data in system manage
*/
import http from '../../src/assets/functions/axiosServer'
import dealData from '../../src/assets/functions/dealData'

// 系统管理
export default {
  state: {
    //所有公司和部门
    topAllDeptTree:[],
    //当前公司和部门和下级
    deptTree:[],
    //所有部门和人员,树形结构
    deptUserTree:[],
    //所有角色
    roleList:[],
    roleOptions:[],
    //所有权限
    permissionList:[],
    //查找人员
    personOptions:[],
    personByJson:[],
    //查找分组
    systemGroupContent:[],
    //班组信息
    teamData:[]
  },
  getters: {},
  mutations: {
    getAllDept(state,msg){
      state.deptTree=msg;
    },
    getRoleList(state,list){
      state.roleList=list;
    },
    getRoleOptions(state,options){
      state.roleOptions=options;
    },
    getPermissionList(state,list){
      state.permissionList=list;
    },
    getPersonOptions(state,list){
      state.personOptions=list;
    },
    getSystemGroupContent(state,list){
      state.systemGroupContent=list;
    },
    getPersonByJson(state,list){
      state.personByJson=list;
    },
    getDeptUserTree(state,list){
      state.deptUserTree=list;
    },
    getTeam(state,list){
      state.teamData=list
    },
    getTopAllDeptTree(state,list){
      state.topAllDeptTree=list;
    },
  },
  actions : {
    //当前公司和部门和下级
    getAllDept({commit}){//当前公司和下级
      http.post('dept/getOrgDept').then(function (res) {
        if(res.data.success){
          commit('getAllDept',dealData.editTree(res.data.data));
        }
      }.bind(this)).catch(function (err) {
        console.log('获取所有部门时的错误:'+err);
      }.bind(this));
    },
    getRoleList({commit}){
      http.post('role/find?pageSize=10000').then(function (res) {
        let tempArray=[];
        if(res.data.success){
          if(res.data.data.list.length){
            for(let i=0;i<res.data.data.list.length;i++){
              tempArray.push({value:res.data.data.list[i].id,label:res.data.data.list[i].role});
            }
          }
          commit('getRoleList',res.data.data.list);
          commit('getRoleOptions',tempArray);
        }
      }.bind(this)).catch(function (err) {
        console.log('获取所有角色:'+err);
      }.bind(this));
    },
    getPermissionList({commit}){
      http.post('permission/findAllMenu').then(function (res) {
        if(res.data.success){
          commit('getPermissionList',res.data.data);
        }
      }.bind(this)).catch(function (err) {
        console.log('获取所有权限:'+err);
      }.bind(this));
    },
    getSystemGroupContent({commit},val){
      http.post("sys/group/findOptionGroup?groupName="+val).then(function (res) {
        let tempList=[];
        res.data.data.forEach(function (item) {
          tempList.push({value:item.id,label:item.optionName})
        });
        commit('getSystemGroupContent',tempList);
      }.bind(this))
    },
    getPersonOptions({commit},val){//全集团范围查找人员
      http.get('user/find?username='+val).then(function (res) {
        if(res.data.success){
          let personOptions=[];
          for (let i = 0; i < res.data.data.list.length; i++) {
            personOptions.push({value:res.data.data.list[i].userId,label:res.data.data.list[i].username});
          }
          commit('getPersonOptions',personOptions);
        }else{
          commit('getPersonOptions',[]);
        }
      }.bind(this)).catch(function (err) {
        console.log('获取人员'+err);
      });
    },
    getPersonByJson({commit},obj){//子公司内查找人员,子公司
      let requestStr='';
      if(obj.companyId<0){//当子公司id小于0时，不按公司查找
        requestStr='user/find?username='+obj.name;
      }else{
        requestStr='user/find?username='+obj.name+'&companyId='+obj.companyId;
      }
      http.get(requestStr).then(function (res) {
        if(res.data.success){
          let personOptions=[];
          for (let i = 0; i < res.data.data.list.length; i++) {
            personOptions.push({value:res.data.data.list[i].userId,label:res.data.data.list[i].username});
          }
          commit('getPersonByJson',personOptions);
        }else{
          commit('getPersonByJson',[]);
        }
      }.bind(this)).catch(function (err) {
        console.log('获取人员'+err);
      });
    },
    getTeam({commit}){//获取班组
      http.post("/team/find").then(function (res) {
        if(res.data.success){
          commit('getTeam',res.data.data)
        }
      }.bind(this)).catch(function (err) {
        this.$message.error("获取班组失败："+err)
      })
    },

    // START YANG
    /*
     * 用户管理---权限管理
     * */
    // 添加
    permissionAdd({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('permission/add', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 更新
    permissionUpdate({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('permission/update', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 查找全部
    permissionFindAllMenu({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('permission/findAllMenu', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 查找单个
    permissionFind({ commit }, params){
      return new Promise(function(resolve, project){
        http.get('permission/find', {
          params : params
        }).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 删除
    permissionDelete({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('permission/delete', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 通知管理--查询
    sysNoticeFindFileNotice({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('/sys/notice/findFileNotice', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 通知管理--添加
    sysNoticeAddFileNotice({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('/sys/notice/addFileNotice', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 通知管理--删除
    sysNoticeDelete({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('/sys/notice/delete', params).then(function (res) {
          resolve(res.data);
        });
      })
    },


    // 安全文件--查询
    sysManageFileFind({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('/sys/sysManageFile/find', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 安全文件--添加修改
    sysManageFileAddOrUpdate({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('/sys/sysManageFile/addOrUpdate', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 安全文件--删除
    sysManageFileDelete({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('/sys/sysManageFile/delete', params).then(function (res) {
          resolve(res.data);
        });
      })
    },

    // END YANG
    //start FAN
    //所有公司的员工
    getDeptUserTree:function ({ commit }) {
      http.get('dept/getAllDept').then(function (res) {
        if(res.data.success){
          commit('getDeptUserTree',dealData.editUserDeptTree(res.data.data,false));
        }
      }.bind(this)).catch(function (err) {
        console.log('获取所有部门时的错误:'+err);
      }.bind(this));
    },
    //所有公司和部门
    getTopAllDeptTree:function ({ commit }) {
      http.get('dept/getAllDept').then(function (res) {
        if(res.data.success){
          commit('getTopAllDeptTree',dealData.editTree(res.data.data));
        }
      }.bind(this)).catch(function (err) {
        console.log('获取所有部门时的错误:'+err);
      }.bind(this));
    },
    //end FAN
  }
};
