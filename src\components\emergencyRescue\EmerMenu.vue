<template>
  <div id="emerMenu" class="background-body">
    <moduleHeader></moduleHeader>
    <!--<div class="subtitle-fixed">-->
    <!--<el-breadcrumb separator-class="el-icon-arrow-right" style="margin-top: 10px;margin-left: 20px">-->
    <!--<el-breadcrumb-item :to="{ path: '/menu' }">首页</el-breadcrumb-item>-->
    <!--<el-breadcrumb-item>应急救援</el-breadcrumb-item>-->
    <!--<el-breadcrumb-item>{{currentPath}}</el-breadcrumb-item>-->
    <!--</el-breadcrumb>-->
    <!--</div>-->
    <div
      style="
        position: absolute;
        top: 0;
        left: 0;
        bottom: -20px;
        width: 150px;
        background-color: #0f6fc6;
        overflow: auto;
      "
    >
      <el-row style="height: 50px; margin: 0; padding: 0">
        <div class="home-block" @click="$router.push('/menu')">
          <img
            style="margin: 23px 0 0 20px; float: left"
            src="../../static/imgs/public/home-icon.png"
          />
          <span style="color: #f2f2f2; float: left; margin: 20px 0 0 10px"
            >返回首页</span
          >
        </div>
      </el-row>
      <!--<h3 style="margin: 10px 55px 0;color: gainsboro;letter-spacing: 2px">应急救援</h3>-->
      <el-row style="margin: 0;font-weight: 700 ;padding: 0">
        <el-menu
          :default-active="activeUrl"
          class="el-menu-vertical-demo"
          router
          unique-opened
          background-color="#0f6fc6"
          text-color="#fff"
          active-text-color="#fff"
          @select="selectMenu"
          style="width: 150%; margin: 0; padding: 0"
        >
          <el-menu-item
            :key="item.id"
            v-for="item in emergencyMenu"
            :index="item.url"
            style="font-size: 16px"
            ><i :class="item.icon"></i>{{ item.permissionName }}</el-menu-item
          >
          <el-submenu
            :key="group.id"
            :index="group.id"
            v-for="group in emergencyGroupMenu"
          >
            <template slot="title">
              <i :class="group.icon"></i>
              <span style="font-size: 16px">{{ group.permissionName }}</span>
            </template>
            <el-menu-item
              :key="item.id"
              v-for="item in group.list"
              :index="item.url"
              >{{ item.permissionName }}</el-menu-item
            >
          </el-submenu>
        </el-menu>
      </el-row>
    </div>

    <div
      style="
        position: absolute;
        left: 150px;
        right: 0;
        top: 50px;
        bottom: -20px;
        overflow: auto;
        min-width: 1000px;
        background-color: #f2f2f2;
      "
    >
      <keep-alive>
        <router-view></router-view>
      </keep-alive>
    </div>
  </div>
</template>
<script>
import { mapGetters } from "vuex";
import moduleHeader from "@/components/common/moduleHeader";

export default {
  name: "emerMenu",
  data() {
    return {
      userName: "用户名",
      activeUrl: "/emer-menu/emer-response-workflow", //默认高亮的菜单
      currentPath: "任务列表", //面包屑显示目前路径
      emergencyMenu: [],
      emergencyGroupMenu: [],
      //path和name对应表
      pathNameTable: {
        "/emer-menu/emer-response": "应急响应",
        "/emer-menu/emer-plan": "应急预案",
        "/emer-menu/emer-material": "应急物资",
        "/emer-menu/emer-group": "应急队伍",
        "/emer-menu/knowledge-base": "知识点管理",
        "/emer-menu/tag-manage": "标签管理",
        "/emer-menu/emer-plan-manage": "应急类型管理",
        "/emer-menu/emer-handle": "应急事件",
        "/emer-menu/emer-response-workflow": "任务列表",
        "/emer-menu/superior-Emer-Response": "记录应急",
      },
    };
  },
  computed: mapGetters(["getEmergencyPath", "getEmergencyFlag"]),
  created: function () {
    let tempMenu = this.$tool.getStorage("SAFE_PLATFORM_MENU").subMenu.emerMenu;
    for (let i = 0; i < tempMenu.length; i++) {
      if (tempMenu[i].list.length) {
        if (this.hasSubMenu(tempMenu[i])) {
          tempMenu[i].id = String(tempMenu[i].id);
          this.emergencyGroupMenu.push(tempMenu[i]);
        } else {
          this.emergencyMenu.push(tempMenu[i]);
        }
      } else {
        if (
          this.$tool.getStorage("LOGIN_USER").parentCompanyId === 0 &&
          tempMenu[i].url === "/emer-menu/superior-Emer-Response"
        ) {
        } else {
          this.emergencyMenu.push(tempMenu[i]);
        }
      }
    }

    if (localStorage.SAFE_PLATFORM_USERNAME) {
      this.userName = localStorage.SAFE_PLATFORM_USERNAME;
    }
    if (this.$route.params.taskFlag) {
      //直接进入任务
      if (this.$route.params.editTaskFlag === "edit") {
        //要填写的任务列表，我的响应
        this.activeUrl = "/emer-menu/superior-Emer-Response";
      } else if (this.$route.params.editTaskFlag === "view") {
        //只是看的任务列表，应急列表
        this.activeUrl = "/emer-menu/emer-response";
      } else if (this.$route.params.editTaskFlag === "deal") {
        this.activeUrl = "/emer-menu/emer-handle";
      } else if (this.$route.params.componentName == "emerDuty") {
        //要填写的任务列表，我的响应
        this.activeUrl = "/emer-menu/emer-duty";
      } else if (this.$route.params.componentName == "holidayDuty") {
        //要填写的任务列表，我的响应
        this.activeUrl = "/emer-menu/holiday-duty";
      } else {
        this.activeUrl = "/emer-menu/emer-response-workflow";
      }
    } else {
      // this.$router.push('/emer-menu/emer-response-workflow');//默认进入的页面
      if (tempMenu[0].list.length > 0) {
        let url = tempMenu[0].list[0].url;
        this.$router.push(url);
        this.activeUrl = url;
      } else {
        let url = tempMenu[0].url;
        this.$router.push(url);
        this.activeUrl = url;
      }
    }
  },
  components: {
    moduleHeader,
  },
  watch: {},
  methods: {
    // 判断是否有子菜单    add by pdn
    hasSubMenu(obj) {
      let flag = false;
      let list = obj.list;
      if (list.length > 0) {
        flag = list.some(function (item, index, array) {
          return item.type === 1;
        });
      }
      return flag;
    },

    selectMenu: function (val) {
      this.currentPath = this.pathNameTable[val];
    },
  },
};
</script>
<style scoped>
.home-block {
  width: 100%;
  height: 50px;
}
.home-block:hover {
  background-color: #0f6fc6;
  cursor: pointer;
}
  .el-menu-item.is-active {
   background-color: #ce7036 !important;
}
</style>
