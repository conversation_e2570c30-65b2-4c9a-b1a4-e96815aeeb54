<template>
  <div id="viewWorkBrief">
    <div class="background-style">
      <el-col :span="16" :offset="4" class="success-background-title">{{form.name}}</el-col>
      <el-col :span="16" :offset="4">
        <el-form :model="form"  label-width="120px">
          <el-col :span="24">
            <el-col :span="12">
              <el-form-item label="编制部门：">
                {{form.dept}}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="记录：">
                {{form.type}}
              </el-form-item>
            </el-col>
          </el-col>
        </el-form>
      </el-col>
      <el-col :span="20" :offset="2">
        <table class="simple-table">
          <tr><th>{{tableHead[0]}}</th><td v-for="item in tableContent.date">{{item}}</td></tr>
          <tr><th>{{tableHead[1]}}</th><td v-for="item in tableContent.defenseWork">{{item}}</td></tr>
          <tr><th>{{tableHead[2]}}</th><td v-for="item in tableContent.dutyPeople">{{item}}</td></tr>
          <tr><th>{{tableHead[3]}}</th><td v-for="item in tableContent.disasterSituation">{{item}}</td></tr>
          <tr><th>{{tableHead[4]}}</th><td v-for="item in tableContent.emergency">{{item}}</td></tr>
          <tr><th>{{tableHead[5]}}</th><td v-for="item in tableContent.emergencyHandle">{{item}}</td></tr>
          <tr><th>{{tableHead[6]}}</th><td v-for="item in tableContent.emergencyAfter">{{item}}</td></tr>
        </table>
        <div style="margin-top: 10px">
          <el-pagination
            background
            layout="prev, pager, next"
            :current-page="currentPage"
            :total="totalItem"
            @current-change="currentPageClick">
          </el-pagination>
        </div>
        <div style="margin-top: 10px;margin-right: 20px">
          <el-button style="float: right"  @click="$router.go(-1)">返回</el-button>
          <el-button style="float: right;margin-right: 5px;" type="primary" @click="downWorkBriefFile">下载</el-button>
        </div>
      </el-col>
    </div>
  </div>
</template>
<script>
  import {mapGetters} from 'vuex'
  export default {
    name: 'viewWorkBrief',
    data() {
      return {
        form:{
          name:'',
          dept:'',
          type:'',
          startId:'',
          companyId:''
        },
        tableHead:['上报日期时间','现阶段防御工作部署落实情况','值班人员到位情况','受损受灾情况','突发事件情况','事件现场应急处置及抢险救灾情况','事件应急结束善后处置情况'],
        tableContent:{},
        currentPage:0,
        totalItem:0,
      }
    },
    computed:mapGetters(['getCurrentUser']),
    created:function () {
      if(this.$route.params.dept&&this.$route.params.emerName){
        this.form.name=this.$route.params.emerName;
        this.form.dept=this.$route.params.dept.label;
        this.form.companyId=this.$route.params.dept.value;
        this.form.startId=this.$route.params.startId;
        this.form.type='应急工作情况简报';
        this.searchWorkBrief();
      }
    },
    watch:{
      $route(to, from){
        if(this.$route.name==='accountEmerList'){
          if(this.$route.params.dept&&this.$route.params.emerName){
            this.form.name=this.$route.params.emerName;
            this.form.dept=this.$route.params.dept.label;
            this.form.companyId=this.$route.params.dept.value;
            this.form.startId=this.$route.params.startId;
            this.form.type='应急工作情况简报';
            this.searchWorkBrief();
          }
        }
      }
    },
    methods:{
      searchWorkBrief:function () {
        let params=new URLSearchParams;
        params.append("pageCurrent",1);
        this.currentPage=1;
        this.sendRequest(params);
      },
      currentPageClick:function (val) {
        if (val) {
          this.currentPage = val;
          let params = new URLSearchParams;
          params.append("pageCurrent", Number(val));
          this.sendRequest(params);
        }
      },
      sendRequest:function (params) {
        params.append("companyId",this.form.companyId);
        params.append("planPublicId",this.form.startId);
        params.append("pageSize",6);
        this.$http.post('workBrief/findBriefOrganize',params).then(function (res) {
          this.tableContent.date=[];
          this.tableContent.defenseWork=[];
          this.tableContent.dutyPeople=[];
          this.tableContent.disasterSituation=[];
          this.tableContent.emergency=[];
          this.tableContent.emergencyHandle=[];
          this.tableContent.emergencyAfter=[];
          if(res.data.success){
            this.totalItem=res.data.data.page.total;
            this.tableContent=res.data.data.workBrief;
            for(let i=0;i<this.tableContent.date.length;i++){
              this.tableContent.date[i]=this.transferTime(this.tableContent.date[i],null,true);
            }
            let tempLength=this.tableContent.date.length;
            for(let i=0;i<(6-tempLength);i++){
              this.tableContent.date.push('');
              this.tableContent.defenseWork.push('');
              this.tableContent.dutyPeople.push('');
              this.tableContent.disasterSituation.push('');
              this.tableContent.emergency.push('');
              this.tableContent.emergencyHandle.push('');
              this.tableContent.emergencyAfter.push('');
            }
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message({
            showClose: true,
            message: '网络错误，请尝试重登录',
            type: 'error'
          });
        }.bind(this));
      },
      downWorkBriefFile:function () {
        const loading = this.$loading({
          lock: true,
          text: 'Loading',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        this.$http({ // 用axios发送post请求
          method: 'get',
          url: '/report/emgWorkBrief/' + this.form.startId+"/"+this.form.companyId, // 请求地址
          responseType: 'blob' // 表明返回服务器返回的数据类型
        }).then((res) => { // 处理返回的文件流
          //console.info(res)
          loading.close()
          const content = res
          const elink = document.createElement('a') // 创建a标签
          elink.download = this.form.name+"-"+this.form.dept + "简报.xlsx" // 文件名
          elink.style.display = 'none'
          const blob = new Blob([res.data])
          elink.href = URL.createObjectURL(blob)
          document.body.appendChild(elink)
          elink.click() // 触发点击a标签事件
          document.body.removeChild(elink)
        })
      }
    }
  }
</script>
<style>
</style>
