<template>
  <div class="background-style" style="padding: 10px">
    <el-container>
      <el-main>
        <el-row style="margin:0">
          <el-col :span="6">
            <el-button type="primary" size="mini" @click="$router.back()">返回</el-button>
          </el-col>
        </el-row>
        <el-row style="margin:10px 0 0 0">
          <el-col :span="24">
            <egrid class="egrid"
                   stripe border
                   maxHeight="400"
                   :data="egrid.data"
                   :columns="egrid.columns"
                   :columns-handler="columnsHandler"
                   :column-type="egrid.columnType">
            </egrid>
          </el-col>
        </el-row>
      </el-main>
      <el-footer>
        <el-dialog
          title="提示"
          :visible.sync="photo.isShow"
          width="30%">
          <img :src="photo.url"/>
        </el-dialog>
      </el-footer>
    </el-container>
  </div>
</template>

<script>

  // 单元格的组件
  var Editor = {
    template:
      `<div>
          <el-button type="primary" size="mini" @click="viewBtnClickHandler">查看</el-button>
          <el-button
          type="danger" size="mini"
          v-show="show"
          @click="downloadBtnClickHandler">下载</el-button>
        </div>`,
    props: ['row', 'col'],
    computed:{
      show(){
        // 只有培训、考核表可下载
        let list = ['record', 'examList'];
        return list.includes(this.row.name);
      }
    },
    methods:{
      // 查看按钮
      viewBtnClickHandler(){
        this.$emit('row-view', this.row)
      },
      // 下载按钮
      downloadBtnClickHandler(){
        this.$emit('row-download', this.row)
      }
    }
  }

  export default {
    data(){
      return {
        form : {
          id : '',
        },
        // 表格
        egrid : {
          data : [],
          columns : [
            { label: '文件名称', prop: 'title' },
          ],
          // columnsProps 用于定义所有 columns 公共的属性
          columnsProps: {
            fit : true,
            sortable: true,
            align : 'center',
          },
          columnsSchema : {

          },
          columnType : 'index'
        },
        // 活动照片
        photo : {
          // 是否显示
          isShow : false,
          // 照片路径
          url : '',
        },
      }
    },
    created(){
      this.init();
    },
    watch:{
      $route(to,from){
        this.form.id = to.params && to.params.row && to.params.row.id || 0;
        if(to.name === 'eduDailyInfosItem') {
          if(this.form.id){
            this.searchBtnClickHandle();
          }
        }
      }
    },
    methods:{
      // 初始化
      init(){
        let id = this.$route.params.row.id;
        this.form.id = id;
        // 搜索
        this.searchBtnClickHandle();
      },
      // 搜索按钮
      searchBtnClickHandle(){
        this.$store.dispatch('eduDailyInfoFind', this.form).then(function(res){
          if(res.success){
            let egrid = [];
            let items = res.data.list[0];
            // 记录
            let record = items.eduDailyRecord;
            egrid.push({
              title : record.trainingName,
              name : 'record',
              data : record
            })
            // 通知
            let notities = items.eduDailyNotify;
            notities.forEach(function(it){
              egrid.push({
                title : it.title,
                name : 'notify',
                data : it
              })
            }.bind(this));
            // 考核表
            let examList = record.eduDailyExamineLists;
            examList.forEach(function(it){
              egrid.push({
                title : it.examineName,
                name : 'examList',
                data : it
              })
            }.bind(this));
            // 活动照片
            var params = new URLSearchParams()
            params.append("contentId", record.id)
            params.append("contentType", 7)
            this.$store.dispatch('fileFindAction', params).then(function(res){
              if (res.success) {
                res.data.forEach(function(it){
                  egrid.push({
                    title : it.fileName,
                    name : 'photo',
                    data : it
                  })
                }.bind(this));
              }
              this.egrid.data = egrid;
            }.bind(this));

          } else {
            this.egrid.data = [];
          }
        }.bind(this));
      },
      //预览图片
      previewClick:function (id) {
        var params = new URLSearchParams()
        params.append("fId", id);
        this.$http({ // 用axios发送post请求
          method: 'post',
          url: '/file/download', // 请求地址
          data: params, // 参数
          responseType: 'blob' // 表明返回服务器返回的数据类型
        }).then(function(res){ // 处理返回的文件流
//          console.log('res', res);
          const blob = new Blob([res.data])
          this.photo.url = URL.createObjectURL(blob);
          this.photo.isShow = true;
        }.bind(this)).catch(function(e){
          console.log('e', e)
        }.bind(this))
      },
      // egrid---操作列
      columnsHandler (cols) {
        let that = this;
        return cols.concat({
          label: '操作',
          fixed: 'right',
          width: 200,
          component: Editor,
          listeners: {
            'row-view' (row) {
              // implement
              console.log('row', row);
              if(row.name === 'photo'){
                that.previewClick(row.data.fId);
                return;
              }
              let name = '';
              switch(row.name){
                // 记录
                case 'record':
                  name = 'eduDailyInfosItemRecord';
                  break;
                // 通知
                case 'notify':
                  name = 'eduDailyInfosItemNotify';
                  break;
                // 考核表
                case 'examList':
                  name = 'eduDailyInfosItemExamList';
                  break;
              }
              that.$router.push({
                name : name,
                params : {
                  row : row
                }
              })
            },
            'row-download'(row){
              console.log(row)
              let params = {};
              // 下载的文件名
              let filename = '';
              switch(row.name){
                case 'record':
                  params = that.$tool.jsonToForm({
                    eduDailyRecordId : row.data.id
                  });
                  filename = row.data.trainingName;
                  that.$store.dispatch('reportEduEduDailyRecordExcel', params, filename);
                  break;
                case 'examList':
                  params = that.$tool.jsonToForm({
                    eduDailyExamineListId : row.data.id
                  });
                  filename = row.data.examineName;
                  that.$store.dispatch('reportEdueduDailyExamineListExcel', params, filename)
                  break;
              }
            }
          }
        });
      },
    }
  }
</script>

<style>

</style>
