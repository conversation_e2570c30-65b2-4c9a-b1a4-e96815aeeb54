<template>
    <el-container class="container">
      <el-main>
        <el-form ref="form" label-width="100px" :rules="rules" :model="form">
          <el-row type="flex">
            <el-col :span="8">
              <el-form-item class="formItem" label="姓名">
                <span>{{form.eduUser.username}}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item class="formItem" label="性别">
                <span>{{form.eduUser.gender ? '男' : '女'}}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item class="formItem" label="出身年月">
                <span>{{$tool.formatDateTime(form.eduUser.birthday)}}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex">
            <el-col :span="8">
              <el-form-item class="formItem" label="文化程度">
                <span>{{form.eduUser.degreeOfEducation}}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item class="formItem" label="入职时间">
                <span>{{$tool.formatDateTime(form.eduUser.birthday)}}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item class="formItem" label="部门">
                <span>{{form.eduUser.deptName}}</span>
                <span></span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="部门培训：" class="title">
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex">
            <el-col :offset="2" :span="7">
              <el-form-item class="formItem" label="培训时间" prop="eduEntryTrainingDepartment.trainingDate">
                <el-date-picker
                  v-model="form.eduEntryTrainingDepartment.trainingDate"
                  type="date"
                  placeholder="选择日期">
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="7">
              <el-form-item class="formItem" label="学时" prop="eduEntryTrainingDepartment.trainingHours">
                <el-input type="number" v-model="form.eduEntryTrainingDepartment.trainingHours"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="7">
              <el-form-item class="formItem" label="教育者" prop="eduEntryTrainingDepartment.teacher">
                <el-input v-model="form.eduEntryTrainingDepartment.teacher"></el-input>
                <!--<userList
                  :query="form.eduEntryTrainingDepartment.teacher"
                  :data="addStaff"
                  @userChange="teacherChange"></userList>-->
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex">
            <el-col :offset="2" :span="22">
              <el-form-item class="formItem" label="培训内容" prop="eduEntryTrainingDepartment.courses">
                <el-input v-model="form.eduEntryTrainingDepartment.courses" autosize type="textarea" :rows="2"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex">
            <el-col :offset="2" :span="7">
              <el-form-item class="formItem" label="部门培训成绩" prop="eduEntryTrainingDepartment.score">
                <el-input-number v-model="form.eduEntryTrainingDepartment.score" :min="0" :max="100" :step="5" label="描述文字"></el-input-number>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex">
            <el-col :span="6">
              <el-form-item class="formItem" label="考试成绩">
                <span>{{form.score}}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex" class="row" justify="center">
            <el-button
              @click="submitBtnClickHandle"
              size="small" :span="2" type="primary">提交</el-button>
            <el-button size="small" :span="2"  @click="$router.back();">返回</el-button>
          </el-row>
        </el-form>
      </el-main>
    </el-container>
</template>

<script>
    import userList from '@/components/common/userList.vue'
    export default {
      components : {
        userList
      },
      data(){
        return {
          form : {
            // 部门
            eduEntryTrainingDepartment : {
              // 培训时间
              trainingDate : '',
              // 学时
              trainingHours : 0,
              // 教育者
              teacher : '',
              // 培训内容
              courses : '',
              //部门培训成绩
              score:'',
            },
            // 用户
            eduUser : {

            },
            // ID
            id : '',
            // 分数
            score : '',
            // 用户id
            userId : '',
          },
          rules: {
            'eduEntryTrainingDepartment.trainingDate': [
              {required: true, message: '请选择日期', trigger: 'change'},
            ],
            'eduEntryTrainingDepartment.trainingHours': [
              {required: true, message: '请输入内容', trigger: 'blur'},
            ],
            'eduEntryTrainingDepartment.teacher': [
              {required: true, message: '请输入内容', trigger: 'blur'},
            ],
            'eduEntryTrainingDepartment.courses': [
              {required: true, message: '请输入内容', trigger: 'blur'},
            ],
          },
          // 添加人员
          addStaff : {
            // 部门
            deptId : '',
            username : '',
          },
        }
      },
      watch:{
        $route(to,from){
          // 如果来至列表页
          if(from.name === 'threeLevelTrainingIndex'){
            this.init();
          }
        }
      },
      created(){
        this.init();
      },
      mounted(){
        this.init();
      },
      methods: {
        init(){
          let user = this.$tool.getStorage('LOGIN_USER');
          this.addStaff.deptId = user.deptId;

          if(this.$route.params.status){
            this.searchBtnClickHandle();
          }
        },
        // 教育者添加
        teacherChange(val){
          this.form.eduEntryTrainingDepartment.teacher = val.username;
        },
        // 根据id搜索信息
        searchBtnClickHandle(){
          let id = this.$route.params.id;
          this.$store.dispatch('eduEntryTrainingFind', { id : id }).then(function(res){
            if(res.success){
              let list = res.data.list[0];
              Object.entries(list).forEach(function(it){
                if(it[1] && this.form.hasOwnProperty(it[0])){
                  this.form[it[0]] = it[1];
                }
              }.bind(this))
              this.$nextTick(function(){
                this.addStaff.username = this.form.eduEntryTrainingDepartment.teacher;
              }.bind(this))
            } else {
              this.$message({
                type : 'error',
                message : res.message || '错误'
              })
            }
          }.bind(this));
        },
        // 格式化时间
        formatDateTime(row, column, cellValue){
          let pro = column.property;
          let num = 10;
          // 年份4位 1999
          if(pro === 'createYear') num = 4;
          let str = this.$tool.formatDateTime(row[pro] || 0);
          return str ? str.substring(0, num) : str;
        },

        // 提交---添加或更新
        submitBtnClickHandle(){
          let params = Object.assign({}, this.form);
          this.$refs['form'].validate(function(valid) {
            if(valid){
              this.$store.dispatch('eduEntryTrainingAddAndUpdateOne', params).then(function (res) {
                if(res.success){
                  this.$message({
                    type : 'success',
                    message : '操作成功'
                  })
                  this.$router.push({ name : 'threeLevelTrainingIndex' })
                }  else {
                  this.$message({
                    type : 'error',
                    message : res.message || '错误'
                  })
                }
              }.bind(this))
            } else {
              return false;
            }
          }.bind(this))
        },
      }
    }
</script>

<style>
  .container{
    background:#fff;
    padding:0px 50px;
  }
  .title{
    background:rgba(64,158,255,.1);
    color:#0f6fc6;
    border: 1px solid rgba(64,158,255,.2);
    border-radius:5px;
    margin:5px;
  }
  .row{
    margin-top:10px;
  }
  .formItem{
    margin:2px;
  }
</style>
