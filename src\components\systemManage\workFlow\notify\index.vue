<template>
  <div id="trainingPlanIndex">
    <div class="background-style">
      <!--搜索区-->
      <div class="search-bar">
        <el-row>
          <el-col :span="5">
            <div>
              <el-radio-group  v-model="rangeSearch"  @change="noticeSearchFn">
                <el-radio-button label="全部通知"></el-radio-button>
                <el-radio-button label="我的创建"></el-radio-button>
              </el-radio-group>
            </div>
          </el-col>
          <el-col :span="4">
            <el-input v-model="form.title" clearable placeholder="请输入标题"></el-input>
          </el-col>
          <el-col :offset="1" :span="7">
            <!--年份：-->
            <el-date-picker
              clearable
              v-model="dateRange"
              type="daterange"
              @change="dateRangeChangeFn"
              range-separator="至"
              start-placeholder="创建开始日期"
              end-placeholder="结束日期">
            </el-date-picker>
          </el-col>
          <el-col :span="2">
            <el-select v-model="noticeType" clearable aria-placeholder="请选择">
              <el-option
                v-for="{label,value} in noticeTypeList"
                :key="value"
                :label="label"
                :value="value">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="3">
            <el-button
              @click="searchBtnClickHandle"
              type="primary" icon="el-icon-search" style="margin-left: 20px">搜索</el-button>
          </el-col>

          <el-col :span="2">
            <el-button
              @click="addBtnClickHandle"
              type="success" style="margin-left: 20px">新增</el-button>
          </el-col>
        </el-row>
      </div>
      <!--表格区-->
      <div style="width: 100%;">
        <div style="padding: 20px 10px 20px 10px">
          <el-table
            border
            :data="tableData.list"
            style="width: 100%">
            <el-table-column
              type="index"
              label="编号"
              width="50"
              align="center"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="title"
              label="名称"
              min-width="150"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="content"
              label="内容"
              show-overflow-tooltip
              width="200"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="writeUserName"
              label="创建人"
              show-overflow-tooltip
              width="100"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="createTime"
              :formatter="formatDateTime"
              label="创建时间"
              width="120"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="companyName"
              label="公司"
              show-overflow-tooltip
              width="200"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="deptName"
              label="部门"
              show-overflow-tooltip
              width="150"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              fixed="right" label="操作"
              label-class-name="header-style"
              align="left" width="160">
              <template slot-scope="scope">
                 <el-button size="mini" type="danger"
                 v-if="delNoticeBtn" @click="itemDeleteClick(scope.row)">删除</el-button>
                 <el-button size="mini" type="success" @click="itemDetailClick(scope.row)">查看</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div style="margin-top: 10px">
          <el-pagination
            background
            layout="prev, pager, next"
            :current-page="tableData.pageNum"
            :page-size="form.pageSize"
            :total="tableData.total"
            @current-change ="disasterPageChangeHandle">
          </el-pagination>
        </div>
      </div>
      <!--新增对话框-->
      <el-dialog
        title="通知对话框"
        :visible.sync="dialog.isShow"
        width="80%"
        :before-close="handleClose">
        <el-form label-width="100px">
          <el-row  class="row">
            <el-col :span="24">
              <el-form-item label="名称">
                <el-input v-model="dialog.form.title"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="文件上传">
                <fileUpload
                  @fileData="fileDataFn"
                  :data="upload"></fileUpload>
                <!-- <fileUpload
                   @fileData="fileDataFn"></fileUpload>-->
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="选择类型">
<el-select v-model="addNoticeType" aria-placeholder="请选择">
              <el-option
                v-for="{label,value} in noticeTypeList"
                :key="value"
                :label="label"
                :value="value">
              </el-option>
            </el-select>

              </el-form-item>
            </el-col>

          </el-row>
          <e-row>
            <el-col :span="24">
              <chooseStaff
                ref="chooseComponent"
                @selectedRows="selectedRows"></chooseStaff>
            </el-col>
          </e-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button
            type="danger"  size="mini"
            @click="dialogOkBtnClickHandle">确定</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>
<script>
  import fileUpload from '@/components/common/fileUploadFileServer'
  import chooseStaff from '@/components/common/chooseStaff'
  export default {
    components: {
      chooseStaff,
      fileUpload
    },
    // computed:{
    //   permissionList:function () {
    //     // this.delNoticeBtn = this.$tool.getPowerBtns2URL('manageMenu', '/manage-menu/user-manage', '/manage-menu/role-manage');
    //     log(this.$store.state.sysManageData.permissionList);
    //     return this.$store.state.sysManageData.permissionList;
    //   }
    // },
    data() {
      return {
        // 通知类型
        noticeTypeList : [{
          label : '文件',
          value : '101'
        },{
          label : '活动',
          value : '102'},
          {
            label : '会议',
            value : '103'
          },
        ],
        delNoticeBtn:false,
        noticeType: '',
        addNoticeType: '101',
        // 时间范围
        dateRange : '',
        // 范围搜索
        rangeSearch : '全部通知',
        form : {
          // 标题
          title : '',
          // 开始时间
          startDate : '',
          // 结束时间
          endDate : '',
          // 如果是我的创建，则为登录人id
          // 当前页
          pageCurrent : 1,
          // 页数大小
          pageSize : 10,
        },
        tableData : {},


        // 对话框
        dialog : {
          // 是否显示
          isShow : false,
          form : {
            title  : '',
            content  : '',
            relateId  : '',
            targetUserIds  : '',
          },
          assist : {
            planList : [],
          }
        },
        upload:{
          tip : "只能上传pdf文件",
//          limit : 1,
          params:{
            contentId : this.$tool.getStorage('LOGIN_USER').userId,
            contentType:19
          },
          btns : {
            // 上传按钮
            upload : {
              isShow : true,
            },
            // 下载按钮
            download : {
              isShow : false,
            },
            // 删除按钮
            delete : {
              isShow : true,
            },
          },
          uploadUrl:'',
          uploadCookies:true,
          fileData : [],
        },


        // 角色 0 员工 1 发布者
        role : 0,
        // 权限按钮
        powerBtns : [],
      }
    },
    mounted(){
      this.init();
    },
    watch:{
      $route(to,from){
        if(to.name === 'workflowNotifyIndex') {
          this.init();
        }
      }
    },
    methods:{
      // 初始化
      init(){
        this.delNoticeBtn = this.$tool.getPowerBtns2URL('manageMenu', '/manage-menu/workflow-manage', this.$route.path).includes('delNoticeBtn');
        // 根据权限按钮设置角色
        this.judgeUserRole();
        // 搜索
        this.searchBtnClickHandle();
      },
      // 日期范围选择
      dateRangeChangeFn(e){
//        console.log(e);
        if(e && e.length == 2){
          this.form.startDate = e[0];
          this.form.endDate = e[1];
        } else {
          this.form.startDate = '';
          this.form.endDate = '';
        }
      },
      judgeUserRole(){
        // 获取权限按钮
        let btns = this.$tool.getPowerBtns('eduTrainingMenu', this.$route.path);
        // 公司
        if(btns.includes('addBtn')){
          this.role = 4;
        } else {
          this.role = 1;
        }
      },
      // 是否为全部还是个人搜索
      noticeSearchFn(item){
        this.form.writeUserId = item == '我的创建' ? this.$tool.getStorage('LOGIN_USER').userId : '';
        this.searchBtnClickHandle();
      },
      // 格式化时间
      formatDateTime(row, column, cellValue){
        let pro = column.property;
        let num = 10;
        // 年份4位 1999
        if(pro === 'createYear') num = 4;
        let str = this.$tool.formatDateTime(row[pro] || 0);
        return str ? str.substring(0, num) : str;
      },
      // 分页
      disasterPageChangeHandle(page){
        this.form.pageCurrent = page;
        this.searchBtnClickHandle();
      },
      // 搜索按钮
      searchBtnClickHandle(){
        let params = {};
        if(this.form.title){
          params['title'] = this.form.title;
        }
        if(this.form.startDate){
          params['startDate'] = this.form.startDate;
        }
        if(this.form.endDate){
          params['endDate'] = this.form.endDate;
        }
        if(this.form.writeUserId){
          params['writeUserId'] = this.$tool.getLoginer().userId;
        }
        params['pageCurrent'] = this.form.pageCurrent;
        params['pageSize'] = this.form.pageSize;
        params['idType'] = this.noticeType;
        this.$store.dispatch('sysNoticeFindFileNotice', params).then(function(res){
          if(res.success){
            this.tableData = res.data;
          } else {
            this.$message({
              type : 'error',
              message : res.message || '错误'
            })
          }
        }.bind(this));
      },
      // 删除按钮
      itemDeleteClick(row){
        this.$confirm('此操作将永久删除, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
          .then(function(){
            this.$store.dispatch('sysNoticeDelete', {
              nId : row.nId
            }).then(function(res){
              if(res.success){
                this.$message({
                  type : 'success',
                  message : '删除成功'
                })
                this.searchBtnClickHandle();
              } else {
                this.$message({
                  type : 'error',
                  message : res.message || '删除失败！！'
                })
              }
            }.bind(this))
          }.bind(this))
      },
      itemDetailClick(row){
        console.log(row)
        //新标签页打开 row.filePath
        window.open(row.filePath);
      },
      // 添加按钮
      addBtnClickHandle(){
        this.clear();
        this.dialog.isShow = true;
      },
      // 文件上传
      fileDataFn(data){
        console.log('父组件获取子组件数据：',data);
        if(data){
          if(data.length == 1){
            let type = data[0].fileData.file.type;
            let fileId = data[0].fileId;
            let fileName = data[0].fileName;
            if(type != 'application/pdf'){
              this.$message({
                type : 'error',
                message : '只能上传PDF格式的文件'
              })
            }
            // 赋值
            this.dialog.form.relateId = fileId;
            this.dialog.form.content = fileName;
          } else if(data.length == 0){


            this.dialog.form.relateId = "";
            this.dialog.form.content = "";
          } else {
            this.$message({
              type : 'error',
              message : '最多只能上传一张PDF格式的文件'
            })
          }
        } else {

          this.dialog.form.relateId = "";
          this.dialog.form.content = "";
        }
      },
      // 人员列表选择组件处理函数
      selectedRows(rows){
//        console.log(11111111111111111,rows);
        // 参与人员列表----用户userId列表
        let userIds = rows.map(function(it){
          return it.userId;
        })
        this.dialog.form.targetUserIds = userIds;
      },
      // 清空数据
      clear(){
        this.dialog.form.title = "";
        this.dialog.form.relateId = "";
        this.dialog.form.content = "";
        this.dialog.form.targetUserIds = [];
        this.upload.fileData = [];
//        this.$refs['chooseStaff2'].changeTableDataHandle([]);
        this.$nextTick(function(){
          this.$refs.chooseComponent.changeTableDataHandle([]);
        }.bind(this))

      },
      // 对话框---确定按钮
      dialogOkBtnClickHandle(){
        let form = this.dialog.form;
        console.log(form);
//        return;



        if(form.title == ''){
          this.$message({
            type : 'error',
            message : '名称不得为空！！'
          })
          return;
        }
        if(form.relateId == '' || form.content == ''){
          this.$message({
            type : 'error',
            message : '上传文件不得为空！！'
          })
          return;
        }
        if(form.targetUserIds == '' || form.targetUserIds.length == 0){
          this.$message({
            type : 'error',
            message : '通知人员不得为空！！'
          })
          return;
        }




        let params = {
          title : form.title,
          relateId : form.relateId,
          content : form.content,
          targetUserIds : form.targetUserIds,
          idType : this.addNoticeType
        }

        this.$store.dispatch('sysNoticeAddFileNotice', params).then(function(res){
          if(res.success){
            this.$message({
              type : 'success',
              message : '操作成功'
            })
            this.handleClose();
            this.searchBtnClickHandle();
            this.dialog.isShow = false;
          }  else {
            this.$message({
              type : 'error',
              message : res.message || '错误'
            })
          }
        }.bind(this));
      },
      // 对话框--关闭
      handleClose(){
        this.dialog.form = this.$tool.clearObj({}, this.dialog.form);
//        this.dialog.assist.planList = [];
        this.dialog.isShow = false;
        this.clear();
      }

    }
  }
</script>
<style>
</style>
