<template>
  <div id="uploadFile">
    <div style="width: 100%;float:left">
      <el-upload
        class="upload-demo"
        ref="upload"
        :action="uploadBaseUrl"
        multiple
        :with-credentials="cookies"
        :http-request="ossUploadRequest"
        :data="fileUploadParams"
        :file-list="fileList"
        :on-preview="fileDownload"
        :before-remove="beforeDelete"
        :on-remove="fileDelete"
        :before-upload="beforeUpload"
        style="width: 300px;margin-bottom: 10px;">
        <el-button size="small" type="primary">选取上传附件</el-button>
      </el-upload>
    </div>
  </div>
</template>
<script>
  import dealData from '../../../assets/functions/dealData'
  export default {
    name: 'uploadFile',
    props:['fileList','data'],
    data() {
      return {
        //上传组件信息
        cookies: true,
        fileUploadParams: {
          contentId: 0,
          contentType: 0
        },
        //上传和预览的基本地址
        downloadBaseUrl:'',
        uploadBaseUrl:'',
      }
    },
    methods:{
      beforeUpload:function () {
        this.fileUploadParams.contentId=this.data.contentId;
        this.fileUploadParams.contentType=this.data.contentType;
      },
      fileDownload:function (file) {
        let link = document.createElement('a');
        link.style.display = 'none';
        link.href = this.fileHttp.defaults.baseURL+file.path;
        link.download = file.name;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      },
      beforeDelete:function (file) {
        return this.$confirm(`确定移除 ${ file.name }？`);
      },
      fileDelete:function (file,list) {
        let params = new URLSearchParams();
        params.append("fId", file.fId);
        this.$http.post("file/delete", params).then(function (res) {
          if (res.data.success) {
            this.fileList=list;
            this.$message({
              showClose: true,
              message: '文件已删除',
              type: 'success'
            });
          }
        }.bind(this))
      },
      //直接上传到阿里云上
      ossUploadRequest:function (item) {
        //获取该文件对应的sign
        this.$http.get('sys/oss/sign?contentId='+this.data.contentId+'&contentType='+this.data.contentType+'&realName='+item.file.name).then(function (res) {
          if(res.data){
            let params=new FormData();
            params.append("name",item.file.name);
            params.append("key",res.data.dir + item.file.name);
            params.append("policy",res.data.policy);
            params.append("OSSAccessKeyId",res.data.accessid);
            params.append('success_action_status','200');
            params.append("callback",res.data.callback);
            params.append("signature",res.data.signature);
            params.append("file",item.file);
            this.fileHttp.post('',params,{headers: {'Content-Type': 'multipart/form-data'}}).then(function (res) {
              if(res.data.file) {
                let resultStr=dealData.decode(res.data.file);
                let resultJson=JSON.parse(resultStr);
                resultJson.name=resultJson.fileName;
                this.fileList.push(resultJson);
                this.$message.success('上传成功');
              }else {
                this.$message.error('上传失败');
              }

            }.bind(this))

          }
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message.error('获取唯一标识失败');
        }.bind(this));
      },
    }
  }
</script>
<style>
</style>
