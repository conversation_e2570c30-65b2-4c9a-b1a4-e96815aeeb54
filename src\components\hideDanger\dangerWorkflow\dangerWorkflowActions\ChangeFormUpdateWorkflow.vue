<template>
  <div id="changeFormUpdateWorkflow">
    <div class="background-style">
      <el-col :span="16" :offset="4" class="primary-background-title">{{titleStr}}</el-col>
      <el-col :span="16" :offset="4" >
        <el-form :model="form" label-width="100px" class="demo-ruleForm" label-position="right">
          <el-col :span="24">
            <el-col :span="12">
              <el-form-item label="检查单编号：" prop="checkNum" style="margin: 0">
                {{form.checkNum}}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="检查类型：" style="margin: 0" label-width="90px">
                {{dangerTypeName}}
              </el-form-item>
            </el-col>
          </el-col>
          <div v-if="dangerTypeName!='自查'">
            <el-col :span="24">
              <el-col :span="12">
                <el-form-item label="受检单位：" prop="targetDeptName" style="margin: 0;" label-width="90px">
                  {{form.targetDeptName}}
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="检查单位：" prop="publicDeptName" style="margin: 0;" label-width="90px">
                  {{form.publicDeptName}}
                </el-form-item>
              </el-col>
            </el-col>
            <el-col :span="24">
              <el-col :span="12">
                <el-form-item label="受检单位承办人：" prop="targetContractorUserName" style="margin: 0;" label-width="140px">
                  {{form.targetContractorUserName}}
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="受检单位现场负责人：" prop="targetLiveChargeUser" style="margin: 0" label-width="160px">
                  {{form.targetLiveChargeUser}}
                </el-form-item>
              </el-col>
            </el-col>
          </div>
        </el-form>
      </el-col>
      <el-col :span="22" :offset="1" style="margin-top: 10px">
        <el-table
          border
          highlight-current-row
          :data="tableData">
          <el-table-column
            type="index"
            label="序号"
            width="50"
            fixed
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            prop="inspectProject"
            label="检查项目"
            width="150"
            fixed
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            prop="inspectContent"
            min-width="400"
            label="检查标准内容"
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            prop="applyUserName"
            label="负责人"
            width="120"
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            prop="inspectResult"
            width="300"
            label="检查结果记录"
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            prop="hiddenDangerLevel"
            width="150"
            label="隐患级别"
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            width="320"
            label="隐患照片"
            label-class-name="inner-header-style">
            <template slot-scope="scope">
              <picture-card :picFileList="scope.row.dangerPics"></picture-card>
            </template>
          </el-table-column>
          <el-table-column
            prop="dangerType"
            width="150"
            label="隐患类型"
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            prop="changeRequire"
            width="200"
            label="整改要求"
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            prop="deadline"
            width="120"
            label="整改期限"
            :formatter="changeFormat"
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            width="100"
            label="操作"
            fixed="right"
            label-class-name="inner-header-style">
            <template slot-scope="scope">
              <el-button size="mini" type="success" @click="itemEditClick(scope.row,scope.$index)">编辑</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
      <el-col :span="22" :offset="1">
        <div style="float: right;margin: 20px">
          <el-button type="warning" v-show="estimationFlag" @click="estimationClick">评估</el-button>
          <el-button type="primary" @click="submitClick">提交</el-button>
          <el-button type="success" @click="saveClick">保存</el-button>
          <el-button @click="$router.push({name:'hideDangerWorkflow'})">返回</el-button>
        </div>
      </el-col>
    </div>

    <!--记录对话框-->
    <el-dialog :title="recordTitle" :visible.sync="recordVisible">
      <el-form :model="recordForm" ref="recordForm" label-position="left" class="demo-ruleForm">
        <el-form-item label="检查标准内容：" prop="inspectContent" label-position="left">


          {{recordForm.inspectContent}}
        </el-form-item>
        <el-form-item label="检查结果记录：" prop="inspectResult" label-position="left">
          <el-input v-model="recordForm.inspectResult"></el-input>
          <!--{{recordForm.inspectResult}}-->
        </el-form-item>
        <el-form-item label="整改负责人：" prop="applyUser"  label-position="left">
          <el-select
            v-model="recordForm.applyUser"
            filterable
            remote
            reserve-keyword
            clearable
            placeholder="请输入姓名后选择"
            :remote-method="remoteCheckPerson"
            style="width: 200px;">
            <el-option
              v-for="item in checkPersonOptions"
              :key="item.value"
              :label="item.label"
              :value="item">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="隐患级别：" prop="hiddenDangerLevel"  label-position="left">
          <el-select
            v-model="recordForm.hiddenDangerLevel"
            clearable
            filterable
            placeholder="请选择">
            <el-option
              v-for="item in levelOption"
              :key="item"
              :label="item"
              :value="item">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="隐患类型：" prop="dangerType"  label-position="left">
          <el-select
            v-model="recordForm.dangerType"
            allow-create
            clearable
            filterable
            placeholder="请选择">
            <el-option
              v-for="item in dangerTypeOption"
              :key="item.id"
              :label="item.typeName"
              :value="item.typeName">
            </el-option>
          </el-select>
          <!--<el-button type="primary" plain @click="dangerTypeManageVisible=true">隐患类型管理</el-button>-->
        </el-form-item>
        <el-form-item label="整改期限：" prop="deadline" label-position="left" >
          <el-date-picker
            v-model="recordForm.deadline"
            type="date"
            placeholder="选择日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="整改要求：" prop="changeRequire" label-position="top">
          <el-input type="textarea" :rows="2" v-model="recordForm.changeRequire"></el-input>
        </el-form-item>
      </el-form>
          <el-dialog title="隐患类型管理" :visible.sync="dangerTypeManageVisible" append-to-body>
        <div v-if="addDangerTypeFlag">
          <el-input style="width: 250px" size="small" placeholder="请输入新的隐患类型" v-model="addDangerTypeStr"></el-input>
          <el-button type="success" size="small" @click="addDangerTypeClick">确定</el-button>
          <el-button size="small" @click="addDangerTypeFlag=false">取消</el-button>
        </div>
        <div v-else><el-button type="primary" size="small" @click="addDangerTypeFlag=true">新增隐患类型</el-button></div>
        <el-table :data="dangerTypeOption">
          <el-table-column type="index" label="序号" width="60"></el-table-column>
          <el-table-column label="隐患类型" prop="typeName"></el-table-column>
          <el-table-column label="操作" width="100" fixed="right">
            <template slot-scope="scope">
              <el-button size="mini" type="danger" @click="dangerTypeDelete(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-dialog>
      <div slot="footer" class="dialog-footer">
        <el-button type="success" @click="determineSave">保存</el-button>
        <el-button @click="recordVisible=false">返回</el-button>
      </div>
    </el-dialog>
    <!--记录对话框结束-->

    <!--选择负责人-->
    <search-people-dialog @determineClick="selectPersonClick" :data="selectPersonData" :defaultPersonId="selectPersonData.defaultPerson.value"></search-people-dialog>

    <!--是否审核对话框-->
    <judge-dialog ref="judgeDialog" @buttonClick="judgeExamine"></judge-dialog>
  </div>
</template>
<script>
  import SearchPeopleDialog from '../../../common/smallComponent/searchSinglePeople.vue'
  import PictureCard from '../../../common/smallComponent/pictureCard.vue'
  import JudgeDialog from '../../../common/smallComponent/judgeDialog.vue'
  export default {
    name: 'changeFormUpdateWorkflow',
    data() {
      return {
        titleStr:'',
        currentId:'',
        form:{},//基本信息
        dangerTypeName:'',
        tableData:[],
        dangerInspectMembers:[],
        examinePerson:'',
        personLoading:false,
        targetCompanyId:'',
        publicDeptId:'',
        //记录对话框
        recordTitle:'检查结果记录',
        recordVisible:false,
        recordForm:{
          id:'',
          applyUser:'',
          inspectProject:'',
          inspectContent:'',
          inspectResult:'',
          hiddenDangerLevel:'',
          dangerType:'',
          changeRequire:'',
          deadline:'',
          dangerPics:[]
        },
        currentIndex:'',
        levelOption:['一般(C级)','一般(B级)','重大(A级)'],
        //------------------选择负责人的对话框-----------------------
        selectPersonData:{title:'请选择负责人',isShow:false,defaultPerson:{value:0,label:''}},
        //是否为发布整改单页面
        estimationFlag:false,
        //流程任务ID
        taskId:'',
        //节点数据
        nodeData:{},
        //参考审核人
        leaderUser:{},
        //---------------------隐患类型管理---------------------------
        dangerTypeManageVisible:false,
        addDangerTypeFlag:false,
        addDangerTypeStr:'',
      }
    },
    computed:{
      checkPersonOptions:function () {
        return this.$store.state.sysManageData.personOptions;
      },
      dangerTypeOption:function () {
        return this.$store.state.hideDangerData.dangerType;
      }
    },
    components : {
      SearchPeopleDialog,
      PictureCard,
      JudgeDialog,
    },
    mounted:function () {
      if(this.$route.params.dangerData){
        this.titleStr=this.$route.params.dangerData.name;
        this.nodeData=this.$route.params.dangerData.nodeData;
        this.dangerTypeName=this.$route.params.dangerData.typeName;
        this.taskId=this.$route.params.dangerData.taskId;
        this.leaderUser={value:this.$route.params.dangerData.examineUserId,label:this.$route.params.dangerData.examineUsername};
        this.searchDataById(this.$route.params.dangerData.id);
      }
    },
    watch:{
      $route(to, from){
        if((from.name==='hideDangerWorkflow'||from.name==='taskNotice')&&this.$route.name==='changeFormUpdateWorkflow') {
          if(this.$route.params.dangerData){
            this.titleStr=this.$route.params.dangerData.name;
            this.nodeData=this.$route.params.dangerData.nodeData;
            this.dangerTypeName=this.$route.params.dangerData.typeName;
            this.taskId=this.$route.params.dangerData.taskId;
            this.leaderUser={value:this.$route.params.dangerData.examineUserId,label:this.$route.params.dangerData.examineUsername};
            this.searchDataById(this.$route.params.dangerData.id);
          }
        }
      }
    },
    methods: {
      searchDataById:function (id) {
        //清除之前数据
        this.tableData.splice(0);
        this.checkPersonOptions.splice(0);
        this.estimationFlag=false;
        this.currentId=id;
        //获取本公司的隐患类型
        this.$store.dispatch('getDangerTypeByCompanyId',this.$tool.getStorage('LOGIN_USER').companyId);

        this.$http.post('danger/inspectListPublic/find', {inspectPublicId:id,needChange:'1'}).then(function (res) {
          if (res.data.success) {
            this.tableData=res.data.data;
            if(this.tableData.length===0){this.$message.warning('当前整改内容为空，请先去生成整改单');}
          }
        }.bind(this)).catch(function (err) {
          this.$message.error('danger/inspectListPublic/find');
          console.log(err);
        });
        this.$http.post('danger/inspectPublic/detail', {id:id}).then(function (res) {
          if (res.data.success) {
            this.form=res.data.data;
          }
        }.bind(this)).catch(function (err) {
          this.$message.error('danger/inspectPublic/detail');
          console.log(err);
        });
        if(this.nodeData.evaluate){//发布整改单页面可以跳转到隐患评估
          this.estimationFlag=true;
        }
      },
      //-----------------------------------表格功能---------------------------------
      //改时间格式
      changeFormat:function (row) {
        return this.transferTime(row.deadline);
      },
      //查找人员
      remoteCheckPerson:function (val) {
        this.$store.dispatch('getPersonOptions',val);
      },
      itemEditClick:function (row,index) {
        this.recordForm.dangerPics.splice(0);
        this.currentIndex=index;
        this.recordForm.id=row.id;
        this.checkPersonOptions.splice(0);
        if(row.applyUserId){
          this.checkPersonOptions.push({value:row.applyUserId,label:row.applyUserName});
          this.recordForm.applyUser={value:row.applyUserId,label:row.applyUserName};
        }else{
          this.recordForm.applyUserId='';
        }
        this.recordForm.inspectProject=row.inspectProject;
        this.recordForm.inspectContent=row.inspectContent;
        this.recordForm.inspectResult=row.inspectResult;
        this.recordForm.hiddenDangerLevel=row.hiddenDangerLevel;
        if(row.dangerType){this.recordForm.dangerType=row.dangerType}else {this.recordForm.dangerType=''}
        this.recordForm.changeRequire=row.changeRequire;
        this.recordForm.deadline=row.deadline;
        this.recordForm.dangerPics=row.dangerPics;
        this.recordVisible=true;
      },
      //检查结果记录确定
      determineSave:function () {
        this.$refs["recordForm"].validate((valid) => {
          if (valid) {
            let tempObj={id:this.recordForm.id,applyUserId:this.recordForm.applyUser.value, inspectProject:this.recordForm.inspectProject, inspectContent:this.recordForm.inspectContent, inspectResult:this.recordForm.inspectResult, hiddenDangerLevel:this.recordForm.hiddenDangerLevel, dangerType:this.recordForm.dangerType, changeRequire:this.recordForm.changeRequire, dangerPics:this.recordForm.dangerPics, deadline:this.recordForm.deadline,};
            this.$http.post('/danger/inspectListPublic/update', tempObj).then(function (res) {
              if (res.data.success) {
                this.$http.post('danger/inspectListPublic/find', {inspectPublicId:this.currentId,needChange:'1'}).then(function (res) {
                  if (res.data.success) {
                    this.tableData=res.data.data;
                  }
                }.bind(this)).catch(function (err) {
                  this.$message.error('danger/inspectListPublic/find');
                  console.log(err);
                });
                this.recordVisible=false;
              }
            }.bind(this)).catch(function (err) {
              console.log('danger/inspectListPublic/update');
              console.log(err);
            });
          } else {
            console.log('error submit!!');
            return false;
          }
        });
      },
      //获取用户ID列表
      getUserIdArray:function () {
        let userIds=[];
        this.tableData.forEach(function (item) {
          userIds.push(item.applyUserId);
        });
        return Array.from(new Set(userIds));
      },
      //------------------------提交和保存----------------------------
      //判断每一条隐患是否有负责人
      judgeExitApplyUser:function () {
        for(let i=0;i<this.tableData.length;i++){
          if(!this.tableData[i].applyUserId){
            return i;
          }
        }
        return -1;//所有隐患都被分配了负责人
      },
      //提交
      submitClick:function () {
        let judgeIndex=this.judgeExitApplyUser();
        if(judgeIndex<0){
          if(this.nodeData.check){
            this.$refs.judgeDialog.openJudgeDialog();
          }else{
            //流程上不需要审核
            this.submitAndDoTask(false,false,0);
          }
        }else{
          this.$message.warning('第'+ (judgeIndex+1) +'条隐患未分配负责人');
        }
      },
      judgeExamine:function (val) {
        if(val){
          this.selectPersonData.title='请选择审核人';
          this.selectPersonData.defaultPerson=this.leaderUser;
          this.selectPersonData.isShow=true;
        }else{
          //选择不审核
          this.submitAndDoTask(false,false,0);
        }
      },
      //保存
      saveClick:function () {
        this.$message.success('保存成功！');
        this.$router.push({name:'hideDangerWorkflow'});
      },
      //进入隐患评估
      estimationClick:function () {
        this.selectPersonData.title='请选择评估人';
        this.selectPersonData.defaultPerson={value:0,label:''};
        this.selectPersonData.isShow=true;
      },
      selectPersonClick:function (val) {
        if(val){
          this.selectPersonData.isShow=false;
          if(this.selectPersonData.title==='请选择评估人'){
            //check=false,evaluate=true隐患评估
            this.submitAndDoTask(false,true,val);
          }else {
            //check=true,evaluate=false提交审核
            this.submitAndDoTask(true,false,val);
          }

        }else{
          this.$message.warning('请选择人员');
        }
      },
      //提交数据并走流程
      submitAndDoTask:function (checkFlag,evaluateFlag,examinePerson) {
        let tempDate=new Date();//当前时间

        let flowParams=new URLSearchParams;
        flowParams.append("taskId",this.taskId);
        flowParams.append("check",checkFlag);
        flowParams.append("evaluate",evaluateFlag);
        if(checkFlag||evaluateFlag){
          //进入审核或隐患评估
        }else{
          flowParams.append("userIds",this.getUserIdArray());
        }
        if(examinePerson){//有审核人或者评估人，才加入该参数
          flowParams.append("applyUserId",examinePerson);
        }
        this.$http.post('dangerFlow/doTask',flowParams).then(function (res) {
          if(res.data.success) {
            if(this.form.checkUserId){//是否已经存在验收人
              this.$message.success('提交成功！');
              this.$router.push({name:'hideDangerWorkflow'});
            }else{
              this.addCheckPerson();//写验收人
            }
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message.error('流程执行失败')
        }.bind(this));

      },
      addCheckPerson:function () {//写验收人，也就是当前用户
        this.$http.post('danger/inspectPublic/update', {id:this.currentId,dangerInspectMembers:this.form.dangerInspectMembers,checkUserId:this.$tool.getStorage('LOGIN_USER').userId}).then(function (res) {
          if (res.data.success) {
            this.$message.success('提交成功！');
            this.$router.push({name:'hideDangerWorkflow'});
          }
        }.bind(this)).catch(function (err) {
          console.log('danger/inspectPublic/update');
          console.log(err);
        });
      },

      //---------------------------隐患类型管理-------------------------
      addDangerTypeClick:function () {
        if(this.addDangerTypeStr.trim()){
          this.$http.post('danger/inspectListType/add?companyId='+this.$tool.getStorage('LOGIN_USER').companyId+'&typeName='+this.addDangerTypeStr.trim()).then(function (res) {
            if (res.data.success) {
              this.$message.success('添加成功！');
              this.$store.dispatch('getDangerTypeByCompanyId',this.$tool.getStorage('LOGIN_USER').companyId);
              this.addDangerTypeFlag=false;
            }
          }.bind(this)).catch(function (err) {
            this.$message.error('添加隐患类型失败');
            console.log(err);
          }.bind(this));
        }else{
          this.$message.warning('请输入内容');
        }
      },
      dangerTypeDelete:function (row) {
        this.$confirm('将删除该隐患类型, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$http.post('danger/inspectListType/delete?id='+row.id).then(function (res) {
            if (res.data.success) {
              this.$message.success('删除成功！');
              this.$store.dispatch('getDangerTypeByCompanyId',this.$tool.getStorage('LOGIN_USER').companyId);
            }
          }.bind(this)).catch(function (err) {
            this.$message.error('删除隐患类型失败');
            console.log(err);
          }.bind(this));
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });
        });

      },
    }
  }
</script>
<style>
</style>
