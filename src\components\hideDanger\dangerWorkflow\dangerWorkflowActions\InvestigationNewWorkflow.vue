<template>
  <div id="investigationNewWorkflow">
    <div class="background-style">
      <el-col :span="16" :offset="4" class="primary-background-title">{{titleStr}}</el-col>
      <el-col :span="16" :offset="4">
        <el-form :model="form" :rules="rules" ref="ruleForm" label-width="110px" class="demo-ruleForm">
          <el-col :span="24">
            <el-col :span="12">
              <el-form-item label="检查单编号：" prop="checkNum">
                <el-input v-model="form.checkNum"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="预计检查日期：" prop="predictInspectDate" label-width="120px">
                <el-date-picker
                  v-model="form.predictInspectDate"
                  type="date"
                  placeholder="选择日期"
                  style="width: 100%">
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-col>
          <el-col :span="24">
            <el-form-item label="检查单名称：" prop="name">
              <el-input v-model="form.name" placeholder="例：交工-309省道项目-节前检查"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24" v-if="isNotSelfCheck">
            <el-col :span="24">
              <el-col :span="12" >
                <!--如果是排查，该项只读-->
                <el-form-item label="受检单位：" prop="targetDeptId">
                  <div v-if="isPaiCha">
                    <el-input v-model="defaultCompanyStr" readonly="readonly"></el-input>
                  </div>
                  <div v-else>
                    <el-cascader
                      change-on-select
                      :options="unitOptions"
                      placeholder="请选择"
                      style="width: 100%"
                      v-model="targetCompanyArray"
                      @change="handlePickCompany">
                    </el-cascader>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="检查组组长：" prop="leaderUser">
                  <el-select
                    v-model="form.leaderUser"
                    filterable
                    remote
                    reserve-keyword
                    clearable
                    placeholder="请输入姓名后选择"
                    @change="leaderUserClick"
                    :remote-method="remotePerson"
                    :loading="personLoading"
                    style="width: 100%">
                    <el-option
                      v-for="item in personOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-col>
            <el-col :span="24">
              <el-form-item label="检查组成员：" prop="dangerInspectMembers">
                <el-select
                  v-model="form.dangerInspectMembers"
                  multiple
                  filterable
                  remote
                  reserve-keyword
                  clearable
                  placeholder="请输入姓名后选择"
                  :remote-method="remotePerson"
                  :loading="personLoading"
                  style="width: 100%">
                  <el-option
                    v-for="item in personOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-col>
          <el-col :span="24">
            <el-form-item label="检查表：">
              <el-input v-model="referTableName"></el-input>
            </el-form-item>
          </el-col>
        </el-form>
      </el-col>
      <el-col :offset="21" style="margin-bottom: 10px;">
        <el-button type="danger"  @click="deleteBatch" size="mini">批量删除</el-button>
      </el-col>
      <el-col :span="22" :offset="1">
        <el-table
          border
          ref="checkTable"
          :data="form.dangerInspectListPublicList"
          @selection-change="handleSelectionChange">
          <el-table-column
            type="selection"
            width="50"
            fixed
            label-class-name="header-style">
          </el-table-column>
          <el-table-column
            type="index"
            label="序号"
            width="50"
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            prop="inspectProject"
            label="检查项目"
            show-overflow-tooltip
            width="150"
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            prop="inspectContent"
            min-width="400"
            show-overflow-tooltip
            label="检查标准内容"
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            label="操作"
            width="100"
            align="center"
            fixed="right"
            label-class-name="inner-header-style">
            <template slot-scope="scope">
              <el-button size="mini" type="danger" @click="itemDeleteClick(scope.row,scope.$index)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div style="width: 100%;height: 40px;background-color: rgb(236,248,255);border-bottom: 1px solid #f2f2f2;">
          <div style="width: 100px;margin: auto">
            <el-button type="text" icon="el-icon-plus" @click="turnStatus">添加检查项目</el-button>
          </div>
        </div>
      </el-col>
      <el-col :span="22" :offset="1">
        <div style="float: right;margin: 20px">
          <el-button type="primary" style="margin-right: 20px" @click="submitClick()">提交</el-button>
          <!--<el-button type="success" style="margin-right: 20px" @click="newInspectPublic(0)">保存</el-button>-->
          <el-button @click="$router.push({name:'hideDangerWorkflow'})">返回</el-button>
        </div>
      </el-col>
    </div>

    <!-- 添加注意事项对话框 -->
    <knowledge-point-dialog
      ref="knowledgePointDialog"
      @selectedRows="selectedRowsHandle"

      >
    </knowledge-point-dialog>
    <!--对话框结束-->

    <search-people-dialog @determineClick="selectPersonClick" :data="selectPersonData" :defaultPersonId="selectPersonData.defaultPerson.value"></search-people-dialog>

    <!--是否审核对话框-->
    <judge-dialog ref="judgeDialog" @buttonClick="judgeExamine"></judge-dialog>


    <!--新增检查项对话框   add by pdn-->
    <el-dialog
      :visible.sync="newdialog.isShow"
      width="50%"
      title="新增检查项"
      :before-close="newdialogCancelBtnClickHandle">
      <el-form label-width="100px">
        <el-row  class="row">
          <el-col :span="24">
            <el-form-item label="检查项目">
              <el-input v-model="newdialog.form.inspectProject"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="检查内容">
              <el-input v-model="newdialog.form.inspectContent"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="newdialogCancelBtnClickHandle">取 消</el-button>
        <el-button
          type="danger"  size="mini"
          @click="newdialogOkBtnClickHandle">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
  import knowledgePointDialog from '../../../common/knowledgePointDialog3.vue'
  import SearchPeopleDialog from '../../../common/smallComponent/searchSinglePeopleAllCompany.vue'
  import JudgeDialog from '../../../common/smallComponent/judgeDialog.vue'
  export default {
    name: 'investigationNewWorkflow',
    data() {
      return {
        titleStr:'',
        //检查单数据
        form:{
          //检查单编号
          checkNum:'',
          //检查日期
          predictInspectDate:'',
          //受检单位
          targetDeptId:'',
          //检查组组长
          leaderUserId:'',
          leaderUser:'',
          //检查组成员
          dangerInspectMembers:[],
          //检查单名称
          name:'' ,
          //检查表模板ID
          inspectId:'',
          //检查内容
          dangerInspectListPublicList:[]
        },
        //检查单不用提交的数据
        referTableName:'',
        rules:{
          targetDeptId:[{ required: true, message: '请选择受检单位', trigger: 'change' }],
          name: [{ required: true, message: '请输入检查单名称', trigger: 'change' }],
        },
        isNotSelfCheck:true,//是否非自查
        isPaiCha:false,//是否为排查
        defaultCompanyStr:'',//排查时，自动显示本公司

        //检查，受检公司选择为数组，需要单独选择
        targetCompanyArray:[],
        //人员查询数据
        personLoading:false,

        // 对话框---添加知识点
        knowledgePoint : {
          // 知识点
          setting : {
            isMultiple : false,       // 是否多选
            isShow : false,         // 对话框是否显示
          },
          selectSingeRow : [],      // 选中的数据//执行操作数据
          // 检测项目
          inspectProject : '',
          // 检测标准内容
          inspectContent : '',
        },
        // 表格被选中的数据
        tableDataSelections : [],
        //新增检查的类型
        investigationType:'',//有检查，排查，自查，四个类型，其中自查为0
        //自动生成的编号
        autoNum:'',

        //------------------选择审核人的对话框-----------------------
        selectPersonData:{title:'请选择审核人',isShow:false,defaultPerson:{value:0,label:''}},
        //保存和发布时的暂存数据
        workBriefStatus:'',
        workBriefSetTime:false,

        // 新增检查项对话框  add by pdn
        newdialog : {
          // 是否显示
          isShow : false,
          form : {
            id : '',
            inspectContent : '',
            inspectProject : '',
          },
        },

        //表格中选中的项目   add by pdn
        selectedItem:[]

      }
    },
    components : {
      'knowledge-point-dialog' : knowledgePointDialog,
      'search-people-dialog' : SearchPeopleDialog,
      JudgeDialog,
    },
    computed:{
      unitOptions:function () {//当前公司的子公司
        if(this.$store.state.hideDangerData.targetDept.length){
          return this.$store.state.hideDangerData.targetDept[0].children;
        }else{
          return [];
        }
      },
      personOptions:function () {
        return this.$store.state.sysManageData.personByJson;
      },
    },
    mounted:function () {
      if(this.$route.params.referPlanId){
        this.form.inspectId=this.$route.params.referPlanId[0];
        this.$nextTick(function () {
          this.referTableName=this.$route.params.referName;
        }.bind(this));
        this.investigationType=this.$route.params.investigationType;
        this.titleStr='新增隐患'+this.$route.params.typeName;
        //自动编号
        this.autoNum='隐患'+this.$route.params.typeName;
        this.initNew(this.$route.params.referPlanId);
      }
    },
    watch:{
      $route(to, from){
        if((from.name=='hideDangerWorkflow'||from.name=='taskNotice')&&this.$route.name=='investigationNewWorkflow') {
          if(this.$route.params.referPlanId){
            this.form.inspectId=this.$route.params.referPlanId[0];
            this.$nextTick(function () {
              this.referTableName=this.$route.params.referName;
            }.bind(this));
            this.investigationType=this.$route.params.investigationType;
            this.titleStr='新增隐患'+this.$route.params.typeName;
            //自动编号
            this.autoNum='隐患'+this.$route.params.typeName;
            this.initNew(this.$route.params.referPlanId);
          }
        }
      }
    },
    methods: {
      //新建的初始化
      initNew: function (referPlanId) {
//        console.log('parames', this.$route.params.referPlanId)
        this.clearData();
        this.isNotSelfCheck=this.investigationType>0;
        this.isPaiCha=false;
        console.log(this.investigationType)
        if(this.investigationType===0){
          this.form.targetDeptId=this.$tool.getStorage('LOGIN_USER').companyId;
        }else if(this.investigationType===2){
          this.form.targetDeptId=this.$tool.getStorage('LOGIN_USER').companyId;
          this.isPaiCha=true;
          this.defaultCompanyStr=this.$tool.getStorage('LOGIN_USER').companyName;
        }else{//检查
          this.$store.dispatch('getTargetDept');
        }

        this.form.predictInspectDate = new Date();
        this.form.dangerInspectListPublicList = [];
        console.log('init findByDangerInspectIds')
        this.$http.post('danger/inspectList/findByDangerInspectIds?inspectIds='+ referPlanId).then(function (res) {
          if (res.data.success) {
            this.form.dangerInspectListPublicList = res.data.data;
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
        });
        this.getLastestNum();
      },
      //清除之前数据
      clearData:function () {
        this.$refs['ruleForm'].resetFields();
//        console.log(this.personOptions)
//        console.log(this.unitOptions)
//        console.log(this.form.dangerInspectListPublicList)
        this.personOptions.splice(0);
        if(this.unitOptions && this.unitOptions.length > 0){
          this.unitOptions.splice(0);
        }
        this.form.dangerInspectListPublicList.splice(0);
        this.referTableName='';
        this.targetCompanyArray=[];
      },
      //自动生成检查单编号
      getLastestNum:function () {
        this.$http.post('danger/inspectPublic/find',{pageSize:1,pageCurrent:1}).then(function (res) {
          if (res.data.success) {
            if(res.data.data.list){
              if(this.investigationType == 0){
                this.form.name=   this.referTableName +'('+this.$tool.getStorage('LOGIN_USER').username+')'+(res.data.data.list[0].id+1)+'_'+this.transferTime(new Date());
                this.form.checkNum = this.form.name;
              }else{
                // this.autoNum+=
                this.form.checkNum=this.referTableName+ (res.data.data.list[0].id+1)+'_'+this.transferTime(new Date());
              }
            }else{
              this.form.checkNum=this.autoNum+'1_'+this.transferTime(new Date());
            }
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
        });
      },
      //查找人员
      remotePerson:function (val) {
        this.$store.dispatch('getPersonByJson',{name:val,companyId:this.$tool.getStorage('LOGIN_USER').companyId});
      },
      //选择组长并加入到小组组员中去
      leaderUserClick:function (val) {
        if(val.value){
          this.form.leaderUserId=val.value;
          if(this.form.dangerInspectMembers.length){
            if(this.form.dangerInspectMembers.findIndex(function (item) {return item.value===val.value;})<0){
              this.form.dangerInspectMembers.push(val);
            }
          }else {
            this.form.dangerInspectMembers.push(val);
          }
        }
      },
      //删除条目
      itemDeleteClick:function (row,index) {
        this.$confirm('删除该条内容, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.form.dangerInspectListPublicList.splice(index,1);
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });
        });
      },
      //提交
      submitClick:function () {
        this.$refs['ruleForm'].validate((valid) => {
          if (valid) {
            if(this.investigationType>0){
              this.$refs.judgeDialog.openJudgeDialog();
            }else {//自查不需要审核
              this.form.activitiParams={type:this.investigationType,check:false,applyUserId:this.$tool.getStorage('LOGIN_USER').userId};
              this.newInspectPublic();
            }
          } else {
            return false;
          }
        });
      },
      judgeExamine:function (val) {
        if(val){
          this.selectPersonData.isShow=true;
        }else{
          this.form.activitiParams={type:this.investigationType,check:false,applyUserId:this.$tool.getStorage('LOGIN_USER').userId};
          this.newInspectPublic();
        }
      },
      selectPersonClick:function (val) {
        if(val){
          this.form.activitiParams={type:this.investigationType,check:true,applyUserId:val};
          this.form.examineUserId=val;
          this.newInspectPublic();
        }else{
          this.$message.warning('请选择审核人');
        }
      },
      newInspectPublic:function () {
        this.form.dangerInspectMembers.forEach(function (item) {
          item.userId=item.value;
        });
        this.form.dangerInspectListPublicList.forEach(function(item){
          item.inspectResult='合格';
        });
        this.form.publicCompanyId=this.$tool.getStorage('LOGIN_USER').companyId;
        this.form.publicDeptId=this.$tool.getStorage('LOGIN_USER').deptId;
        this.$http.post('dangerFlow/startDangerFlow', this.form).then(function (res) {
          if (res.data.success) {
            if(this.selectPersonData.isShow){this.selectPersonData.isShow=false;}
            this.$message.success('新增成功！');
            this.$router.push({name:'hideDangerWorkflow'});
          }else {
            this.$message.error(res.data.message);
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
        });
      },

      //--------------------------添加检查内容对话框------------------------
      //添加检查项
      selectedRowsHandle(rows){
        rows.forEach(function (it) {
          this.form.dangerInspectListPublicList.push({
            inspectProject : it.labels[it.labels.length-1],
            inspectContent : it.content
          })
        }.bind(this))
      },
      // 知识点---事件----单选事件
      currentChangeHandle(val){
        this.knowledgePoint.selectSingeRow = val;
      },
      // 知识点确定--添加数据
      execOkBtnClickHandle(){
        if(this.knowledgePoint.inspectProject&&this.knowledgePoint.inspectContent){
          this.form.dangerInspectListPublicList.push({
            inspectProject : this.knowledgePoint.inspectProject,
            inspectContent : this.knowledgePoint.inspectContent,
          });
        }else{
          this.$message.warning('内容不完整，不能添加');
        }
        this.knowledgePoint.inspectProject = '';
        this.knowledgePoint.inspectContent = '';
      },
      addCheckObject:function () {
        if(this.knowledgePoint.selectSingeRow.length){
          this.knowledgePoint.inspectProject = this.knowledgePoint.selectSingeRow[0].content;
        }else{
          this.$message.warning('请选择知识点');
        }
      },
      addCheckContent:function () {
        if(this.knowledgePoint.selectSingeRow.length){
          this.knowledgePoint.inspectContent = this.knowledgePoint.selectSingeRow[0].content;
        }else{
          this.$message.warning('请选择知识点');
        }
      },
      //选择受检单位
      handlePickCompany:function () {
        this.form.targetDeptId=this.targetCompanyArray[this.targetCompanyArray.length-1];
      },

      // 新增检查项对话框  取消按钮事件  add by pdn
      newdialogCancelBtnClickHandle(){
        // 关闭对话框
        this.newdialog.isShow = false;
        // 情况对话框
        this.newdialog.form.id = '';
        this.newdialog.form.inspectContent = '';
        this.newdialog.form.inspectProject = '';
      },

      //新增检查项  确定按钮事件  add by pdn
      newdialogOkBtnClickHandle(rows){
        if(this.newdialog.form.inspectProject==''||this.newdialog.form.inspectContent==''){
          this.$message({
            type : 'error',
            message : '输入内容不能为空！'
          })
        }else {
          this.form.dangerInspectListPublicList.push({
            inspectProject: this.newdialog.form.inspectProject,
            inspectContent: this.newdialog.form.inspectContent
          })
          this.newdialog.form.inspectContent = '';
          this.newdialog.form.inspectProject = '';
          this.newdialog.isShow = false;
        }
      },

      turnStatus(){
        this.newdialog.isShow = true;
      },

      // add by pdn
      handleSelectionChange(val){
        this.selectedItem = val;
        //console.log(this.form.dangerInspectListPublicList.length);
        //console.log(this.selectedItem);
      },

      //  批量删除选中项
      deleteBatch(){
        if(this.selectedItem.length == 0){
          this.$message({
            type : 'error',
            message : '请选择要删除的项目'
          })
        }else{
          this.$confirm('此操作将批量删除检查项, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {

            //遍历List，删除id相同的项   T-T
            for(var i=0;i<this.selectedItem.length;i++){
              for(var j=0;j<this.form.dangerInspectListPublicList.length;j++){
                if(this.selectedItem[i].id == this.form.dangerInspectListPublicList[j].id){
                  this.form.dangerInspectListPublicList.splice(j,1);
                  console.log("deleted 1 item");
                }
              }
            }
            this.$message({
              type: 'success',
              message: '删除成功'
            });
            this.$refs.checkTable.clearSelection();

          }).catch(() => {
            this.$message({
              type: 'info',
              message: '已取消删除'
            });
          });

          //console.log(this.form.dangerInspectListPublicList);
        }
      },

    },

  }
</script>
<style>
</style>
