<template>
  <el-container class="container">
    <el-main>
      <!--发布-->
      <el-form ref="info" label-width="100px" :model="info">
        <el-row type="flex">
          <el-col :span="8">
            <el-form-item label="培训时间">
              <span>{{ this.$tool.formatDateTime(info.trainingDate) && this.$tool.formatDateTime(info.trainingDate).substring(0, 10) }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="学时">
              <span>{{ info.trainingHours }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="培训内容">
              <span>{{ info.courses }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex">
          <el-col :span="8">
            <el-form-item label="参加人员">
              <span>{{ info.participants }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="组织部门">
              <span>{{ info.department }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="地点">
              <span>{{ info.location }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <!-- 是否打卡 -->
            <el-form-item label="是否打卡">
              <el-radio-group v-model="isPunch">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col v-if="isPunch" :span="12">
            <!-- 打卡位置: -->
            <el-button @click="dialogTableVisible = true">打卡位置:</el-button>
            <span>{{ location.poiname }}</span>
            <span style="margin-left: 20px;">详细地址:{{ location.poiaddress }}</span>
            <el-dialog width="400px" title="打卡位置" :visible.sync="dialogTableVisible" class="ka">
              <iframe class="if" id="mapPage" width="100%" height="600px" frameborder=0 src="https://apis.map.qq.com/tools/locpicker?search=1&type=1&key=MNFBZ-B5DWQ-37N5W-4AIZ6-P7EZQ-O4BDK&referer=myapp">
              </iframe>
            </el-dialog>
          </el-col>
        </el-row>
        <el-row type="flex">
          <el-col :span="24">
            <el-form-item label="人员列表">
              <chooseStaff ref="chooseStaff"></chooseStaff>
            </el-form-item>
          </el-col>
        </el-row>
        <!--        <el-row type="flex">-->
        <!--          <el-col :span="8">-->
        <!--            <el-form-item label="通知列表">-->
        <!--              <ul style="cursor: pointer;color:blue;">-->
        <!--                <li v-for="o in info.eduDailyNotify" @click="chooseNotify(o)">{{o.title }}</li>-->
        <!--              </ul>-->
        <!--            </el-form-item>-->
        <!--          </el-col>-->
        <!--        </el-row>-->
        <el-row type="flex">
          <el-col :span="8">
            <el-form-item label="通知标题">
              <el-input v-model="assist.eduDailyNotify.title"></el-input>
            </el-form-item>
          </el-col>
          <!--          <el-col :span="8">-->
          <!--            <el-form-item>-->
          <!--              <el-button-->
          <!--                @click="addNotifyClickHandle"-->
          <!--                icon="el-icon-plus"-->
          <!--                size="small" :span="2" type="danger" >通知</el-button>-->
          <!--            </el-form-item>-->
          <!--          </el-col>-->
        </el-row>
        <el-row type="flex">
          <el-col :span="24">
            <el-form-item label="通知内容">
              <vue-editor v-model="assist.eduDailyNotify.notify"></vue-editor>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" class="row" justify="center">
          <el-button @click="saveBtnClickHandle({ status: '2' })" size="small" :span="2" type="danger">活动开始</el-button>
          <el-button size="small" :span="2" @click="$router.back();">返回</el-button>
        </el-row>
      </el-form>
    </el-main>
  </el-container>
</template>

<script>
import { VueEditor } from 'vue2-editor'
import chooseStaff from '@/components/common/chooseStaff'
export default {
  components: {
    chooseStaff,
    VueEditor
  },
  data() {
    return {
      // 打卡位置弹窗状态
      dialogTableVisible: false,
      isPunch: 0,
      //选择的打卡位置
      location: {
        "module": "locationPicker",
        "latlng": {
          "lat": 29.899166,
          "lng": 121.629406
        },
        "poiaddress": "",
        "poiname": "",
        "cityname": ""
      },
      // info表
      info: {
        // ID
        id: '',
        // 培训时间
        trainingDate: '',
        // 时长
        trainingHours: '',
        // 培训内容
        courses: '',
        // 地点
        location: '',
        // 参加人员
        participants: '全体人员',
        // 参与人员列表
        eduDailyParticipants: [],
        // 组织部门
        department: '',
        // 活动通知
        eduDailyNotify: [],
        // 状态 0 未发布    1 已发布    2 进行中    3 待评级    4 已完结
        status: '0',
      },
      // 辅助字段
      assist: {
        // 通知
        eduDailyNotify: {
          title: '',
          notify: '',
        },
        // 参与人员表格
        eduDailyParticipants: [],
      },
      // 参与人员对话框数据
      staffDialog: {
        isShow: false,
      }
    }
  },
  watch: {
    $route(to, from) {
      // 如果来至列表页
      if (from.name === 'dailyTrainingIndex') {
        this.init();
      }
    },
  },
  created() {
    this.init();
  },
  mounted() {
    this.init();
    window.addEventListener('message', (event) => {
      // 接收位置信息，用户选择确认位置点后选点组件会触发该事件，回传用户的位置信息
      var loc = event.data;
      if (loc && loc.module == 'locationPicker') {//防止其他应用也会向该页面post信息，需判断module是否为'locationPicker'
        console.log('location', loc);
        // 此处可以将用户的位置信息发送给后端，进行保存
        this.location = loc
      }
      this.dialogTableVisible = false;
    }, false);
  },
  methods: {
    // 初始化
    init() {
      if (this.$route.params.status) {
        this.searchBtnClickHandle();
      } else {
        this.clear();
      }
    },
    // 清空数据
    clear() {
      this.info = this.$tool.clearObj({}, this.info);
      this.assist = this.$tool.clearObj({}, this.assist);
    },
    // 选择通知
    chooseNotify(item) {
      Object.entries(item).forEach(function (it) {
        if (it[1] && this.assist.eduDailyNotify.hasOwnProperty(it[0])) {
          this.assist.eduDailyNotify[it[0]] = it[1];
        }
      }.bind(this));
    },
    // 添加通知
    addNotifyClickHandle() {
      let params = this.$tool.deepClone(this.assist.eduDailyNotify, {});
      params['infoId'] = this.info.id;
      this.$store.dispatch('eduDailyNotifyAddOrUpdate', params).then(function (res) {
        if (res.success) {
          this.$message({
            type: 'success',
            message: '操作成功'
          })
          this.searchBtnClickHandle();
        } else {
          this.$message({
            type: 'error',
            message: res.message || '操作失败！！'
          })
        }
      }.bind(this));
    },
    // 根据id搜索信息
    searchBtnClickHandle() {
      this.clear();
      let id = this.$route.params.id;
      this.$store.dispatch('eduDailyInfoShow', { id: id }).then(function (res) {
        if (res.success) {
          let list = res.data.list[0];
          // 发布培训信息
          Object.entries(list).forEach(function (it) {
            if (it[1] && this.info.hasOwnProperty(it[0])) {
              this.info[it[0]] = it[1];
            }
          }.bind(this));
          // 打卡位置
          this.location.poiaddress = list.poiaddress
          this.location.poiname = list.poiname
          this.location.longitude = list.longitude
          this.location.latitude = list.latitude
          this.isPunch = list.isPunch;

          // 通知
          if (this.info.eduDailyNotify.length > 0) {                   //add by pdn  2020.11.12
            Object.entries(this.info.eduDailyNotify[0]).forEach(function (it) {
              if (it[1] && this.assist.eduDailyNotify.hasOwnProperty(it[0])) {
                this.assist.eduDailyNotify[it[0]] = it[1];
              }
            }.bind(this));
          }
          // 参与人员
          this.assist.eduDailyParticipants = list.eduDailyParticipants.map(function (it) {
            return {
              companyName: it.eduUser.companyName,
              deptName: it.eduUser.deptName,
              username: it.eduUser.username,
            }
          })

          // 发布培训的人员列表
          this.$refs['chooseStaff'].changeTableDataHandle(this.assist.eduDailyParticipants);
          // edit 添加和修改的时候，按钮显示；view 查看的时候，按钮隐藏
          this.$refs['chooseStaff'].isShowBtnHandle(true);

        } else {
          this.$message({
            type: 'error',
            message: res.message || '错误'
          })
        }
      }.bind(this));
    },
    // 未发布/已发布/进行中【开始按钮】--培训发布--保存按钮
    saveBtnClickHandle(options) {
      let id = this.$route.params.id;
      // 要选择参与人员
      if (this.info.eduDailyParticipants.length == 0) {
        this.$message({
          type: 'error',
          message: '请点击“参与人员”编辑图标，添加人员！！'
        })
        return;
      }
      // 判断培训的状态，0 未发布，1 已发布
      if (options) {
        this.info.status = options.status || '0';
      }
      // 通知
      let eduDailyNotify = this.assist.eduDailyNotify;
      if (eduDailyNotify.title) {
        if (id) {
          // 修改
          this.info.eduDailyNotify[0].title = eduDailyNotify.title;
          this.info.eduDailyNotify[0].notify = eduDailyNotify.notify;
        } else {
          // 添加
          this.info.eduDailyNotify.push({
            title: eduDailyNotify.title,
            notify: eduDailyNotify.notify,
          })
        }
      }
      let params = this.$tool.filterObj({}, this.$tool.filterObj({}, this.info));
      // 加入地址 是否打卡
      params.poiaddress = this.location.poiaddress;
      params.poiname = this.location.poiname;
      params.longitude = this.location.latlng.lng;
      params.latitude = this.location.latlng.lat;
      params.isPunch = this.isPunch
      
      this.$store.dispatch('eduDailyInfoAddOrUpdate', params).then(function (res) {
        if (res.success) {
          this.$message({
            type: 'success',
            message: '操作成功'
          })

          // 活动开始按钮
          if (options.status == 2) {
            this.$router.push({
              name: 'dailyTrainingProcessHaveInHand',
              params: {
                id: res.data.id,
                progress: 2,
                status: 'edit'
              }
            })
          } else {
            // 保存、提交等按钮
            this.$router.push({ name: 'dailyTrainingIndex' })
          }
        } else {
          this.$message({
            type: 'error',
            message: res.message || '错误'
          })
        }
      }.bind(this))
    },
  }
}
</script>

<style>
.container {
  background: #fff;
  padding: 0px 20px 20px;
}

.title {
  background: rgba(64, 158, 255, .1);
  color: #0f6fc6;
  border: 1px solid rgba(64, 158, 255, .2);
  border-radius: 5px;
}

.row {
  margin-top: 10px;
}
</style>
