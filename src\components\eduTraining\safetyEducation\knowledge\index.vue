<template>
  <div id="">
    <el-container class="container">
      <el-main>
        <el-form ref="form" :model="form" label-width="5px">
          <el-row>
            <el-col :span="8">
              <el-radio-group v-model="rangeSearch" @change="personSearchFn">
                <el-radio-button label="全部"></el-radio-button>
                <el-radio-button label="我的上传"></el-radio-button>
              </el-radio-group>
            </el-col>
          </el-row>
          <el-row style="margin-top:10px;">
            <el-col :span="2">
              <el-form-item >
                <el-select clearable v-model="form.type" placeholder="类型">
                  <el-option label="视频" value="0"></el-option>
                  <el-option label="文章" value="1"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="3">
              <el-form-item >
                <el-input clearable placeholder="名称" v-model="form.name"></el-input>
              </el-form-item>
            </el-col>

            <el-col :span="4">
              <el-form-item >
                <el-date-picker
                  clearable
                  v-model="form.startDate"
                  type="date"
                  placeholder="发布开始时间">
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="4" :offset="2">
              <el-form-item >
                <el-date-picker
                  v-model="form.endDate"
                  type="date"
                  clearable
                  placeholder="发布结束时间">
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :offset="2" :span="1">
              <el-button type="primary" @click="searchBtnClickHandle">搜索</el-button>
            </el-col>
            <el-col :offset="1" :span="1">
              <!--公司/组织者-->
              <el-button type="success"
                         icon="el-icon-plus"
                         @click="$router.push({ name : 'safetyEducationKnowledgeAdd' });">新增</el-button>
            </el-col>
          </el-row>
          <el-row>
            <el-table
              border
              :data="tableData.list"
              style="width: 100%">
              <el-table-column
                type="index"
                label="编号"
                width="100"
                align="center"
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="name"
                label="名称"
                show-overflow-tooltip
                min-width="200"
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="typeName"
                label="类型"
                width="60"
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="createUserName"
                label="创建人"
                width="80"
                label-class-name="header-style">
              </el-table-column>

              <el-table-column
                prop="companyName"
                label="公司"
                width="150"
                show-overflow-tooltip
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="createTime"
                :formatter="formatDateTime"
                label="创建时间"
                width="100"
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                fixed="right" label="操作"
                label-class-name="header-style"
                align="left" width="230">
                <template slot-scope="scope">
                  <!--<el-button size="mini" type="primary" @click="itemUpdateClick(scope.row)">修改</el-button>-->
                  <template >
                    <el-button size="mini" type="success" @click.native="itemViewClick(scope.row)">查看</el-button>
                    <el-button
                      size="mini" type="primary"
                      @click="shareClick(scope.row)">{{scope.row.isShare == 0 ? '共享' : '取享'}}</el-button>
                    <el-button
                      size="mini" type="danger" @click="itemDeleteClick(scope.row)">删除</el-button>
                  </template>
                </template>
              </el-table-column>
            </el-table>
            <el-pagination
              background
              layout="prev, pager, next"
              :current-page="tableData.pageNum"
              :page-size="form.pageSize"
              :total="tableData.total"
              @current-change ="disasterPageChangeHandle">
            </el-pagination>
          </el-row>
        </el-form>
      </el-main>
    </el-container>
  </div>
</template>
<script>
  export default {
    name: '',
    data() {
      return {


        // 范围搜索
        rangeSearch : '全部',
        // 搜索
        form : {
          // 名称
          startDate : '',
          endDate : '',
          name : '',
          type : '',
          // 个人查询的时候需要，全部查询不需要
          createUser : '',
          // 类型
//          newsType : '',
          // 当前页
          pageCurrent : 1,
          // 页数大小
          pageSize : 10,
        },
        assist:{
          // 类型
          newsType : [
            { value : '党建巡礼', label : '党建巡礼' },
            { value : '廉洁教育', label : '廉洁教育' },
            { value : '理论学习', label : '理论学习' },
            { value : '党务学习', label : '党务学习' },
          ],
        },
        tableData : {},
        // 角色 0 组织者或公司      1 部门        2  班组
        role : 0,
      }
    },
    mounted(){
      this.init();
    },
    watch:{
      $route(to,from){
        if(to.name === 'safetyEducationKnowledgeIndex') {
          this.init();
        }
      }
    },
    methods:{
      // 初始化
      init(){
        this.judgeUserRole();
        // 搜索
        this.searchBtnClickHandle();
      },
      judgeUserRole(){
        // 获取权限按钮
        let btns = this.$tool.getPowerBtns('eduTrainingMenu', this.$route.path);
//        console.log('btns', btns)
        // 公司
        if(btns.includes('addBtn')){
          this.role = 4;
        }
      },
      // 清空数据
      clear(){

      },
      // 格式化时间
      formatDateTime(row, column, cellValue){
        let pro = column.property;
        let num = 10;
        let str = this.$tool.formatDateTime(row[pro]) || '';
        return str ? str.substring(0, num) : str;
      },
      // 分页
      disasterPageChangeHandle(page){
        this.form.pageCurrent = page;
        if(this.isMore){
          this.saveScoreBtnClickHandle();
        } else {
          this.searchBtnClickHandle();
        }
      },
      // 是否为全部还是个人搜索
      personSearchFn(item){
        this.form.createUser = item == '我的上传' ? this.$tool.getStorage('LOGIN_USER').userId : '';
        this.searchBtnClickHandle();
      },
      // 共享按钮
      shareClick(row){
        console.log(row)
        let params = {
          id : row.id,
          isShare : row.isShare == 0 ? 1 : 0
        }

        this.$store.dispatch('eduMediumLibraryUpdateShareStatus', params).then(function(res){
          if(res.success){
            this.$message({
              type : 'success',
              message : res.message || '操作成功'
            })

          } else {
            this.$message({
              type : 'error',
              message : res.message || '错误'
            })
          }
          this.searchBtnClickHandle();
        }.bind(this));

      },
      // 搜索按钮
      searchBtnClickHandle(){
        let params = {};
        params['pageCurrent'] = this.form.pageCurrent;
        params['pageSize'] = this.form.pageSize;
        if(this.form.name) {
          params['name'] = this.form.name;
        }
        if(this.form.type) {
          params['type'] = this.form.type;
        }
        if(this.form.startDate) {
          params['startDate'] = this.form.startDate;
        }
        if(this.form.endDate) {
          params['endDate'] = this.form.endDate;
        }

        if(this.form.createUser) {
          params['createUser'] = this.form.createUser;
        }



        this.$store.dispatch('safeEduMediumLibraryQueryMedium', params).then(function(res){
          if(res.success){
            this.tableData = res.data;
          } else {
            this.$message({
              type : 'error',
              message : res.message || '错误'
            })
          }
        }.bind(this));
      },
      // 查看
      itemViewClick(row){
        let name = 'safetyEducationKnowledgeView';
        let params = {
          id : row.id,
          status : 'view'
        }
        this.$router.push({ name : name, params : params})
      },
      // 修改
      itemUpdateClick(row){
        let name = '';
        let params = {
          id : row.id,
          status : 'edit'
        }

        this.$router.push({ name : "safetyEducationKnowledgeAdd", params : params})
      },
      // 删除按钮
      itemDeleteClick(row){
        this.$confirm('此操作将永久删除, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
          .then(function(){
            this.$store.dispatch('safeEduMediumLibraryDeleteMedium', {
              id : row.id
            }).then(function(res){
              if(res.success){
                this.$message({
                  type : 'success',
                  message : '删除成功'
                })
              } else {
                this.$message({
                  type : 'error',
                  message : res.message || '删除失败！！'
                })
              }
              this.searchBtnClickHandle();
            }.bind(this))
          }.bind(this))
      },
      // 批量保存分数
      saveScoreBtnClickHandle(){
        this.$store.dispatch('eduEntryTrainingBatchInputScores', this.more).then(function(res){

          if(res.success){
            this.$message({
              type : 'success',
              message : '打分成功'
            })
            this.searchBtnClickHandle();
          } else {
            this.$message({
              type : 'error',
              message : res.message || '打分失败！！'
            })
          }

        }.bind(this));
      },
      // 培训时间
      trainingDateChange(val){
        this.form.startDate = val ? val[0] : '';
        this.form.endDate = val ? val[1] : '';
      },
    }
  }
</script>
<style>
  .container{
    background:#fff;
    padding:0 20px;
  }
  .row{
    margin-top:10px;
  }
  
</style>
