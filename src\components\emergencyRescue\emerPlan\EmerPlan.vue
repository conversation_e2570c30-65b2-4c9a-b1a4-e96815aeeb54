<template>
  <div id="emerPlan">
    <div class="background-style">

      <!--新增预案区-->
      <div style="width: 100%;height: 50px;border-bottom: 5px solid #F2F2F2;padding-bottom: 10px;display: block">
        <div style="height: 50px;padding:10px 10px 0 20px;">
          <!--<el-cascader-->
          <!--:options="cascaderOptions"-->
          <!--v-model="selectedOptions"-->
          <!--@change="handleChange"-->
          <!--:show-all-levels="false"-->
          <!--</el-cascader>-->
          分类:
          <el-select v-model="value01" placeholder="请选择" @change="firstPlanTypeChange" style="margin-left: 10px;width: 160px">
            <el-option
              v-for="item in selectOptions01"
              :key="item.value"
              :label="item.label"
              :value="item">
            </el-option>
            <el-option
              key="add"
              label=" "
              value="add"
              style="color: coral">
              <div @click="firstClassAdd"><i class="el-icon-plus"></i> 添加分类</div>
            </el-option>
          </el-select>
          <el-select v-model="value02" placeholder="请选择" :disabled="secondClassDisabled" @change="secondPlanTypeChange" style="width: 120px">
            <el-option
              v-for="item in selectOptions02"
              :key="item.value"
              :label="item.label"
              :value="item">
            </el-option>
            <el-option
              key="add"
              label=" "
              value="add"
              style="color: coral">
              <div @click="secondClassAdd"><i class="el-icon-plus"></i> 添加分类</div>
            </el-option>
          </el-select>
          <span style="margin-left: 20px">级别：</span>
          <el-select v-model="level" placeholder="请选择" style="width: 120px">
            <el-option
              v-for="item in levelOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
            <el-option
              key="add"
              label=" "
              value="add"
              style="color: coral">
              <div @click="levelAdd"><i class="el-icon-plus"></i> 添加级别</div>
            </el-option>
          </el-select>
          <span style="margin-left: 20px;color: coral">类型：</span>
          <el-select v-model="newPlanType" placeholder="请选择" style="width:120px">
            <el-option value="newPlanType01" label="应急预警"></el-option>
            <el-option value="newPlanType02" label="事件处置"></el-option>
          </el-select>
          <el-button type="success"  icon="el-icon-plus" style="margin-left: 30px;position: relative" @click="newClick">新增预案</el-button>
        </div>
      </div>

      <!--表格区-->
      <div style="width: 100%;">
        <div style="float: left;margin: 10px">
          <div style="width: 100%;">
            <!--YANG START-->
            <!--预案分类-->
            <el-select v-model="planType" placeholder="预案类别" style="width:120px">
              <el-option value="0" label="应急预警"></el-option>
              <el-option value="1" label="事件处置"></el-option>
            </el-select>
            <!--YANG END-->

            <el-cascader
              :options="cascaderOptions"
              v-model="selectedOptions"
              placeholder="分类选择">
            </el-cascader>
            <el-select v-model="tableLevel" placeholder="全部级别" style="width: 120px">
              <el-option
                v-for="item in tableLevelOptions"
                :key="item.value"
                :label="item.label"
                :value="item.label">
              </el-option>
            </el-select>
            <!--<el-select v-model="tableType" placeholder="全部类型" style="width: 120px">-->
            <!--<el-option-->
            <!--v-for="item in tableTypeOptions"-->
            <!--:key="item.value"-->
            <!--:label="item.label"-->
            <!--:value="item.label">-->
            <!--</el-option>-->
            <!--</el-select>-->
            <el-input placeholder="请输入预案名称" v-model="searchInput" style="width: 200px;margin-right: 20px">
            </el-input>
            <el-button icon="el-icon-search" @click="searchClick" type="primary">搜 索</el-button>
            <!--<el-button icon="el-icon-message" type="primary" @click="publicPlanClick">发布预案</el-button>-->
          </div>

        </div>

        <template v-if="planType === '0' ">
          <div style="padding: 10px 10px 20px 10px;">
            <el-table
              ref="multipleEmgTable"
              :data="emgPlanList.list"
              border
              highlight-current-row
              @row-click="emgRowClick"
              @selection-change="handleSelectionChange"
              style="width: 100%;">
              <el-table-column
                type="selection"
                width="55"
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="name"
                label="预案名称"
                min-width="350"
                show-overflow-tooltip
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="topTypeName"
                label="一级分类"
                width="200"
                align="center"
                show-overflow-tooltip
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="typeName"
                label="二级分类"
                width="200"
                align="center"
                show-overflow-tooltip
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="respLevel"
                label="级别"
                width="80"
                align="center"
                show-overflow-tooltip
                label-class-name="header-style">
              </el-table-column>
              <!--<el-table-column-->
                <!--prop="warnSignal"-->
                <!--label="预警信号"-->
                <!--min-width="120"-->
                <!--align="center"-->
                <!--label-class-name="header-style">-->
              <!--</el-table-column>-->
              <el-table-column fixed="right" label="操作" label-class-name="header-style" align="center" width="250">
                <template slot-scope="scope">
                  <el-button size="mini" type="success" @click="itemViewClick(scope.row)">查看</el-button>
                  <el-button size="mini" type="primary" @click="itemUpdateClick(scope.row)">修改</el-button>
                  <el-button size="mini" type="danger" @click="itemDeleteClick(scope.row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div >
            <el-pagination
              background
              layout="prev, pager, next"
              :current-page="emgPlanList.pageNum"
              :page-size="pageSize"
              :total="emgPlanList.total"
              @current-change="emgPlanPageChangeHandle">
            </el-pagination>
          </div>
        </template>


        <!--灾后处理-->
        <template v-if="planType === '1' ">
          <div style="padding: 10px 10px 20px 10px; ">
            <el-table
              ref="multipleTreatTable"
              :data="postDisasterTreatmentList.list"
              border
              highlight-current-row
              @row-click="treatRowClick"
              @selection-change="handleSelectionChange"
              style="width: 100%;">
              <el-table-column
                type="selection"
                width="55"
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="name"
                label="预案名称"
                min-width="350"
                show-overflow-tooltip
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="topTypeName"
                label="一级分类"
                width="200"
                align="center"
                show-overflow-tooltip
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="typeName"
                label="二级分类"
                width="200"
                align="center"
                show-overflow-tooltip
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="eventLevel"
                label="级别"
                width="80"
                align="center"
                show-overflow-tooltip
                label-class-name="header-style">
              </el-table-column>
              <el-table-column fixed="right" label="操作" label-class-name="header-style" align="center" width="250">
                <template slot-scope="scope">
                  <el-button size="mini" type="success" @click="itemViewClick(scope.row)">查看</el-button>
                  <el-button size="mini" type="primary" @click="itemUpdateClick(scope.row)">修改</el-button>
                  <el-button size="mini" type="danger" @click="itemDeleteClick(scope.row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div >
            <el-pagination
              background
              layout="prev, pager, next"
              :current-page="postDisasterTreatmentList.pageNum"
              :page-size="pageSize"
              :total="postDisasterTreatmentList.total"
              @current-change ="disasterPageChangeHandle">
            </el-pagination>
          </div>
        </template>



      </div>

    </div>
  </div>
</template>
<script>
  export default {
    name: 'emerPlan',
    data() {
      return {

//      -----------------------新增预案的选择数据------------------------
        planTypeArray:[],
        value01:'',
        selectOptions01:[],
        value02:'',
        selectOptions02:[],
        secondClassDisabled:true,
        device:[],
        publicSecurity:[],
        other:[],

        level:'',
        levelOptions:[
          {value:'应急警报', label:'应急警报'},
          {value:'4级', label:'4级'},
          {value:'3级', label:'3级'},
          {value:'2级', label:'2级'},
          {value:'1级', label:'1级'}
        ],
        newPlanType:'',
        //-----------------表格刷选数据------------------------------
        // 单个多级选择数据
        selectedOptions:['all'],
        cascaderOptions: [],
        tableLevel:'全部级别',
        tableLevelOptions:[
          {
            value:'全部级别',
            label:'全部级别'
          },
          {
            value:'1级',
            label:'1级'
          },
          {
            value:'2级',
            label:'2级'
          },
          {
            value:'3级',
            label:'3级'
          },
          {
            value:'4级',
            label:'4级'
          },
          {
            value:'应急警报',
            label:'应急警报'
          }
        ],
        tableType:'全部类型',
        tableTypeOptions:[
          {
            value:'全部类型',
            label:'全部类型'
          },
          {
            value:'应急预警',
            label:'应急预警'
          },
          {
            value:'灾后处置',
            label:'灾后处置'
          }
        ],
        searchInput:'',
        tableData: [],
        //勾选的内容
        selectedData:[],
        currentPage:0,
        totalItem:0,

        // YANG START
        planType : '0',       // 0  应急预警    1  灾后处置
        emgPlanPageCurrent : 1,      // 分页显示--当前页---紧急预案
        disasterPageCurrent : 1,      // 分页显示--当前页---灾后处置
        pageSize : 10,               // 每页条数
        // YANG END
      }
    },
    mounted:function () {
      this.getPlanType();
      this.searchClick();
    },
    watch:{
      $route(to, from){
        if((from.name==='newEmerPlan'||from.name==='updateEmerPlan')&&this.$route.name==='emerPlan'){
          this.searchClick();
        }
      },
      planType(to, from){
        this.selectedData=[];
        this.sendRequest();
      }
    },
    computed:{
      // 灾后处置---列表
      postDisasterTreatmentList : function(){
        if(this.$store.state.emerHandleModule.postDisasterTreatmentList){
          return this.$store.state.emerHandleModule.postDisasterTreatmentList;
        }else{
          return [];
        }
      },
      // 应急预案---列表
      emgPlanList : function(){
        if(this.$store.state.emerHandleModule.emgPlanList){
          return this.$store.state.emerHandleModule.emgPlanList;
        }else{
          return [];
        }
      },
    },
    methods:{
      //--------------------------初始化-------------------------------
      //获取预案分类
      getPlanType:function () {
        this.$http.get('emgType/getAll/'+this.$tool.getStorage('LOGIN_USER').companyId).then(function (res) {
          this.editPlanTypeArray(res.data.data);
        }.bind(this)).catch(function (err) {
          this.$message({
            showClose: true,
            message: '网络错误，请尝试重登录',
            type: 'error'
          });
        }.bind(this));
      },
      //编写预案分类
      editPlanTypeArray:function (typeTree) {
        this.selectOptions01=[];
        this.selectOptions02=[];
        this.planTypeArray=[];
        this.cascaderOptions=[];
        for(let i=0;i<typeTree.length;i++){
          this.selectOptions01.push({value:typeTree[i].id,label:typeTree[i].typeName,orderId:i});
          let tempArray=[];
          for(let j=0;j<typeTree[i].subTypes.length;j++){
            tempArray.push({value:typeTree[i].subTypes[j].id,label:typeTree[i].subTypes[j].typeName});
          }
          this.planTypeArray.push(tempArray);
        }
        this.secondClassDisabled=true;

        this.cascaderOptions.push({value:'all',label:'全部分类'});
        for(let i=0;i<typeTree.length;i++){
          let tempArray={value:typeTree[i].id,label:typeTree[i].typeName};
          if(typeTree[i].subTypes.length){
            tempArray.children=[];
            for(let j=0;j<typeTree[i].subTypes.length;j++){
              tempArray.children.push({value:typeTree[i].subTypes[j].id,label:typeTree[i].subTypes[j].typeName});
            }
          }
          this.cascaderOptions.push(tempArray);
        }
      },
      //---------------------------新建预案的跳转---------------------
      newClick:function () {
        if(this.newPlanType==='newPlanType01'){
          this.$router.push({ name: 'newEmerPlan',params:{firstClass:this.value01,secondClass:this.value02,level:this.level}});
        }else if(this.newPlanType==='newPlanType02'){
          this.$router.push({ name: 'emerHandleAddPlan',params:{firstClass:this.value01,secondClass:this.value02,level:this.level}});
        }else{
          this.$message({
            showClose: true,
            message: '请选择预案类型',
            type: 'warning'
          });
        }
      },
      //---------------------------新建预案，分类的监视----------------
      firstPlanTypeChange:function (val) {
        if(val.value==='add'||val.value===''){
          this.secondClassDisabled=true;
        }else{
          this.selectOptions02=this.planTypeArray[val.orderId];
          this.secondClassDisabled=false;
        }
      },
      secondPlanTypeChange:function (val) {

      },
      //--------------------------新建预案，添加分类---------------------
      firstClassAdd:function () {
        this.$prompt('请输入一级分类名称', '编辑', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
        }).then(({ value }) => {
          this.value01='';
        if(value.trim()){
          let params=new URLSearchParams;
          params.append('typeName',value.trim());
          params.append('parentId',0);
          params.append("companyId",this.$tool.getStorage('LOGIN_USER').companyId);
          this.$http.post('emgType/addOrUpdate',params).then(function (res) {
            this.getPlanType();
            this.$message({
              showClose: true,
              message: '添加一级分类成功！',
              type: 'success'
            });
          }.bind(this)).catch(function (err) {
            this.$message({
              showClose: true,
              message: '网络错误，请尝试重登录',
              type: 'error'
            });
          }.bind(this));
        }else {
          this.$message({
            type: 'warning',
            message: '名称不能为空'
          });
        }
      }).catch(() => {
          this.value01='';
        this.$message({
          type: 'info',
          message: '取消输入'
        });
      });
      },
      secondClassAdd:function () {
        this.$prompt('请输入二级分类名称', '编辑', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
        }).then(({ value }) => {
          this.value02='';
        if(value.trim()){
          let params=new URLSearchParams;
          params.append('typeName',value.trim());
          params.append('parentId',this.value01.value);
          params.append("companyId",this.$tool.getStorage('LOGIN_USER').companyId);
          this.$http.post('emgType/addOrUpdate',params).then(function (res) {
            this.getPlanType();
            this.value01='';
            this.$message({
              showClose: true,
              message: '添加二级分类成功！',
              type: 'success'
            });
          }.bind(this)).catch(function (err) {
            this.$message({
              showClose: true,
              message: '网络错误，请尝试重登录',
              type: 'error'
            });
          }.bind(this));
        }else {
          this.$message({
            type: 'warning',
            message: '名称不能为空'
          });
        }
      }).catch(() => {
          this.value02='';
        this.$message({
          type: 'info',
          message: '取消输入'
        });
      });
      },
      levelAdd:function () {
        this.$prompt('请输入级别名称', '编辑', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
        }).then(({ value }) => {
          this.level='';
        if(value.trim()){
          this.levelOptions.push({value:value,label:value});
        }else {
          this.$message({
            type: 'warning',
            message: '名称不能为空'
          });
        }
      }).catch(() => {
          this.level='';
        this.$message({
          type: 'info',
          message: '取消输入'
        });
      });
      },

      //-------------------------表格响应函数-----------------------------
      searchClick:function () {
        this.sendRequest();
      },
      currentPageClick:function (val) {
        let params=new URLSearchParams;
        params.append("pageCurrent",Number(val));
        this.sendRequest(params);
      },
      sendRequest:function () {
        let page = this.planType == '0' ? this.emgPlanPageCurrent : this.disasterPageCurrent;
        let data = {
          pageCurrent: page,
          pageSize: this.pageSize,
          companyId:this.$tool.getStorage('LOGIN_USER').companyId
        }
        if(this.selectedOptions[0] && this.selectedOptions[0] != 'all') data['topTypeId'] = this.selectedOptions[0];
        if(this.selectedOptions[1]) data['typeId'] = this.selectedOptions[1];
        if(this.tableLevel && this.tableLevel != '全部级别') data['eventLevel'] = this.tableLevel;
        if(this.searchInput) data['name'] = this.searchInput;
        // START YANG
        if(this.planType == '0') {   // 应急预警
          this.$store.dispatch('emgPlanListAction', data);
        } else if (this.planType == '1') {   // 灾后处置
          // 灾后处置--获取列表
          this.$store.dispatch('postDisasterTreatmentListAction', data);
        }
      },
      // 灾后处理--分页
      disasterPageChangeHandle(page){
        this.disasterPageCurrent = page;
        this.sendRequest();
      },
      // 紧急预案--分页
      emgPlanPageChangeHandle(page){
        this.emgPlanPageCurrent = page;
        this.sendRequest();
      },
      //点击行即可勾选
      emgRowClick:function (row) {
        this.$refs.multipleEmgTable.toggleRowSelection(row);
      },
      treatRowClick:function (row) {
        this.$refs.multipleTreatTable.toggleRowSelection(row);
      },
      //勾选条目
      handleSelectionChange:function (val) {
        this.selectedData=val;
      },
      //发布预案
      publicPlanClick:function () {
        if(this.selectedData.length){
          alert('该功能涉及通知模块，还未完成');
        }else{
          this.$message({
            type : 'warning',
            message : '请先勾选待发布的预案'
          })
        }
      },
      //-----------------------条目响应函数--------------------------------
      itemViewClick:function (row) {
        let name = this.planType === '0' ? 'viewEmerPlan' : 'emerHandleLookPlan';
        let id = row.id ;
        this.$router.push({ name: name ,params:{planId:id}});
      },
      itemUpdateClick:function (row) {
        let name = this.planType === '0' ? 'updateEmerPlan' : 'emerHandleAddPlan';
        let id = row.id ;
        //直接进入应急预案修改页面
        this.$router.push({ name: name ,params:{planId:id}});
      },
      itemDeleteClick:function (row) {
        this.$confirm('此操作将永久删除该预案, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          if(this.planType === '1'){
            // 删除---灾后处置
            this.$store.dispatch('postDisasterTreatmentDeleteAction', {
              id : row.id
            }).then(function(res){
              if(res.success){
                this.$message({
                  type : 'success',
                  message : '删除成功'
                })
                this.sendRequest();
              } else {
                this.$message({
                  type : 'error',
                  message : res.message || '删除失败！！'
                })
              }
            }.bind(this))
          }

          let params=new URLSearchParams;
          params.append("id",Number(row.id));
          params.append("companyId",this.$tool.getStorage('LOGIN_USER').companyId);
          this.$http.post('emgPlan/delete',params).then(function (res) {
            this.$message({
              showClose: true,
              message: '删除成功',
              type: 'success'
            });
            this.sendRequest();
          }.bind(this)).catch(function (err) {
            this.$message({
                showClose: true,
                message: '网络错误，请尝试重登录',
                type: 'error'
              });
            }.bind(this));
          }).catch(() => {
            this.$message({
            type: 'info',
            message: '已取消删除'
          });
        });
      },


    }
  }
</script>
<style>

</style>
