<template>
  <div id="newRole">
    <div style="top: 20px;left:10px;right:10px;position:absolute;background-color: white;padding-bottom: 40px;min-height: 100%">

      <el-col :span="12" :offset="6" style="text-align: center;margin-bottom: 20px;margin-top:30px;font-size: large;letter-spacing: 2px;color:#3576AA;border-left:5px solid #049ff1">新增角色</el-col>

      <el-col :span="12" :offset="6">
        <el-form ref="ruleForm" :model="form" :rules="rules" label-width="80px">
          <el-col :span="24">
            <el-form-item label="角色名称" prop="roleName">
              <el-input v-model="form.roleName" placeholder="请填入角色名称"></el-input>
            </el-form-item>
            <el-form-item label="角色排序" prop="sort">
              <el-input v-model="form.sort" placeholder="请填入角色排序"></el-input>
            </el-form-item>
            <el-form-item label="备注" prop="other">
              <el-input v-model="form.other"></el-input>
            </el-form-item>
            <p style="font-size: 15px;margin-left: 10px">功能权限:</p>
            <el-tree
              :data="permissionList"
              :props="defaultProps"
              node-key="id"
              ref="tree"
              highlight-current
              default-expand-all
              show-checkbox>
            </el-tree>
            <el-form-item style="margin-top: 100px">
              <el-button style="float: right;margin-left: 20px" @click="returnClick">返回</el-button>
              <el-button style="float: right;margin-left: 20px" type="primary" plain @click="resetForm('ruleForm')">重置</el-button>
              <el-button type="primary" style="float: right" @click="submitClick('ruleForm')">新建</el-button>
            </el-form-item>
          </el-col>
        </el-form>
      </el-col>

    </div>
  </div>
</template>
<script>
  export default {
    name: 'newRole',
    data() {
      return {
        form:{
          roleName:null,
          sort:9999,
          other:null
        },
        defaultProps: {
          children: 'list',
          label: 'permissionName'
        },
        rules:{
          roleName:[
            {required:true,message:'请输入角色名称',trigger:'change'}
          ],
        }
      }
    },
    computed:{
      permissionList:function () {
        return this.$store.state.sysManageData.permissionList;
      }
    },
    watch:{
      // 监听路由
      $route(to,from){
        if(to.name === 'newRole') {
          this.resetForm('ruleForm');
        }
      },
    },
    mounted:function () {
      this.resetForm('ruleForm');
      this.$store.dispatch("getPermissionList");
    },
    methods:{
      submitClick:function (formName) {
        this.$refs[formName].validate((valid) => {
          if (valid) {
            if(this.$refs.tree.getCheckedKeys().length){
              let permissionArray=this.$refs.tree.getCheckedKeys();
              let params = new URLSearchParams;
              params.append("role",this.form.roleName);
              params.append("sort",this.form.sort);
              if(this.form.other!==null){
                params.append("description",this.form.other);
              }
              let startStr='sysRolePermissions[';
              let endStr='].permissionId';
              for(let i=0;i<permissionArray.length;i++){
                params.append(startStr+i+endStr,permissionArray[i]);
              }
              this.$http.post('role/add',params).then(function (res) {
                this.$message({
                  showClose: true,
                  message: '新增角色成功！',
                  type: 'success'
                });
                this.$router.push({name:'roleManage'});
              }.bind(this)).catch(function (err) {
                console.log(err);
                this.$message({
                  showClose: true,
                  message: '新增失败',
                  type: 'error'
                });
              }.bind(this));
            }else {
              this.$message({
                showClose: true,
                message: '请选择至少一项权限！',
                type: 'warning'
              });
            }
          } else {
            return false;
          }
        });
      },
      returnClick:function () {
        this.resetForm('ruleForm');
        this.$router.go(-1);
      },
      resetForm(formName) {
        this.$refs[formName].resetFields();
        this.$refs.tree.setCheckedKeys([]);
      }

    }
  }
</script>
<style>
</style>
