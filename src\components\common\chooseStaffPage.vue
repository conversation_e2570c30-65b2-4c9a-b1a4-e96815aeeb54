<template>
    <div>

        <el-col :span="6">
          <el-input clearable placeholder="姓名" v-model="keywords"></el-input>
        </el-col>
        <el-col :span="3" :offset="2">
          <el-button style="margin-bottom:5px;" type="primary" @click="searchBtnClickHandle()">搜索</el-button>
        </el-col>

      <el-table
        ref="staffTable"
        @selection-change="selectionChange"
        :data="frontPage.tableData">
        <el-table-column
          v-if="frontPage.isShowSelection"
          :selectable="function(row, index){ return frontPage.isChecked;}"
          width="70"
          type="selection">
        </el-table-column>
        <el-table-column
          width="70"
          label="序号"
          type="index">
        </el-table-column>
        <!--<el-table-column-->
          <!--prop="num"-->
          <!--label="序号"-->
          <!--width="70">-->
        <!--</el-table-column>-->
        <el-table-column
          prop="username"
          label="姓名"
          width="120">
        </el-table-column>
        <el-table-column
          prop="deptName"
          label="部门"
          width="150">
        </el-table-column>
        <el-table-column
          prop="companyName"
          show-overflow-tooltip
          label="公司"
          width="150">
        </el-table-column>
        <el-table-column
          v-if="frontPage.isJoin"
          show-overflow-tooltip
          label="是否参加"
          width="150">
          <template slot-scope="scope">
            {{scope.row.status == 1 ? '已参加' : '未参加'}}
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          width="150">
          <template slot-scope="scope">
            <!--{{scope.row}}-->
            <!--只能删除本公司和子公司的-->
            <!--
               v-if="(scope.row.companyId == $tool.getStorage('LOGIN_USER').companyId)
               || (scope.row.parentCompanyId == $tool.getStorage('LOGIN_USER').companyId)"
               -->
            <el-button type="danger"  size="mini"  v-if="frontPage.isShowDelete"
                       @click.native.prevent="emgHandleListsDelHandle(scope.$index, scope.row, frontPage.tableData)">删除</el-button>
            <el-button type="success"  size="mini"  v-if="frontPage.isShowParticipate&&scope.row.participate"
                       @click.native.prevent="handleParticipateHandle(scope.$index, scope.row, frontPage.tableData)" :disabled="frontPage.isEditDisable">已参加</el-button>
            <el-button type="danger"  size="mini"  v-if="frontPage.isShowParticipate&&(!scope.row.participate)"
                       @click.native.prevent="handleParticipateHandle(scope.$index, scope.row, frontPage.tableData)" :disabled="frontPage.isEditDisable">未参加</el-button>



          <!--  <el-button type="danger"  size="mini"
                       v-if="(scope.row.companyId == $tool.getStorage('LOGIN_USER').companyId)
               || (scope.row.parentCompanyId == $tool.getStorage('LOGIN_USER').companyId)"
                       @click.native.prevent="emgHandleListsDelHandle(scope.$index, scope.row, frontPage.tableData)">删除</el-button>
      -->    </template>
        </el-table-column>


      </el-table>
      <el-pagination
        background
        layout="prev, pager, next"
        :current-page="frontPage.pageCurrent"
        :total="frontPage.total"
        :page-size="frontPage.pageSize"
        @current-change ="disasterPageChangeHandle">
      </el-pagination>
    </div>
</template>

<script>
    export default {
      //  总条数---实际数据，     前端数据
      props : ['staffData'],
      data(){
        return {
          // 总数据---实际数据
          // 前端分页
          frontPage : {
            // 是否可以勾选
            isChecked : true,
            // 是否显示勾选框
            isShowSelection : false,
            // 是否显示“删除”按钮
            isShowDelete : true,
            // 是否显示参加状态按钮
            isShowParticipate : false,
            // 参加按钮是否可编辑
            isEditDisable : false,
            // 当前页
            pageCurrent : 1,
            // 页数大小
            pageSize : 10,
            // 总数
            total : 0,
            // 表格数据
            tableData : [],
            // 表格字段--是否有参加字段
            isJoin : false,
          },
          currentStaff : [],

          keywords:"",
        }
      },
      watch:{
        staffData(val, oldVal){
          if(val){
            this.init();
          }
        },
      },
      mounted(){
        // 初始化数据
        if(this.staffData){
          this.init();
        }
      },
      methods:{
        // 初始化数据
        init(){
          // 分页总数
          this.keywords = '';
          this.frontPage.total = this.staffData.length;
          this.currentChangePage();
        },
        // 分页
        disasterPageChangeHandle(page){
          this.frontPage.pageCurrent = page;
          this.currentChangePage();
        },
        // 前端分页
        currentChangePage() {
          let page = this.frontPage;
          let from = (page.pageCurrent - 1) * page.pageSize;
          let to = page.pageCurrent * page.pageSize;
          page.tableData = [];
          for (; from < to; from++) {
            let item = this.staffData[from];
            if (item) {
              page.tableData.push(item);
            }
          }

          this.$nextTick(function(){
            this.toggleSelection();
          }.bind(this));
        },
        // 删除按钮--处理函数
        emgHandleListsDelHandle(index, item, rows) {
          this.$emit('deleteHandle', item);
          //获取当前元素在staffData里的索引值
          let num = (this.frontPage.pageCurrent - 1) * this.frontPage.pageSize + index ;
          this.staffData.splice(num,1);
          this.currentChangePage();
          //rows.splice(index, 1);
          this.$emit("update:staffData",this.staffData);
        },
        // 显示已经选中的人员
        toggleSelection() {
          let userIdArr = this.currentStaff.map(function(it){ return it.userId; });
          if (userIdArr.length > 0) {
            this.frontPage.tableData.forEach(function(it, index){
              if(it.participate == true){    //原代码  userIdArr.includes(it.userId)
                this.$nextTick(()=>{
                  this.$refs['staffTable'].toggleRowSelection(this.frontPage.tableData[index],true);
                })
              }
            }.bind(this))
          } else {
            this.$refs.staffTable.clearSelection();
          }
        },
        // 参与人员---对话框---勾选员工
        selectionChange(val){
          // 已经选中的人员
          let userIdArr = this.currentStaff.map(function(it){ return it.userId; })
          // 当前表格显示的人员
          let curTableUserIdArr = this.frontPage.tableData.map(function(it){
            if(val.includes(it)){
              it.participate = true;
            }else{
              it.participate = false;
            }
            return it.userId; });
          if(val.length > 0){
            // 先将 userIdArr 中包含 dialog.dataTable 的剔除
            // 例如已选择的有【1，2，8，9】，当前表格为【1，2，3，4，5】，那么去除之后就是【8，9】
            this.currentStaff = this.currentStaff.filter(function(it){
              return !curTableUserIdArr.includes(it.userId);
            }.bind(this))
            // 再将当前表格选中的添加进去
            val.forEach(function(it){
              if(it){
                this.currentStaff.push(it);
              }
            }.bind(this))
          }
          // 选中的行
          this.$emit('selectedRows', this.frontPage.tableData);
        },

        /*
        * 对外接口---父组件可以调用的方法
        * */
        // 是否可以勾选
        isCheckedHandle(flag){
          this.frontPage.isChecked = flag;
        },
        // 是否显示参加字段
        isShowJoinHandle(flag){
          this.frontPage.isJoin = flag;
        },
        // 是否显示勾选框
        isShowSelectionHandle(flag){
          this.frontPage.isShowSelection = flag;
        },
        // 是否显示删除按钮
        isShowDeleteHandle(flag){
          this.frontPage.isShowDelete = flag;
        },
        // 是否显示变换参加状态按钮
        isShowParticipateHandle(flag){
          this.frontPage.isShowParticipate = flag;
        },
        // 参加按钮编辑状态
        isEditDisableHandle(flag){
          this.frontPage.isEditDisable = flag;
        },
        // 设置总体表格的数据
        setTotalTableDataHandle(list){
          if(this.frontPage.isShowSelection&&this.staffData.length){
            // 默认选中
            list.forEach(function(it, index){
              if(it.participate){
                this.$nextTick(function () {
                  this.$refs['staffTable'].toggleRowSelection(it);
                }.bind(this))
              }
            }.bind(this));
          }
        },

        searchBtnClickHandle(){
          if (this.keywords != '') {
            var flag = 0 ;
            this.staffData.forEach(item => {
              if (item.username == this.keywords) {
                //获取搜索元素在staffData里的索引值
                var index = this.staffData.indexOf(item);
                var pageNum = parseInt((index + 1) / this.frontPage.pageSize) + 1;
                this.disasterPageChangeHandle(pageNum);
                flag = 1;
              }
              //遍历到数组最后仍然没有查到人员
              if ((this.staffData.indexOf(item) + 1 == this.staffData.length) && (flag == 0)) {
                this.$message({
                  message: '未找到相关人员，请检查是否输入正确的全名',
                  type: 'warning'
                });
              }
            })
          }else {
            this.$message({
              message: '输入内容不能为空',
              type: 'warning'
            });
          }
        },

        handleParticipateHandle(index , item , table){
          //获取当前元素在staffData里的索引值
          let num = (this.frontPage.pageCurrent - 1) * this.frontPage.pageSize + index ;
          this.staffData[num].participate = !this.staffData[num].participate;
          this.currentChangePage();
          //this.$emit("update:staffData",this.staffData);
        },

      }
    }
</script>

<style>

</style>
