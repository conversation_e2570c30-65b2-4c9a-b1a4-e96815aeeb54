<template>
  <div class="div">
    <div style="display: flex; flex: 1">
      <div id="map">
        <el-popover
          ref="popover"
          placement="top-start"
          width="150"
          v-model="visible"
        >
          <p style="text-align: center">{{ carInfo }}</p>
          <div
            slot="reference"
            :style="{
              position: 'absolute',
              top: `${popoverPosition.y}px`,
              left: `${popoverPosition.x}px`,
            }"
          ></div>
        </el-popover>
      </div>
    </div>
  </div>
</template>
<script>
import "ol/ol.css";
import { Map,View,Feature } from "ol/index";
import { Tile as TileLayer,Vector as VectorLayer } from "ol/layer";
import { Vector as VectorSource,XYZ } from "ol/source";
import { Circle,Polygon,Point } from "ol/geom";
import { Fill,Text,Stroke,Style,Circle as CircleStyle,Icon } from "ol/style";
import { Draw,defaults } from "ol/interaction";
let list=[],map,featureLayers={ 1: 1,2: 2,3: 3,4: 4 },icons=[],getLists=[],ws=null;
let cardata={}
let stylePoly={
  1: "rgba( 26, 185, 244,0.8)",
  2: "rgba( 255, 255, 26,0.8)",
  3: "rgba( 240, 138, 69,0.9)",
  4: "rgba( 255, 26, 26,0.8)",
};
let opacityPoly={
  1: "rgba( 26, 185, 244,0.5)",
  2: "rgba( 255, 255, 26,0.5)",
  3: "rgba( 240, 138, 69,0.5)",
  4: "rgba( 255, 26, 26,0.5)",
};
const geometryType=[null,"Circle","Polygon"];
//初始map
function initMap() {
  map=new Map({
    target: "map",
    interactions: defaults({
      doubleClickZoom: false,
    }),
    view: new View({
      center: [121.6843688888890,29.8130175000000],zoom: 16.1,maxZoom: 18,projection: "EPSG:4326",
    }),
  });
  map.addLayer(new TileLayer({
    source: new XYZ({
      url: "http://t0.tianditu.gov.cn/img_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=img&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=fe33c2fc4c05d18c2e81f7fdf10be4d6",
    }),
  }));
  for(let i=1;i<=4;i++) {
    featureLayers[i]=new VectorLayer({
      source: new VectorSource({ features: [],}),
      style: new Style({
        fill: new Fill({
          color: stylePoly[i],
        }),
      }),
    });
    map.addLayer(featureLayers[i]);
  }
}


let drawInteraction,drawFeatures=[]; // 用于存储绘制的图形
// 手绘图形
function drawGeometry(type,params) {
  // drawFeatures=[];
  drawInteraction&&map.removeInteraction(drawInteraction);
  drawInteraction=new Draw({
    type: type,
    source: featureLayers[params.rlevel].getSource(),
    features: drawFeatures,
  });
  drawInteraction.on("drawend",(event) => {
    let geometry=event.feature.getGeometry(); // 获取圆的几何信息
    if(geometry.constructor.name==="Circle") {
      params["points"]=[geometry.getCenter()];
      params["radius"]=geometry.getRadius();
      list.push(params);
    } else {
      params["points"]=geometry.getCoordinates()[0];
      list.push(params);
    }
    drawInteraction.finishDrawing();
    drawInteraction.setActive(false);
  });
  map.addInteraction(drawInteraction);
}

function clearFeature() {
  Object.values(featureLayers).forEach((layer) => {
    layer.getSource().clear();
  });
}
function addIcon(LLARRAY) {
  const coordinates=LLARRAY;  // 你所有的坐标点
  const iconStyle=new Style({
    image: new Icon({
      anchor: [0.5,0],
      anchorXUnits: 'fraction',
      anchorYUnits: 'pixels',
      scale: 0.1,
      src: 'http://www.versoon.cn:40072/s/ltdk0i' // 输入图标资源路径
    })
  });
  for(let i=0;i<coordinates.length;i++) {
    let iconFeature=new Feature({
      geometry: new Point(coordinates[i].latAndLng),
      name: coordinates[i].name,
      popover: true,
    });
    iconFeature.setStyle(iconStyle);
    icons.push(iconFeature);  // 将 icon 放入数组中
  }
  const vectorSource=new VectorSource({
    features: icons
  });
  const vectorLayer=new VectorLayer({
    source: vectorSource,
    zIndex: 9999
  });

  map.addLayer(vectorLayer);
}
export default {
  data() {
    return {
      typeOptions: [
        { value: "1",label: "爆破",},
        { value: "2",label: "特种作业",},
      ],
      levelOptions: [
        { value: "1",label: "低风险",},
        { value: "2",label: "一般风险",},
        { value: "3",label: "较大风险",},
        { value: "4",label: "重大风险",},
      ],
      tableData: [],
      form: {
        rtype: "1",rlevel: "1",rdate: [
          "12.02 00:00",
          "12.30 00:00"
        ],
        title: "",
      },
      stylePoly: { 1: "#1ab9f4",2: "#ffff1a",3: "#f08a45",4: "#ff1a1a",},
      currentDate: new Date(),
      visible: false,carInfo: 'xx车',
      popoverPosition: { x: 0,y: 0 },
    };
  },
  mounted() {
    initMap();
    this.getData();
    // this.open2();
    map.on('singleclick',(event) => {
      map.forEachFeatureAtPixel(event.pixel,(feature,layer) => {
        if(feature.values_.popover) {
          let coordinate=event.coordinate;
          let pixel=map.getPixelFromCoordinate(coordinate);
          this.popoverPosition.x=pixel[0];
          this.popoverPosition.y=pixel[1];
          this.carInfo=feature.values_.name
          this.visible=true;
        }
      });
    });
  },
  activated() {
    this.open2();
    this.getData();
  },
  deactivated() {
    ws.close()
    console.log('关闭');
  },
  methods: {
    draw(kind,params) {
      drawGeometry(geometryType[kind],params);
    },
    getData() {
      clearFeature();
      getLists=[];
      this.$http
        .post("/sys/sysMineDynamic/findMineDynamicInfo",{})
        .then((res) => {
          this.tableData=res.data.data.list.map((i) => {
            i.rdate=i.rdate&&JSON.parse(i.rdate);
            return i;
          });
          this.tableData.forEach((i) => {
            const points=i.points;
            const opacity=!!i.rdate;
            const radius=i.radius;
            const title=i.title;
            this.setPoly(points,i.rlevel,opacity,radius,title);
          });
        });
    },
    setPoly(points,level,opacity,radius,title) {
      let geometry;
      if(radius) {
        geometry=new Circle(...points,radius);
      } else {
        geometry=new Polygon([points]);
      }
      const feature=new Feature({
        geometry: geometry,
      });
      // alert(title);
      feature.setStyle([
        new Style({
          fill: title!=='外圈'? new Fill({
            color: opacity? stylePoly[level]:opacityPoly[level], // 填充颜色
          }):null,
          stroke: new Stroke({
            color: stylePoly[level], // 边框颜色
            width: 2, // 边框宽度
          })
        }),
        new Style({
          text: new Text({
            text: title==='外圈'? '':title,
            overflow: true,
            // textAlign: 'center',
            // font: '15px 宋体',
            // fill: new Fill({ color: 'black' }), // 文字颜色
          })
        }),
      ]);
      featureLayers[level].getSource().addFeature(feature);
    },
    createStyle() {
      return new Style({
        stroke: new Stroke({
          color: "rgba(255, 0, 0, 1)",
          width: 3,
        }),
        // fill: new Fill({
        //   color: "rgba(255, 0, 0, 0.1)",
        // }),
        image: new CircleStyle({
          radius: 7,
          fill: new Fill({
            color: "rgba(255, 0, 0, 1)",
          }),
        }),
      });
    },
    addBtnClickHandle() {
      // 在绘制前检查是否选择了时间
      // if(!this.form.rdate||this.form.rdate.length!==2) {
      //   this.$message.error("请先选择持续时间");
      //   return;
      // }
      const params={
        rtype: this.form.rtype,
        rlevel: this.form.rlevel,
        title: this.form.title,
        rdate: this.form.rdate&&JSON.stringify(this.form.rdate),
      };
      this.draw(this.form.rtype,params);
    },
    clear() {
      clearFeature();
      this.getData();
    },
    save() {
      this.$http.post("/sys/sysMineDynamic/saveDynamic",list).then((res) => {
        this.$message.success("保存成功");
        // this.reset();
        this.getData();
        list=[];
      });
    },
    back() {
      getLists.forEach((poly) => {
        poly.opacity=poly.isShow? 0.8:0;
        poly.borderWidth=0.01;
      });
    },
    rouClassNameFn({ row,rowIndex }) {
      //把每一行的索引放进row
      row.index=rowIndex;
    },
    isCurrentTimeInRange(rdate) {
      if(!Array.isArray(rdate)||rdate.length!==2) {
        return false;
      }

      const currentYear=this.currentDate.getFullYear();
      const startTime=new Date(`${currentYear}-${rdate[0]}`);
      const endTime=new Date(`${currentYear}-${rdate[1]}`);

      return this.currentDate>=startTime&&this.currentDate<=endTime;
    },
    itemDeleteClick(row) {
      this.$confirm("此操作将永久删除该风险区域, 是否继续?","提示",{
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$http
            .post("/sys/sysMineDynamic/delDynamic",{
              id: row.id,
            })
            .then((res) => {
              this.$message.success("删除成功");
              this.getData();
            });
        })
        .catch(() => { });
    },
    // 格式化时间
    formatDateTime(startTime) {
      var date=new Date(startTime*1000); //时间戳为10位需*1000，时间戳为13位的话不需乘1000
      let Y=date.getFullYear()+'-';
      let M=date.getMonth()+1<10? '0'+(date.getMonth()+1)+'-':date.getMonth()+1+'-';
      let D=date.getDate()<10? '0'+date.getDate()+' ':date.getDate()+' ';
      let h=date.getHours()<10? '0'+date.getHours()+':':date.getHours()+':';
      let m=date.getMinutes()<10? '0'+date.getMinutes()+':':date.getMinutes()+':';
      let s=date.getSeconds()<10? '0'+date.getSeconds():date.getSeconds();
      return Y+M+D+h+m+s;
    },
    open2() {
      const list={
        '0868120295820036': 'Nj-112-02挖机',
        '0868120295826462': 'Nj-112-09挖机',
        '0868120295824053': 'Nj-112-06挖机',
        '0868120295820085': 'Nj-214-01 铲车',
        '0868120295826694': '00014号车',
        '0868120295826702': '00018号车',
        '0868120295826496': '00011号车',
        '0868120295826421': '00002号车',
        '0868120295821786': '00003号车',
      }
      const risk={
        '3': '较大风险',
        '4': '重大风险',
        '5': '区域边界',
      }
      // ws=new WebSocket('ws://192.168.110.50:8087/safe/websocket');
      ws=new WebSocket('ws://47.100.125.33:8081/safe/websocket');
      ws.onmessage=(e) => {
        if(e.data&&e.data.indexOf('latAndLng')==-1&&!cardata[e.data]) {
          cardata[e.data]=true
          const data=JSON.parse(e.data)
          this.$notify({
            title: '警告',
            dangerouslyUseHTMLString: true,
            message: `<p><strong>事件类型:${risk[data.alertType]}</strong></p><p><strong>发生地点:${data.dynamicName}</strong></p><p><strong>车辆人员编号：${list[data.deviceCode]}</strong></p><p><strong>发生时间：${this.formatDateTime(data.startTime)}</strong></p>`,
            duration: 60000,
            type: 'warning'
          });
        }
        if(e.data&&e.data.indexOf('latAndLng')!==-1) {
          addIcon(JSON.parse(e.data).map((i) => {
            i.name=list[i.deviceCode]
            return i
          }))
        }
      };
    }
  },
};
</script>
<style scoped>
.div {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
}
#map {
  flex: 1;
  margin-top: 20px;
  overflow: hidden;
  position: relative;
}
.search-bar {
  width: 100%;
  height: 50px;
  padding: 20px 20px 0 20px;
  display: block;
}
.el-notification__content {
  white-space: pre-line;
}
</style>
