<template>
  <el-container class="container">
    <el-main>
      <el-row type="flex">
        <el-button
          @click="saveBtnClickHandle"
          size="small" :span="2" type="primary" v-if="pageStatus === 'edit' || pageStatus === 'add'">保存</el-button>
        <el-button size="small" :span="2" type="primary" @click="$router.back();">返回</el-button>
      </el-row>
      <el-row type="flex" class="row">
        <el-form ref="form" label-width="100px" :model="form">
          <el-form-item label="检查表名称">
            <el-input v-model="form.name"  :readonly="pageStatus === 'view'"></el-input>
          </el-form-item>
          <el-form-item label="类别">
            <el-cascader
              :disabled="pageStatus === 'view'"
              :props="tableProp"
              :options="labelList"
              clearable
              filterable
              :debounce="400"
              change-on-select
              v-model="safeDangerInspectLabelsSelectedArr"
              @change="safeDangerInspectLabelsChangeHandle"
              placeholder="请选择或输入关键字"
              style="width: 100%">
            </el-cascader>
          </el-form-item>
          <el-form-item label="文件编号" itemtype="f">
            <el-input v-model="form.docNum"  :readonly="pageStatus === 'view'"></el-input>
          </el-form-item>
        </el-form>
      </el-row>
      <el-row type="flex" v-if="pageStatus === 'edit'">
        <!--@click="$refs['knowledgePointDialog'].isShow = true;"   本行是原39行代码  by pdn-->
        <el-button
          icon="el-icon-plus"
          @click="turnStatus"
          size="mini" :span="2" type="primary">检查项</el-button>
        <el-button
          :disabled="tableDataSelections.length == 0"
          @click="deleteMoreBtnClickHandle"
          size="mini" :span="2" type="primary">批量删除</el-button>
      </el-row>
      <el-row class="row" type="flex" v-if="pageStatus === 'edit' || pageStatus === 'view'" >
        <el-table
          @selection-change="handleSelectionChange"
          :data="form.dangerInspectLists">
          <el-table-column
            type="selection"
            width="50">
          </el-table-column>
          <el-table-column
            type="index"
            label="序号"
            width="100">
          </el-table-column>
          <el-table-column
            prop="inspectProject"
            label="检查项目"
            show-overflow-tooltip
            width="300">
          </el-table-column>
          <el-table-column
            prop="inspectContent"
            width="300"
            show-overflow-tooltip
            label="检查标准内容">
          </el-table-column>
          <el-table-column
            v-if="pageStatus === 'edit'"
            label="操作"
            width="300"
            align="center"
            label-class-name="inner-header-style">
            <template slot-scope="scope">
              <!--<el-button type="success" icon="el-icon-arrow-up"  size="mini" @click.native.prevent="toUp(scope.$index, form.dangerInspectLists)"></el-button>-->
              <!--<el-button type="primary" icon="el-icon-arrow-down"   size="mini" @click.native.prevent="toDown(scope.$index, form.dangerInspectLists)"></el-button>-->
              <el-button type="success"  size="mini" @click.native.prevent="emgHandleListsEditHandle(scope.$index, scope.row, form.dangerInspectLists)">修改</el-button>
              <el-button type="danger"  size="mini" @click.native.prevent="emgHandleListsDelHandle(scope.$index, scope.row, form.dangerInspectLists)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-row>
    </el-main>
    <el-footer>
      <!-- 添加注意事项对话框 -->
      <knowledge-point-dialog
        ref="knowledgePointDialog"
        @selectedRows="selectedRowsHandle">
      </knowledge-point-dialog>
      <!--对话框结束-->
    </el-footer>



    <!--修改检查项对话框-->
    <el-dialog
      :visible.sync="dialog.isShow"
      width="50%"
      title="修改检查项"
      :before-close="dialogCancelBtnClickHandle">
      <el-form label-width="100px">
        <el-row  class="row">
          <el-col :span="24">
            <el-form-item label="检查项目">
              <el-input v-model="dialog.form.inspectProject"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="检查内容">
              <el-input v-model="dialog.form.inspectContent"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogCancelBtnClickHandle">取 消</el-button>
        <el-button
          type="danger"  size="mini"
          @click="dialogOkBtnClickHandle">确定</el-button>
      </div>
    </el-dialog>

    <!--新增检查项对话框   add by pdn-->
    <el-dialog
      :visible.sync="newdialog.isShow"
      width="50%"
      title="新增检查项"
      :before-close="newdialogCancelBtnClickHandle">
      <el-form label-width="100px">
        <el-row  class="row">
          <el-col :span="24">
            <el-form-item label="检查项目">
              <el-input v-model="newdialog.form.inspectProject"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="检查内容">
              <el-input v-model="newdialog.form.inspectContent"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="newdialogCancelBtnClickHandle">取 消</el-button>
        <el-button
          type="danger"  size="mini"
          @click="newdialogOkBtnClickHandle">确定</el-button>
      </div>
    </el-dialog>

  </el-container>
</template>

<script>
  import knowledgePointDialog from '@/components/common/knowledgePointDialog3'
  export default {
    components : {
      'knowledge-point-dialog' : knowledgePointDialog
    },
    data(){
      return {
        form : {
          //检查表名称
          name : '',
          //文件編號
          docNum : '',
          // 公司id
          companyId : '',
          // 类别
          safeDangerInspectLabels : [],
          // 检测知识点
          dangerInspectLists : [],
        },
        // 选中的类别
        safeDangerInspectLabelsSelectedArr : [],
        // 表格被选中的数据
        tableDataSelections : [],
        // 页面状态
        pageStatus : 'edit',
        //---------------检查表分类选择--------------------
        tableProp:{
          children: 'dangerInspectTableLabels',
          label: 'label',
          value:'label'
        },


        // 检查项对话框
        dialog : {
          // 是否显示
          isShow : false,
          form : {
            id : '',
            inspectContent : '',
            inspectProject : '',
          },
        },

        // 新增检查项对话框  add by pdn
        newdialog : {
          // 是否显示
          isShow : false,
          form : {
            id : '',
            inspectContent : '',
            inspectProject : '',
          },
        },
      }
    },
    computed:{
      // 标签
      labelList : function(){
        console.log(this.$store.state.hideDangerData.tableTreeLabels);
        return this.$store.state.hideDangerData.tableTreeLabels;
      },
    },
    watch:{
      $route(to,from){
        // 如果来至列表页
        if(from.name === 'TableManage'){
          if(to.params.status === 'view'){
            this.viewPage();
          }
          else if(to.params.status === 'edit'){
            this.editPage();
          } else {
            // 添加页面，清空数据
            this.clear();
          }
        }
      }
    },
    created(){
      if(this.$route.params.status === 'view'){
        this.viewPage();
      }
      else if(this.$route.params.status === 'edit'){
        this.editPage();
      }
      else {
        this.clear();
      }
    },
    mounted(){
      this.init();
    },
    methods:{
      init(){
        // 获取知识点标签列表
        this.$store.dispatch("labelListAction");
        //检查表的标签，树形的
        this.$store.dispatch("getTableTreeLabels",this.$tool.getStorage('LOGIN_USER').companyId);
      },
      // 清空数据
      clear(){
        this.pageStatus = 'add';
        this.form.name = '';
        this.form.docNum = '';
        this.safeDangerInspectLabelsSelectedArr = [];
        this.form.dangerInspectLists = [];
      },
      // 添加检查项
      addTable(){
        this.$refs['knowledgePointDialog'].isShow = true;
      },
      // 查看页面
      viewPage(){
        this.pageStatus = 'view';
        this.init();
        this.searchBtnClickHandle();
      },
      // 修改页面
      editPage(){
        this.viewPage();
        this.pageStatus = 'edit';
      },
      // 根据id搜索信息
      searchBtnClickHandle(){
        let id = this.$route.params.id
        this.$store.dispatch('dangerInspectFind', {
          dangerInspect : {
            id : id
          }
        }).then(function(res){
          if(res.success){
            let list = res.data.list[0];
            this.form.name = list.name;
            this.form.docNum = list.docNum;
            this.safeDangerInspectLabelsSelectedArr = list.safeDangerInspectLabels.map(function(it){
              return it.label
            });
            this.form.safeDangerInspectLabels = list.safeDangerInspectLabels.map(function(it){
              return {
                label : it.label
              }
            });
          }
        }.bind(this));
        this.$store.dispatch('dangerInspectListFind', {
          inspectId : id,
          dangerInspect:{companyId: this.$tool.getStorage('LOGIN_USER').companyId}
      }).then(function(res){
          if(res.success){
            let list = res.data;
            this.form.dangerInspectLists = list.map(function(it){
              return {
                id : it.id,
                inspectProject : it.inspectProject,
                inspectContent : it.inspectContent,
              }
            })
          }
        }.bind(this));
      },
      // 类别改变处理函数
      safeDangerInspectLabelsChangeHandle(val){
        this.form.safeDangerInspectLabels = val.map(function(it){
          return { label : it };
        })
      },
      // 保存按钮点击处理
      saveBtnClickHandle(){
        // 保存和查看操作
        let id = this.$route.params.id;
        if(id){
          // 更新数据---表单
          let params1 = {
            name : this.form.name,
            docNum : this.form.docNum,
            safeDangerInspectLabels : this.form.safeDangerInspectLabels,
            id : id,
            companyId: this.$tool.getStorage('LOGIN_USER').companyId
          }
          this.$store.dispatch('dangerInspectUpdate', params1).then(function(res){
            if(res.success){
              this.$message({
                type : 'success',
                message : '更新成功'
              })
              this.$router.push({ name : 'TableManage' })
            }
            else {
              this.$message({
                type : 'error',
                message : '更新失败！！！！'
              })
            }
          }.bind(this));
          return;
        }
        // 这里添加了公司id---companyId
        this.form.companyId=this.$tool.getStorage('LOGIN_USER').companyId;
        // 添加
        this.$store.dispatch('dangerInspectAdd', this.form).then(function(res){
          if(res.success){
            this.$message({
              type : 'success',
              message : '添加成功'
            })
            this.$router.push({ name : 'TableManage' })
          }
          else {
            this.$message({
              type : 'error',
              message : '添加失败！！！！'
            })
          }
        }.bind(this))
      },
      // 知识点对话框----确定按钮
      selectedRowsHandle(rows){
        // 修改---有id代表修改
        let id = this.$route.params.id;
        let list = [];
        // 如果是添加的化，直接赋值；如果是修改的化，那么追加到尾部；
        let item = {};
        // 去重
        let lists = this.form.dangerInspectLists.map(function(it){
          return it.inspectContent;
        })
        rows.forEach(function(it){
          item = {
            inspectProject : it.labels[it.labels.length - 1],
            inspectContent : it.content,
          }
          if(id){
            item['inspectId'] = id;
          }
          if(lists.indexOf(item.inspectContent) === -1){
            list.push(item);
            this.form.dangerInspectLists.push(item);
          }
        }.bind(this))
        if(id){
          this.$store.dispatch('dangerInspectListAddBatch', JSON.stringify(list)).then(function(res){
            if(res.success){
              this.$message({
                type : 'success',
                message : '更新成功'
              })
              this.searchBtnClickHandle();
            }
            else {
              this.$message({
                type : 'error',
                message : '更新失败！！！！'
              })
            }
          }.bind(this));
        }

      },
      // 表格勾选
      handleSelectionChange(val){
        this.tableDataSelections = val;
      },
      // 批量删除
      deleteMoreBtnClickHandle(){
        // 修改操作
        let id = this.$route.params.id;
        if(id) {
          let ids = this.tableDataSelections.map(function(it){ return it.id });
          this.$store.dispatch('dangerInspectListDelete', { ids : ids }).then(function(res){
            if(res.success){
              this.$message({
                type : 'success',
                message : '批量删除成功'
              })
              this.editPage();
            }
            else {
              this.$message({
                type : 'error',
                message : '批量删除失败！！！！'
              })
            }
          }.bind(this))
          return;
        }
        let result = this.tableDataSelections.map(function(it){ return it.inspectProject; });
        this.form.dangerInspectLists = this.form.dangerInspectLists.filter(function(it){
          return result.indexOf(it.inspectProject) === -1;
        }.bind(this));
        this.tableDataSelections = [];
      },
      // 执行清单---向上移动
      toUp(index, rows){
        if(index === 0) {
          this.$message({
            type : 'warning',
            message : '当前位置为第一位'
          })
          return;
        }
        // 上一个 和 当前个
        let prev = rows[index - 1];
        let current = rows[index];
        // 删除当前
        rows.splice(index - 1, 2, current, prev);
      },
      // 执行清单---向下移动
      toDown(index, rows){
        if(index === rows.length - 1) {
          this.$message({
            type : 'warning',
            message : '当前位置为最后一位'
          })
          return;
        }
        // 上一个 和 当前个
        let next = rows[index + 1];
        let current = rows[index];
        // 删除当前
        rows.splice(index, 2, next, current);
      },

      // 执行清单--删除按钮--处理函数
      emgHandleListsEditHandle(index, item, rows){
//        console.log(item);
        // 将数据传到修改对话框中
        this.dialog.form.id = item.id;
        this.dialog.form.inspectContent = item.inspectContent;
        this.dialog.form.inspectProject = item.inspectProject;

        this.dialog.isShow = true;
      },
      // 执行清单--删除按钮--处理函数
      emgHandleListsDelHandle(index, item, rows) {
        // 修改操作
        let id = this.$route.params.id;
        if(id){
          this.$store.dispatch('dangerInspectListDelete', { ids : [item.id] }).then(function(res){
            if(res.success){
              this.$message({
                type : 'success',
                message : '删除成功'
              })
              this.editPage();
            }
            else {
              this.$message({
                type : 'error',
                message : '删除失败！！！！'
              })
            }
          }.bind(this))
          return;
        }
        rows.splice(index, 1);
      },


      dialogCancelBtnClickHandle(){
        // 关闭对话框
        this.dialog.isShow = false;
        // 情况对话框
        this.dialog.form.id = '';
        this.dialog.form.inspectContent = '';
        this.dialog.form.inspectProject = '';
      },


      // 新增检查项对话框  取消按钮事件  add by pdn
      newdialogCancelBtnClickHandle(){
        // 关闭对话框
        this.newdialog.isShow = false;
        // 情况对话框
        this.newdialog.form.id = '';
        this.newdialog.form.inspectContent = '';
        this.newdialog.form.inspectProject = '';
      },


      dialogOkBtnClickHandle(){

        if(this.dialog.form.inspectProject==''||this.dialog.form.inspectContent==''){
          this.$message({
            type : 'error',
            message : '输入内容不能为空！'
          })
        }else {
          let params = {
            id: this.dialog.form.id,
            inspectProject: this.dialog.form.inspectProject,
            inspectContent: this.dialog.form.inspectContent,
          }

//        return;
          this.$store.dispatch('dangerInspectListUpdate', params).then(function (res) {
            if (res.success) {
              // 关闭对话框，情况数据
              this.dialog.isShow = false;
              this.searchBtnClickHandle();
            } else {
              this.$message({
                type: 'error',
                message: res.message || '错误'
              })
            }
          }.bind(this));
        }
      },


      //新增检查项  确定按钮事件  add by pdn
      newdialogOkBtnClickHandle(){
        if(this.newdialog.form.inspectProject==''||this.newdialog.form.inspectContent==''){
          this.$message({
            type : 'error',
            message : '输入内容不能为空！'
          })
        }else {
          let params = {
            inspectId: this.$route.params.id,
            inspectProject: this.newdialog.form.inspectProject,
            inspectContent: this.newdialog.form.inspectContent,
          }

//        return;
          this.$store.dispatch('dangerInspectListAddBatch', params).then(function (res) {
            if (res.success) {
              this.$message({
                type: 'success',
                message: '更新成功'
              })
              this.newdialog.isShow = false;
              this.searchBtnClickHandle();
            }
            else {
              this.$message({
                type: 'error',
                message: '更新失败！！！！'
              })
              this.newdialog.isShow = false;
            }
          }.bind(this));
        }
      },

      turnStatus(){
        this.newdialog.isShow = true;
      }

    }
  }
</script>

<style>
  .container{
    background:#fff;
  }
  .row{
    margin-top:10px;
  }
</style>
