<template>
  <div id="sysLogManage" class="background-style">
    <!--表格区-->
    <div style="width: 100%;">
      <div style="float: left;margin: 10px">
        <div style="width: 100%;">
          时间范围：
          <el-date-picker
            v-model="search.startDate"
            type="date"
            placeholder="选择日期">
          </el-date-picker>-
          <el-date-picker
            v-model="search.endDate"
            type="date"
            placeholder="选择日期">
          </el-date-picker>
          用户名：
          <el-input v-model="search.username" style="width: 200px;"></el-input>
          <el-button icon="el-icon-search" @click="loadLog(1)" class="search-btn">搜 索</el-button>
        </div>
      </div>
      <div style="width: 100%;float: left;">
        <div style="padding: 10px">
          <el-table
            :data="logData"
            border
            highlight-current-row
            style="width: 100%">
            <el-table-column
              type="index"
              label="编号"
              width="60"
              align="center"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="username"
              label="用户名"
              width="100"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="operation"
              label="操作描述"
              width="200"
              align="center"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="method"
              label="执行函数"
              width="420"
              align="center"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="params"
              label="参数"
              width="420"
              align="center"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="time"
              label="执行时间/ms"
              min-width="100"
              align="center"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="ip"
              label="ip地址"
              min-width="120"
              align="center"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="createDate"
              :formatter="dateFormat"
              label="时间"
              min-width="150"
              align="center"
              label-class-name="header-style">
            </el-table-column>
          </el-table>
        </div>
        <div >
          <el-pagination
            background
            layout="prev, pager, next"
            :current-page="currentPage"
            :total="totalItem"
            @current-change="currentPageClick">
          </el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'sysLogManage',
    data() {
      return {
        logData:[],
        currentPage:1,
        totalItem:0,
        search:{
          startDate:'',
          endDate:'',
          name:'',
          username:''
        }
      }
    },
    mounted: function () {
      this.loadLog(1)
    },
    methods: {
      dateFormat(row, column) {
        //.replace(/年|月/g, "-").replace(/日/g, " ")
        return new Date(row.createDate).Format("yyyy-MM-dd hh:mm").toLocaleString();
      },
      currentPageClick:function (val) {
        this.loadLog(val)
      },
      loadLog:function (pageCurrent) {
        var params=new URLSearchParams()
        if(this.search.startDate!=''&&this.search.startDate!=null){
            params.append("startDate",this.search.startDate)
        }
        if(this.search.startDate!=''&&this.search.endDate!=null){
          params.append("endDate",this.search.endDate)
        }
        if(this.search.username!=''&&this.search.username.trim().length>0){
          params.append("username",this.search.username)
        }
        params.append("pageCurrent",pageCurrent)
        this.$http.post("sys/log/find",params).then(function (res) {
          if(res.data.success){
            this.logData=res.data.data.list
            this.currentPage=pageCurrent
            this.totalItem=res.data.data.total
          }
        }.bind(this))
      },

    }
  }

</script>
<style>
  .el-table__body tr > td {
    padding: 5px !important;
  }
</style>
