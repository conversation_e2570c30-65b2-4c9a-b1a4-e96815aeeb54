<template>
  <div id="uploadFileTable" style="width: 100%">
    <div style="width: 100%;float:left">
      <el-upload
        class="upload-demo"
        ref="upload"
        :action="uploadBaseUrl"
        multiple
        :with-credentials="cookies"
        :http-request="ossUploadRequest"
        :data="fileUploadParams"
        :before-upload="beforeUpload"
        style="width: 300px;margin-bottom: 10px;">
        <el-button size="small" type="primary">选取上传附件</el-button>
      </el-upload>
    </div>
    <div style="width: 100%;float: left">
      <el-table
        :data="fileList"
        border
        style="width: 100%">
        <el-table-column
          type="index"
          align="center"
          label-class-name="inner-header-style"
          width="50">
        </el-table-column>
        <el-table-column
          prop="fileName"
          label="文件名称"
          align="center"
          label-class-name="inner-header-style"
          min-width="320">
        </el-table-column>
        <el-table-column
          prop="uploadTime"
          label="上传时间"
          align="center"
          :formatter="dateFormat"
          label-class-name="inner-header-style"
          width="150">
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
          fixed="right"
          width="140"
          label-class-name="inner-header-style">
          <template slot-scope="scope">
            <el-button type="text" size="medium" style="color: #5daf34"
                       @click="fileDownload(scope.row)">下载
            </el-button>
            <el-button type="text" size="medium" style="color: #dd6161"
                       @click="deleteUploadFile(scope.row,scope.$index)">刪除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>
<script>
  import dealData from '../../../assets/functions/dealData'
  export default {
    name: 'uploadFileTable',
    props:['fileList','data'],
    data() {
      return {
        //上传组件信息
        cookies: true,
        fileUploadParams: {
          contentId: 0,
          contentType: 0
        },
        //上传和预览的基本地址
        downloadBaseUrl:'',
        uploadBaseUrl:'',
      }
    },
    methods:{
      beforeUpload:function () {
        this.fileUploadParams.contentId=this.data.contentId;
        this.fileUploadParams.contentType=this.data.contentType;
      },
      fileDownload:function (file) {
        const elink = document.createElement('a'); // 创建a标签
        elink.download = file.fileName ;// 文件名
        elink.style.display = 'none';
        elink.href = this.fileHttp.defaults.baseURL+file.path;
        document.body.appendChild(elink);
        elink.click();// 触发点击a标签事件
        document.body.removeChild(elink);
      },
      //直接上传到阿里云上
      ossUploadRequest:function (item) {
        //获取该文件对应的sign
        this.$http.get('sys/oss/sign?contentId='+this.data.contentId+'&contentType='+this.data.contentType+'&realName='+item.file.name).then(function (res) {
          if(res.data){
            let params=new FormData();
            params.append("name",item.file.name);
            params.append("key",res.data.dir + item.file.name);
            params.append("policy",res.data.policy);
            params.append("OSSAccessKeyId",res.data.accessid);
            params.append('success_action_status','200');
            params.append("callback",res.data.callback);
            params.append("signature",res.data.signature);
            params.append("file",item.file);
            this.fileHttp.post('',params,{headers: {'Content-Type': 'multipart/form-data'}}).then(function (res) {
              if(res.data.file) {
                let resultStr=dealData.decode(res.data.file);
                let resultJson=JSON.parse(resultStr);
                this.fileList.push(resultJson);
                this.$message.success('上传成功');
              }else {
                this.$message.error('上传失败');
              }

            }.bind(this))

          }
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message.error('获取唯一标识失败');
        }.bind(this));
      },
      dateFormat(row) {
        //.replace(/年|月/g, "-").replace(/日/g, " ")
        return new Date(row.uploadTime).Format("yyyy-MM-dd hh:mm").toLocaleString();
      },
      deleteUploadFile: function (row, index) {
        this.$confirm('此操作将删除该文件, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          var params = new URLSearchParams()
          params.append("fId", row.fId)
          this.$http.post("file/delete", params).then(function (res) {
            if (res.data.success) {
              this.fileList.splice(index, 1)
            }
          }.bind(this))
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });
        });
      },
    }
  }
</script>
<style>
</style>
