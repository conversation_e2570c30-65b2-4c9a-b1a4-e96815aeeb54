<template>
  <div id="">
    <el-container class="container">
      <el-main>
        <el-form ref="form" :model="form" label-width="5px">
          <el-row>
            <el-col :span="6">
              <el-form-item >
                <el-date-picker
                  style="width: 100%"
                 start-placeholder="教育时间范围"
                  @change="trainingDateChangeHandle"
                  v-model="assist.trainingDate"
                  type="daterange">
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="3">
              <el-form-item >
                <el-input clearable placeholder="姓名" v-model="form.eduUser.username"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item>
                <el-input clearable placeholder="原岗位" v-model="form.oldPost"></el-input>
              </el-form-item>
            </el-col>

          <!--</el-row>-->
          <!--<el-row>-->
            <el-col :span="4">
              <el-form-item >
                <el-input clearable  placeholder="现岗位" v-model="form.newPost"></el-input>
              </el-form-item>
            </el-col>
            <el-col :offset="1" :span="1">
              <el-button type="primary" @click="searchBtnClickHandle">搜索</el-button>
            </el-col>
            <el-col :offset="1" :span="1">
              <!--公司/组织者-->
              <el-button type="success"
                         v-if="role === 4"
                         icon="el-icon-plus"
                         @click="$router.push({ name : 'transferPostTrainingAdd' });">转岗培训</el-button>
            </el-col>
          </el-row>
          <el-row>
            <el-table
              :data="tableData.list"
              border>
              <el-table-column
                label-class-name="header-style"
                label="序号"
                width="100"
                type="index">
              </el-table-column>
              <el-table-column
                label-class-name="header-style"
                prop="eduUser.username"
                label="姓名"
                width="120">
              </el-table-column>
              <el-table-column
                label-class-name="header-style"
                prop="eduUser.gender"
                label="性别"
                width="120">
                  <template slot-scope="scope">{{ scope.row.eduUser && (scope.row.eduUser.gender ? '男' : '女')}}</template>
              </el-table-column>
              <el-table-column
                label-class-name="header-style"
                prop="oldPost"
                label="原岗位"
                width="120">
              </el-table-column>
              <el-table-column
                label-class-name="header-style"
                prop="newPost"
                label="现岗位"
                width="120">
              </el-table-column>
              <el-table-column
                label-class-name="header-style"
                prop="trainingDate"
                :formatter="formatDateTime"
                label="教育时间"
                width="120">
              </el-table-column>
              <el-table-column
                label-class-name="header-style"
                prop="trainingHours"
                label="学时"
                width="120">
              </el-table-column>
              <el-table-column
                label-class-name="header-style"
                prop="teacher"
                label="部门教育者"
                width="120">
              </el-table-column>
              <el-table-column
                label-class-name="header-style"
                prop="score"
                label="考试成绩"
                width="120">
              </el-table-column>
              <el-table-column
                fixed="right" label="操作"
                label-class-name="header-style"
                align="left" width="250">
                <template slot-scope="scope">
                  <el-button size="mini" type="success" @click="itemViewClick(scope.row)">查看</el-button>
                  <!--公司/组织者-->
                  <template v-if="role === 4">
                    <el-button size="mini" type="primary" @click="itemUpdateClick(scope.row)">修改</el-button>
                    <el-button size="mini" type="danger" @click="itemDeleteClick(scope.row)">删除</el-button>
                  </template>
                </template>
              </el-table-column>
            </el-table>
            <el-pagination
              background
              layout="prev, pager, next"
              :current-page="tableData.pageNum"
              :page-size="form.pageSize"
              :total="tableData.total"
              @current-change ="disasterPageChangeHandle">
            </el-pagination>
          </el-row>
        </el-form>
      </el-main>
    </el-container>
  </div>
</template>
<script>
  export default {
    name: '',
    data() {
      return {
        form : {
          // 教育时间---开始时间
          startDate : '',
          // 教育时间---结束时间
          endDate : '',
          // 用户
          eduUser : {
            // 姓名
            username : '',
          },
          // 原岗位
          oldPost : '',
          // 现岗位
          newPost : '',
          // 当前页
          pageCurrent : 1,
          // 页数大小
          pageSize : 10,
        },
        assist : {
          // 教育时间
          trainingDate : '',
        },
        tableData : {},
        // 角色 0 员工 1 发布者
        role : 0,
      }
    },
    mounted(){
      this.init();
    },
    watch:{
      $route(to,from){
        if(to.name === 'transferPostTrainingIndex') {
          this.init();
        }
      }
    },
    methods:{
      // 初始化
      init(){
        this.judgeUserRole();
        // 搜索
        this.searchBtnClickHandle();
      },
      judgeUserRole(){
        // 获取权限按钮
        let btns = this.$tool.getPowerBtns('eduTrainingMenu', this.$route.path);
        // 公司
        if(btns.includes('addBtn')){
          this.role = 4;
        } else {
          this.role = 1;
        }
      },
      // 教育时间
      trainingDateChangeHandle(val){
        if(val){
          this.form.startDate = val[0];
          this.form.endDate = val[1];
        } else {
          this.form.startDate = '';
          this.form.endDate = '';
        }
      },
      // 格式化时间
      formatDateTime(row, column, cellValue){
        let pro = column.property;
        let num = 10;
        // 年份4位 1999
        if(pro === 'createYear') num = 4;
        let str = this.$tool.formatDateTime(row[pro] || 0);
        return str ? str.substring(0, num) : str;
      },
      // 分页
      disasterPageChangeHandle(page){
        this.form.pageCurrent = page;
        this.searchBtnClickHandle();
      },
      // 搜索按钮
      searchBtnClickHandle(){
        let params = this.$tool.filterObj({}, this.$tool.filterObj({}, this.form));
        if(this.role !== 4){
          // 员工
          params['eduUser'] = {
            userId : this.$tool.getStorage('LOGIN_USER').userId
          }
        }
        this.$store.dispatch('eduReassignFind', params).then(function(res){
          if(res.success){
            this.tableData = res.data;
          } else {
            this.$message({
              type : 'error',
              message : res.message || '错误'
            })
          }
        }.bind(this));
      },
      // 查看
      itemViewClick(row){
        let name = 'transferPostTrainingAdd';
        let params = {
          id : row.id,
          status : 'view'
        }
        this.$router.push({ name : name, params : params})
      },
      // 修改
      itemUpdateClick(row){
        let name = 'transferPostTrainingAdd';
        let params = {
          id : row.id,
          status : 'edit'
        }
        this.$router.push({ name : name, params : params})
      },
      // 删除按钮
      itemDeleteClick(row){
        this.$confirm('此操作将永久删除, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
          .then(function(){
            this.$store.dispatch('eduReassignDelete', {
              id : row.id
            }).then(function(res){
              if(res.success){
                this.$message({
                  type : 'success',
                  message : '删除成功'
                })
                this.searchBtnClickHandle();
              } else {
                this.$message({
                  type : 'error',
                  message : res.message || '删除失败！！'
                })
              }
            }.bind(this))
          }.bind(this))
      },

    }
  }
</script>
<style>
  .container{
    background:#fff;
    padding:0 20px;
  }
  .row{
    margin-top:10px;
  }
</style>
