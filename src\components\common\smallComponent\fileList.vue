<template>
  <div id="fileList">
    <div style="width: 100%;float:left;margin-left: 20px">
      <el-button type="text" v-for="item in fileList" :key="item.fId" @click="downloadClick(item)" style="margin-right: 20px">{{item.fileName}}</el-button>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'fileList',
    props:['fileList'],
    data() {
      return {}
    },
    methods:{
      downloadClick:function (file) {
        const elink = document.createElement('a'); // 创建a标签
        elink.download = file.fileName ;// 文件名
        elink.style.display = 'none';
        elink.href = this.fileHttp.defaults.baseURL+file.path;
        document.body.appendChild(elink);
        elink.click() ;// 触发点击a标签事件
        document.body.removeChild(elink);
      },
    }
  }
</script>
<style>
</style>
