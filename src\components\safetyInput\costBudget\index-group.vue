<template>
  <div class="background-style" style="padding:20px;">
    <el-row>
      <el-col :span="6">
        <el-date-picker
          @change="yearChange"
          v-model="form.year"
          type="year"
          style="width: 100px;margin-top:-30px"
          placeholder="选择日期">
        </el-date-picker>
      </el-col>
      <el-col :span="18">
        <span style="margin-left: 20px">{{ ($tool.formatDateTime(form.year) || '').substring(0,4)}}年度安全生产预算跟踪表（单位：元）</span>
      </el-col>
    </el-row>
    <el-row style="margin-top:10px;">
      <el-table
        show-summary
        :data="groupTable">
        <el-table-column
          width="100"
          type="index"
          align="center">
        </el-table-column>
        <el-table-column
          prop="deptName"
          show-overflow-tooltip
          label="公司"
          width="300"
          align="center">
        </el-table-column>
        <el-table-column
          prop="budgetFee"
          show-overflow-tooltip
          label="预算费用"
          width="100"
          align="center">
          <template slot-scope="scope">
            <span>{{scope.row.budgetFee | rounding}}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="actualFee"
          show-overflow-tooltip
          label="预算执行"
          width="100"
          align="center">
          <template slot-scope="scope">
            <span>{{scope.row.actualFee | rounding}}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="remainFee"
          show-overflow-tooltip
          label="预算余额"
          width="100"
          align="center"
        >
          <template slot-scope="scope">
            <span>{{scope.row.remainFee | rounding}}</span>
          </template>
        </el-table-column>
      </el-table>
    </el-row>
  </div>
</template>
<script>
  export default {
    data() {
      return {
        form : {
          // 年份
          year : new Date,
        },
        // 选项卡----集团预算，上面的字段都是本部预算
        groupTable : [],
      }
    },
    created(){
      this.init();
    },
    watch:{
      $route(to,from){
        if(to.name == 'costBudgetIndexGroup') {
          this.init();
        }
      }
    },
    methods:{
      // 初始化
      init(){
        this.getGroupFee();
      },
      clear(){

      },
      yearChange(val){
        this.getGroupFee();
      },
      // 获取集团预算列表
      getGroupFee(){
        let params = {
          deptId : this.$tool.getStorage('LOGIN_USER').companyId,
          year : this.form.year,
        }
        // 根据年份获取项目
        this.$store.dispatch('costBudgetPlanGetGroupBudget', params).then(function(res){
          if(res.success){
            let list = res.data && res.data.list || [];
//            console.log(8888888,list)
            this.groupTable = list.map(function(it){
              let actualFee = it.costAccountRegs && it.costAccountRegs[0].totalAccount || 0
              return {
                // 公司名称
                deptName : it.deptName || 0,
                // 预算费用
                budgetFee : it.totalBudget || 0,
                // 预算执行
                actualFee : actualFee,
                // 预算余额
                remainFee : (it.totalBudget || 0) - actualFee
              }
            }.bind(this))
          } else {
            this.$message({
              type : 'warning',
              message : res.message || '很遗憾，没有查询数据！'
            })
          }
        }.bind(this))
      },

    }
  }
</script>
<style>
</style>
