<template>
  <div id="costBudgetPrintOne">
    <div v-html="rawhtml"></div>
  </div>
</template>

<script>
    export default {
      data(){
        let that = this;
        return {
          rawhtml : "",

          // 需要展开的key数组
          expandRowKeys : [],
          // 路由信息
          routeInfo : {},
          // 表格数据
          tableData : [],
        }
      },

      mounted(){
        this.init();
      },
      watch:{
        // 监听路由
        $route(to,from){
          if(to.name === 'costBudgetPrintOne') {
            this.init();
          }
          if (from.name == 'costBudgetIndex'){

          }
        },
      },
      methods:{

        // 初始化
        init(){

          this.rawhtml = this.$route.params.data;
          return;
          // 获取路由信息
          this.routeInfo = this.$route.params;
          console.log("routeInfo = ", this.routeInfo)
          return;
          // 搜索
          this.searchBtnClickHandle();
        },
        clear(){
//          this.dialog.form = this.$tool.clearObj({}, this.dialog.form);
        },
        // 获取九大类和子类的信息
        filterNineTypeAndSubTypeInfo2(parentArr){
          let result = [];
          for(var i = 0; i < parentArr.length; i++){
            let row = {};
            row['id'] = parentArr[i].id;
            row['item'] = parentArr[i].item;
            row['itemCurrentBudget'] = parentArr[i].itemCurrentBudget;
            row['deptName'] = parentArr[i].deptName || '';
            //row['children'] = [];
            result.push(row)
            let costBudgetSubItems = parentArr[i].costBudgetSubItems;
            for(var j = 0; j < costBudgetSubItems.length;j++){
              let subRow = {}
              subRow['id'] = costBudgetSubItems[j].id;
              subRow['item'] = costBudgetSubItems[j].item;
              subRow['itemCurrentBudget'] = costBudgetSubItems.itemCurrentBudget;
              subRow['deptName'] = costBudgetSubItems[j].deptName;
//              row['children'].push(subRow)
              result.push(row)
            }
            //result.push(row)
          }
          return result || [];
        },
        filterNineTypeAndSubTypeInfo(parentArr){
          let result = [];
          this.expandRowKeys = [];
          for(var i = 0; i < parentArr.length; i++){
            let row = {};
            row['id'] = parentArr[i].id;
            this.expandRowKeys.push(parentArr[i].id)
            row['item'] = parentArr[i].item;
            row['itemCurrentBudget'] = parentArr[i].itemCurrentBudget;
            row['deptName'] = parentArr[i].deptName || '';
            row['children'] = [];
            let costBudgetSubItems = parentArr[i].costBudgetSubItems;
            for(var j = 0; j < costBudgetSubItems.length;j++){
              let subRow = {}
              subRow['id'] = costBudgetSubItems[j].id;
              subRow['item'] = costBudgetSubItems[j].item;
              subRow['itemCurrentBudget'] = costBudgetSubItems.itemCurrentBudget;
              subRow['deptName'] = costBudgetSubItems[j].deptName;
              row['children'].push(subRow)
            }
            result.push(row)
          }
          return result || [];
        },
        // 搜索按钮
        searchBtnClickHandle(){
//          this.clear();
          let id = this.routeInfo.data.id
          this.$store.dispatch('getCostBudgetPlanHtmlInfo', {
            year : 2019
          }).then(function(res){
            if(res.success){
              console.log("okok")

            } else {
              this.$message({
                type : 'error',
                message : res.message || '错误'
              })
            }
          }.bind(this));
        },
      }

    }
</script>

<style>

</style>
