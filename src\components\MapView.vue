<template>
  <div id="mapView">
    <safeHeader></safeHeader>
    <whole-screen-map class="map"></whole-screen-map>
  </div>
</template>
<script>
  import WholeScreenMap from '../components/common/smallComponent/wholeScreenMap.vue'
  import SafeHeader from '@/components/common/header'
  export default {
    name: 'mapView',
    data() {
      return {}
    },
    components:{WholeScreenMap,SafeHeader},
    mounted:function () {
      this.$store.dispatch('changeViewFlag',{ menuFlag:true, managerViewFlag:true, gisFlag:false});
    },
    methods:{

    }
  }
</script>
<style scoped>
  #mapView{
    width:100%;
    height:100%;
    position: relative;
  }
  .map{
    position: absolute;
    left: 0;top: 90px;right: 0;bottom: 0;
    width:100%;
    height:100%;
  }
</style>
