<template>
  <div class="background-style" style="padding: 10px">
    <el-row style="margin:0">
      <el-col :span="6">
        <el-button type="primary" size="mini" @click="$router.back()">返回</el-button>
      </el-col>
    </el-row>
    <el-row style="margin:10px 0 0 0">
      <el-col :span="24">
        <egrid class="egrid"
               stripe border
               maxHeight="500"
               :data="egrid.data"
               :columns="egrid.columns"
               :column-type="egrid.columnType">
        </egrid>
      </el-col>
    </el-row>
  </div>
</template>

<script>


  export default {
    data(){
      return {
        form : {
          id : '',
        },
        // 表格
        egrid : {
          data : [],
          columns : [
            { label: '项目', prop: 'planItems' },
            { label: '实施时间', prop: 'implementDate' },
            { label: '备注', prop: 'remark' },
          ],
          // columnsProps 用于定义所有 columns 公共的属性
          columnsProps: {
            fit : true,
            sortable: true,
            align : 'center',
          },
          columnsSchema : {

          },
          columnType : 'index'
        }
      }
    },
    created(){
      this.init();
    },
    watch:{
      $route(to,from){
        let id = to.params && to.params.row && to.params.row.data && to.params.row.data.id || -1;
        if(to.name === 'eduPlanList') {
          if(id){
            this.searchBtnClickHandle();
          }
        }
      }
    },
    methods:{
      // 初始化
      init(){
        console.log(this.$route.params.row)
        let id = this.$route.params.row.data.id;
        this.form.id = id;
        // 搜索
        this.searchBtnClickHandle();
      },
      // 搜索按钮
      searchBtnClickHandle(){
        this.$store.dispatch('eduPlanFind', this.form).then(function(res){
          if(res.success){
            let items = res.data.list[0].eduPlanItems;

            let list = items.map(function(it){
              return {
                planItems : it.planItems || '',
                implementDate : (this.$tool.formatDateTime(it.implementDate) || '').substring(0, 10),
                remark : it.remark || '',
              }
            }.bind(this));
            this.egrid.data = list;
          } else {
            this.egrid.data = [];
          }
        }.bind(this));
      },
    }
  }
</script>

<style>

</style>
