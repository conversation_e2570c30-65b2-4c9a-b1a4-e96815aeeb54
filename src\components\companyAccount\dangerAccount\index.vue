<template>
  <div id="accountDangerIndex">
    <div class="background-style">

      <!--表格区-->
      <div style="width: 100%;">
        <!--搜索条件选取-->
        <!--粗分类-->

        <div style="float: left;margin: 10px;width: 100%;">
          <div style="float: left;margin-left: 10px ;">
            <el-radio-group v-model="radioResponseType" @change="responseTypeChange">
              <el-radio-button   :id="radioResponseButtons[0].value" :label="radioResponseButtons[0].value" :key="radioResponseButtons[0].value">{{radioResponseButtons[0].name}}</el-radio-button>
              <el-radio-button   v-if="this.$tool.getStorage('LOGIN_USER').parentCompanyId != 0"  :id="radioResponseButtons[1].value" :label="radioResponseButtons[1].value" :key="radioResponseButtons[1].value">{{radioResponseButtons[1].name}}</el-radio-button>
            </el-radio-group>
          </div>
          <el-cascader
            change-on-select
            :options="unitOptions"
            v-model="targetCompanyArray"
            placeholder="受检单位" clearable style="width:120px"
            @change="handlePickCompany">
          </el-cascader>
<!--          <el-date-picker-->
<!--            v-model="year"-->
<!--            type="year"-->
<!--            placeholder="选择年">-->
<!--          </el-date-picker>-->
          <el-date-picker
            id="investigationDateRange"
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期">
          </el-date-picker>
          <el-input style="width: 200px" v-model="searchInput" placeholder="请输入检查单名称" clearable></el-input>
          <el-button type="primary" icon="el-icon-search" @click="searchClick">搜索</el-button>
        </div>
        <!--搜索条件选取结束-->
        <!--类别标签-->
        <div style="float: left;margin: 5px 0 0 10px;width: 100%;height: 30px">
          <span style="float: left;line-height: 30px">类别标签：</span>
          <el-tag
            :key="tag"
            v-for="tag in dynamicTags"
            closable
            :disable-transitions="false"
            @close="handleClose(tag)"
            style="float: left">
            {{tag}}
          </el-tag>
          <div v-if="inputVisible" style="float: left">
            <el-select
              v-model="search.searchTag"
              filterable
              remote
              reserve-keyword
              clearable
              placeholder="请输入标签名后选择"
              :remote-method="remoteTag"
              :loading="search.tagLoading"
              @change="labelSelectChange"
              size="small"
              style="width: 150px;margin-left: 10px">
              <el-option
                v-for="item in tableTagOption"
                :key="item.label"
                :label="item.label"
                :value="item.label">
              </el-option>
            </el-select>
            <el-button type="info" size="small" style="margin-left: 10px"
                       @click="search.searchTag='';inputVisible=false">取消
            </el-button>
          </div>
          <div v-else style="float: left">
            <el-button type="warning" size="small" @click="inputVisible = true;" icon="el-icon-plus"
                       style="margin-left: 10px">添加类别
            </el-button>
            <el-button type="primary"
                       @click="viewDangerPlan"
                       size="small">
              {{this.$tool.formatDateTime(year).substring(0,4)}}安全检查计划表
            </el-button>
            <el-button type="primary"
                       @click="viewDangerInspectSummary"
                       size="small">
              {{this.$tool.formatDateTime(year).substring(0,4)}}隐患排查治理汇总表
            </el-button>
            <el-button type="primary"
                       @click="viewDangerReformSummary"
                       size="small">
              {{this.$tool.formatDateTime(year).substring(0,4)}}生产安全事故隐患排查治理情况一览表
            </el-button>
          </div>
        </div>
        <!--类别标签结束-->
        <!--列表-->
        <div style="width: 100%;float: left;">
          <div style="padding: 10px">
            <el-table
              :data="tableData"
              border
              highlight-current-row
              @row-dblclick="itemViewClick"
              style="width: 100%">
              <el-table-column
                prop="num"
                width="50"
                align="center"
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                label="状态"
                width="120"
                align="center"
                label-class-name="header-style">
                <template slot-scope="scope">
                  <el-tag :type="scope.row.statusTagType">{{scope.row.statusTag}}</el-tag>
                </template>
              </el-table-column>
              <el-table-column
                prop="checkNum"
                label="检查单编号"
                width="200"
                align="center"
                show-overflow-tooltip
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="name"
                label="检查单名称"
                width="200"
                align="center"
                show-overflow-tooltip
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="inspectDate"
                label="检查日期"
                width="120"
                align="center"
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="targetDeptName"
                label="受检单位"
                min-width="300"
                show-overflow-tooltip
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                label="分类标签"
                min-width="200"
                show-overflow-tooltip
                label-class-name="header-style">
                <template slot-scope="scope">
                  <el-tag v-for="item in scope.row.labels" :key="item">{{item}}</el-tag>
                </template>
              </el-table-column>
              <el-table-column
                fixed="right" label="验收单操作" label-class-name="header-style" width="155">
                <template slot-scope="scope">
                  <!--<el-button size="mini" type="success" @click="itemViewClick(scope.row)">查看</el-button>-->
                  <el-button size="mini" type="danger" @click="itemDownloadClick(scope.row,1)" v-if="scope.row.needFeedback==1">下载</el-button>
                  <el-button size="mini" type="primary" @click="itemPrintClick(scope.row,1)" v-if="scope.row.needFeedback==1">预览</el-button>
                  <!--                  <el-button size="mini" type="primary" @click="loadInspectPublicFiles(scope.row)">整改回执</el-button>-->
                </template>
              </el-table-column>
              <el-table-column
                fixed="right" label="检查单操作" label-class-name="header-style" width="155">
                <template slot-scope="scope">
                  <!--<el-button size="mini" type="success" @click="itemViewClick(scope.row)">查看</el-button>-->
                  <el-button size="mini" type="danger" @click="itemDownloadClick(scope.row,0)">下载</el-button>
                  <el-button size="mini" type="primary" @click="itemPrintClick(scope.row,0)">预览</el-button>
<!--                  <el-button size="mini" type="primary" @click="loadInspectPublicFiles(scope.row)">整改回执</el-button>-->
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div>
            <el-pagination
              background
              layout="prev, pager, next"
              :current-page="currentPage"
              :total="totalItem"
              @current-change="currentPageClick">
            </el-pagination>
          </div>
        </div>
        <!--列表结束-->
      </div>
    </div>
    <el-dialog
      title="相关下载"
      :visible.sync="inspectPlanListDialogVisible"
      width="70%"
      >
      <div >
        <el-table
          :data="reformReplyData"
          border
          center
          highlight-current-row
          style="width: 100%;"
        >
          <el-table-column
            label="序号"
            type="index"
            label-class-name="header-style"
            width="50">
          </el-table-column>
          <el-table-column
            label="回执类型"
            width="150"
            label-class-name="header-style">
            <template slot-scope="scope">
              <span v-if="scope.row.type==1">督办回执</span>
              <span v-if="scope.row.type==0">整改回执</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="userName"
            label="填写人"
            width="150"
            label-class-name="header-style">
          </el-table-column>
          <el-table-column
            prop="createTime"
            label="回执时间"
            min-width="150"
            label-class-name="header-style">
            <template slot-scope="scope">
              <span>{{$tool.formatDateTime(scope.row.createTime).substring(0,16)}}</span>
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" label-class-name="header-style" width="170">
            <template slot-scope="scope">
              <el-button size="mini" type="success" @click="viewReformReply(scope.row)">查看</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-col :span="24" class="card-shadow-style" style="margin-top: 10px;">
          <fileUpload ref="upload" :data="upload" ></fileUpload>
        </el-col>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="inspectPlanListDialogVisible = false">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
  import fileUpload from '@/components/common/fileUpload'
  export default {
    name: 'accountDangerIndex',
    data() {
      var that = this
      return {
        timer : null,
        //台账年度
        year: new Date(),
        //粗分类
        radioResponseButtons: [{value: 'all', name: '全部检查'},{value:'check',name:'督办整改'}],
        radioResponseType: 'all',
        //搜索类别选择
        publicUnit: '',
        unit: '',
        status: [],
        //受检公司选择为数组，需要从数组转字符
        targetCompanyArray:[],
        //标签对应表
        statusOptions: [
          {value: 0, label: '未发布', type: 'primary'},
          {value: 1, label: '发布审核', type: 'warning'},
          {value: 2, label: '修改检查单', type: 'danger'},
          {value: 3, label: '待检查', type: 'success'},
          {value: 4, label: '发布检查单', type: 'primary'},
          {value: 5, label: '隐患评估', type: 'primary'},
          {value: 6, label: '整改单审核', type: 'warning'},
          {value: 7, label: '待整改', type: 'success'},
          {value: 8, label: '整改审核', type: 'warning'},
          {value: 9, label: '验收', type: 'warning'},
          {value: 10, label: '督办整改', type: 'primary'},
          {value: 11, label: '督办审核', type: 'warning'},
          {value: 12, label: '督办验收', type: 'warning'},
          {value: 13, label: '督办整改审核', type: 'warning'},
          {value: 14, label: '验收审核', type: 'warning'},
          {value: 15, label: '已完成', type: 'info'},
        ],
        //状态选择项目
        statusTable: [
          {value: 0, label: '未发布', type: 'primary'},
          {value: 1, label: '发布审核', type: 'warning'},
          {value: 2, label: '修改检查单', type: 'danger'},
          {value: 3, label: '待检查', type: 'success'},
          {value: 4, label: '发布检查单', type: 'primary'},
          {value: 5, label: '隐患评估', type: 'primary'},
          {value: 6, label: '整改单审核', type: 'warning'},
          {value: 7, label: '待整改', type: 'success'},
          {value: 8, label: '整改审核', type: 'warning'},
          {value: 9, label: '验收', type: 'warning'},
          {value: 10, label: '督办整改', type: 'primary'},
          {value: 11, label: '督办审核', type: 'warning'},
          {value: 12, label: '督办验收', type: 'warning'},
          {value: 13, label: '督办整改审核', type: 'warning'},
          {value: 14, label: '验收审核', type: 'warning'},
          {value: 15, label: '已完成', type: 'info'},
        ],
        dateRange: '',
        searchInput: '',

        //表格数据
        tableData: [],
        currentPage: 0,
        totalItem: 0,

        //筛选标签
        dynamicTags: [],
        inputVisible: false,
        search: {
          searchTag: '',
          tagLoading: false,
        },
        inspectPlanListDialogVisible: false,
        reformReplyData:[],
        // 上传文件
        upload: {
          // 地址
          url: that.$http.defaults.baseURL + 'file/upload',
          // token
          cookies: true,
          // 上传参数
          params: {
            contentId: 0,
            contentType: 5
          },
          // 文件列表
          fileData: [],
        },
      }
    },
    components: {
      fileUpload
    },
    computed: {
      unitOptions: function () {
        if (this.$store.state.hideDangerData.targetDept.length) {
          return this.$store.state.hideDangerData.targetDept;
        } else {
          return [];
        }
      },
      tableTagOption: function () {
        return this.$store.state.hideDangerData.tableLabels;
      },
    },
    mounted: function () {
      this.$store.dispatch('getTargetDept');
      this.searchClick();
    },
    watch: {
      $route(to, from) {
        this.searchClick();
      }
    },
    methods: {
      responseTypeChange: function (val) {
        this.searchClick();
      },
      searchClick: function () {
        this.currentPage = 1;
        let params = {"pageCurrent": 1};
        this.sendRequest(params);
      },
      currentPageClick: function (val) {
        if (val) {
          this.currentPage = val;
          let params = {"pageCurrent": Number(val)};
          this.sendRequest(params);
        }
      },
      sendRequest: function (params) {
        var statuses=new Array()
        statuses[0]=15
        // params.publicCompanyId = this.$tool.getStorage('LOGIN_USER').companyId
        // params.targetDeptId = this.unit;
        // params.publicDeptId = this.publicUnit;
        params.statuses = statuses;
        if (this.dateRange) {
          params.startDate = this.dateRange[0];
          params.endDate = this.dateRange[1];
        }
        if(this.radioResponseType=='check') {
          // if (this.unit == null || this.unit == '') {
          //   params.targetDeptId = this.$tool.getStorage('LOGIN_USER').companyId;
          // } else {
          //   params.targetDeptId = this.unit;
          // }
          params.targetDeptId = this.$tool.getStorage('LOGIN_USER').companyId;
          params.type=1;
        }else{
          params.publicCompanyId = this.$tool.getStorage('LOGIN_USER').companyId;//只能看当前公司发布的隐患
          // params.type=this.check;
          params.targetDeptId=this.unit;
        }
        params.name = this.searchInput;
        params.label = {labels: this.dynamicTags};
        this.$http.post('danger/inspectPublic/find', params).then(function (res) {
          if (res.data.success) {
            this.tableData = res.data.data.list;
            this.totalItem = res.data.data.total;
            if (this.tableData) {
              for (let i = 0; i < this.tableData.length; i++) {
                this.tableData[i].num = (this.currentPage - 1) * 10 + i + 1;
                this.tableData[i].inspectDate = this.transferTime(this.tableData[i].inspectDate);
                this.tableData[i].statusTagType = this.statusOptions[this.tableData[i].status].type;
                this.tableData[i].statusTag = this.statusOptions[this.tableData[i].status].label;
              }
            } else {
              this.tableData = [];
            }
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
        });
      },

      //----------------------------------检查表的查看，修改，记录和删除---------------------------------------
      itemViewClick: function (row) {
        this.$router.push({name: 'dangerInspectDetail', params: {id: row.id, name: row.name}})
      },
      // 台账下载

      itemDownloadClick:function (row,needChange) {

        const loading = this.$loading({
          lock: true,
          text: 'Loading',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        this.$http({ // 用axios发送post请求
          method: 'get',
          url: '/report/dangerInspectPublicDetailExcel/' + row.id+'/'+needChange, // 请求地址
          responseType: 'blob' // 表明返回服务器返回的数据类型
        }).then((res) => { // 处理返回的文件流
          //console.info(res)
          loading.close()
        const content = res
        const elink = document.createElement('a') // 创建a标签
        elink.download = row.name + ".xlsx" // 文件名
        elink.style.display = 'none'
        const blob = new Blob([res.data])
        elink.href = URL.createObjectURL(blob)
        document.body.appendChild(elink)
        elink.click() // 触发点击a标签事件
        document.body.removeChild(elink)
      })
      },
      // 预览
      itemPrintClick:function(row,needChange){
        this.loginLoading=this.$loading({
          lock: true,
          text: '数据加载中',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.5)'
        });
        this.$store.dispatch('reportDangerInspectPublicDetailHtml', {
          id : row.id,needChange: needChange
        }).then(function(res){
          this.loginLoading.close();//关闭登陆加载
          if(res.success){
            let myWindow=window.open("".href, "_blank");
            myWindow.document.write(res.data);
            window.clearTimeout(this.timer);
            this.timer = window.setTimeout(function(){
              myWindow.print();
            }, 300)
//                    myWindow.print()
          } else {
            this.$message({
              type : 'error',
              message : res.message || '预览失败！！'
            })
          }
        }.bind(this))
      },
      viewDangerPlan: function () {
        this.$router.push({name: 'dangerPlanFormAccount', params: {year: this.year}})
      },
      viewDangerInspectSummary: function () {
        this.$router.push({name: 'dangerInspectSummary', params: {year: this.year}})
      },
      viewDangerReformSummary : function (){
//        console.log(this.dateRange);
//        return;
        let startDate, endDate;

        if(this.dateRange && this.dateRange.length == 2){
          startDate = this.dateRange[0];
          endDate = this.dateRange[1];
        }
        this.$router.push({name: 'dangerReformSummary', params: {
          year: this.year,
          startDate : startDate,
          endDate : endDate
        }})
      },


      //-----------------------------------筛选标签-----------------------------------------------
      remoteTag: function (val) {
        let params = new URLSearchParams;
        if (val !== null && val !== '') {
          this.search.tagLoading = true
          params.append("label", val);
          params.append("pageSize", 20);
          params.append("pageCurrent", 1);
          this.$http.post('label/find', params).then(function (res) {
            if (res.data.success) {
              this.editTag(res.data.data.list);
            } else {
              this.tableTagOption = [];
            }
            this.search.tagLoading = false
          }.bind(this)).catch(function (err) {
            console.log(err);
          });
        } else {
          this.$store.dispatch("getTableLabels", this.dynamicTags)
        }
      },
      //选择标签
      labelSelectChange: function (label) {
        this.search.searchTag = label
        if (this.dynamicTags.length < 5) {
          if (label == '') {
            this.inputVisible = false
          } else {
            var index = this.dynamicTags.indexOf(label)
            if (index == -1) {
              this.dynamicTags.splice(this.dynamicTags.length, 0, label)
              this.search.searchTag = ''
              this.inputVisible = false
            } else {
              this.search.searchTag = ''
              this.inputVisible = false
            }
          }
        } else {
          this.$message.warning('标签数量已达上限');
          this.search.searchTag = ''
          this.inputVisible = false
        }
      },
      //关闭标签
      handleClose(tag) {
        this.dynamicTags.splice(this.dynamicTags.indexOf(tag), 1);
      },
      loadInspectPublicFiles:function (row) {
        var params=new URLSearchParams();
        params.append("dangerInspectPublicId",row.id)
        this.upload.params.contentId=row.id
        this.$http.post("/danger/reformReply/find",params).then(function (res) {
          if(res.data.success){
            this.reformReplyData=res.data.data
            this.inspectPlanListDialogVisible=true
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
        })
      },
      viewReformReply:function (row) {
        this.inspectPlanListDialogVisible=false
          this.$router.push({name:'dangerReformReply',params:{reformReply:row}})
      },
      //选择受检单位
      handlePickCompany:function () {
        this.unit=this.targetCompanyArray[this.targetCompanyArray.length-1];
      }
    }
  }
</script>
<style>
  .el-tag + .el-tag {
    margin-left: 10px;
  }

  .button-new-tag {
    margin-left: 10px;
    height: 32px;
    line-height: 30px;
    padding-top: 0;
    padding-bottom: 0;
  }

  .input-new-tag {
    width: 90px;
    margin-left: 10px;
    vertical-align: bottom;
  }
</style>
