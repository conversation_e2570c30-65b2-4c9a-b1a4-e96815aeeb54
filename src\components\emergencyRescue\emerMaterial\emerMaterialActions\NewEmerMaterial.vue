<template>
  <div id="newEmerMaterial">
    <div class="background-style">
      <el-col :span="16" :offset="4" class="primary-background-title">
        {{dialogTitle}}
      </el-col>
      <el-col :span="16" :offset="4">
        <el-form :model="form" :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm">
            <el-col :span="24">
            <el-col :span="8">
              <el-form-item label="物资类型：" prop="type">
                <el-select
                  v-model="form.type" :disabled="goodsTypeDisable"
                  filterable
                  remote
                  reserve-keyword
                  clearable
                  placeholder="请输入物资类型后选择"
                  :remote-method="findMaterialType"
                  style="width: 100%">
                  <el-option
                    v-for="item in typeOptions"
                    :key="item.name"
                    :label="item.name"
                    :value="item.name">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="类型管理：">
                <el-button type="primary" plain @click="materialTypeClick">物资类型管理</el-button>
              </el-form-item>
            </el-col>
          </el-col>
          <el-col :span="24">
            <el-col :span="8">
              <el-form-item label="数量：" prop="num">
                <el-input v-model="form.count" :disabled="goodsCountDisable" type="number"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="存放地点："  prop="place">
                <el-autocomplete
                  v-model="form.place"
                  :fetch-suggestions="loadDeptWarehouse"
                  @select="getLoadDeptWarehouse"
                  placeholder="请输入存放地点后并选择">
                  <template slot-scope="{ item }">
                    <div>{{ item.name }}</div>
                  </template>
                </el-autocomplete>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="地点管理：">
                <el-button type="primary" plain @click="materialPlaceClick">存放地点管理</el-button>
              </el-form-item>
            </el-col>
          </el-col>
          <el-col :span="24">
            <el-col :span="8">
              <el-form-item label="管理员：" prop="manager">
                <el-input v-model="form.manager"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="联系方式：" prop="contactInfo">
                <el-input v-model="form.contactInfo"></el-input>
              </el-form-item>
            </el-col>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注：" prop="other">
              <el-input type="textarea" :autosize="{ minRows: 3}" v-model="form.other"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-button style="float: right;margin-left: 20px" @click="returnClick()">返回</el-button>
            <el-button type="primary" style="float: right" @click="addOrUpdate()">保存</el-button>
          </el-col>
        </el-form>
      </el-col>
    </div>

    <!--管理物资类型对话框开始-->
    <el-dialog title="物资类型管理" :visible.sync="materialDiagFlag">
      <el-dialog
        title="添加"
        :visible.sync="addMaterialVisible"
        append-to-body>
        <el-form ref="materialForm" :model="materialForm" label-width="100px">
          <el-form-item label="物资类型">
            <el-input v-model="materialForm.newMaterialStr"></el-input>
          </el-form-item>
          <el-form-item label="物资单位">
            <el-select v-model="materialForm.newMaterialUnit" placeholder="请选择或者自定义" style="width: 100%" filterable allow-create>
              <el-option
                v-for="item in unitOptions"
                :key="item"
                :label="item"
                :value="item">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="addMaterialClick">确定</el-button>
          <el-button @click="addMaterialVisible=false">返回</el-button>
        </div>
      </el-dialog>
      <el-col :span="6">
        <el-input clearable placeholder="物资类型名称" v-model="materialDiagName"></el-input>
      </el-col>
      <el-col :offset="1" :span="1">
        <el-button type="primary" @click="materialTypeClick">搜索</el-button>
      </el-col>
      <el-col :offset="1" :span="1">
<!--        <el-button v-if="powerBtns.includes('addGoodsTypeBtn')" type="primary" style="margin-left: 20px" @click="addMaterialVisible=true">添加类型</el-button>-->
        <el-button  type="primary" style="margin-left: 20px" @click="addMaterialVisible=true">添加类型</el-button>
      </el-col>
       <el-table
        :data="materialList"
        border
        style="width: 100%;margin-top: 10px">
        <el-table-column
          type="index"
          width="45"
          label-class-name="inner-header-style">
        </el-table-column>
        <el-table-column
          prop="name"
          label="物资类型"
          label-class-name="inner-header-style"
          min-width="120">
        </el-table-column>
        <el-table-column
          prop="unit"
          label="单位"
          label-class-name="inner-header-style"
          width="120">
        </el-table-column>
        <el-table-column
          label="操作"
          label-class-name="inner-header-style"
          width="100"
          v-if="powerBtns.includes('delGoodsTypeBtn')" >
          <template slot-scope="scope">
            <el-button  size="mini" type="danger" @click="materialItemDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div style="margin-top: 10px">
        <el-pagination
          background
          layout="prev, pager, next"
          :current-page="currentMaterialPage"
          :page-size="materialPageSize"
          :total="totalMaterialItem"
          @current-change="currentMaterialPageClick">
        </el-pagination>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="materialDiagFlag=false">返回</el-button>
      </div>
    </el-dialog>
    <!--管理物资类型对话框结束-->

    <!--管理存放地点对话框开始-->
    <el-dialog title="存放地点管理" :visible.sync="materialPlaceDiagFlag">
      <el-dialog
        title="添加"
        :visible.sync="addMaterialPlaceVisible"
        append-to-body>
        <el-form ref="materialForm" :model="materialPlaceForm" label-width="100px">
          <el-form-item label="存放地点">
            <el-input v-model="materialPlaceForm.name"></el-input>
          </el-form-item>
          <el-form-item label="管理员">
            <el-select
              v-model="materialPlaceForm.manageUser.username"
              @change="userChange"certificate-training-add
              filterable
              remote
              reserve-keyword
              placeholder="请输入姓名"
              :remote-method="remoteMethod">
              <el-option
                v-for="item in assist.staffList"
                :key="item.userId"
                :label="item.username"
                :value="item">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="联系方式">
            <el-input v-model="materialPlaceForm.manageUser.mobile"></el-input>
          </el-form-item>
          <el-form-item label="所属部门">
            <el-input v-model="materialPlaceForm.deptName"></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="addMaterialPlaceClick">确定</el-button>
          <el-button @click="addMaterialPlaceVisible=false">返回</el-button>
        </div>
      </el-dialog>
      <el-button type="primary" style="margin-left: 20px" @click="addMaterialPlaceVisible=true">添加存放地点</el-button>
      <el-table
        :data="materialPlaceList"
        border
        style="width: 100%;margin-top: 10px">
        <el-table-column
          type="index"
          width="45"
          label-class-name="inner-header-style">
        </el-table-column>
        <el-table-column
          prop="name"
          label="存放地点"
          label-class-name="inner-header-style"
          min-width="120">
        </el-table-column>
        <el-table-column
          prop="manageUser.username"
          label="管理员"
          label-class-name="inner-header-style"
          width="120">
        </el-table-column>
        <el-table-column
          prop="manageUser.mobile"
          label="联系方式"
          label-class-name="inner-header-style"
          width="120">
        </el-table-column>

        <el-table-column
          fixed="right"
          label="操作"
          align="center"
          label-class-name="inner-header-style"
          width="200">
          <template  slot-scope="scope">
            <el-button size="mini" type="primary" @click="materialItemPlaceDelete(scope.row)">修改</el-button>
            <el-button size="mini" type="danger" @click="materialItemPlaceDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div style="margin-top: 10px">
        <el-pagination
          background
          layout="prev, pager, next"
          :current-page="currentMaterialPlacePage"
          :page-size="materialPlacePageSize"
          :total="totalMaterialPlaceItem"
          @current-change="currentMaterialPlacePageClick">
        </el-pagination>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="materialPlaceDiagFlag=false">返回</el-button>
      </div>
    </el-dialog>
    <!--管理存放地点对话框结束-->

    <el-dialog title="存放地点管理" :visible.sync="materialPlaceDiagFlag">
      <el-dialog
        title="修改"
        :visible.sync="updateMaterialPlaceVisible"
        append-to-body>
        <el-form ref="materialForm" :model="materialPlaceForm" label-width="100px">
          <el-form-item label="存放地点">
            <el-input v-model="materialPlaceForm.name"></el-input>
          </el-form-item>
          <el-form-item label="管理员">
            <el-select
              v-model="materialPlaceForm.manageUser.username"
              @change="userChange"certificate-training-add
              filterable
              remote
              reserve-keyword
              placeholder="请输入姓名"
              :remote-method="remoteMethod">
              <el-option
                v-for="item in assist.staffList"
                :key="item.userId"
                :label="item.username"
                :value="item">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="联系方式">
            <el-input v-model="materialPlaceForm.manageUser.mobile"></el-input>
          </el-form-item>
        <!--  <el-form-item label="所属部门">
            <el-input v-model="materialPlaceForm.deptName"></el-input>
          </el-form-item>-->
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="addMaterialPlaceClick">确定</el-button>
          <el-button @click="updateMaterialPlaceClickCancel()" >返回</el-button>
        </div>
      </el-dialog>
      <el-button type="primary" style="margin-left: 20px" @click="addMaterialPlaceVisible=true">添加存放地点</el-button>
      <el-table
        :data="materialPlaceList"
        border
        style="width: 100%;margin-top: 10px">
        <el-table-column
          type="index"
          width="45"
          label-class-name="inner-header-style">
        </el-table-column>
        <el-table-column
          prop="name"
          label="存放地点"
          label-class-name="inner-header-style"
          min-width="120">
        </el-table-column>
        <el-table-column
          prop="manageUser.username"
          label="管理员"
          label-class-name="inner-header-style"
          width="120">
        </el-table-column>
        <el-table-column
          prop="manageUser.mobile"
          label="联系方式"
          label-class-name="inner-header-style"
          width="120">
        </el-table-column>

        <el-table-column
          fixed="right"
          label="操作"
          align="center"
          label-class-name="inner-header-style"
          width="200">
          <template slot-scope="scope">
            <el-button size="mini" type="primary"  @click="materialItemPlaceUpdate(scope.row)" >修改</el-button>
            <el-button size="mini" type="danger" @click="materialItemPlaceDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div style="margin-top: 10px">
        <el-pagination
          background
          layout="prev, pager, next"
          :current-page="currentMaterialPlacePage"
          :page-size="materialPlacePageSize"
          :total="totalMaterialPlaceItem"
          @current-change="currentMaterialPlacePageClick">
        </el-pagination>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="materialPlaceDiagFlag=false">返回</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
  export default {
    name: 'newEmerMaterial',
    data() {
      return {
        form: {
          department: '',
          place: '',
          placeId:'',
          name: '',
          type: '',
          count: 0,
          deptId: '',
          unit: '',
          manager: '',
          userId: 0,
          contactInfo: '',
          other: ''
        },
        rules: {
          place: [{required: true, message: '请选择存放地点', trigger: 'blur'}],
          name: [{required: true, message: '请选择输入物品名称', trigger: 'blur'}],
          type: [{required: true, message: '请选择选择物资类型', trigger: 'blur'}],
          count: [{required: true, message: '请选择输入物资数量', trigger: 'blur'}],
          manager: [{required: true, message: '请选择输入管理员姓名', trigger: 'blur'}],
          contactInfo: [{required: true, message: '请选择输入联系方式', trigger: 'blur'}]
        },
        departmentOptions: [],
        deptOptionProps: {
          value: 'id',
          label: 'name',
          children: 'subDept'
        },
        placeOptions: [],
        typeOptions: [],
        edit:false,
        dialogTitle:'新增物资',
        goodsTypeDisable:false,
        goodsCountDisable:false,
        emgGoodsId:0,

        //物资类型管理
        materialDiagFlag:false,
        materialDiagPageSize:10,
        materialDiagName:'',
        materialPlaceDiagFlag:false,
        addMaterialVisible:false,
        addMaterialPlaceVisible:false,
        updateMaterialPlaceVisible:false,
        materialForm:{
          newMaterialStr:'',
          newMaterialUnit:'',
        },
        materialPlaceForm:{
          id:0,
          manageUserId:'',
          name:'',
          deptId:0,
          deptName:'',
          manageUser:{
            deptId:0,
            userId:0,
            username:'',
            mobile:'',
            deptName:'',
          }
        },
        assist : {
          // 员工数组
          staffList: [],
        },
        unitOptions:['个','套','包','双'],
        materialList:[],
        materialPlaceList:[],
        currentMaterialPage:0,
        currentMaterialPlacePage:0,
        materialPlacePageSize:0,
        materialPageSize:0,
        totalMaterialItem:0,
        totalMaterialPlaceItem:0,
        powerBtns : [],
      }
    },
    mounted: function () {
      this.loadDeptWarehouse()
      this.initRouteData()
      // this.findMaterialType()
    },
    watch:{
      $route(to, from){
        if(from.name==='emerMaterial'&&this.$route.name==='newEmerMaterial'){
          this.loadDeptWarehouse()
          this.initRouteData()
        }
      }
    }
    ,
    methods: {
      returnClick: function () {
        this.$refs['ruleForm'].resetFields();
        this.$router.go(-1);
      },
      addOrUpdate:function(){
        if(this.edit){
          this.updateClick()
        }else{
          this.saveClick()
        }
      },
      saveClick: function () {
        this.$refs['ruleForm'].validate((valid) => {
          if (valid) {
            console.info(this.form)
            var params=new URLSearchParams()
            params.append("count",this.form.count)
            params.append("goodsName",this.form.type)
            params.append("warehouseId",this.form.placeId)
            params.append("remarks",this.form.other)
            params.append("deptId",this.form.deptId)
            this.$http.post('emgGoods/addOrUpdate',params).then(function (res) {
              if(res.data.success){
                this.$message.success("创建成功")
                this.$refs['ruleForm'].resetFields();
                this.$router.go(-1);
              }else{
                this.$message.error(res.data.message)
              }
            }.bind(this)).catch(function (err) {
              console.info(err)
            }.bind(this))
          } else {
            console.log('error submit!!');
            return false;
          }
        });
      },
      clear(){
        this.form = this.$tool.clearObj({},this.form);
        this.emgGoodsId = this.$route.params.emgGoodsId;
      },
      updateClick:function(){
        this.$refs['ruleForm'].validate((valid) => {
          if (valid) {
            console.info(this.form)
            var params=new URLSearchParams()
            params.append("count",this.form.count)
            params.append("warehouseId",this.form.placeId)
            params.append("remarks",this.form.other)
            params.append("id",this.emgGoodsId)
            params.append("deptId",this.form.deptId)
            this.$http.post('emgGoods/addOrUpdate',params).then(function (res) {
              if(res.data.success){
                this.$message.success("创建成功")
                this.$refs['ruleForm'].resetFields();
                this.$router.go(-1);
              }else{
                this.$message.error(res.data.message)
              }
            }.bind(this)).catch(function (err) {
              console.info(err)
            }.bind(this))
          } else {
            console.log('error submit!!');
            return false;
          }
        });
      },
      loadDeptWarehouse: function (str,cb) {
        if(str == undefined){
          str = '';
        }
        this.$http.post('emgWareHouse/find?name='+str).then(function (res) {
          if (res.data.success) {
            console.info(res.data)
            let list = res.data.data.list;
            cb(list);
            /* if(this.placeOptions.length>0&&this.edit==false){


             this.form.place=this.placeOptions[0].name;
              // this.loadWarehouseInfo(this.form.place)
              var managerUser = res.data.data.list[0].manageUser;
              if (managerUser != null) {
                this.form.manager = managerUser.username
                this.form.userId = managerUser.id
                this.form.contactInfo = managerUser.mobile
              }
            }*/
          } else {

          }
        }.bind(this)).catch(function (err) {
          console.log(err)
        })
      },
  /*    loadWarehouseInfo: function (whId) {
        console.info(whId)
        this.$http.get('emgWareHouse/find?id=' + whId).then(function (res) {
          if (res.data.success) {
            var managerUser = res.data.data.list[0].manageUser;
            if (managerUser != null) {
              this.form.manager = managerUser.username
              this.form.userId = managerUser.id
              this.form.contactInfo = managerUser.mobile
            }
          } else {

          }
        }.bind(this)).catch(function (err) {
          console.log(err)
        })
      },*/
      initEditData:function(){
        this.form.placeId=this.$route.params.warehouseId
        this.form.place=this.$route.params.warehouseName
        this.form.type=this.$route.params.goodsName
        this.form.contactInfo=this.$route.params.contact
        this.form.manager=this.$route.params.manageUser
        this.form.other=this.$route.params.remark
        this.form.count=this.$route.params.count
        this.emgGoodsId=this.$route.params.emgGoodsId
      },
      initRouteData:function(){
        let url = '/emer-menu/emer-material';
        this.powerBtns = this.$tool.getPowerBtns2URL('emerMenu','/emer-menu/emer-material', url);
        this.edit=this.$route.params.edit
        if(this.edit){
          this.dialogTitle='编辑物资'
          this.goodsTypeDisable=true
          this.goodsCountDisable=true
          this.initEditData()
        }else{
          this.clear();
          this.dialogTitle='新增物资'
          this.goodsTypeDisable=false
          this.goodsCountDisable=false
        }
      },

      //----------------------------------------物资类型管理------------------------------
      findMaterialType:function (val) {
        let params=new URLSearchParams;
        if(val != null && val != ''){
          params.append("name",val);
        }
        this.$http.post('emgGoodsType/findAll',params).then(function (res) {
          if (res.data.success) {
            this.typeOptions = res.data.data
          } else {
            this.$message.error("请求失败")
          }
        }.bind(this)).catch(function (err) {
          console.log(err)
          this.$message.error("发生错误")
        }.bind(this))
      },
      materialTypeClick:function () {
        let params=new URLSearchParams;
        params.append("pageCurrent",1);
        this.currentMaterialPage=1;
        params.append("pageSize",this.materialDiagPageSize);
        params.append("name",this.materialDiagName);
        this.findMaterialSendRequest(params);
        this.materialDiagFlag=false;
      },
      currentMaterialPageClick:function (val) {
        let params=new URLSearchParams;
        params.append("pageCurrent",val);
        params.append("pageSize",this.materialDiagPageSize);
        params.append("name",this.materialDiagName);
        this.currentMaterialPage=val;
        this.findMaterialSendRequest(params);
      },
      materialPlaceClick:function () {
        let params=new URLSearchParams;
        params.append("pageCurrent",1);
        this.currentMaterialPlacePage=1;
        this.findMaterialPlaceSendRequest(params);
        this.materialPlaceDiagFlag=false;
      },
      currentMaterialPlacePageClick:function (val) {
        let params=new URLSearchParams;
        params.append("pageCurrent",val);
        this.currentMaterialPlacePage=val;
        this.findMaterialPlaceSendRequest(params);
      },
      findMaterialSendRequest:function (params) {
        this.$http.post('emgGoodsType/find',params).then(function (res) {
          if (res.data.success) {
            this.totalMaterialItem=res.data.data.total;
            this.materialPageSize=res.data.data.pageSize;
            this.materialList = res.data.data.list;
            this.materialDiagFlag=true;
          } else {
            this.$message.error("请求失败")
          }
        }.bind(this)).catch(function (err) {
          console.log(err)
          this.$message.error("发生错误")
        }.bind(this))
      },
      findMaterialPlaceSendRequest:function (params) {
        this.$http.post('emgWareHouse/find',params).then(function (res) {
          if (res.data.success) {
            this.totalMaterialPlaceItem=res.data.data.total;
            this.materialPlacePageSize=res.data.data.pageSize;
            this.materialPlaceList = res.data.data.list;
            this.materialPlaceDiagFlag=true;
          } else {
            this.$message.error("请求失败")
          }
        }.bind(this)).catch(function (err) {
          console.log(err)
          this.$message.error("发生错误")
        }.bind(this))
      },
      addMaterialClick:function () {
        if(this.materialForm.newMaterialStr&&this.materialForm.newMaterialUnit){
          let params=new URLSearchParams;
          params.append("name",this.materialForm.newMaterialStr);
          params.append("unit",this.materialForm.newMaterialUnit);
          this.$http.post('emgGoodsType/addOrUpdate',params).then(function (res) {
            if (res.data.success) {
              this.$message.success("添加成功！");
              this.materialTypeClick();
              this.findMaterialType();
              this.addMaterialVisible=false;
            } else {
              this.$message.error(res.data.message);
            }
          }.bind(this)).catch(function (err) {
            console.log(err)
            this.$message.error("发生错误")
          }.bind(this))
        }else{
          this.$message.warning("类型或者单位不能为空");
        }
      },

      addMaterialPlaceClick:function () {
        if(this.materialPlaceForm.manageUserId && this.materialPlaceForm.name){
          let params=new URLSearchParams;
          params.append("name",this.materialPlaceForm.name);
          params.append("manageUserId",this.materialPlaceForm.manageUserId);
          params.append("deptId",this.materialPlaceForm.deptId);
          if(this.materialPlaceForm.id){
            params.append("id",this.materialPlaceForm.id);
          }
          this.$http.post('emgWareHouse/addOrUpdate',params).then(function (res) {
            if (res.data.success) {
              this.$message.success("添加成功！");
              this.materialPlaceClick();
              this.addMaterialPlaceVisible=false;
              this.updateMaterialPlaceVisible=false;
              this.materialPlaceForm = this.$tool.clearObj({}, this.materialPlaceForm);
            } else {
              this.$message.error(res.data.message);
            }
          }.bind(this)).catch(function (err) {
            console.log(err)
            this.$message.error("发生错误")
          }.bind(this))
        }else{
          this.$message.warning("存放地点或者管理员不能为空");
        }
      },
      materialItemDelete:function (row) {
        this.$confirm('此操作将永久删除该物资类型, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let params=new URLSearchParams;
          params.append("id",row.id);
          this.$http.post('emgGoodsType/delete',params).then(function (res) {
            if (res.data.success) {
              this.materialTypeClick();
              this.findMaterialType();
              this.$message.success("删除成功！")
            } else {
              this.$message.error("请求失败")
            }
          }.bind(this)).catch(function (err) {
            console.log(err)
            this.$message.error("发生错误")
          }.bind(this))
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });
        });
      },
      materialItemPlaceDelete:function (row) {
        this.$confirm('此操作将永久删除该存放地点,并删除相关物资信息 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let params=new URLSearchParams;
          params.append("id",row.id);
          this.$http.post('emgWareHouse/delete',params).then(function (res) {
            if (res.data.success) {
              this.materialPlaceClick();
              this.$message.success("删除成功！")
            } else {
              this.$message.warning(res.data.message)
            }
          }.bind(this)).catch(function (err) {
            console.log(err)
            this.$message.error("发生错误")
          }.bind(this))
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });
        });
      },
      materialItemPlaceUpdate:function (row) {
        console.log(row);
        this.updateMaterialPlaceVisible=true;
        this.materialPlaceForm.name = row.name;
        this.materialPlaceForm.manageUser.username = row.manageUser.username;
        this.materialPlaceForm.manageUserId = row.manageUser.userId;
        this.materialPlaceForm.deptId =row.deptId;
        this.materialPlaceForm.id = row.id;
        this.materialPlaceForm.manageUser.mobile = row.manageUser.mobile;
      },
      updateMaterialPlaceClickCancel(){
        this.updateMaterialPlaceVisible=false;
        this.materialPlaceForm.name = '';
        this.materialPlaceForm.id = 0;
        this.materialPlaceForm.manageUser.mobile = '';
        // this.materialPlaceForm.manageUserId = 0;
        // this.materialPlaceForm.deptId =0;
        this.materialPlaceForm.manageUser.username = '';
        // this.materialPlaceForm = this.$tool.clearObj({},this.form);
        // this.$refs['materialPlaceForm'].resetFields();
        // this.materialPlaceForm = this.$tool.clearObj({}, this.materialPlaceForm);
      },
      // 模糊证书选择
      getLoadDeptWarehouse(item){
        this.form.place = item.name;
        this.form.placeId = item.id;
        this.form.deptId = item.deptId;
        var managerUser = item.manageUser;
        if (managerUser != null) {
          this.form.manager = managerUser.username
          this.form.userId = managerUser.id
          this.form.contactInfo = managerUser.mobile
        }
        // this.form.certificateTypeId = item.id;
      },
      // 用户选择
      userChange(val){
        this.$tool.cloneObj(this.materialPlaceForm.manageUser, val);
        this.materialPlaceForm.manageUserId = this.materialPlaceForm.manageUser.userId;
        this.materialPlaceForm.mobile = this.materialPlaceForm.manageUser.mobile;
        this.materialPlaceForm.deptName = this.materialPlaceForm.manageUser.deptName;
        this.materialPlaceForm.deptId = this.materialPlaceForm.manageUser.deptId;
      },
      // 员工模糊搜索
      remoteMethod(query) {
        let params = new URLSearchParams();
        if (query !== '') {
          params.append('username', query)
          setTimeout(function(){
            this.$store.dispatch('userFind', params).then(function(res){
              if(res.success){
                this.assist.staffList = res.data.list;
              } else {
                this.$message({
                  type : 'error',
                  message : res.message || '错误'
                })
              }
            }.bind(this));
          }.bind(this), 200);
        } else {
          this.assist.staffList = [];
        }
      },
      addWarehouseInfo(){
        let name = this.form.place;
        if(!name) return;
        let params = {
          certName : name
        }
        this.$confirm(`是否要将“${name}”添加到知识库中?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(function(){
          this.$store.dispatch('eduCertTypeAddOrUpdate', params).then(function(res){
            if(res.success) {
              this.form.certificateTypeId  = res.data.id;
              this.$message({
                type: 'success',
                message: '操作成功'
              })
            }  else {
              this.$message({
                type : 'error',
                message : res.message || '错误'
              })
            }
          }.bind(this));
        }.bind(this))
      },
    }
  }
</script>
<style>
</style>
