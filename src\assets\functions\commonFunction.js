export default {
  findSecondMenuButtonList(arr,url_1,url_2){
    let firstMenuItem=arr.find(function (item) {
      return item.url===url_1;
    });
    if(firstMenuItem>=0){
      let secondMenuItem=firstMenuItem.list.find(function (item) {
        return item.url===url_2;
      });
      if(secondMenuItem>=0){
        return secondMenuItem.list.map(function (item) {
          return item.name;
        });
      }
      return [];
    }
    return [];
 },

}
