<template>
  <div id="uploadPicture">
    <el-upload
      ref="uploadPic"
      :action="uploadBaseUrl"
      multiple
      list-type="picture-card"
      :with-credentials="cookies"
      :http-request="ossUploadRequest"
      :data="fileUploadParams"
      :file-list="picFileList"
      :before-upload="beforeUpload"
      :before-remove="beforeDelete"
      :on-remove="fileDelete"
      :on-preview="filePreview">
      <i class="el-icon-plus"></i>
    </el-upload>
    <el-dialog :visible.sync="dialogVisible" append-to-body width="70%">
      <img width="100%" :src="dialogImageUrl" alt="">
    </el-dialog>
  </div>
</template>
<script>
  import dealData from '../../../assets/functions/dealData'
  export default {
    name: 'uploadPicture',
    props:['data','picFileList'],
    data() {
      return {
        //上传组件信息
        cookies: true,
        fileUploadParams: {
          contentId: 0,
          contentType: 0
        },
        uploadBaseUrl:'',
        //预览图片信息
        dialogVisible:false,
        dialogImageUrl:'',
      }
    },
    methods:{
      beforeUpload:function (file) {
        this.fileUploadParams.contentId=this.data.contentId;
        this.fileUploadParams.contentType=this.data.contentType;
      },
      beforeDelete:function (file) {
        return this.$confirm(`确定移除 ${ file.name }？`);
      },
      fileDelete:function (file) {
        let params = new URLSearchParams();
        params.append("fId", file.fId);
        this.$http.post("file/delete", params).then(function (res) {
          if (res.data.success) {
            this.$message({
              showClose: true,
              message: '文件已删除',
              type: 'success'
            });
          }
        }.bind(this))
      },
      filePreview:function (file) {//图片就只有预览没有下载，要下载自己另存为
        this.dialogImageUrl= file.oriUrl;
        this.dialogVisible=true;
      },

      //直接上传到阿里云上
      ossUploadRequest:function (item) {
        //获取该文件对应的sign
        this.$http.get('sys/oss/sign?contentId='+this.data.contentId+'&contentType='+this.data.contentType+'&realName='+item.file.name).then(function (res) {
          if(res.data){
            let params=new FormData();
            params.append("name",item.file.name);
            params.append("key",res.data.dir + item.file.name);
            params.append("policy",res.data.policy);
            params.append("OSSAccessKeyId",res.data.accessid);
            params.append('success_action_status','200');
            params.append("callback",res.data.callback);
            params.append("signature",res.data.signature);
            params.append("file",item.file);
            this.fileHttp.post('',params,{headers: {'Content-Type': 'multipart/form-data'}}).then(function (res) {
              if(res.data.file) {
                let resultStr=dealData.decode(res.data.file);
                let resultJson=JSON.parse(resultStr);
                resultJson.url=this.fileHttp.defaults.baseURL+resultJson.path+'?x-oss-process=image/resize,m_fixed,h_70,w_70';
                resultJson.oriUrl=this.fileHttp.defaults.baseURL+resultJson.path;
                this.picFileList.push(resultJson);
                this.$message.success('上传成功');
              }else {
                this.$message.error('上传图片失败');
              }

            }.bind(this))

          }
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message.error('获取唯一标识失败');
        }.bind(this));
      },
    }
  }
</script>
<style>
</style>
