<template>
  <div id="viewRelieve">
    <div class="background-style">
      <el-col :span="16" :offset="4" style="text-align: center;margin-bottom: 20px;margin-top:30px;letter-spacing: 2px;height: 50px;line-height: 50px">
        <h2>{{title}}</h2>
      </el-col>
      <el-col :span="16" :offset="4">
        <el-col :span="24" style="margin-top: 20px">
          <vue-editor v-model="content"></vue-editor>
        </el-col>
        <el-col :span="24" style="margin-top: 20px" v-if="isLeader">
          <el-form :model="form" ref="singleForm" label-width="100px" class="demo-ruleForm">
            <el-form-item label="审核意见：" prop="examine">
              <el-dropdown @command="editExamine">
                <el-button type="primary" size="small">
                  审核参考<i class="el-icon-arrow-down el-icon--right"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item v-for="item in selectOptions" :key="item.id" :command="item.content">{{item.name}}</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
              <el-input type="textarea" :autosize="{ minRows: 3}" v-model="form.examine"></el-input>
            </el-form-item>
            <el-form-item label="查看应急：" >
              <el-button type="success" size="small" @click="viewResponseProcess">查看应急响应</el-button>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="24" style="margin-top: 20px">
          <div style="float: right">
            <el-button type="primary" v-show="isLeader" @click="releaseRelieve">签发</el-button>
            <el-button type="danger" v-show="isLeader" @click="returnRelieve">退回</el-button>
            <el-button @click="$router.push({name:'emerResponseWorkflow'})">返回</el-button>
          </div>
        </el-col>
      </el-col>
    </div>
    <search-people-dialog @determineClick="selectPersonClick" :data="selectPersonData" :defaultPersonId="selectPersonId"></search-people-dialog>
  </div>
</template>
<script>
  import SearchPeopleDialog from '../../../common/smallComponent/searchSinglePeople.vue'
  import { VueEditor } from 'vue2-editor'
  export default {
    name: 'viewRelieve',
    data() {
      return {
        title:'',
        content:'',
        form:{examine:''},
        tempResponseData:{},//当前应急响应对象
        //参考审核数据
        selectOptions:[
          {id:'examine01',name:'同意签发',content:'经审核，同意解除该应急响应。'},
          {id:'examine02',name:'退回修改',content:'经审核，不同意解除该应急响应，意见如下：'}
        ],
        isLeader:false,

        currentEmerId:'',//当前应急ID
        taskId:'',//当前任务ID
        doTaskLoading:'',//走流程时的缓冲图

        //------------------选择负责人的对话框-----------------------
        selectPersonData:{title:'请选择总结应急的人',isShow:false,defaultPerson:{value:0,label:''}},
        selectPersonId:0,
      }
    },
    components: {
      'vue-editor':VueEditor,
      'search-people-dialog' : SearchPeopleDialog
    },
    created:function () {
      if(this.$route.params.emerData){
        this.tempResponseData=this.$route.params.emerData;
        this.title='关于解除'+this.$route.params.emerData.name+'的通知';
        if(this.$route.params.emerData.relieveNotice){
          this.content=this.$route.params.emerData.relieveNotice.content;
        }
        this.currentEmerId=this.$route.params.emerData.id;
        this.taskId=this.$route.params.emerData.taskId;
        let status=this.$route.params.emerData.status;
        this.isLeader=false;
        if(status===9){
          this.isLeader=true;
        }
        this.selectPersonData.defaultPerson={value:this.$route.params.emerData.createUserId,label:this.$route.params.emerData.createUserName};
      }
    },
    watch:{
      $route(to, from){
        if((from.name==='emerResponseWorkflow'||from.name==='emergencyProcessWorkflow'||from.name==='taskNotice')&&this.$route.name==='viewRelieveWorkflow'){
          this.clearContent();
          if(this.$route.params.emerData){
            this.tempResponseData=this.$route.params.emerData;
            this.title='关于解除'+this.$route.params.emerData.name+'的通知';
            if(this.$route.params.emerData.relieveNotice){
              this.content=this.$route.params.emerData.relieveNotice.content;
            }
            this.currentEmerId=this.$route.params.emerData.id;
            this.taskId=this.$route.params.emerData.taskId;
            let status=this.$route.params.emerData.status;
            this.isLeader=false;
            if(status===9){
              if(Number(this.$route.params.emerData.operateId)===this.$tool.getStorage('LOGIN_USER').userId){
                this.isLeader=true;
              }
            }
            this.selectPersonData.defaultPerson={value:this.$route.params.emerData.createUserId,label:this.$route.params.emerData.createUserName};
          }
        }
      },
    },
    methods:{
      //填写审核意见
      editExamine:function (content) {
        this.form.examine=content;
      },
      //查看应急响应的记录
      viewResponseProcess:function () {
        this.$router.push({name:'viewEmergencyProcessWorkflow',params:{emerData:this.tempResponseData}});
      },
      //签发
      releaseRelieve:function () {
        this.selectPersonId=this.selectPersonData.defaultPerson.value;
        this.selectPersonData.isShow=true;
      },
      //退回
      returnRelieve:function () {
        this.$confirm('此操作将退回解除, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.doTaskClick(0,0,'解除退回成功');
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消退回'
          });
        });
      },
      selectPersonClick:function (val) {
        if(!val){this.message.warning('负责人默认为应急响应的创建者')}
        this.selectPersonData.isShow=false;
        this.doTaskClick(1,val,'应急解除成功');
      },
      doTaskClick:function (result,operatePerson,str) {
        let doTaskLoading=this.$loading({
          lock: true,
          text: 'Loading',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        let flowParams=new URLSearchParams;
        flowParams.append("taskId",this.taskId);
        flowParams.append("comment",this.form.examine);
        flowParams.append("result",result);
        if(operatePerson){
          flowParams.append("applyUserId",operatePerson);
        }
        this.$http.post('emgFlow/doTask',flowParams).then(function (res) {
          if(res.data.success){
            this.$message({
              showClose: true,
              message: str,
              type: 'success'
            });
            doTaskLoading.close();
            this.$router.push({name:'emerResponseWorkflow'});
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message.error('操作失败')
        }.bind(this));
      },
      clearContent:function () {
        this.title='';
        this.content='';
        this.isLeader=false;
      },
    }
  }
</script>
<style>
</style>
