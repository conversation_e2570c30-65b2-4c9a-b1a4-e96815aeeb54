<template>
  <div class="background-style">
    <el-col :span="24" style="margin-top: 10px">
      <el-row style="margin: 0;padding-left: 10px">
        <el-cascader
          clearable
          placeholder="分类"
          v-model="planTypeListArr"
          :options="planTypeList"
          style="width: 120px">
        </el-cascader>
        <el-select v-model="form.eventLevel" clearable placeholder="级别" style="width: 120px">
          <el-option
            v-for="item in select"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
        <el-select v-model="form.status" clearable placeholder="状态" style="width: 120px">
          <el-option
            v-for="item in status"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
        <el-input v-model="form.eventTitle" clearable placeholder="请输入事件名称" style="width: 200px"></el-input>
        <el-button type="primary" @click="searchBtnClickHandle" icon="el-icon-search">搜索</el-button>
        <el-button
          v-if="!viewRole"
          type="success" @click="addEventBtnHandle" >添加事件</el-button>
        <!--<el-button
          size="small"
          :disabled="selectedEvent && emgEvent && selectedEvent.id == emgEvent.id && emgEvent.reported == 1"
          type="primary"
          @click="importInfoReportBtnClickHandle">重大信息上报</el-button>-->
        <el-button
          v-if="!viewRole"
          type="warning"
          @click="$router.push({ name : 'emerHandleReportLower' })">下级信息上报</el-button>
      </el-row>
      <el-row style="margin:10px 0 0 10px">
        <el-table
          :data="tableData.list"
          highlight-current-row
          @current-change="handleCurrentChange"
          border
          style="width: 100%;margin-bottom: 10px">
          <el-table-column
            label-class-name="header-style"
            prop="status"
            label="状态"
            align="center"
            width="120">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.status === 1" type="success">已完结</el-tag>
              <el-tag type="danger" v-else>进行中</el-tag>
            </template>
          </el-table-column>
          <el-table-column
            label-class-name="header-style"
            prop="eventTitle"
            label="响应名称"
            show-overflow-tooltip
            width="120">
          </el-table-column>
          <el-table-column
            label-class-name="header-style"
            prop="createTime"
            label="创建时间"
            show-overflow-tooltip
            :formatter="formatDateTime"
            width="200">
          </el-table-column>
          <el-table-column
            label-class-name="header-style"
            prop="topTypeName"
            label="一级分类"
            show-overflow-tooltip
            width="120">
          </el-table-column>
          <el-table-column
            label-class-name="header-style"
            prop="typeName"
            label="二级分类"
            show-overflow-tooltip
            width="120">
          </el-table-column>
          <el-table-column
            label-class-name="header-style"
            prop="eventLevel"
            label="级别"
            show-overflow-tooltip
            width="120">
          </el-table-column>
          <el-table-column
            label-class-name="header-style"
            prop="deptName"
            show-overflow-tooltip
            label="发布单位"
            min-width="200">
          </el-table-column>
          <el-table-column fixed="right" label="操作" label-class-name="header-style" align="center" width="250">
            <template slot-scope="scope">
              <el-button size="mini" type="success" @click.native="itemViewClick($event,scope.row)">查看</el-button>
              <el-button :disabled="scope.row.status === 1" size="mini" type="primary" @click="itemUpdateClick($event,scope.row)">修改</el-button>
              <el-button size="mini" type="danger" @click="itemDeleteClick(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          background
          layout="prev, pager, next"
          :current-page="tableData.pageNum"
          :page-size="form.pageSize"
          :total="tableData.total"
          @current-change ="disasterPageChangeHandle">
        </el-pagination>
      </el-row>
    </el-col>
  </div>
</template>
<script>
  import {mapGetters} from 'vuex'
  export default {
    name: 'emerHandle',
    data() {
      return {
        // 搜索框-提交的数据
        form : {
          // 分类
          topTypeId : 0,
          typeId : 0,
          // 级别
          eventLevel : '',
          // 状态
          status : '',
          // 名称
          eventTitle : '',
          // 当前页
          pageCurrent : 1,
          // 页数大小
          pageSize : 10,
        },
        // 当前选中的事件
        selectedEvent : '',
        // 分类选中数组
        planTypeListArr : [],
        // 级别
        select: [
          { value : 1, label : '1级' },
          { value : 2, label : '2级' },
          { value : 3, label : '3级' },
          { value : 4, label : '4级' },
        ],
        // 状态
        status:[
          {value:0, label:'进行中'},
          {value:1, label:'已完结'},
        ],
        // 表格数据
        tableData: [],
        //浏览角色模式
        viewRole : false,
      }
    },
    computed:{
      // 分类列表
      planTypeList : function(){
        return this.$store.state.emerHandleModule.planTypeList
      },
      // 事件
      emgEvent : function(){
        return this.$store.state.emerHandleModule.emgEvent
      },
    },
    mounted(){
      this.init();
    },
    watch:{
      $route(to,from){
        if(this.$route.name==='emerHandle'){
          this.init();
        }
      }
    },
    methods:{

      init(){
        this.viewRole = this.$tool.judgeViewRole();
        // 获取分类列表
        this.$store.dispatch("planTypeListAction");
        this.searchBtnClickHandle();
      },

      // 格式化时间
      formatDateTime(row, column, cellValue){
        return this.$tool.formatDateTime(row.createTime);
      },
      // 上报重大事件按钮点击处理函数
      importInfoReportBtnClickHandle(){
        // 判断是否有选中的
        this.$router.push({ name: 'emerHandleReport'});
      },
      // 添加事件按钮处理函数
      addEventBtnHandle(){
        this.$store.commit('emgEventMutation',{});
        this.$router.push({ name: 'emerHandleAdd', params : { status : 'add' }});
      },
      // 搜索按钮
      searchBtnClickHandle(){
        let params = {};
        if(this.planTypeListArr[0]) this.form.topTypeId = this.planTypeListArr[0];
        if(this.planTypeListArr[1]) this.form.typeId = this.planTypeListArr[1];
        Object.entries(this.form).forEach(function(it){
          // 如果分类、等级、状态、名称有值
          if(it[1]) params[it[0]] = it[1]
        })
        params['status'] = this.form.status;

        this.$store.dispatch('emgEventFindAction', params).then(function(res){
          if(res.success){
            this.tableData = res.data;
          }
        }.bind(this));
      },
      // 分页
      disasterPageChangeHandle(page){
        this.form.pageCurrent = page;
        this.searchBtnClickHandle();
      },
      // 表格--单选
      handleCurrentChange(val){
        if(val){
          this.selectedEvent = val;
          this.$store.commit('emgEventMutation',val);
        }
      },
      // 查看
      itemViewClick(event, row){
        // 阻止冒泡
        event.cancelBubble = true;
        let name = 'emerHandleDealEvent';
        let params = {
          status : 'view',
        }
        // 点击之后，把值传个state
        this.$store.commit('emgEventMutation',row);
        this.$router.push({ name, params })
      },
      // 修改
      itemUpdateClick(event, row){
        // 阻止冒泡
        event.cancelBubble = true;
        let name = 'emerHandleDealEvent';
        let params = {
          status : 'edit',
        }
        // 点击之后，把值传个state
        this.$store.commit('emgEventMutation',row);
        this.$router.push({ name, params })
      },
      // 删除
      itemDeleteClick:function (row) {
        this.$confirm('此操作将永久删除该预案, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(function(){
          // 删除---灾后处置
          this.$store.dispatch('emgEventDeleteAction', {
            id : row.id
          }).then(function(res){
            if(res.success){
              this.$message({
                type : 'success',
                message : '删除成功'
              })
              this.$store.commit('emgEventMutation',{});
              this.searchBtnClickHandle();
            } else {
              this.$message({
                type : 'error',
                message : res.message || '删除失败！！'
              })
            }
          }.bind(this))
        }.bind(this))
      },
    }
  }
</script>
<style>
  .el-container{
    padding:0;
    background : #fff;
  }
  .row{
    margin:10px;
  }
</style>
