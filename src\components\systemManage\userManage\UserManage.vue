<template>
  <div id="userManage" style="height: 100%;width: 100%">

    <div class="background-style">
      <!--搜索区-->
      <div style="height:60px;width: 100%;margin-top: 10px">
        <div id="simpleSearch" style="float: left;width: 100%;height: 100%;background-color: white;">
          <div style="margin-left: 20px;width: 100%">
            <div style="display: inline-block;padding-top: 10px;padding-bottom: 10px;padding-right: 20px;margin: auto">
              <!--<div style="float: left;margin-left: 20px">-->
              <!--搜索条件：-->
              <!--<el-cascader-->
              <!--:options="personOptions"-->
              <!--v-model="classValue"-->
              <!--:show-all-levels="false"-->
              <!--style="width: 100px;">-->
              <!--</el-cascader>-->
              <!--</div>-->
              <div style="float: left;margin-left: 2px">
                <el-input v-model="input" placeholder="请输入人员姓名" style="width: 350px">
                  <template slot="prepend">姓名搜索:</template>
                </el-input>
              </div>
              <div style="float: left;margin-left: 10px">
                <el-button class="search-btn" @click="sendRequest(input.trim(),1)">搜&nbsp;&nbsp;索</el-button>
              </div>
              <div style="float: right;margin-left: 50px">
                <el-button type="primary" @click="addButtonClick">添加用户</el-button>
                <el-button type="warning" icon="el-icon-upload2" @click="fileList=[];uploadVisible=true">导入表格</el-button>
                <el-button type="success" icon="el-icon-download" @click="downloadPersonTable">导出表格</el-button>
<!--                <el-button type="primary" @click="updateUserReport">更新花名册</el-button>-->
                <el-button type="primary"  icon="el-icon-download" @click="downloadTemplate">导入模板下载</el-button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div style="width: 100%;background-color: white;margin-top: 10px;">
        <el-col :xs="7" :sm="7" :md="7" :lg="7" :xl="5">
          <div style="height: 480px;overflow: scroll;width: 100%">
            <el-tree
              :data="deptOptions"
              :props="defaultProps"
              @check="checkHandle"
              empty-text="公司数据加载中"
              show-checkbox
              highlight-current
              node-key="id"
              style="width: 350px">
            </el-tree>
          </div>
        </el-col>
       <el-col :xs="17" :sm="17" :md="17" :lg="17" :xl="19">
         <!--表格区-->
         <div style="margin-bottom: 20px;width: 100%">
           <div style="width: 100%;margin: auto">
             <div style="padding-left: 5px;padding-right: 5px">
               <el-table
                 v-loading="loading"
                 element-loading-text="同步数据中"
                 element-loading-spinner="el-icon-loading"
                 element-loading-background="rgba(255, 255, 255, 0.9)"
                 ref="singleTable"
                 :data="tableData"
                 highlight-current-row
                 style="width: 100%;"
                 stripe
                 border>
                 <el-table-column
                   type="index"
                   width="50"
                   align="center"
                   label-class-name="header-style">
                 </el-table-column>
                 <el-table-column
                   prop="username"
                   label="姓名"
                   width="150"
                   align="center"
                   label-class-name="header-style">
                 </el-table-column>
                 <el-table-column
                   prop="loginName"
                   label="登录名"
                   width="120"
                   align="center"
                   label-class-name="header-style">
                 </el-table-column>
                 <el-table-column
                   prop="deptName"
                   label="所属部门"
                   width="200"
                   align="center"
                   show-overflow-tooltip
                   label-class-name="header-style">
                 </el-table-column>
                 <el-table-column
                   prop="companyName"
                   label="所属公司"
                   width="200"
                   align="center"
                   show-overflow-tooltip
                   label-class-name="header-style">
                 </el-table-column>
                 <el-table-column
                   prop="teamName"
                   label="所属班组"
                   width="180"
                   align="center"
                   show-overflow-tooltip
                   label-class-name="header-style">
                 </el-table-column>
                 <el-table-column
                   label="角色"
                   min-width="250"
                   show-overflow-tooltip
                   label-class-name="header-style">
                   <template slot-scope="scope">
                     <el-tag size="medium" v-for="item in scope.row.roles" :key="item.id">{{item.role}}</el-tag>
                   </template>
                 </el-table-column>
                 <el-table-column
                   label="操作"
                   align="center"
                   width="200"
                   fixed="right"
                   label-class-name="header-style">
                   <template slot-scope="scope">
                     <el-button
                       size="mini"
                       type="primary"
                       @click="handleEdit(scope.row)">修改
                     </el-button>
                     <el-button
                       size="mini"
                       type="danger"
                       @click="handleDelete(scope.row)">删除
                     </el-button>
                   </template>
                 </el-table-column>
               </el-table>
             </div>
           </div>
           <!--分页-->
           <div style="margin:10px 0 0 20px">
             <el-pagination
               background
               layout="prev, pager, next"
               :total="itemNumber"
               @current-change="currentPage">
             </el-pagination>
           </div>
         </div>
       </el-col>
      </div>

    </div>

    <!--修改人员对话框-->
    <el-dialog title="修改人员" :visible.sync="editPersonVisible">

      <el-form :model="editPersonForm" :rules="editPersonRules" ref="editPersonForm" class="demo-ruleForm" label-position="right" label-width="140px" >
        <el-row :gutter="20">
          <el-col :span="11">
            <el-form-item label="姓名：" prop="name" label-position="left"  >
              <el-input v-model="editPersonForm.name" ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="登录名：" prop="loginName" label-position="left" >
              <el-input v-model="editPersonForm.loginName" ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="11">
            <el-form-item label="重设密码：" prop="password" label-position="left" >
              <el-input type="password" v-model="editPersonForm.password" ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="角色：" prop="roles" label-position="left">
              <el-select v-model="editPersonForm.roles" multiple style="width: 100%">
                <el-option
                  v-for="item in roleOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="22">
            <el-form-item label="公司/部门：" prop="deptStr" label-position="left">
              <el-input v-model="editPersonForm.deptStr" readonly="readonly"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="11">
            <el-form-item label="修改部门：" prop="dept" label-position="left">
              <el-cascader
                :options="deptOptions"
                :props="deptProps"
                v-model="editPersonForm.dept"
                clearable
                style="width: 100%">
              </el-cascader>
            </el-form-item>
          </el-col>
          <el-col :span="11"><el-form-item label="班组：" prop="team" label-position="left">
            <el-select
              v-model="editPersonForm.team"
              filterable
              clearable
              style="width: 100%">
              <el-option
                v-for="item in teamOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id">
              </el-option>
            </el-select>
          </el-form-item></el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="11">
            <el-form-item label="联系电话：" prop="phone" label-position="left">
              <el-input v-model="editPersonForm.phone"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="入职日期：" prop="entryDate"  label-position="left">
              <el-date-picker
                id="entryDate2"
                v-model="editPersonForm.entryDate"
                type="date"
                style="width: 100%"
                placeholder="选择日期">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="11">
            <el-form-item label="学历：" prop="degreeOfEducation" label-position="left">
              <el-select v-model="editPersonForm.degreeOfEducation" placeholder="请选择" style="width: 100%;">
                <el-option
                  v-for="item in eduDegreeOptions"
                  :key="item.value"
                  :label="item.value"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="性别：" prop="gender" label-position="left">
              <el-select v-model="editPersonForm.gender" placeholder="请选择" style="width: 100%;">
                <el-option
                  v-for="item in genderOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="11">
            <el-form-item label="身份证号：" prop="idNumber">
              <el-input v-model="editPersonForm.idNumber"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="生日：" prop="birthday"  label-position="left">
              <el-date-picker
                id="birthdayDate"
                v-model="editPersonForm.birthday"
                type="date"
                style="width: 100%"
                placeholder="选择日期">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="11">
            <el-form-item label="岗位：" prop="postId">
              <el-select v-model="editPersonForm.postId" filterable clearable placeholder="请选择" style="width: 100%;">
                <el-option
                  v-for="item in postList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="职务：" prop="duty">
              <el-input v-model="editPersonForm.duty"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="人员归属：" prop="temp">
              <el-radio-group v-model="editPersonForm.temp">
                <el-radio :label="1">公司员工</el-radio>
                <el-radio :label="2">借调</el-radio>
                <el-radio :label="0">劳务派遣</el-radio>
                <el-radio :label="3">其他</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item labelWidth="300px" label="是否需要进行三级安全教育：" prop="entryTrain" >
              <el-radio-group v-model="editPersonForm.entryTrain" clearable>
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item labelWidth="300px" label="是否需要进行调、脱岗安全教育：" prop="reassignTrain">
              <el-radio-group v-model="editPersonForm.reassignTrain" clearable>
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="11">
            <el-form-item label="岗位调整：" prop="reassignFlag">
             <!-- <el-input v-model="editPersonForm.reassignFlag"></el-input>-->
              <el-select v-model="editPersonForm.reassignFlag" clearable style="width: 100%">
                <el-option label="转岗" value="转岗"></el-option>
                <el-option label="脱岗一年以上" value="脱岗一年以上"></el-option>
                <el-option label="其他" value="其他"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="岗位调整时间：" prop="reassignDate">
              <el-date-picker
                id="reassignDatePicker"
                v-model="editPersonForm.reassignDate"
                type="date"
                style="width: 100%"
                placeholder="选择日期">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="11">
            <el-form-item label="离职：" prop="leaveFlag">
              <!--<el-input v-model="editPersonForm.leaveFlag"></el-input>-->
              <el-select v-model="editPersonForm.leaveFlag" clearable style="width: 100%">
                <el-option label="调职" value="调职"></el-option>
                <el-option label="离职" value="离职"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="离职时间：" prop="leaveDate">
              <el-date-picker
                id="leaveDatePicker"
                v-model="editPersonForm.leaveDate"
                type="date"
                style="width: 100%"
                placeholder="选择日期">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="11">
            <el-form-item label="是否管理者：" prop="isManager" label-position="left">
              <el-select v-model="editPersonForm.isManager" placeholder="请选择" style="width: 100%;">
                <el-option
                  v-for="item in managerOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="updatePerson">保存</el-button>
        <el-button @click="$refs['editPersonForm'].resetFields();editPersonVisible = false">取消</el-button>
      </div>
    </el-dialog>
    <!--修改人员对话框结束-->

    <!--添加人员对话框-->
    <el-dialog title="添加人员" :visible.sync="addPersonVisible">
      <el-form :model="addPersonForm" :rules="addPersonRules" ref="addPersonForm" class="demo-ruleForm"  label-position="right" label-width="140px">
        <el-row :gutter="20">
          <el-col :span="11">
            <el-form-item label="姓名：" prop="name" label-position="left">
              <el-input v-model="addPersonForm.name"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="登录名：" prop="loginName"  label-position="left">
              <el-input v-model="addPersonForm.loginName"  auto-complete=“new-accounts” placeholder="默认为姓名"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="11">
            <el-form-item label="密码：" prop="password"  label-position="left">
              <el-input type="password"  auto-complete=“new-password” v-model="addPersonForm.password"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="角色：" prop="roles" label-position="left">
              <el-select v-model="addPersonForm.roles" multiple style="width: 100%">
                <el-option
                  v-for="item in roleOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="11">
            <el-form-item label="部门：" prop="dept" label-position="left">
              <el-cascader
                :options="deptOptions"
                :props="deptProps"
                v-model="addPersonForm.dept"
                clearable
                style="width: 100%">
              </el-cascader>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="班组："
                          prop="team" label-position="left">
              <el-select
                v-model="addPersonForm.team"
                filterable
                clearable
                style="width: 100%">
                <el-option
                  v-for="item in teamOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="11">
            <el-form-item label="联系电话：" prop="phone" label-position="left">
              <el-input v-model="addPersonForm.phone"></el-input>
            </el-form-item>
          </el-col>
        <!--  <el-col :span="11">
            <el-form-item label="短号：" prop="shortPhone" label-position="left">
              <el-input v-model="addPersonForm.shortPhone"></el-input>
            </el-form-item>
          </el-col>-->
          <el-col :span="11">
            <el-form-item label="入职日期：" prop="entryDate"  label-position="left">
              <el-date-picker
                id="entryDate"
                v-model="addPersonForm.entryDate"
                type="date"
                style="width: 100%"
                placeholder="选择日期">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="11">
            <el-form-item label="身份证号：" prop="idNumber">
              <el-input v-model="addPersonForm.idNumber"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="生日：" prop="birthday"  label-position="left">
              <el-date-picker
                id="birthdayDatePicker02"
                v-model="addPersonForm.birthday"
                type="date"
                style="width: 100%"
                placeholder="选择日期">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="11">
            <el-form-item label="学历：" prop="degreeOfEducation" label-position="left">
              <el-select v-model="addPersonForm.degreeOfEducation" placeholder="请选择" style="width: 100%;">
                <el-option
                  v-for="item in eduDegreeOptions"
                  :key="item.value"
                  :label="item.value"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="性别：" prop="gender" label-position="left">
              <el-select v-model="addPersonForm.gender" placeholder="请选择" style="width: 100%;">
                <el-option
                  v-for="item in genderOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="11">
            <el-form-item label="岗位：" prop="postId">
              <el-select v-model="addPersonForm.postId" placeholder="请选择" clearable filterable style="width: 100%;">
                <el-option
                  v-for="item in currentCompanyPostList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="职务：" prop="duty">
              <el-input v-model="addPersonForm.duty"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="人员归属："  prop="temp">
              <el-radio-group v-model="addPersonForm.temp">
                <el-radio :label="1">公司员工</el-radio>
                <el-radio :label="2">借调</el-radio>
                <el-radio :label="0">劳务派遣</el-radio>
                <el-radio :label="3">其他</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="20">
            <el-form-item labelWidth="200px" label="是否需要进行三级安全教育：" prop="entryTrain" label-position="top">
              <el-radio-group v-model="addPersonForm.entryTrain" clearable>
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <!--<el-col :span="11">
            <el-form-item label="调、脱岗安全教育：" prop="reassignTrain">
              <el-radio-group v-model="addPersonForm.reassignTrain">
                <el-radio :label="false">否</el-radio>
                <el-radio :label="true">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>-->
        </el-row>
       <!-- <el-row :gutter="20">
          <el-col :span="11">
            <el-form-item label="岗位调整：" prop="reassignFlag">
              <el-input v-model="addPersonForm.reassignFlag"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="岗位调整时间：" prop="reassignDate">
              <el-date-picker
                id="reassignDatePicker02"
                v-model="addPersonForm.reassignDate"
                type="date"
                style="width: 100%"
                placeholder="选择日期">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="11">
            <el-form-item label="离职：" prop="leaveFlag">
              <el-input v-model="addPersonForm.leaveFlag"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="离职时间：" prop="leaveDate">
              <el-date-picker
                id="leaveDatePicker02"
                v-model="addPersonForm.leaveDate"
                type="date"
                style="width: 100%"
                placeholder="选择日期">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>-->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="addUser">添加</el-button>
        <el-button @click="$refs['addPersonForm'].resetFields();addPersonVisible = false">取消</el-button>
      </div>
    </el-dialog>
    <!--添加人员对话框结束-->

    <!--导入列表对话框-->
    <el-dialog title="导入人员表格" :visible.sync="uploadVisible">
      <el-upload
        class="upload-demo"
        ref="upload"
        :action="uploadBaseUrl"
        multiple
        :with-credentials="cookies"
        :file-list="fileList"
        :on-success="uploadPersonTableSuccess"
        style="width: 300px;margin-bottom: 10px;">
        <el-button size="small" type="primary">选取人员表格</el-button>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button @click="uploadVisible = false">返回</el-button>
      </div>
    </el-dialog>
    <!--导入列表对话框结束-->
  </div>
</template>
<script>
  export default {
    name: 'userManage',
    data() {
      let checkIdNumber = (rule, value, callback) => {
        if (value === '') {
          callback(new Error('请输入身份证号码'));
        } else if(value.length!==18){
          callback(new Error('需为18位号码'));
        }else{
          callback();
        }
      };
      return {
        //搜索数据
        personOptions: [
          {value: "姓名", label: "姓名"},
        ],
        classValue: ['姓名'],
        input: '',
        //表格数据
        tableData: [],
        itemNumber: 0,
        nowPage: 1,
        multipleSelection: [],
        loading: false,
        //选择器的数据
        eduDegreeOptions:[
          {value:"初中",label:"初中"},
          {value:"高中",label:"高中"},
          {value:"大专",label:"大专"},
          {value:"本科",label:"本科"},
          {value:"硕士",label:"硕士"},
          {value:"博士",label:"博士"},
        ],
        genderOptions:[
          {value:false,label:'女'},
          {value:true,label:'男'},
        ],
        managerOptions:[
          {value:0,label:'否'},
          {value:1,label:'是'},
        ],
        postList:[],
        currentCompanyPostList:[],//当前公司的岗位，只会查找一次
        //修改人员
        editPersonVisible: false,
        editPersonForm: {
          id: '',
          name: '',
          loginName: '',
          password: '',
          deptStr: '',
          dept: [],
          roles: [],
          phone: '',
          // 短号
//          shortPhone: '',
          entryDate: '',
          team: 0,
          degreeOfEducation:'',
          gender:true,
          birthday:'',
          idNumber:'',
          temp:1,
          duty:'',
          entryTrain:true,
          reassignTrain:true,
          reassignFlag:'',
          reassignDate:'',
          leaveFlag:'',
          leaveDate:'',
          postId:'',//岗位ID
          isManager:0,
        },
        editPersonRules: {
          name: [{required: true, message: '请输入姓名', trigger: 'change'}],
        },

        //添加人员
        addPersonVisible: false,
        addPersonForm: {
          name: '',
          loginName: '',
          password: '',
          dept: [],
          roles: [],
          phone: '',
          // 短号
//          shortPhone: '',
          entryDate: '',
          team: 0,
          degreeOfEducation:"本科",
          gender:true,
          birthday:'',
          idNumber:'',
          temp:0,
          duty:'',
          entryTrain:true,
          reassignTrain:true,
          reassignFlag:'',
          reassignDate:'',
          leaveFlag:'',
          leaveDate:'',
          postId:'',//岗位ID
        },
        addPersonRules: {
          name: [{required: true, message: '请输入姓名', trigger: 'change'}],
          password: [{required: true, message: '请输入密码', trigger: 'change'}],
          dept: [{required: true, message: '请选择部门', trigger: 'change'}],
          idNumber:[{required: true, message: '请输入身份证号码', trigger: 'change'},
            { validator: checkIdNumber, trigger: 'blur'}],
        },

        deptProps: {
          children: 'subDept',
          label: 'name',
          value: 'id'
        },
        /*班组的信息*/

      //-----------------部门选择数据----------------------------
        defaultProps: {
          children: 'subDept',
          label: 'name'
        },
        searchDeptIds:[],
        //-----------------上传下载的baseurl-----------------
        cookies: true,
        uploadBaseUrl:'',
        //对话框数据
        uploadVisible:false,
        fileList:[],

      }
    },
    computed: {
      roleOptions: function () {
        return this.$store.state.sysManageData.roleOptions;
      },
      deptOptions: function () {//当前公司和下级
        return this.$store.state.sysManageData.deptTree;
      },
      teamOptions: function () {
        return this.$store.state.sysManageData.teamData;
      },
      treeArray:function(){//所有公司
        return this.$store.state.sysManageData.topAllDeptTree;
      }

    },
    mounted: function () {
      //获取所有用户数据
      this.sendRequest(this.input.trim(), 1);
      //当前公司和下级
      this.$store.dispatch("getAllDept");
      //获取所有角色
      this.$store.dispatch("getRoleList");
      //获取班组
      this.$store.dispatch("getTeam");
      //所有公司
      this.$store.dispatch("getTopAllDeptTree");
      //当前公司的岗位
      this.searchPost();

      this.uploadBaseUrl=this.$http.defaults.baseURL + 'user/addBatch';
    },
    watch: {
      $route(to, from) {
        if (this.$route.name === 'userManage') {
          this.$store.dispatch("getRoleList");
        }
      }
    },
    methods: {
      //-----------------------------交互事件-------------------------------
      //向服务器发送请求，查找人员
      sendRequest: function (name, pageCurrent) {
        let params=new URLSearchParams;
        params.append("username",name);
        params.append("pageCurrent",pageCurrent);
        if(this.searchDeptIds.length){
          params.append("deptIds",this.searchDeptIds);
        }
          console.log(params)

        this.$http.post('user/find',params).then(function (res) {
          if (res.data.success) {
            this.itemNumber = res.data.data.total;
            this.tableData = res.data.data.list;
            for (let i = 0; i < res.data.data.list.length; i++) {
              this.tableData[i].roleIds = [];
              if (res.data.data.list[i].roles.length > 0) {
                for (let j = 0; j < res.data.data.list[i].roles.length; j++) {
                  if(res.data.data.list[i].roles[j]){
                    this.tableData[i].roleIds.push(res.data.data.list[i].roles[j].id);
                  }else {
                    this.tableData[i].roles.splice(j,1);
                  }
                }
              }
            }
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
        });
      },
      //查找所有岗位
      searchPost:function () {
        this.currentCompanyPostList=[];
        this.$http.post('post/find?pageSize=1000&companyId='+this.$tool.getStorage('LOGIN_USER').companyId).then(function (res) {
          if (res.data.success) {
            this.currentCompanyPostList=res.data.data.list;
            this.currentCompanyPostList.forEach(function (item) {
              item.value=item.id;
              item.label=item.postName;
            })
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message({
            showClose: true,
            message: '岗位查找失败',
            type: 'error'
          });
        }.bind(this));
      },
      //查找对应公司的岗位
      searchPostByCompanyId:function (companyId) {
        this.postList=[];
        this.$http.post('post/find?pageSize=1000&companyId='+companyId).then(function (res) {
          if (res.data.success) {
            this.postList=res.data.data.list;
            this.postList.forEach(function (item) {
              item.value=item.id;
              item.label=item.postName;
            })
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message({
            showClose: true,
            message: '岗位查找失败',
            type: 'error'
          });
        }.bind(this));
      },
      //-----------------------------对话框事件----------------------------------
      addButtonClick:function () {
        this.addPersonForm.name='';
        this.addPersonForm.loginName='';
        this.addPersonForm.phone='';
        this.addPersonForm.shortPhone='';
        this.addPersonForm.degreeOfEducation='本科';
        this.addPersonForm.gender=true;
        this.addPersonForm.birthday='';
        this.addPersonForm.idNumber='';
        this.addPersonForm.temp=0;
        this.addPersonForm.duty='';
        this.addPersonForm.entryTrain=true;
        this.addPersonForm.reassignTrain=true;
        this.addPersonForm.reassignFlag='';
        this.addPersonForm.reassignDate='';
        this.addPersonForm.leaveFlag='';
        this.addPersonForm.leaveDate='';
        this.addPersonForm.postId='';
        this.addPersonVisible=true;
        this.addPersonForm.loginName='';
        this.addPersonForm.password='';

      },
      addUser: function () {
        this.$refs['addPersonForm'].validate((valid) => {
          if (valid) {


//            console.log(this.addPersonForm,99999)
//            return;
            let requestStr = 'user/addOrUpdate';
            var params =new URLSearchParams();
            params.append("username",this.addPersonForm.name)
            params.append("password",this.addPersonForm.password)
            params.append("mobile",this.addPersonForm.phone)

            //判断上级公司ID
            this.$http.get('dept/findCompanyId/'+this.addPersonForm.dept[this.addPersonForm.dept.length - 1]).then(function (res) {
              if (res.data.success) {
                params.append("deptId",this.addPersonForm.dept[this.addPersonForm.dept.length - 1])
                params.append("companyId",res.data.data)
                console.log(res.data.data)

                if (this.addPersonForm.loginName.trim()) {
                  params.append("loginName",this.addPersonForm.loginName);

                } else {
                  params.append("loginName",this.addPersonForm.name);
                }
//                params.append("shortPhone",this.addPersonForm.shortPhone);
                if (this.addPersonForm.team != 0) {
                  params.append("teamId",this.addPersonForm.team);
                }
                if(this.addPersonForm.birthday){
                  params.append("birthday",this.addPersonForm.birthday);
                }
                // 入职时间
                if(this.addPersonForm.entryDate){
                  params.append("entryDate",this.addPersonForm.entryDate);
                }
                params.append("degreeOfEducation",this.addPersonForm.degreeOfEducation);
                params.append("gender",this.addPersonForm.gender);
                params.append("idNumber",this.addPersonForm.idNumber);
                params.append("temp",this.addPersonForm.temp);
                if(this.addPersonForm.duty.trim()){params.append("duty",this.addPersonForm.duty);}
                params.append("entryTrain",this.addPersonForm.entryTrain);
                params.append("reassignTrain",this.addPersonForm.reassignTrain);
                if(this.addPersonForm.reassignFlag.trim()){params.append("reassignFlag",this.addPersonForm.reassignFlag);}
                if(this.addPersonForm.reassignDate){params.append("reassignDate",this.addPersonForm.reassignDate);}
                if(this.addPersonForm.leaveFlag.trim()){params.append("leaveFlag",this.addPersonForm.leaveFlag);}
                if(this.addPersonForm.leaveDate){params.append("leaveDate",this.addPersonForm.leaveDate);}

                this.$http.post(requestStr,params).then(function (res) {
                  if (res.data.success) {
                    //加岗位
                    if(this.addPersonForm.postId){
                      this.$http.post('userPost/add?userId='+res.data.data.userId+'&postId='+this.addPersonForm.postId).then(function (res) {
                        if (res.data.success) {}
                      }.bind(this)).catch(function (err) {
                        console.log(err);
                      }.bind(this));
                    }

                    if (this.addPersonForm.roles.length > 0) {
                      let params = new URLSearchParams;
                      params.append("userId", res.data.data.userId);
                      params.append("roleIds", this.addPersonForm.roles);
                      this.$http.post('userRole/add', params).then(function (res) {
                        if (res.data.success) {
                          this.$message({
                            showClose: true,
                            message: '人员添加成功！',
                            type: 'success'
                          });
                          this.$refs['addPersonForm'].resetFields();
                          this.addPersonVisible = false;
                          this.currentPage(this.nowPage);
                        }
                      }.bind(this)).catch(function (err) {
                        console.log(err);
                        this.$message({
                          showClose: true,
                          message: '角色赋予失败！',
                          type: 'error'
                        });
                      }.bind(this));
                    } else {//没有角色
                      this.$message({
                        showClose: true,
                        message: '人员添加成功！',
                        type: 'success'
                      });
                      this.$refs['addPersonForm'].resetFields();
                      this.addPersonVisible = false;
                      this.currentPage(this.nowPage);
                    }
                  }
                }.bind(this)).catch(function (err) {
                  console.log('添加人员失败！' + err);
                }.bind(this));

              }
            }.bind(this)).catch(function (err) {
              console.log(err);
            }.bind(this));

          } else {
            console.log('error submit!!');
            return false;
          }
        });
      },
      updatePerson: function () {
        this.$refs['editPersonForm'].validate((valid) => {
          if (valid) {
            let requestStr = 'user/addOrUpdate';
            var params=new URLSearchParams()
            params.append("userId",this.editPersonForm.id)
            params.append("username",this.editPersonForm.name)
            if (this.editPersonForm.password.trim()) {
              params.append("password",this.editPersonForm.password)
            }
            params.append("mobile",this.editPersonForm.phone)

            if (this.editPersonForm.loginName && this.editPersonForm.loginName.trim()) {
              params.append("loginName",this.editPersonForm.loginName)
            } else {
              params.append("loginName",this.editPersonForm.name)
            }
            params.append("shortPhone",this.editPersonForm.shortPhone)
            if (this.editPersonForm.team != 0&&this.editPersonForm.team) {
              params.append("teamId",this.editPersonForm.team);
            }
            // 入职日期
            if(this.editPersonForm.entryDate){
              params.append("entryDate",this.editPersonForm.entryDate);
            }
            if(this.editPersonForm.birthday){
              params.append("birthday",this.editPersonForm.birthday);
            }
            params.append("degreeOfEducation",this.editPersonForm.degreeOfEducation);
            params.append("gender",this.editPersonForm.gender);
            params.append("idNumber",this.editPersonForm.idNumber);
            params.append("temp",this.editPersonForm.temp);
            if(this.editPersonForm.duty.trim()){params.append("duty",this.editPersonForm.duty);}
            params.append("entryTrain",this.editPersonForm.entryTrain);
            params.append("reassignTrain",this.editPersonForm.reassignTrain);
            params.append("isManager",this.editPersonForm.isManager);
            if(this.editPersonForm.reassignFlag && this.editPersonForm.reassignFlag.trim()){params.append("reassignFlag",this.editPersonForm.reassignFlag);}
            if(this.editPersonForm.leaveFlag && this.editPersonForm.leaveFlag.trim()){params.append("leaveFlag",this.editPersonForm.leaveFlag);}

//            params.append("reassignFlag",this.editPersonForm.reassignFlag);
//            params.append("leaveFlag",this.editPersonForm.leaveFlag);
//            if(this.editPersonForm.reassignFlag){params.append("reassignFlag",this.editPersonForm.reassignFlag);}
            if(this.editPersonForm.reassignDate){params.append("reassignDate",this.editPersonForm.reassignDate);}
//            if(this.editPersonForm.leaveFlag){params.append("leaveFlag",this.editPersonForm.leaveFlag);}
            if(this.editPersonForm.leaveDate){params.append("leaveDate",this.editPersonForm.leaveDate);}

            if (this.editPersonForm.dept.length > 0) {//修改了部门
              this.$http.get('dept/findCompanyId/'+this.editPersonForm.dept[this.editPersonForm.dept.length - 1]).then(function (res) {
                if(res.data.success){
                  params.append("deptId",this.editPersonForm.dept[this.editPersonForm.dept.length - 1]);
                  params.append("companyId",res.data.data);
                  this.updatePersonSendRequest(requestStr,params);
                }
      }.bind(this)).catch(function (err) {
          console.log(err);
        }.bind(this));
      }else{//没有修改部门
        this.updatePersonSendRequest(requestStr,params);
  }

  } else {
    console.log('error submit!!');
            return false;
          }
        });
      },
      updatePersonSendRequest:function (requestStr,params) {
        this.$http.post(requestStr,params).then(function (res) {
          if (res.data.success) {
            //加岗位
            if(this.editPersonForm.postId&&this.editPersonForm.postId!=this.editPersonForm.tempPostId){
              this.$http.post('userPost/add?userId='+this.editPersonForm.id+'&postId='+this.editPersonForm.postId).then(function (res) {
                if (res.data.success) {}
              }.bind(this)).catch(function (err) {
                console.log(err);
              }.bind(this));
            }

            if (this.editPersonForm.roles.length > 0) {
              let params = new URLSearchParams;
              params.append("userId", this.editPersonForm.id);
              params.append("roleIds", this.editPersonForm.roles);
              this.$http.post('userRole/add', params).then(function (res) {
                if (res.data.success) {
                  this.$message({
                    showClose: true,
                    message: '人员修改成功！',
                    type: 'success'
                  });
                  this.$refs['editPersonForm'].resetFields();
                  this.editPersonVisible = false;
                  this.currentPage(this.nowPage);
                }
              }.bind(this)).catch(function (err) {
                console.log(err);
                this.$message({
                  showClose: true,
                  message: '人员角色修改失败！',
                  type: 'error'
                });
              }.bind(this));
            } else {//无角色
              this.$message({
                showClose: true,
                message: '人员修改成功！',
                type: 'success'
              });
              this.$refs['editPersonForm'].resetFields();
              this.editPersonVisible = false;
              this.currentPage(this.nowPage);
            }

          }
        }.bind(this)).catch(function (err) {
          console.log('修改人员失败：' + err);
        }.bind(this));
      },

      //-----------------------------表格事件-------------------------------
      //翻页
      currentPage: function (val) {
        this.nowPage = val;
        this.sendRequest(this.input.trim(), this.nowPage);
      },
      handleEdit(row) {
        this.editPersonForm.id = row.userId;
        this.editPersonForm.name = row.username;
        this.editPersonForm.loginName = row.loginName;
        this.editPersonForm.deptStr = row.companyName + ' / ' + row.deptName;
        this.editPersonForm.roles = row.roleIds;
        this.editPersonForm.phone = row.mobile;
//        this.editPersonForm.shortPhone = row.shortPhone;
        this.editPersonForm.entryDate = new Date(row.entryDate);
        this.editPersonForm.birthday = new Date(row.birthday);
//        this.editPersonForm.reassignDate = new Date(row.reassignDate);
//        this.editPersonForm.leaveDate = new Date(row.leaveDate);
        this.editPersonForm.degreeOfEducation = row.degreeOfEducation?row.degreeOfEducation:'';
        this.editPersonForm.gender = row.gender!==''?row.gender:true;
        this.editPersonForm.isManager = row.isManager ;
        this.editPersonForm.team = row.teamId;
        this.editPersonForm.idNumber = row.idNumber;
        console.log(row);
        this.editPersonForm.temp = row.temp;
        this.editPersonForm.duty=row.duty?row.duty:'';
        this.editPersonForm.entryTrain=row.entryTrain!==''?row.entryTrain:false;
        this.editPersonForm.reassignTrain=row.reassignTrain!==''?row.reassignTrain:false;
        this.editPersonForm.reassignFlag=row.reassignFlag?row.reassignFlag:'';
        this.editPersonForm.reassignDate=row.reassignDate?new Date(row.reassignDate):'';
        this.editPersonForm.leaveFlag=row.leaveFlag?row.leaveFlag:'';
        this.editPersonForm.leaveDate=row.leaveDate?new Date(row.leaveDate):'';
        this.editPersonForm.postId=this.editPersonForm.tempPostId=row.posts.length?row.posts[0].postId:'';
        this.searchPostByCompanyId(row.companyId);
        this.editPersonVisible = true;
      },
      handleDelete(row) {
        this.$confirm('此操作将永久删除该人员, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let params = new URLSearchParams;
          params.append("userId", row.userId);
          this.$http.post('user/delete', params).then(function (res) {
            if (res.data.success) {
              this.$message({
                showClose: true,
                message: '删除人员成功！',
                type: 'success'
              });
              this.currentPage(1);
            }
          }.bind(this)).catch(function (err) {
            console.log(err);
            this.$message({
              showClose: true,
              message: '操作失败',
              type: 'error'
            });
          }.bind(this));
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });
        });
      },
      //-----------------------------------部门侧边框事件---------------------------------
      checkHandle(data, status){
        this.searchDeptIds = status.checkedKeys;
        this.sendRequest(this.input.trim(), 1);
      },

      //--------------------------------导出列表----------------------------------
      downloadPersonTable:function () {
        const loading = this.$loading({
          lock: true,
          text: 'Loading',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        let params=new URLSearchParams;
        if(this.searchDeptIds.length){
          params.append("deptIds",this.searchDeptIds);
        }


        this.$http({ // 用axios发送post请求
          method: 'post',
          url: 'user/sysUserOutput', // 请求地址
          data: params, // 参数
          responseType: 'blob' // 表明返回服务器返回的数据类型
        }).then((res) => { // 处理返回的文件流
          loading.close()
          const elink = document.createElement('a') // 创建a标签
          elink.download = '人员名册'+this.transferTime(new Date(),null,true)+'.xlsx' // 文件名
          elink.style.display = 'none'
          const blob = new Blob([res.data])
          elink.href = URL.createObjectURL(blob)
          document.body.appendChild(elink)
          elink.click() // 触发点击a标签事件
          document.body.removeChild(elink)
        })
      },

      downloadTemplate:function () {
        const loading = this.$loading({
          lock: true,
          text: 'Loading',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        let params=new URLSearchParams;
        this.$http({ // 用axios发送post请求
          method: 'post',
          url: 'user/downloadTemplate', // 请求地址
          data: params, // 参数
          responseType: 'blob' // 表明返回服务器返回的数据类型
        }).then((res) => { // 处理返回的文件流
          loading.close()
          const elink = document.createElement('a') // 创建a标签
          elink.download = '人员导入模板'+'.xlsx' // 文件名
          elink.style.display = 'none'
          const blob = new Blob([res.data])
          elink.href = URL.createObjectURL(blob)
          document.body.appendChild(elink)
          elink.click() // 触发点击a标签事件
          document.body.removeChild(elink)
        })
      },
      uploadPersonTableSuccess:function (res) {
        if(res.success){
          this.$message.success('上传成功！');
          this.uploadVisible=false;
        }else{
          this.$message.warning('上传失败，请检查网络');
          this.uploadVisible=false;
        }
      },
      //--------------------------------更新花名册----------------------------------
      updateUserReport:function () {
        const loading = this.$loading({
          lock: true,
          text: 'Loading',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        this.$http.post('sysUserReport/addOrUpdateThisYear').then(function (res) {
          loading.close()

          if (res.data.success) {
            this.$message({
              showClose: true,
              message: '更新成功！',
              type: 'success'
            });
          }
        }.bind(this)).catch(function (err) {
          loading.close()
          console.log(err);
          this.$message({
            showClose: true,
            message: '操作失败',
            type: 'error'
          });
        }.bind(this));
      }
    }
  }
</script>
<style>
  .form-item{
    width: 100%;
  }
</style>


