<template>
  <div id="inspectionPlan">
    <div class="background-style">

      <!--搜索区-->
      <div style="padding:10px;float: left">
        年份选择：
        <el-select v-model="year" clearable placeholder="请选择或自定义" filterable allow-create>
          <el-option
            v-for="item in yearOptions"
            :key="item"
            :label="item"
            :value="item">
          </el-option>
        </el-select>
        <el-button type="primary" icon="el-icon-search" @click="searchPlan" style="margin-left: 10px">搜索</el-button>
        <el-button type="success" @click="addPlan" style="margin-left: 20px">新增计划</el-button>
      </div>
      <!--表格区-->
      <div style="width: 100%;">
        <div style="padding: 10px">
          <el-table
            :data="tableData"
            border
            highlight-current-row
            style="width: 100%">
            <el-table-column
              prop="num"
              label="编号"
              width="60"
              align="center"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="name"
              label="计划名称"
              min-width="300"
              show-overflow-tooltip
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="year"
              label="年份"
              width="150"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column label="操作" label-class-name="header-style" align="center" width="300" fixed="right">
              <template slot-scope="scope">
                <el-button size="mini" type="success" @click="viewClick(scope.row)">查看</el-button>
                <el-button size="mini" type="primary" @click="updateClick(scope.row)">修改</el-button>
                <el-button size="mini" type="danger" @click="deleteClick(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div>
          <el-pagination
            background
            layout="prev, pager, next"
            :current-page="currentPage"
            :total="totalItem"
            @current-change="currentPageClick">
          </el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'inspectionPlan',
    data() {
      return {
        //年份选择
        year:'',
        yearOptions:[],

        //表格数据
        tableData:[],
        currentPage:0,
        totalItem:0,
      }
    },
    mounted:function () {
      let currentTime=new Date();
      for(let i=currentTime.getFullYear()+2;i>=2017;i--){
        this.yearOptions.push(i);
      }
      this.searchPlan();
    },
    watch:{
      $route(to, from){
        if(from.name==='planForm'&&this.$route.name==='inspectionPlan') {
          this.searchPlan();
        }
      }
    },
    methods:{
      searchPlan:function () {
        this.sendRequest(1);
      },
      currentPageClick:function (val) {
        if(val){
          this.sendRequest(Number(val));
        }
      },
      sendRequest:function (pageCurrent) {
        let params={pageCurrent:pageCurrent,pageSize:10};
        if(this.year){params.year=this.year}
        params.companyId=this.$tool.getStorage('LOGIN_USER').companyId;
        this.$http.post('danger/safePlan/find',params).then(function (res) {
          if(res.data.success){
            this.currentPage=pageCurrent;
            this.totalItem=res.data.data.total;
            this.tableData=res.data.data.list;
            this.tableData.forEach(function (item,index) {
              item.num=(pageCurrent-1)*10+index+1;
            })
          }else{
            console.log('danger/safePlan/find'+'数据申请失败');
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message({
            showClose: true,
            message: '网络错误，请尝试重登录',
            type: 'error'
          });
        }.bind(this));
      },
      addPlan:function () {
        this.$router.push({name:'planForm',params:{operateType:'add'}});
      },
      //----------------------------检查计划的查看，修改，删除----------------------------
      viewClick:function (row) {
        this.$router.push({name:'viewPlanForm',params:{planId:row.id,planName:row.name,planYear:row.year,docNum:row.docNum,recordNum:row.recordNum}});
      },
      updateClick:function (row) {
        this.$router.push({name:'planForm',params:{operateType:'update',planId:row.id,planName:row.name,planYear:row.year,docNum:row.docNum,recordNum:row.recordNum}});
      },
      deleteClick:function (row) {
        this.$confirm('此操作将删除该检查计划, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$http.post('danger/safePlan/delete',{id:row.id}).then(function (res) {
              if(res.data.success){
                this.sendRequest(1);
              }else{
                console.log('danger/safePlan/delete'+'数据申请失败');
              }
            }.bind(this)).catch(function (err) {
              console.log(err);
              this.$message({
                showClose: true,
                message: '删除检查计划失败',
                type: 'error'
              });
            }.bind(this));
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });
        });
      }

    }
  }
</script>
<style>
</style>
