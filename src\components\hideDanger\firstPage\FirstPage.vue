<template>
  <div id="firstPage">
    <div class="left">
      <!--<ul class="ul">-->
        <!--<li v-for="item in count">{{item.label}}：{{item.value}}</li>-->
      <!--</ul>-->
      <!--刘杰0925修改-->
      <el-row  style="margin: 10px 0 0 0">
        <el-col :span="6"  class="countLabelColumn" >待检查</el-col>
        <el-col :span="6" class="countLabelColumn" >隐患评估中</el-col>
        <el-col :span="6" class="countLabelColumn" >发布整改单</el-col>
        <el-col :span="6" class="countLabelColumn" >发布审核</el-col>
      </el-row>
      <el-row style="margin: 0">
        <el-col :span="6" class="countValueColumn" >{{count[1].value}}</el-col>
        <el-col :span="6" class="countValueColumn" >{{count[4].value}}</el-col>
        <el-col :span="6" class="countValueColumn" >{{count[3].value}}</el-col>
        <el-col :span="6" class="countValueColumn" >{{count[0].value}}</el-col>
      </el-row>

      <el-row  style="margin: 10px 0 0 0">
        <el-col :span="6" class="countLabelColumn" >待整改</el-col>
        <el-col :span="6" class="countLabelColumn" >整改审核</el-col>
        <el-col :span="6" class="countLabelColumn" >待验收</el-col>
        <el-col :span="6" class="countLabelColumn" >验收审核</el-col>
      </el-row>
      <el-row style="margin: 0">
        <el-col :span="6" class="countValueColumn" >{{count[6].value}}</el-col>
        <el-col :span="6" class="countValueColumn" >{{count[7].value}}</el-col>
        <el-col :span="6" class="countValueColumn" >{{count[8].value}}</el-col>
        <el-col :span="6" class="countValueColumn" >{{count[13].value}}</el-col>
      </el-row>

      <el-row  style="margin: 10px 0 0 0">
        <el-col :span="6" class="countLabelColumn" >督办整改中</el-col>
        <el-col :span="6" class="countLabelColumn" >督办整改审核</el-col>
        <el-col :span="6" class="countLabelColumn" >督办待验收</el-col>
        <el-col :span="6" class="countLabelColumn" >督办验收审核</el-col>
      </el-row>
      <el-row style="margin: 0">
        <el-col :span="6" class="countValueColumn" >{{count[9].value}}</el-col>
        <el-col :span="6" class="countValueColumn" >{{count[10].value}}</el-col>
        <el-col :span="6" class="countValueColumn" >{{count[11].value}}</el-col>
        <el-col :span="6" class="countValueColumn" >{{count[12].value}}</el-col>
      </el-row>

      <el-row style="margin: 10px 0 0 0">
        <el-col :span="8" style="font-size:16px;text-align: center;color:#0f6fc6">
          <div style="width: 100%;height: 130px;" id="pieOfCheck"></div>
        </el-col>
        <el-col :span="8">
          <div style="width: 100%;height: 130px;" id="pieOfChange"></div>
        </el-col>
        <el-col :span="8">
          <div style="width: 100%;height: 130px;" id="pieOfSupervising"></div>
        </el-col>
      </el-row>

      <div class="div1">
        <div class="title">
          <span></span>
          <el-date-picker
            type="year"
            v-model="year"
            @change="yearChange"
            placeholder="选择日期">
          </el-date-picker>
        </div>
        <div style="width: 550px;height: 350px;margin: 0" id="dangerLevelCountHistogram"></div>
      </div>

      <div class="div1">
        <div style="width: 550px;height: 380px;margin: 20px 0 0 0" id="deptCountHistogram"></div>
      </div>
    </div>
    <div class="right">
      <!--12条数据-->
      <ul class="latestDanger">
        <li style="color:#3576aa;">最新隐患</li>
        <li v-for="item in newDanger.slice(0,10)">{{item.inspectProject}}</li>
      </ul>
      <div class="div1">
        <div style="height: 35px;line-height: 40px;margin: 0 20px 0 20px;border-bottom: 2px solid #049ff1;color:#049ff1;font-size: large">
          隐患统计分析
        </div>
        <div style="padding: 5px 0 0 20px">
          <el-radio-group v-model="radioType" size="small" @change="pieDataTypeClick">
            <el-radio-button v-for="item in radioTypeArray" :label="item.value" :key="item.value">{{item.label}}</el-radio-button>
          </el-radio-group>
        </div>
        <div style="padding-top: 15px">
          <div style="width: 400px;height: 400px;" id="pieChart"></div>
        </div>
      </div>
    </div>

    <!--刘杰1015 增 起-->
    <!--隐患统计分析饼图点击弹出框-->
<!--    <el-dialog :title="dangerDialogTitle" :visible.sync="pieDialogVisible" width="80%">-->
<!--      &lt;!&ndash;<div style="margin:0">&ndash;&gt;-->
<!--      &lt;!&ndash;<el-radio-group v-model="dangerDialogTab" size="small" @change="searchDangerTableData">&ndash;&gt;-->
<!--      &lt;!&ndash;<el-radio-button label="全部隐患"></el-radio-button>&ndash;&gt;-->
<!--      &lt;!&ndash;<el-radio-button label="已整改"></el-radio-button>&ndash;&gt;-->
<!--      &lt;!&ndash;<el-radio-button label="已超期"></el-radio-button>&ndash;&gt;-->
<!--      &lt;!&ndash;</el-radio-group>&ndash;&gt;-->
<!--      &lt;!&ndash;</div>&ndash;&gt;-->
<!--      <div style="margin: 5px 0 5px 0">-->
<!--        <el-table-->
<!--          :data="pieChartDialogData"-->
<!--          border-->
<!--          v-loading="dangerTableLoading"-->
<!--          max-height="400"-->
<!--          style="width: 100%">-->
<!--          <el-table-column-->
<!--            type="index"-->
<!--            align="center"-->
<!--            width="50"-->
<!--            fixed-->
<!--            label-class-name="inner-header-style">-->
<!--          </el-table-column>-->
<!--          <el-table-column-->
<!--            prop="inspectProject"-->
<!--            label="检查项目"-->
<!--            width="150"-->
<!--            label-class-name="inner-header-style">-->
<!--          </el-table-column>-->
<!--          <el-table-column-->
<!--            prop="inspectContent"-->
<!--            min-width="400"-->
<!--            label="检查标准内容"-->
<!--            label-class-name="inner-header-style">-->
<!--          </el-table-column>-->
<!--          <el-table-column-->
<!--            prop="inspectResult"-->
<!--            width="300"-->
<!--            label="检查结果记录"-->
<!--            label-class-name="inner-header-style">-->
<!--          </el-table-column>-->
<!--          <el-table-column-->
<!--            prop="targetDeptName"-->
<!--            width="200"-->
<!--            label="受检单位"-->
<!--            label-class-name="inner-header-style">-->
<!--          </el-table-column>-->
<!--          <el-table-column-->
<!--            prop="hiddenDangerLevel"-->
<!--            width="200"-->
<!--            label="隐患级别"-->
<!--            label-class-name="inner-header-style">-->
<!--          </el-table-column>-->
<!--          <el-table-column-->
<!--            prop="dangerType"-->
<!--            width="200"-->
<!--            label="隐患类型"-->
<!--            label-class-name="inner-header-style">-->
<!--          </el-table-column>-->
<!--          <el-table-column-->
<!--            min-width="200"-->
<!--            label="隐患照片"-->
<!--            label-class-name="inner-header-style">-->
<!--            <template slot-scope="scope">-->
<!--              <picture-card :picFileList="scope.row.dangerPics"></picture-card>-->
<!--            </template>-->
<!--          </el-table-column>-->
<!--          <el-table-column-->
<!--            prop="changeTime"-->
<!--            width="120"-->
<!--            label="整改时间"-->
<!--            :formatter="changeTimeFormat"-->
<!--            label-class-name="inner-header-style">-->
<!--          </el-table-column>-->
<!--          <el-table-column-->
<!--            prop="deadline"-->
<!--            width="120"-->
<!--            label="整改期限"-->
<!--            :formatter="changeTimeFormat"-->
<!--            label-class-name="inner-header-style">-->
<!--          </el-table-column>-->
<!--          <el-table-column-->
<!--            prop="changeExplain"-->
<!--            width="180"-->
<!--            label="整改说明"-->
<!--            label-class-name="inner-header-style">-->
<!--          </el-table-column>-->
<!--        </el-table>-->
<!--      </div>-->

<!--      <div slot="footer" class="dialog-footer">-->
<!--        <el-button @click="pieDialogVisible=false">确 定</el-button>-->
<!--      </div>-->
<!--    </el-dialog>-->
    <!--刘杰1015 增 终-->
  </div>
</template>
<script>
  export default {
    name: 'firstPage',
    data() {
      // 隐患
      return {
        // 隐患
        dangerLevelCountMap: {
          setting : {
            stack: { '用户': ['已整改', '未整改'] }
          },
          columns: ['等级', '已整改', '未整改'],
          rows: []
        },
        // 各部门隐患排查完成率
        deptCountMap: {
          setting : {
            stack: { '用户': ['已整改', '未整改'] }
          },
          columns: ['deptName', '已整改', '未整改'],
          rows: []
        },
        //柱状图数据
        dangerDataLevel:[],
        dangerDataDone:[],
        dangerDataSum:[],
        deptDataDeptname:[],
        deptDataDeptDone:[],
        deptDataDeptSum:[],
        // 饼状图----隐患统计分析
        pieChartDialogData:[],//饼图对话框数据 刘杰1015 增
        pieDialogVisible:false,//饼图对话框是否显示 刘杰1015 增
        radioType:1,
        radioTypeArray:[
          {value:1,label:'按等级'},
          {value:2,label:'按类型'},
          {value:3,label:'按整改状态'},
        ],
        dangerType:[{'':3},{'个人防护':6},{'安全管理类':1},{'作业场所类':1},{'消防设施':4},{'电气管理':3},{'设备设施类':4},{'警示标牌':1},{'文明施工':1}],
        dangerStatus:[{'超期未整改':30},{'待整改':7},{'已整改':14}],
        dangerLevel:[{'无':1},{'重大(A级)':2},{'一般(B级)':11},{'一般(C级)':37}],
        pieChartData:[],//饼图数据

        // 统计信息
        count : [
          {value:0,label:''},{value:0,label:''},{value:0,label:''},{value:0,label:''},{value:0,label:''},
          {value:0,label:''},{value:0,label:''},{value:0,label:''},{value:0,label:''},{value:0,label:''},
          {value:0,label:''},{value:0,label:''},{value:0,label:''},{value:0,label:''},{value:0,label:''}
        ],
        // 当前年份
        year : '',
        // 最新隐患
        newDanger : [],
      }
    },
    mounted:function()
    {
      this.year = new Date();
      this.getData();
    },
    methods:{
      // 年份改变
      yearChange(year){
        this.getData();
      },
      // 获取数据
      getData(){


        this.dangerTaskCount();
        this.latestDanger();
        this.chartDataFn();
        //刘杰 增 起
        //先加载缓存，用缓存的数据绘制图表，数据获取到之后再更新
//        window.localStorage.clear();
        let f3=JSON.parse(window.localStorage.getItem('dangerType'));
        if(f3 !=null){
          this.dangerType = f3;
        }

        let f4=JSON.parse(window.localStorage.getItem('dangerStatus'));
        if(f4 !=null){
          this.dangerStatus = f4;
        }

        let f5=JSON.parse(window.localStorage.getItem('dangerLevel'));
        if(f5 !=null){
          this.dangerLevel = f5;
        }
        this.pieDataTypeClick(1);//隐患分析饼图
        //刘杰 增 终
        this.pieData();
      },
      // 统计数据
      dangerTaskCount(){
        let params = {
          companyId : this.$tool.getStorage('LOGIN_USER').companyId,
        }
        this.count.forEach(function (item) {
          item.value=0;
          item.label='';
        })
        this.$store.dispatch('dangerTaskCount', params).then(function(res){
          if(res.success){
            if(Object.entries(res.data).length){
              Object.entries(res.data).forEach(function(it,index){
                this.count[index].label=it[0];
                this.count[index].value=it[1];
              }.bind(this));
              this.pieOfCheck(1);
              this.pieOfChange(1);
              this.pieOfSupervising(1);
            }
          }
        }.bind(this))
      },
      // 最新隐患
      latestDanger(){
        let params = {
          companyId : this.$tool.getStorage('LOGIN_USER').companyId,
        }
        this.$store.dispatch('latestDanger', params).then(function(res){
          if(res.success){
            this.newDanger = res.data;
          }
        }.bind(this))
      },
      // 柱状图
      chartDataFn(){
        let params = {
          companyId : this.$tool.getStorage('LOGIN_USER').companyId,
          year : (this.$tool.formatDateTime(this.year) || '').substring(0,4)
        }
        this.dangerDataLevel=[];
        this.dangerDataDone=[];
        this.dangerDataSum=[];
        this.deptDataDeptname=[];
        this.deptDataDeptDone=[];
        this.deptDataDeptSum=[];
        this.$store.dispatch('chartData', params).then(function(res){
          if(res.success){
            this.dangerLevelCountMap.rows = res.data.dangerLevelCountMap;
            this.deptCountMap.rows = res.data.deptCountMap;

            //数据转换
            this.dangerDataLevel.push('0');
            this.dangerDataDone.push(0);
            this.dangerDataSum.push(0);
            this.deptDataDeptname.push('0');
            this.deptDataDeptDone.push(0);
            this.deptDataDeptSum.push(0);
            if(this.dangerLevelCountMap.rows){
              this.dangerLevelCountMap.rows.forEach(function (item) {
                this.dangerDataLevel.push(item['等级']);
                this.dangerDataDone.push(Number(item['已整改']));
                this.dangerDataSum.push(Number(item['未整改'])+Number(item['已整改']));
              }.bind(this));
            }
           if(this.deptCountMap.rows){
             this.deptCountMap.rows.forEach(function (item) {
               this.deptDataDeptname.push(item['deptName']);
               this.deptDataDeptDone.push(item['已整改']);
               this.deptDataDeptSum.push(item['未整改']+item['已整改']);
             }.bind(this));
           }
            this.dangerDataLevel.push('-');
            this.dangerDataDone.push(0);
            this.dangerDataSum.push(0);
            this.deptDataDeptname.push('-');
            this.deptDataDeptDone.push(0);
            this.deptDataDeptSum.push(0);

            this.$nextTick(function () {
              this.drawDepartmentScore();
              this.drawSecondChart();
            })
          }
        }.bind(this))
      },
      // 饼状图数据
      pieData(){
//        //刘杰 增 起
//        //先加载缓存，用缓存的数据绘制图表，数据获取到之后再更新
//        this.dangerType = JSON.parse(window.localStorage.getItem('dangerType') || '{}');
//        this.dangerStatus = JSON.parse(window.localStorage.getItem('dangerStatus') || '{}');
//        this.dangerLevel = JSON.parse(window.localStorage.getItem('dangerLevel') || '{}');
//        this.pieDataTypeClick(1);//隐患分析饼图
//        //刘杰 增 终
        let year = (this.$tool.formatDateTime(this.year) || '').substring(0,4);
        //隐患统计分析
        this.$http.get('index/analysisHiddenDangers/'+year).then(function (res) {
          if (res.data.success) {
            this.dangerType=[];
            this.dangerStatus=[];
            this.dangerLevel=[];
            if(res.data.data.dangerType){
              let tempObj=res.data.data;
              for(let typeItem in tempObj.dangerType){
                this.dangerType.push({'name':typeItem?typeItem:'其他','value':tempObj.dangerType[typeItem]});
              }
              for(let statusItem in tempObj.dangerStatus){
                this.dangerStatus.push({'name':statusItem?statusItem:'其他','value':tempObj.dangerStatus[statusItem]});
              }
              for(let levelItem in tempObj.dangerLevel){
                this.dangerLevel.push({'name':levelItem?levelItem:'其他','value':tempObj.dangerLevel[levelItem]});
              }

              window.localStorage.setItem('dangerType',JSON.stringify(this.dangerType));//刘杰1015 增
              window.localStorage.setItem('dangerStatus',JSON.stringify(this.dangerStatus));//刘杰1015 增
              window.localStorage.setItem('dangerLevel',JSON.stringify(this.dangerLevel));//刘杰1015 增

              this.radioType=1;
              this.pieDataTypeClick(1);
            }else {
              this.radioType=1;
              this.pieDataTypeClick(1);
            }
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
        });
      },
      // 饼状图按照状态切换
      pieDataTypeClick:function (val) {
        if(val===1){
          this.pieChartData=this.dangerLevel;
        }else if(val===2){
          this.pieChartData=this.dangerType;
        }else{
          this.pieChartData=this.dangerStatus;
        }
        let pieChartData = this.echarts.init(document.getElementById('pieChart'));
        pieChartData.setOption({
          title : {show:false},
          tooltip : {trigger: 'item'},
          legend: {},
          series : [
            {
              name: '隐患数量',
              type: 'pie',
              radius : '55%',
              center: ['50%', '40%'],
              data:this.pieChartData,
              itemStyle: {
                emphasis: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }
          ]
        });
        // 处理点击事件 刘杰 1015 增
        pieChartData.on('click', function (params) {
          this.searchDangerPieData(this.radioType,params.name);
          this.pieDialogVisible = true;
//          alert("饼图点击事件： 名称："+(params.name)+"，值："+(params.value));
        }.bind(this));
      },
      drawDepartmentScore:function () {
        var colors = ['rgba(251,210,73,0.7)'];
        var aCategorys = this.dangerDataLevel.length>2?this.dangerDataLevel:['0','部门二','部门三','部门四','-'];//
        var topdata = this.dangerDataSum.length>2?this.dangerDataSum:[0,90,96, 96, 0];//峰值data
        var aSeries = [{
          name: '总量',
          type: 'line',
          symbol:'circle',
          symbolSize:12,
          itemStyle: {
            normal: {
              color: '#F6A623',
              borderColor:"#ffffff",
              borderWidth:2
            }
          },
          lineStyle: {
            normal: {
              opacity: 0
            }
          },
          data:this.dangerDataSum.length>2?this.dangerDataSum:[0,90,96, 96, 0]
        },{
          name: '已完成',
          type: 'line',
          symbol:'circle',
          symbolSize:12,
          lineStyle: {
            normal: {
              opacity: 0
            }
          },
          itemStyle: {
            normal: {
              color: '#A5A7AD',
              borderColor:"#ffffff",
              borderWidth:2
            }
          },
          data: this.dangerDataDone.length>2?this.dangerDataDone:['0',77, 89, 79, 0]
        }];

        aCategorys.forEach(function(v, i, a) {
          var name = v;
          if (v !== '') {
            var data = [];
            for (var j = 0; j < aCategorys.length; j++) {
              data.push('-');
            }
            data[i - 1] = 0;
            data[i] = topdata[i];
            data[i + 1] = 0;
            aSeries.push({
              name: name,
              type: 'pictorialBar',
              smooth: false,
              legendHoverLink:false,
              symbol: 'path://M0,10 L10,10 C5.5,10 5.5,5 5,0 C4.5,5 4.5,10 0,10 z',
              barCategoryGap: '-130%',
              areaStyle: {
                normal: {
                  color: {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    colorStops: [{
                      offset: 0,
                      color: colors[i - 1] // 0% 处的颜色
                    }, {
                      offset: 1,
                      color: colors[i - 1] // 100% 处的颜色
                    }],
                    globalCoord: false // 缺省为 false
                  }
                }
              },
              data: data,
            });
          }
        });

        let dangerLevelData = this.echarts.init(document.getElementById('dangerLevelCountHistogram'));
        dangerLevelData.setOption({
          title:{
            text:'隐患分级统计'
          },
          color: colors,
          tooltip: {
            trigger: 'axis',
            formatter: function(params) {

              var rValue =params[0].name+'<br>';
              params.forEach(function(v, i, a) {
                if (v.data !== 0 && v.data !== "-" && v.seriesType == "line") {
                  rValue+='<span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:' + v.color + '"></span>'+v.seriesName + ':' + v.data +'<br>';
                }
              })
              return rValue;
            }
          },
          legend: {
            icon: 'circle',
            itemWidth: 14,
            itemHeight: 14,
            itemGap: 15,
            data: ['总量','已完成'],
            textStyle: {
              fontSize: 14,
              color: '#424242'
            }
          },
          xAxis: [{
            type: 'category',
            boundaryGap: false,
            data: aCategorys,
            axisLabel:{
              textStyle:{
                fontSize:14
              }
            },
            splitLine: {
              show: true,
              lineStyle:{
                color:'#f7f7f7'
              }
            }
          }],
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          yAxis: [{
            type: 'value',
            splitLine: {
              show: true,
              lineStyle:{
                color:'#f7f7f7'
              }
            }
          }],
          series: aSeries
        });

      },
      drawSecondChart:function () {
        var colors = ['rgba(251,210,73,0.7)'];
        var aCategorys = this.deptDataDeptname.length>2?this.deptDataDeptname:['0','部门二','部门三','部门四','部门五','部门六','-'];//
        var topdata = this.deptDataDeptSum.length>2?this.deptDataDeptSum:[0,90,96, 96, 97, 95];//峰值data
        var aSeries = [{
          name: '总量',
          type: 'line',
          symbol:'circle',
          symbolSize:12,
          itemStyle: {
            normal: {
              color: '#F6A623',
              borderColor:"#ffffff",
              borderWidth:2
            }
          },
          lineStyle: {
            normal: {
              opacity: 0
            }
          },
          data:this.deptDataDeptSum.length>2?this.deptDataDeptSum:[0,90,96, 96, 97, 95]
        },{
          name: '已完成',
          type: 'line',
          symbol:'circle',
          symbolSize:12,
          lineStyle: {
            normal: {
              opacity: 0
            }
          },
          itemStyle: {
            normal: {
              color: '#A5A7AD',
              borderColor:"#ffffff",
              borderWidth:2
            }
          },
          data: this.deptDataDeptDone.length>2?this.deptDataDeptDone:['0',77, 89, 79, 70, 69]
        }];

        aCategorys.forEach(function(v, i, a) {
          var name = v;
          if (v !== '') {
            var data = [];
            for (var j = 0; j < aCategorys.length; j++) {
              data.push('-');
            }
            data[i - 1] = 0;
            data[i] = topdata[i];
            data[i + 1] = 0;
            aSeries.push({
              name: name,
              type: 'pictorialBar',
              smooth: false,
              legendHoverLink:false,
              symbol: 'path://M0,10 L10,10 C5.5,10 5.5,5 5,0 C4.5,5 4.5,10 0,10 z',
              barCategoryGap: '-130%',
              areaStyle: {
                normal: {
                  color: {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    colorStops: [{
                      offset: 0,
                      color: colors[i - 1] // 0% 处的颜色
                    }, {
                      offset: 1,
                      color: colors[i - 1] // 100% 处的颜色
                    }],
                    globalCoord: false // 缺省为 false
                  }
                }
              },
              data: data,
            });
          }
        });

        let deptCountData=this.echarts.init(document.getElementById('deptCountHistogram'));
        deptCountData.setOption({
          title:{
            text:'子公司隐患统计'
          },
          color: colors,
          tooltip: {
            trigger: 'axis',
            formatter: function(params) {

              var rValue =params[0].name+'<br>';
              params.forEach(function(v, i, a) {
                if (v.data !== 0 && v.data !== "-" && v.seriesType == "line") {
                  rValue+='<span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:' + v.color + '"></span>'+v.seriesName + ':' + v.data +'<br>';
                }
              })
              return rValue;
            }
          },
          legend: {
            icon: 'circle',
            itemWidth: 14,
            itemHeight: 14,
            itemGap: 15,
            data: ['总量','已完成'],
            textStyle: {
              fontSize: 14,
              color: '#424242'
            }
          },
          xAxis: [{
            type: 'category',
            boundaryGap: false,
            data: aCategorys,
            axisLabel:{
              textStyle:{
                fontSize:14
              }
            },
            splitLine: {
              show: true,
              lineStyle:{
                color:'#f7f7f7'
              }
            }
          }],
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          yAxis: [{
            type: 'value',
            splitLine: {
              show: true,
              lineStyle:{
                color:'#f7f7f7'
              }
            }
          }],
          series: aSeries
        });
      },
      //0926刘杰 概况中隐患检查饼图
      pieOfCheck:function (val) {
        let pieChartData = this.echarts.init(document.getElementById('pieOfCheck'));
        var checksum=this.count[0].value + this.count[1].value + this.count[2].value + this.count[3].value+ this.count[4].value+ this.count[5].value;
        pieChartData.setOption({
//          title : {text:'应急响应',textAlign:'center'},
//          tooltip : {trigger: 'item'},
//          legend: {},
          series : [
            {
              name: ' 现有公司（家）',
              type: 'pie',
              radius: ['90%', '70%'],
              startAngle: 225,
              color: [new this.echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                offset: 0,
                color: '#f125ff'
              }, {
                offset: 1,
                color: '#2dcbff'
              }]), "transparent"],
              labelLine: {
                normal: {
                  show: false
                }
              },
              label: {
                normal: {
                  position: 'center'
                }
              },
              data: [{
                value: 75,
//                name: '现有公司',
                label: {
                  normal: {
//                    formatter: '12%',
                    textStyle: {
                      color: '#000',
                      fontSize: 14

                    }
                  }
                }
              }, {
                value: 25,
                name: '%',
                label: {
                  normal: {
                    formatter: function(params){ return '\n'+checksum; },
                    textStyle: {
                      color: '#ED704E',
                      fontSize: 26

                    }
                  }
                }
              },
                {
                  value: 0,
                  name: '%',
                  label: {
                    normal: {
                      formatter: '检查中',
                      textStyle: {
                        color: '#222',
                        fontSize: 14

                      }
                    }
                  }
                }
              ]
            }
          ]
        });
      },

      //0926刘杰 概况中隐患整改饼图
      pieOfChange:function (val) {
        let pieChartData = this.echarts.init(document.getElementById('pieOfChange'));
        var changesum=this.count[6].value + this.count[7].value + this.count[8].value + this.count[13].value;

        pieChartData.setOption({
//          title : {text:'应急响应',textAlign:'center'},
//          tooltip : {trigger: 'item'},
//          legend: {},
          series : [
            {
              name: ' 现有公司（家）',
              type: 'pie',
              radius: ['90%', '70%'],
              startAngle: 225,
              color: [new this.echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                offset: 0,
                color: '#f125ff'
              }, {
                offset: 1,
                color: '#2dcbff'
              }]), "transparent"],
              labelLine: {
                normal: {
                  show: false
                }
              },
              label: {
                normal: {
                  position: 'center'
                }
              },
              data: [{
                value: 75,
//                name: '现有公司',
                label: {
                  normal: {
//                    formatter: '应急响应',
                    textStyle: {
                      color: '#000',
                      fontSize: 14

                    }
                  }
                }
              }, {
                value: 25,
                name: '%',
                label: {
                  normal: {
                    formatter: function(params){ return '\n'+changesum; },
                    textStyle: {
                      color: '#ED704E',
                      fontSize: 26

                    }
                  }
                }
              },
                {
                  value: 0,
                  name: '%',
                  label: {
                    normal: {
                      formatter: '整改中',
                      textStyle: {
                        color: '#222',
                        fontSize: 14

                      }
                    }
                  }
                }
              ]
            }
          ]
        });
      },

      //0926刘杰 概况中隐患督办饼图
      pieOfSupervising:function (val) {
        let pieChartData = this.echarts.init(document.getElementById('pieOfSupervising'));
        var supervisingsum=this.count[9].value + this.count[10].value + this.count[11].value + this.count[12].value;
        pieChartData.setOption({
//          title : {text:'应急响应',textAlign:'center'},
//          tooltip : {trigger: 'item'},
//          legend: {},
          series : [
            {
              name: ' 现有公司（家）',
              type: 'pie',
              radius: ['90%', '70%'],
              startAngle: 225,
              color: [new this.echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                offset: 0,
                color: '#f125ff'
              }, {
                offset: 1,
                color: '#2dcbff'
              }]), "transparent"],
              labelLine: {
                normal: {
                  show: false
                }
              },
              label: {
                normal: {
                  position: 'center'
                }
              },
              data: [{
                value: 75,
//                name: '现有公司',
                label: {
                  normal: {
//                    formatter: '应急响应',
                    textStyle: {
                      color: '#000',
                      fontSize: 14

                    }
                  }
                }
              }, {
                value: 25,
                name: '%',
                label: {
                  normal: {
                    formatter: function(params){ return '\n'+supervisingsum; },
                    textStyle: {
                      color: '#ED704E',
                      fontSize: 26

                    }
                  }
                }
              },
                {
                  value: 0,
                  name: '%',
                  label: {
                    normal: {
                      formatter: '督办中',
                      textStyle: {
                        color: '#222',
                        fontSize: 14

                      }
                    }
                  }
                }
              ]
            }
          ]
        });
      },

      //刘杰 1015 增 起
      //隐患分析点击事件对话框
      searchDangerPieData:function (radioType,name) {

        this.pieChartDialogData=[];
        let params={needChange:1};
//        {needChange:1,downward:true};
        let startDate = new Date();
        startDate.setFullYear(this.safeSumYear,1,1);
        let endDate = new Date();
        endDate.setFullYear(this.safeSumYear,12,31);
        params.startDate=startDate;params.endDate=endDate;
        switch(radioType){
          case 1:params.hiddenDangerLevel=name;break;
          case 2:params.dangerType=name;break;
          case 3:break;

        }
        this.dangerTableLoading=true;
        this.$http.post('danger/inspectListPublic/find',params).then(function (res) {
          if (res.data.success) {
            this.dangerTableLoading=false;
            let tempList=[];
            tempList=res.data.data;
            if(radioType==3){//记录按整改状态区分，与另外两种情况不同需单独处理
              let haschanged = [];
              let overtime = [];
              let nochange = [];
              for(let i=0;i<tempList.length;i++){
                if(tempList[i].changeTime) {
                  haschanged.push(tempList[i]);
                }else if(tempList[i].changeOverTime&&(tempList[i].changeTime===null)){
                  overtime.push(tempList[i]);
                }else{
                  nochange.push(tempList[i]);
                }
              }
              if(name=="已整改"){
                this.pieChartDialogData=haschanged;
              }else if(name=="超期未整改"){
                this.pieChartDialogData=overtime;
              }else{
                this.pieChartDialogData=nochange;
              }

            }else{
              this.pieChartDialogData=tempList;
            }
          }
        }.bind(this)).catch(function (err) {
          this.$message.error('隐患数据获取失败！');
          console.log(err);
        });
      },
      //刘杰 1015 增 终
    },

  }



</script>
<style>
  #firstPage {
    display:flex;

    /*刘杰0925修改*/
    width:98%;

    margin:0 auto;
  }
  #firstPage .left{
    width:55%;
    height:100%;
    margin-right:50px;
    display:flex;
    flex-direction: column;
  }
  #firstPage .left .ul{
    width:100%;
    padding:0;
    margin:0 0 20px 0;
    list-style: none;
  }
  #firstPage .left .ul li{
    width:24%;
    float:left;
    height:50px;
    line-height:50px;
    border-right:1px solid #ccc;
    text-align:center;
    background:#fff;
    margin-bottom : 10px;
  }
  #firstPage .left .ul li:nth-last-child(4n-2) {
    border:none;
  }
  #firstPage .left .div1{
    width:100%;
    height:470px;
    background:#fff;
    margin-bottom:10px;
  }
  #firstPage .left .div1 .title{
    padding:10px;
    border-bottom:1px solid #ccc;
  }
  #firstPage .right{
    width:40%;
    height:100%;
    display: flex;
    flex-direction: column;
  }
  #firstPage .right .latestDanger{
    display: block;
    padding:0;
    margin:0;
    width:100%;
    line-height:20px;
    list-style:none;
    background:#fff;
    margin-bottom:10px;
  }
  #firstPage .right .latestDanger li{
    height:40px;
    line-height:40px;
    text-indent: 1em;
    border-bottom:1px solid #f1f1f1;
  }
  #firstPage .right .div1{
    width:100%;
    height:400px;
    background:#fff;
    margin-bottom:10px;
  }
  /*刘杰0925修改*/
  .countLabelColumn{
    font-size: 14px;
    text-align: center;
    color:gray;
  }
  /*刘杰0925修改*/
  .countValueColumn{
    font-size: 26px;
    text-align: center;
    color: #ED704E;
  }
</style>
