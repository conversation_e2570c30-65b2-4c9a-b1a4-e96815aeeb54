<template>
  <div id="">
    <el-container class="container">
      <el-main>
        <el-form ref="form" :model="form" label-width="5px">
          <el-row>
            <el-col :span="4">
              <el-form-item>
                <el-input
                  clearable
                  placeholder="任务名称"
                  v-model="form.title"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :offset="1" :span="1">
              <el-button type="primary" @click="searchBtnClickHandle"
                >搜索</el-button
              >
            </el-col>
            <el-col :offset="1" :span="1">
              <!--公司/组织者-->
              <el-button
                type="success"
                icon="el-icon-plus"
                @click="$router.push({ name: 'ReportStatisticsAdd' })"
                >发布任务</el-button
              >
            </el-col>
          </el-row>
          <el-row>
            <el-table border :data="tableData.list" style="width: 100%">
              <el-table-column
                type="index"
                label="编号"
                width="100"
                align="center"
                label-class-name="header-style"
              >
              </el-table-column>
              <el-table-column
                prop="title"
                label="任务名称"
                min-width="150"
                label-class-name="header-style"
              >
              </el-table-column>
              <el-table-column
                prop="remark"
                label="描述"
                width="150"
                label-class-name="header-style"
              >
              </el-table-column>
              <el-table-column
                prop="createTime"
                :formatter="formatDateTime"
                label="创建时间"
                width="150"
                label-class-name="header-style"
              >
              </el-table-column>
              <el-table-column
                prop="createTime"
                :formatter="formatDateTime"
                label="状态"
                width="150"
                label-class-name="header-style"
              >
                <template slot-scope="scope">
                  <!--{{scope.row.status}}-->
                  <span v-if="scope.row.status == 0">草稿</span>
                  <span v-else-if="scope.row.status == 1">发布中</span>
                  <span v-else-if="scope.row.status == 2">已完成</span>
                </template>
              </el-table-column>
              <el-table-column
                label="操作"
                label-class-name="header-style"
                align="left"
                width="250"
              >
                <template slot-scope="scope">
                  <template>
                    <el-button
                      size="mini"
                      plain
                      type="primary"
                      @click="itemUpdateClick(scope.row)"
                      >修改</el-button
                    >
                    <el-button
                      size="mini"
                      plain
                      type="danger"
                      @click="itemDeleteClick(scope.row)"
                      >删除</el-button
                    >
                    <el-button
                      size="mini"
                      plain
                      type="primary"
                      @click="view(scope.row)"
                      >汇总查看</el-button
                    >
                  </template>
                </template>
              </el-table-column>
            </el-table>
            <el-pagination
              background
              layout="prev, pager, next"
              :current-page="tableData.pageNum"
              :page-size="form.pageSize"
              :total="tableData.total"
              @current-change="disasterPageChangeHandle"
            >
            </el-pagination>
          </el-row>
        </el-form>
      </el-main>
    </el-container>
  </div>
</template>
<script>
export default {
  name: "",
  data() {
    return {
      // 搜索
      form: {
        // 名称
        // newsName : '',
        // title : '',
        // 类型
        //          newsType : '',
        // 当前页
        pageCurrent: 1,
        // 页数大小
        pageSize: 10,
      },
      // 查看人数对话框
      lookNumDialog: {
        // 搜索条件
        form: {
          studyId: "",
          // 当前页
          pageCurrent: 1,
          // 页数大小
          pageSize: 10,
          userName: "",
        },
        // 是否显示
        isShow: false,
        // 标题
        title: "",
        // 表格数据
        tableData: [],
      },
      assist: {
        // 类型
        newsType: [
          { value: "党建巡礼", label: "党建巡礼" },
          { value: "廉洁教育", label: "廉洁教育" },
          { value: "理论学习", label: "理论学习" },
          { value: "党务学习", label: "党务学习" },
        ],
      },
      tableData: {},
      // 角色 0 组织者或公司      1 部门        2  班组
      role: 0,
    };
  },
  mounted() {
    this.init();
  },
  watch: {
    $route(to, from) {
      if (to.name === "ReportStatisticsIndex") {
        this.init();
      }
    },
  },
  methods: {
    // 初始化
    init() {
      // this.judgeUserRole();
      // 搜索
      this.searchBtnClickHandle();
    },
    judgeUserRole() {
      // 获取权限按钮
      let btns = this.$tool.getPowerBtns("eduTrainingMenu", this.$route.path);
      //        console.log('btns', btns)
      // 公司
      if (btns.includes("addBtn")) {
        this.role = 4;
      }
    },
    // 清空数据
    clear() {},
    // 格式化时间
    formatDateTime(row, column, cellValue) {
      let pro = column.property;
      let num = 10;
      let str = this.$tool.formatDateTime(row[pro]) || "";
      return str ? str.substring(0, num) : str;
    },
    formatAllDateTime(row, column, cellValue) {
      let pro = column.property;
      let str = this.$tool.formatDateTime(row[pro]) || "";
      return str;
    },
    // 分页
    disasterPageChangeHandle(page) {
      this.form.pageCurrent = page;
      if (this.isMore) {
        this.saveScoreBtnClickHandle();
      } else {
        this.searchBtnClickHandle();
      }
    },
    // 搜索按钮
    searchBtnClickHandle() {
      let _this = this;
      let params = Object.assign({}, this.form);

      let url = "sys/sysManageFile/listReportForm";

      _this.$http.post(url, params).then(function (res) {
        if (res.data.success) {
          _this.tableData = res.data.data;
        } else {
          // _this.$message({
          //   showClose: true,
          //   message: "操作3失败！",
          //   type: "error",
          // });
        }
      });
    },
    // 汇总查看
    view(row) {
      let params = {
        id: row.id,
        status: "view",
      };
      this.$router.push({ name: "ReportStatisticsView", params: params });
    },
    // 修改
    itemUpdateClick(row) {
      let params = {
        id: row.id,
        status: "edit",
      };

      this.$router.push({ name: "ReportStatisticsAdd", params: params });
    },
    // 删除按钮
    itemDeleteClick(row) {
      let _this = this;
      this.$confirm("此操作将永久删除, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(
        function () {
          let url = "sys/sysManageFile/delReportForm";
          let params = {
            id: row.id,
          };
          _this.$http.post(url, params).then(function (res) {
            // if (res.data.success) {
              _this.$message.success("操作成功");
              _this.searchBtnClickHandle();
            // } else {
              // _this.$message({
              //   showClose: true,
              //   message: "操作1失败！",
              //   type: "error",
              // });
            // }
          });

          return;
          this.$store
            .dispatch("eduNewsDelete", {
              id: row.id,
            })
            .then(
              function (res) {
                if (res.data.success) {
                  this.$message({
                    type: "success",
                    message: "删除成功",
                  });
                  this.searchBtnClickHandle();
                } else {
                  this.$message({
                    type: "error",
                    message: res.message || "删除失败！！",
                  });
                }
              }.bind(this)
            );
        }.bind(this)
      );
    },
    // 批量保存分数
    saveScoreBtnClickHandle() {
      this.$store.dispatch("eduEntryTrainingBatchInputScores", this.more).then(
        function (res) {
          if (res.data.success) {
            this.$message({
              type: "success",
              message: "打分成功",
            });
            this.searchBtnClickHandle();
          } else {
            this.$message({
              type: "error",
              message: res.message || "打分失败！！",
            });
          }
        }.bind(this)
      );
    },
    // 培训时间
    trainingDateChange(val) {
      this.form.startDate = val ? val[0] : "";
      this.form.endDate = val ? val[1] : "";
    },
  },
};
</script>
<style >
.container {
  background: #fff;
  padding: 0 20px;
}
.row {
  margin-top: 10px;
}
.container .el-table__fixed-right{
  height: 100%;
}
</style>
