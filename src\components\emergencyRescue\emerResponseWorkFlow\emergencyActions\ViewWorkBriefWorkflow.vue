<template>
  <div id="viewWorkBrief">
    <div class="background-style">
      <el-col :span="16" :offset="4" class="success-background-title">{{form.name}}</el-col>
      <el-col :span="16" :offset="4">
        <el-form :model="form"  label-width="120px">
          <el-col :span="24">
            <el-col :span="12">
              <el-form-item label="编制部门：">
                {{form.dept}}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="记录：">
                {{form.type}}
              </el-form-item>
            </el-col>
          </el-col>
        </el-form>
      </el-col>
      <el-col :span="20" :offset="2">
        <table class="simple-table">
          <tr><th>{{tableHead[0]}}</th><td v-for="item in tableContent.date">{{item}}</td></tr>
          <tr><th>{{tableHead[1]}}</th><td v-for="item in tableContent.defenseWork">{{item}}</td></tr>
          <tr><th>{{tableHead[2]}}</th><td v-for="item in tableContent.dutyPeople">{{item}}</td></tr>
          <tr><th>{{tableHead[3]}}</th><td v-for="item in tableContent.disasterSituation">{{item}}</td></tr>
          <tr><th>{{tableHead[4]}}</th><td v-for="item in tableContent.emergency">{{item}}</td></tr>
          <tr><th>{{tableHead[5]}}</th><td v-for="item in tableContent.emergencyHandle">{{item}}</td></tr>
          <tr><th>{{tableHead[6]}}</th><td v-for="item in tableContent.emergencyAfter">{{item}}</td></tr>
        </table>
        <div style="margin-top: 10px">
          <el-pagination
            background
            layout="prev, pager, next"
            :current-page="currentPage"
            :page-size="pageSize"
            :total="totalItem"
            @current-change="currentPageClick">
          </el-pagination>
        </div>
        <div style="margin-top: 10px;margin-right: 20px">
          <el-button style="float: right" type="primary" @click="$router.go(-1)">返回</el-button>
        </div>
      </el-col>
    </div>
  </div>
</template>
<script>
  import {mapGetters} from 'vuex'
  export default {
    name: 'viewWorkBrief',
    data() {
      return {
        form:{
          name:'',
          dept:'',
          type:'',
          startId:'',
          deptId:''
        },
        tableHead:['上报日期时间','现阶段防御工作部署落实情况','值班人员到位情况','受损受灾情况','突发事件情况','事件现场应急处置及抢险救灾情况','事件应急结束善后处置情况'],
        tableContent:{},
        currentPage:0,
        totalItem:0,
        pageSize:6,
      }
    },
    created:function () {
      if(this.$route.params.emerData){
        this.form.name=this.$route.params.emerData.name;
        if(this.$route.params.currentCompany){//如果传了特定的公司，则查特定公司的简报
          this.form.dept=this.$route.params.currentCompany.label;
          this.form.deptId=this.$route.params.currentCompany.value;
        }else{
          this.form.dept=this.$route.params.emerData.companyName;
          this.form.deptId=this.$route.params.emerData.companyId;
        }
        this.form.startId=this.$route.params.emerData.startPlanId;
        this.form.type='应急工作情况简报';
        this.searchWorkBrief();
      }
    },
    watch:{
      $route(to, from){
        if(this.$route.name==='viewWorkBriefWorkflow'){
          if(this.$route.params.emerData){
            this.form.name=this.$route.params.emerData.name;
            if(this.$route.params.currentCompany){//如果传了特定的公司，则查特定公司的简报
              this.form.dept=this.$route.params.currentCompany.label;
              this.form.deptId=this.$route.params.currentCompany.value;
            }else{
              this.form.dept=this.$route.params.emerData.companyName;
              this.form.deptId=this.$route.params.emerData.companyId;
            }
            this.form.startId=this.$route.params.emerData.startPlanId;
            this.form.type='应急工作情况简报';
            this.searchWorkBrief();
          }
        }
      }
    },
    methods:{
      searchWorkBrief:function () {
        let params=new URLSearchParams;
        params.append("pageCurrent",1);
        this.currentPage=1;
        this.sendRequest(params);
      },
      currentPageClick:function (val) {
        if (val) {
          this.currentPage = val;
          let params = new URLSearchParams;
          params.append("pageCurrent", Number(val));
          this.sendRequest(params);
        }
      },
      sendRequest:function (params) {
        params.append("companyId",this.form.deptId);
        params.append("planPublicId",this.form.startId);
        params.append("pageSize",6);
        this.$http.post('workBrief/findBriefOrganize',params).then(function (res) {
          this.tableContent.date=[];
          this.tableContent.defenseWork=[];
          this.tableContent.dutyPeople=[];
          this.tableContent.disasterSituation=[];
          this.tableContent.emergency=[];
          this.tableContent.emergencyHandle=[];
          this.tableContent.emergencyAfter=[];
          if(res.data.success){
            this.totalItem=res.data.data.page.total;
            this.tableContent=res.data.data.workBrief;
            for(let i=0;i<this.tableContent.date.length;i++){
              this.tableContent.date[i]=this.transferTime(this.tableContent.date[i],null,true);
            }
            let tempLength=this.tableContent.date.length;
            for(let i=0;i<(6-tempLength);i++){
              this.tableContent.date.push('');
              this.tableContent.defenseWork.push('');
              this.tableContent.dutyPeople.push('');
              this.tableContent.disasterSituation.push('');
              this.tableContent.emergency.push('');
              this.tableContent.emergencyHandle.push('');
              this.tableContent.emergencyAfter.push('');
            }
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message({
            showClose: true,
            message: '网络错误，请尝试重登录',
            type: 'error'
          });
        }.bind(this));
      },
    }
  }
</script>
<style>
</style>
