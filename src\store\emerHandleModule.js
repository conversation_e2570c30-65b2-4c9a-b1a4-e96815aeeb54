/**
 * Created by 小炮子子 on 2018-03-22.
 */
import http from '../../src/assets/functions/axiosServer'
import tool from '../../src/components/common/tool'
// 应急处理模块
export default {
  state: {
    // 分类列表
    planTypeList : [],
    // 知识点标签列表
    labelList : [],
    // 知识点列表
    knowledgeList : [],
    // 参考预案列表
    referPlanList : [],
    // 物资类型列表
    goodsTypeList : [],
    // 获取灾后处置预案列表
    postDisasterTreatmentList : [],
    // 获取应急预案预案列表
    emgPlanList : [],
    // 应急事件----添加的数据
    emgEvent : {},
  },
  getters: {},
  mutations: {
    // 获取分类列表
    planTypeListMutation(state, list){
      state.planTypeList = list;
    },
    // 获取知识点标签列表
    labelListMutation(state, list){
      state.labelList = list;
    },
    // 获取知识点列表
    knowledgeListMutation(state, list){
      state.knowledgeList = list;
    },
    // 获取参考预案列表
    referPlanListMutation(state, list){
      state.referPlanList = list;
    },
    // 获取物资类型列表
    goodsTypeListMutation(state, list){
      state.goodsTypeList = list;
    },
    // 灾后处置--预案列表
    postDisasterTreatmentListMutation(state, list){
      state.postDisasterTreatmentList = list;
    },
    // 紧急预案--列表
    emgPlanListMutation(state, list){
      state.emgPlanList = list;
    },
    // 应急事件----添加的数据
    emgEventMutation(state, list){
      state.emgEvent = list;
    },
    // 应急事件---处置事件--发布事件
    emgHandlePublicMutation(state, list){
      state.emgHandlePublic = list;
    },
  },
  actions : {
    // 获取应急响应分类列表
    planTypeListAction({ commit }){
      http.get('emgType/getAll/'+tool.getStorage('LOGIN_USER').companyId).then(function (res) {
        let typeTree = res.data.data;
        let result = [];
        for(let i=0;i<typeTree.length;i++){
          let tempArray={value:typeTree[i].id,label:typeTree[i].typeName};
          if(typeTree[i].subTypes.length){
            tempArray.children=[];
            for(let j=0;j<typeTree[i].subTypes.length;j++){
              tempArray.children.push({value:typeTree[i].subTypes[j].id,label:typeTree[i].subTypes[j].typeName});
            }
          }
          result.push(tempArray);
        }
        commit('planTypeListMutation',result)
      }.bind(this)).catch(function (err) {
        console.log("我的错误：" + err);
      }.bind(this));
    },
    // 获取知识点标签列表
    labelListAction({ commit }){
      http.post('label/find').then(function (res) {
        let typeTree = res.data.data.list;
        let result = typeTree.map(function(ele,index){
          return { "value" : ele.id, "label" : ele.label };
        })
        commit('labelListMutation',result)
      }.bind(this)).catch(function (err) {
        console.log("我的错误：" + err);
      }.bind(this));
    },
    // 获取知识点列表
    knowledgeListAction({ commit }, params){
      http.post('knowledge/findJson', params).then(function (res) {
        let typeTree = res.data.data;
        commit('knowledgeListMutation',typeTree)
      }.bind(this)).catch(function (err) {
        console.log("我的错误：" + err);
      }.bind(this));
    },
    // 获取知识点列表
    knowledgeList({ commit }, params){
      return new Promise(function(resolve, reject){
        http.post('knowledge/findJson', params).then(function (res) {
          resolve(res.data);
        }.bind(this))
      })
    },
    // 获取参考预案--通过分类
    referPlanListAction({ commit }, params){
      http.post('emgHandle/find', params).then(function (res) {
        let typeTree = res.data.data.list;
        let result = [];
        result = typeTree.map(function(it){
          return {
            value : it.name,
            label : it.name,
            emgHandleLists : it.emgHandleLists,  // 执行清单
            remark : it.remark,  // 注意事项
            emgHandleGoodsList : it.emgHandleGoodsList,  // 物资
          }
        })
        commit('referPlanListMutation',result)
      }.bind(this)).catch(function (err) {
        console.log("我的错误：" + err);
      }.bind(this));
    },
    // 灾后处置--添加
    postDisasterTreatmentAddAction({ commit }, params){
      return new Promise(function(resolve, reject){
        http.post('emgHandle/add', params).then(function (res) {
          resolve(res.data);
        }.bind(this))
      })
    },
    // 灾后处置--修改
    postDisasterTreatmentUpdateAction({ commit }, params){
      return new Promise(function(resolve, reject){
        http.post('emgHandle/update', params).then(function (res) {
          resolve(res.data);
        }.bind(this))
      })
    },
    // 灾后处置--删除
    postDisasterTreatmentDeleteAction({ commit }, params){
      return new Promise(function(resolve, reject){
        http.post('emgHandle/delete', params).then(function (res) {
          resolve(res.data);
        }.bind(this))
      })
    },
    // 灾后处置--列表
    postDisasterTreatmentListAction({ commit }, params){
      http.post('emgHandle/find', params).then(function (res) {
        commit('postDisasterTreatmentListMutation',res.data.data)
      }.bind(this))
    },
    // 灾后处置--列表---Promise
    postDisasterTreatmentListPromiseAction({ commit }, params){
      return new Promise(function(resolve, reject){
        http.post('emgHandle/find', params).then(function (res) {
          resolve(res.data.data)
        }.bind(this))
      })
    },
    // 灾后处置--获取单条数据
    postDisasterTreatmentOneAction({ commit }, params){
      return new Promise(function(resolve, reject){
        http.post('emgHandle/find', params).then(function (res) {
          //commit('postDisasterTreatmentOneMutation',res.data.data.list[0])
          resolve(res.data.data.list[0])
        }.bind(this))
      })
    },
    // 应急预案--列表
    emgPlanListAction({ commit }, params){
      http.post('emgPlan/find', params).then(function (res) {
        commit('emgPlanListMutation',res.data.data)
      }.bind(this))
    },
    // 获取物资
    goodsTypeListAction({ commit }, params){
      http.post('emgGoodsType/find', params).then(function (res) {
        let typeTree = res.data.data.list;
        let result = typeTree.map(function(it){
          return {
            value : it.id,
            label : it.name
          }
        })
        commit('goodsTypeListMutation',result)
      });
    },
    // #########事件响应##############
    // 应急事件----获取事件等级
    emgEventCountEventLevelAction({ commit }, params){
      return new Promise(function(resolve, reject){
        http.post('emgEvent/countEventLevel', params).then(function (res) {
          //commit('emgEventCountEventLevelMutation',res.data.data)
          resolve(res.data)
        }.bind(this))
      })
    },
    // 应急事件----添加
    emgEventAddAction({ commit }, params){
      return new Promise(function(resolve, reject){
        http.post('emgEvent/add', params).then(function (res) {
          //commit('emgEventMutation',res.data.data)
          resolve(res.data)
        }.bind(this))
      })
    },
    // 应急事件----查看
    emgEventFindAction({ commit }, params){
      return new Promise(function(resolve, reject){
        http.post('emgEvent/find', params).then(function (res) {
          resolve(res.data)
        }.bind(this))
      })
    },
    // 应急事件----删除
    emgEventDeleteAction({ commit }, params){
      return new Promise(function(resolve, reject){
        http.post('emgEvent/delete', params).then(function (res) {
          resolve(res.data)
        }.bind(this))
      })
    },
    // 应急事件----更新
    emgEventUpdateAction({ commit }, params){
      return new Promise(function(resolve, reject){
        http.post('emgEvent/update', params).then(function (res) {
          resolve(res.data)
        }.bind(this))
      })
    },
    // 事件与预案关联
    emgHandlePublicAddOrUpdateAction({ commit }, params){
      return new Promise(function(resolve, reject){
        http.post('emgHandlePublic/add', params).then(function (res) {
          resolve(res.data);
        }.bind(this))
      })
    },
    // 处理事件--上传备注
    emgHandlePublicUpdateAction({ commit }, params){
      return new Promise(function(resolve, reject){
        http.post('emgHandlePublic/update', params).then(function (res) {
          resolve(res.data);
        }.bind(this))
      })
    },
    // 发布事件---查询
    emgHandlePublicFindAction({ commit }, params){
      return new Promise(function(resolve, reject){
        http.post('emgHandlePublic/find', params).then(function (res) {
          resolve(res.data);
        }.bind(this))
      })
    },
    // 查看库存--通过物资名称---GET请求
    emgGoodsFindGetAction({ commit }, params){
      let goodsName = params.goodsName;
      return new Promise(function(resolve, reject){
        http.get('emgGoods/find?goodsName=' + goodsName).then(function (res) {
          resolve(res.data);
        }.bind(this))
      })
    },
    // 发布事件---单条勾选完成执行清单----TEST
    emgHandleListPublicAddOrUpdateAction({ commit }, params){
      return new Promise(function(resolve, reject){
        http.post('emgHandleListPublic/update', params).then(function (res) {
          resolve(res.data);
        }.bind(this))
      })
    },
    // 文件---获取列表
    fileFindAction({ commit }, params){
      return new Promise(function(resolve, reject){
        http.post('file/find', params).then(function (res) {
          resolve(res.data);
        }.bind(this))
      })
    },
    // 文件---删除
    fileDeleteAction({ commit }, params){
      return new Promise(function(resolve, reject){
        http.post('file/delete', params).then(function (res) {
          resolve(res.data);
        }.bind(this))
      })
    },
    // 文件---下载
    fileDownloadAction({ commit }, params){
      return new Promise(function(resolve, reject){
        http({ // 用axios发送post请求
          method: 'post',
          url: '/file/download', // 请求地址
          data: params, // 参数
          responseType: 'blob' // 表明返回服务器返回的数据类型
        }).then(function (res) {
          resolve(res);
        }.bind(this))
      })
    },
    // 重大事件报告--添加
    emgHandleReportAddAction({ commit }, params){
      return new Promise(function(resolve, reject){
        http.post('emgHandleReport/add', params).then(function (res) {
          resolve(res.data);
        }.bind(this))
      })
    },
    // 重大事件报告--添加--根据用户名称搜索用户信息
    userFindAction({ commit }, params){
      return new Promise(function(resolve, reject){
        http.get('user/find?username=' + params).then(function (res) {
          resolve(res.data);
        }.bind(this))
      })
    },
    // 重大事件报告--查看
    emgHandleReportFindAction({ commit }, params){
      return new Promise(function(resolve, reject){
        http.post('emgHandleReport/find', params).then(function (res) {
          resolve(res.data);
        }.bind(this))
      })
    },
    // 重大事件报告--查看--上级部门人员查看本报告的列表 POST /dept/getFartherCompanyDept
    deptGetFartherCompanyDept({ commit }, params){
      return new Promise(function(resolve, reject){
        http.post('dept/getFartherCompanyDept', params).then(function (res) {
          resolve(res.data);
        }.bind(this))
      })
    },
    // 知识点补充的接口：
    // 获取标签列表
    labelFind({ commit }, params){
      return new Promise(function(resolve, reject){
        http.post('label/find', params).then(function (res) {
          resolve(res.data);
        }.bind(this))
      })
    },
    findLabelStr({ commit }, params){
      return new Promise(function(resolve, reject){
        http.post('label/findLabelStr', params).then(function (res) {
          resolve(res.data);
        }.bind(this))
      })
    },
    // 获取知识点列表
    knowledgelFind({ commit }, params){
      return new Promise(function(resolve, reject){
        http.post('knowledge/find', params).then(function (res) {
          resolve(res.data);
        }.bind(this))
      })
    },
    // 检查表修改添加知识点 POST /danger/inspectList/addBatch
    //路由修改/addBatch --> /add     edit by pdn  2020.9.16
    dangerInspectListAddBatch({ commit }, params){
      return new Promise(function(resolve, reject){
        http.post('danger/inspectList/add', params, {
          headers: {'content-type': 'application/json'},
        }).then(function (res) {
          resolve(res.data);
        }.bind(this))
      })
    },
  }
};
