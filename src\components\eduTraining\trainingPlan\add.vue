<template>
    <el-container class="container">
      <el-main>
        <el-form ref="form" label-width="100px" :rules="rules" :model="form">
          <el-row type="flex" class="row">
            <el-col :span="8">
              <el-form-item label="名称" prop="title" style="margin: 0px">
                <span v-if="pageStatus === 'view'">{{form.title}}</span>
                <el-input v-if="pageStatus === 'edit'" v-model="form.title"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="年份" prop="createYear" style="margin: 0px">
                <span v-if="pageStatus === 'view'">{{form.createYear && $tool.formatDateTime(form.createYear).substring(0,4)}}</span>
                <el-date-picker
                  v-if="pageStatus === 'edit'"
                  v-model="form.createYear"
                  type="year"
                  placeholder="选择日期">
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
          <template v-if="$route.params.id">
            <!--<el-row  class="row" style="border:1px solid #f1f1f1;padding:5px 0px;"  v-if="pageStatus === 'edit'">
              <el-col :span="2">
                <el-form-item style="margin: 0px" label-width="30px">
                  <el-button type="success"  size="mini"
                             icon="el-icon-edit"
                             @click="editItemHandle">完成修改</el-button>
                </el-form-item>
              </el-col>
            </el-row>-->

           <!-- <el-row  class="row" style="border:1px solid #f1f1f1;padding:5px 0px;"  v-if="pageStatus === 'edit'">
              <el-col :span="2">
                <el-form-item style="margin: 0px" label-width="30px">
                  <el-button type="success"  size="mini"
                             icon="el-icon-edit"
                             @click="addItemHandle">新增</el-button>
                </el-form-item>
              </el-col>
            </el-row>-->
            <el-row type="flex" class="row">
              <el-col :span="24">
                <el-form-item label="项目" prop="eduPlanItems">
                  <el-table
                    highlight-current-row
                    @current-change="handleCurrentChange"
                    :data="form.eduPlanItems">
                    <el-table-column
                      type="index"
                      label="序号"
                      width="60">
                    </el-table-column>
                    <el-table-column
                      prop="planItems"
                      label="项目"
                      show-overflow-tooltip
                      width="250">
                    </el-table-column>
                    <el-table-column
                      prop="implementDateName"
                      label="实施时间"
                      show-overflow-tooltip
                      width="100">
                    </el-table-column>
                    <!--<el-table-column
                      prop="implementDate"
                      :formatter="formatDateTime"
                      label="实施时间"
                      show-overflow-tooltip
                      width="100">
                    </el-table-column>-->
                    <el-table-column
                      prop="trainee"
                      label="参培人员"
                      show-overflow-tooltip
                      width="100">
                    </el-table-column>
                    <el-table-column
                      prop="trainMode"
                      label="培训方式"
                      show-overflow-tooltip
                      width="100">
                    </el-table-column>
                    <el-table-column
                      prop="remark"
                      label="备注"
                      show-overflow-tooltip
                      width="100">
                    </el-table-column>
                    <el-table-column
                      v-if="pageStatus === 'edit'"
                      label="操作"
                      width="250"
                      align="center"
                      label-class-name="inner-header-style">
                      <template slot-scope="scope">
                        <el-button type="primary"  size="mini" @click="dialog.isShow = true;">新增</el-button>
                        <el-button type="warning"  size="mini" @click="emgHandleListsEditHandle(scope.row)">修改</el-button>
                        <el-button type="danger"  size="mini" @click="emgHandleListsDelHandle(scope.row)">删除</el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-form-item>
              </el-col>
            </el-row>
          </template>
          <el-row type="flex" class="row" justify="center">
            <el-button
              @click="submitBtnClickHandle"
              v-if="pageStatus === 'edit'"
              size="small" :span="2" type="primary">提交</el-button>
            <el-button size="small" :span="2"  @click="$router.back();">返回</el-button>
          </el-row>
        </el-form>
      </el-main>
      <el-footer>
        <!--对话框-->
        <el-dialog
          :visible.sync="dialog.isShow"
          width="80%"
          title="新增/修改项目"
          :before-close="dialogCancelBtnClickHandle">
          <el-form label-width="100px">
            <el-row  class="row">
              <el-col :span="22">
                <el-form-item label="项目:" style="margin: 0px">
                  <el-input v-model="dialog.form.planItems" clearable ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row class="row">
              <el-col :span="8">
                <el-form-item label="实施时间" style="margin: 0px">
                  <el-select v-model="dialog.form.implementDate" clearable placeholder="请选择">
                    <el-option label="1月" value="1"></el-option>
                    <el-option label="2月" value="2"></el-option>
                    <el-option label="3月" value="3"></el-option>
                    <el-option label="4月" value="4"></el-option>
                    <el-option label="5月" value="5"></el-option>
                    <el-option label="6月" value="6"></el-option>
                    <el-option label="7月" value="7"></el-option>
                    <el-option label="8月" value="8"></el-option>
                    <el-option label="9月" value="9"></el-option>
                    <el-option label="10月" value="10"></el-option>
                    <el-option label="11月" value="11"></el-option>
                    <el-option label="12月" value="12"></el-option>
                    <el-option label="全年" value="13"></el-option>
                  </el-select>
                  <!--<el-input v-model="eduPlanItems.implementDate" ></el-input>-->
                  <!--<el-date-picker
                    v-model="dialog.form.implementDate"
                    type="date"
                    placeholder="选择日期">
                  </el-date-picker>-->
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="参培人员" style="margin: 0px">
                  <el-input v-model="dialog.form.trainee" clearable ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="培训方式" style="margin: 0px">
                  <el-input v-model="dialog.form.trainMode" clearable ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row class="row">
              <el-col :span="24" >
                <el-form-item label="备注" style="margin-top: 4px">
                  <el-input
                    v-model="dialog.form.remark"
                    clearable
                    type="textarea" :rows="2" ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button @click="dialogCancelBtnClickHandle">取 消</el-button>
            <el-button
              type="danger"
              @click="dialogOkBtnClickHandle">确定</el-button>
          </div>
        </el-dialog>
      </el-footer>
    </el-container>
</template>

<script>

    export default {
      data(){
        let that = this;
        return {
          form : {
            // 名称
            title : '',
            // 年份
            createYear : '',
            // 项目
            eduPlanItems : [],
          },
          rules: {
            title: [
              {required: true, message: '请输入名称', trigger: 'blur'},
            ],
            createYear: [
              { type: 'date', required: true, message: '请选择日期', trigger: 'blur' }
            ],
            eduPlanItems: [
              { type: 'array', required: true, message: '请至少添加一个项目', trigger: 'blur' }
            ],
          },
          // 项目
          eduPlanItems : {
            // 项目名称
            planItems : '',
            // 实施时间
            implementDate : '',
            implementDateName : '',
            // 参培人员
            trainee : '',
            // 实施方式
            trainMode : '',
            // 备注
            remark : '',
            // ID
            id : '',
            // 计划ID
            "planId": '',
            // 计划内容
            "planItems": "",
          },
          // 状态
          pageStatus : 'edit',
          // 导入
          // 上传文件
          upload : {
            // 地址
            url: that.$http.defaults.baseURL + 'eduPlan/addBatch',
            // token
            cookies: true,
            params : {
              id : -1,
            },
          },
          // 对话框
          dialog : {
            // 是否显示
            isShow : false,
            form : {
              // 项目名称
              planItems : '',
              // 实施时间
              implementDate : '',
//              implementDateName : '',
              // 参培人员
              trainee : '',
              // 实施方式
              trainMode : '',
              // 备注
              remark : '',
              // ID
              id : '',
              // 计划ID
              "planId": '',
              // 计划内容
              "planItems": "",
            },
          },
        }
      },
      watch:{
        $route(to,from){
          // 如果来至列表页
          if(from.name === 'trainingPlanIndex' || from.name === 'trainingPlanLower'){
            this.init();
          }
        }
      },
      created(){
        this.init();
      },
      mounted(){
        this.init();
      },
      methods:{
        // 初始化
        init(){
//          console.log('数据：', this.$route.params)
          this.upload.params.id = this.$route.params.id || -1;
          this.dialog.form.planId = this.$route.params.id || -1;
          if(this.$route.params.status){
            this.searchBtnClickHandle();
          } else {
            this.clear();
          }
        },
        // 格式化时间
        formatDateTime(row, column, cellValue){
          let pro = column.property;
          let num = 10;
          // 年份4位 1999
          if(pro === 'createYear') num = 4;
          let str = this.$tool.formatDateTime(row[pro] || 0);
          console.log('str=', str)
          return str ? str.substring(0, num) : str;
        },
        // 清空数据
        clear(){
          this.form = this.$tool.clearObj({}, this.form);
          this.eduPlanItems = this.$tool.clearObj({}, this.eduPlanItems);
          // 默认值
          this.form.createYear = new Date();
          this.form.title = this.$tool.formatDateTime(new Date()).substring(0, 4) + '年员工培训计划';
          this.pageStatus = 'edit';
        },
        // 格式化时间
       /* formatDateTime(row, column, cellValue){
          let pro = column.property;
          let num = 7;
          // 年份4位 1999
          if(pro === 'createYear') num = 4;
          let str = this.$tool.formatDateTime(row[pro] || 0);
          return str ? str.substring(0, num) : str;
        },*/
        // 提交---添加或更新
        submitBtnClickHandle(){
          let id = this.$route.params.id;
          this.form.createYear = new Date(this.form.createYear);
          let params = Object.assign({}, this.form);
          params['createYear'] = new Date(this.form.createYear);
          if(id) params['id'] = id;
          this.$refs['form'].validate(function(valid) {
            if(valid){
              this.$store.dispatch('eduPlanAddOrUpdate', params).then(function (res) {
                if(res.success){
                  this.$message({
                    type : 'success',
                    message : '操作成功'
                  })
                  this.$router.push({ name : 'trainingPlanIndex' })
                } else {
                  this.$message({
                    type : 'error',
                    message : res.message || '操作失败'
                  })
                }
              }.bind(this))
            } else {
              return false;
            }
          }.bind(this))
        },
        // 根据id搜索信息
        searchBtnClickHandle(){
          //this.clear();
          let companyId = this.$route.params.companyId;
          let id = this.$route.params.id;
          let params = {
            id : id,
          }
          if(companyId) {
            params['companyId'] = companyId;
          }
          this.$store.dispatch('eduPlanFind', params).then(function(res){
            if(res.success){
              let list = res.data.list[0];
              Object.entries(list).forEach(function(it){
                if(this.form.hasOwnProperty(it[0])){
                  this.form[it[0]] = it[1];
                }
              }.bind(this))
              // 如果查看，项目只显示填写过的内容
              if(this.$route.params.status == 'view'){
                this.form.eduPlanItems = this.form.eduPlanItems.filter(function(it){
                  return it.implementDate;
                }.bind(this))
              }
              this.pageStatus = this.$route.params.status || 'edit';
            } else {
              this.$message({
                type : 'error',
                message : res.message || '错误'
              })
            }
          }.bind(this));
        },
        // 单选行---修改子项
        handleCurrentChange(val){
          Object.entries(val).forEach(function(it){
            this.eduPlanItems[it[0]] = it[1];
          }.bind(this))
        },
        // 添加、修改子项
        addItemHandle(params){
          this.$store.dispatch('eduPlanItemAddOrUpdate', params).then(function(res){
            if(res.success){
              this.$message({
                type : 'success',
                message : '更新成功'
              })
              this.dialogCancelBtnClickHandle();
              this.searchBtnClickHandle();
            } else {
              this.$message({
                type : 'error',
                message : res.message || '更新失败！！'
              })
            }
          }.bind(this))
        },

        // 添加子项按钮
        emgHandleListsAddHandle(event, index, rows){
          this.dialog.isShow = true;
        },
        // 编辑子项按钮
        emgHandleListsEditHandle(row){
          // 将数据传到对话框去
          this.dialog.form.planItems = row.planItems;
          this.dialog.form.implementDate = row.implementDate;
          this.dialog.form.trainee = row.trainee;
          this.dialog.form.trainMode = row.trainMode;
          this.dialog.form.remark = row.remark;
          this.dialog.form.planId = row.planId;
          this.dialog.form.id = row.id;
          // 打开对话框
          this.dialog.isShow = true;

        },
        // 删除子项按钮
        emgHandleListsDelHandle(row){
          // 找到id
          let params = {
            id : row.id
          }
          this.$store.dispatch('eduPlanItemDelete', params).then(function(res){
            if(res.success){
              this.$message({
                type : 'success',
                message : '删除成功'
              })
              this.searchBtnClickHandle();
            } else {
              this.$message({
                type : 'error',
                message : res.message || '更新失败！！'
              })
            }
          }.bind(this))
        },
        // 导入
        //上传文件
        uploadSuccess: function (response, file, fileList) {
          if (response.success) {
            this.$message({
              type : 'success',
              message : '导入成功'
            })
            // 获取项目列表
            this.searchBtnClickHandle();
          } else {
            this.$message({
              type : 'error',
              message : response.message || '导入失败'
            })
          }
        },

        // 对话框---取消按钮
        dialogCancelBtnClickHandle(){
          // 关闭对话框
          this.dialog.isShow = false;
          // 情况对话框
          this.dialog.form.planItems = '';
          this.dialog.form.implementDate = '';
          this.dialog.form.trainee = '';
          this.dialog.form.trainMode = '';
          this.dialog.form.remark = '';
          this.dialog.form.id = '';
        },
        // 对话框---确认按钮
        dialogOkBtnClickHandle(){
          // 根据是否有项目的ID，来判断是新增，还是修改
          let form = this.dialog.form;

//          console.log(this.dialog.form);
          let params = {
            // 项目名称
            planItems : form.planItems,
            // 实施时间
            implementDate : form.implementDate,
//              implementDateName : '',
            // 参培人员
            trainee : form.trainee,
            // 实施方式
            trainMode : form.trainMode,
            // 备注
            remark : form.remark,
            // 计划ID
            planId: form.planId,
          }
          if(form.id){
            params['id'] = form.id;
          }
          this.addItemHandle(params);

        }


      }
    }
</script>

<style>
  .container{
    background:#fff;
    padding:0px 20px 20px;
  }
  .row{
    margin:20px 0;
  }
</style>
