<template>
  <div id="trainingPlanIndex">
    <div class="background-style">

      <!--搜索区-->
      <div class="search-bar">
        <div style="padding:10px 10px 0 10px;float: left">
          <!--年份：-->
          <el-date-picker
            v-model="form.createYear"
            type="year"
            placeholder="年份">
          </el-date-picker>
          <el-button
            @click="searchBtnClickHandle"
            type="primary" icon="el-icon-search" style="margin-left: 20px">搜索</el-button>
          <el-button
            v-if="role == 4"
            @click="$router.push({ name : 'trainingPlanAdd' });"
            type="success" icon="el-icon-plus" style="margin-left: 20px">培训计划</el-button>
          <el-button
            v-if="powerBtns.includes('lowerBtn')"
            @click="$router.push({ name : 'trainingPlanLower' });"
            type="error" style="margin-left: 20px">下级培训</el-button>
        </div>
      </div>

      <!--表格区-->
      <div style="width: 100%;">
        <div style="padding: 20px 10px 20px 10px">
          <el-table
            border
            :data="tableData.list"
            style="width: 100%">
            <el-table-column
              type="index"
              label="编号"
              width="100"
              align="center"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="title"
              label="名称"
              min-width="150"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="companyName"
              label="公司"
              show-overflow-tooltip
              width="200"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="createYear"
              :formatter="formatDateTime"
              label="年份"
              width="150"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              fixed="right" label="操作"
              label-class-name="header-style"
              align="left" width="300">
              <template slot-scope="scope">
                <el-button size="mini" type="success" @click.native="itemViewClick(scope.row)">查看</el-button>
                <template v-if="role == 4">
                  <el-button size="mini" type="primary" @click="itemUpdateClick(scope.row)">修改</el-button>
                  <el-button size="mini" type="danger" @click="itemDeleteClick(scope.row)">删除</el-button>
                </template>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div style="margin-top: 10px">
          <el-pagination
            background
            layout="prev, pager, next"
            :current-page="tableData.pageNum"
            :page-size="form.pageSize"
            :total="tableData.total"
            @current-change ="disasterPageChangeHandle">
          </el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'trainingPlanIndex',
    data() {
      return {
        form : {
          // 年份
          createYear : '',
          // 当前页
          pageCurrent : 1,
          // 页数大小
          pageSize : 10,
        },
        tableData : {},
        // 角色 0 员工 1 发布者
        role : 0,
        // 权限按钮
        powerBtns : [],
      }
    },
    mounted(){
      this.init();
    },
    watch:{
      $route(to,from){
        if(to.name === 'trainingPlanIndex') {
          this.init();
        }
      }
    },
    methods:{
      // 初始化
      init(){

        this.powerBtns = this.$tool.getPowerBtns('eduTrainingMenu', this.$route.path);
        // 根据权限按钮设置角色
        this.judgeUserRole();
        // 搜索
        this.searchBtnClickHandle();
      },
      judgeUserRole(){
        // 获取权限按钮
        let btns = this.$tool.getPowerBtns('eduTrainingMenu', this.$route.path);
        // 公司
        if(btns.includes('addBtn')){
          this.role = 4;
        } else {
          this.role = 1;
        }
      },
      // 格式化时间
      formatDateTime(row, column, cellValue){
        let pro = column.property;
        let num = 10;
        // 年份4位 1999
        if(pro === 'createYear') num = 4;
        let str = this.$tool.formatDateTime(row[pro] || 0);
        return str ? str.substring(0, num) : str;
      },
      // 分页
      disasterPageChangeHandle(page){
        this.form.pageCurrent = page;
        this.searchBtnClickHandle();
      },
      // 搜索按钮
      searchBtnClickHandle(){
        this.$store.dispatch('eduPlanFind', this.form).then(function(res){
          if(res.success){
            this.tableData = res.data;
          } else {
            this.$message({
              type : 'error',
              message : res.message || '错误'
            })
          }
        }.bind(this));
      },
      // 查看
      itemViewClick(row){
        let name = 'trainingPlanAdd';
        let params = {
          id : row.id,
          status : 'view'
        }
        this.$router.push({ name : name, params : params})
      },
      // 修改
      itemUpdateClick(row){
        let name = 'trainingPlanAdd';
        let params = {
          id : row.id,
          status : 'edit'
        }
        this.$router.push({ name : name, params : params})
      },
      // 删除按钮
      itemDeleteClick(row){
        this.$confirm('此操作将永久删除, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
          .then(function(){
            this.$store.dispatch('eduPlanDelete', {
              id : row.id
            }).then(function(res){
              if(res.success){
                this.$message({
                  type : 'success',
                  message : '删除成功'
                })
                this.searchBtnClickHandle();
              } else {
                this.$message({
                  type : 'error',
                  message : res.message || '删除失败！！'
                })
              }
            }.bind(this))
          }.bind(this))
      },
      // 作废
      itemVoidClick(row){
        let params = {
          id : row.id,
          status : 3
        };
        this.$confirm('此操作将作废, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
          .then(function(){
            // 删除---灾后处置
            this.$store.dispatch('eduReqInvAddOrUpdate', params).then(function(res){
              if(res.success){
                this.$message({
                  type : 'success',
                  message : '作废成功'
                })
                this.searchBtnClickHandle();
              } else {
                this.$message({
                  type : 'error',
                  message : res.message || '作废失败！！'
                })
              }
            }.bind(this))
          }.bind(this))
      },

    }
  }
</script>
<style>
</style>
