<template>
  <div class="div">
    <div style="display: flex; flex: 1">
      <div id="map"></div>
    </div>
  </div>
</template>
<script>
import "ol/ol.css";
import * as control from "ol/control";
import { Map,View,Feature } from "ol/index";
import { Tile as TileLayer,Vector as VectorLayer } from "ol/layer";
import { Vector as VectorSource,XYZ } from "ol/source";
import { Circle,Polygon } from "ol/geom";
import { Fill,Text,Stroke,Style,Circle as CircleStyle } from "ol/style";
import { Draw,defaults } from "ol/interaction";
let list=[],map,featureLayers={ 1: 1,2: 2,3: 3,4: 4 },getLists=[];
let stylePoly={
  1: "rgba( 26, 185, 244,0.8)",
  2: "rgba( 255, 255, 26,0.8)",
  3: "rgba( 240, 138, 69,0.9)",
  4: "rgba( 255, 26, 26,0.8)",
};
const geometryType=[null,"Circle","Polygon"];
//初始map
function initMap() {
  map=new Map({
    target: "map",
    interactions: defaults({
      doubleClickZoom: false,
    }),
    view: new View({
      center: [121.6843688888890,29.8130175000000],zoom: 16.1,maxZoom: 18,projection: "EPSG:4326",
    }),
  });
  map.addLayer(new TileLayer({
    source: new XYZ({
      url: "http://t0.tianditu.gov.cn/img_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=img&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=fe33c2fc4c05d18c2e81f7fdf10be4d6",
    }),
  }));
  for(let i=1;i<=4;i++) {
    featureLayers[i]=new VectorLayer({
      source: new VectorSource({ features: [],}),
      style: new Style({
        fill: new Fill({
          color: stylePoly[i],
        }),
      }),
    });
    map.addLayer(featureLayers[i]);
  }
  map.on('click',function(event) {
    // 获取点击的坐标
    var coordinates=event.coordinate;
    // 在控制台输出经纬度信息
  });
}


let drawInteraction,drawFeatures=[]; // 用于存储绘制的图形
// 手绘图形
function drawGeometry(type,params) {
  // drawFeatures=[];
  drawInteraction&&map.removeInteraction(drawInteraction);
  drawInteraction=new Draw({
    type: type,
    source: featureLayers[params.rlevel].getSource(),
    features: drawFeatures,
  });
  drawInteraction.on("drawend",(event) => {
    let geometry=event.feature.getGeometry(); // 获取圆的几何信息
    if(geometry.constructor.name==="Circle") {
      params["points"]=[geometry.getCenter()];
      params["radius"]=geometry.getRadius();
      list.push(params);
    } else {
      params["points"]=geometry.getCoordinates()[0];
      list.push(params);
    }
    drawInteraction.finishDrawing();
    drawInteraction.setActive(false);
  });
  map.addInteraction(drawInteraction);
}

function clearFeature() {
  Object.values(featureLayers).forEach((layer) => {
    layer.getSource().clear();
  });
}

export default {
  data() {
    return {
      typeOptions: [
        { value: "1",label: "爆破",},
        { value: "2",label: "特种作业",},
      ],
      levelOptions: [
        { value: "1",label: "低风险",},
        { value: "2",label: "一般风险",},
        { value: "3",label: "较大风险",},
        { value: "4",label: "重大风险",},
      ],
      tableData: [],
      form: {
        rtype: "1",rlevel: "1",rdate: [
          "12.02 00:00",
          "12.30 00:00"
        ],
        title: "",
      },
      stylePoly: { 1: "#1ab9f4",2: "#ffff1a",3: "#f08a45",4: "#ff1a1a",},
      currentDate: new Date(),
    };
  },
  mounted() {
    initMap();
    this.getData();

  },
  activated() {
    this.getData();
  },

  methods: {
    draw(kind,params) {

      drawGeometry(geometryType[kind],params);
    },
    getData() {
      clearFeature();
      getLists=[];
      this.$http
        .post("/sys/sysMineDynamic/findMineDynamicInfo",{})
        .then((res) => {
          this.tableData=res.data.data.list.map((i) => {
            i.rdate=i.rdate&&JSON.parse(i.rdate);
            return i;
          });
          this.tableData.forEach((i) => {
            if(i.rdate) return
            const points=i.points;
            const opacity=this.isCurrentTimeInRange(i.rdate);
            const radius=i.radius;
            const title=i.title;
            this.setPoly(points,i.rlevel,opacity,radius,title);
          });
        });
    },
    setPoly(points,level,opacity,radius,title) {
      let geometry;
      if(radius) {
        geometry=new Circle(...points,radius);
      } else {
        geometry=new Polygon([points]);
      }
      const feature=new Feature({
        geometry: geometry,
      });
      // alert(title);
      feature.setStyle([
        new Style({
          fill: title!=='外圈'? new Fill({
            color: stylePoly[level], // 填充颜色
          }):null,
          stroke: new Stroke({
            color: stylePoly[level], // 边框颜色
            width: 2, // 边框宽度
          })
        }),
        new Style({
          text: new Text({
            text: title==='外圈'? '':title,
            overflow: true,
            // textAlign: 'center',
            // font: '15px 宋体',
            // fill: new Fill({ color: 'black' }), // 文字颜色
          })
        }),
      ]);
      featureLayers[level].getSource().addFeature(feature);
    },
    createStyle() {
      return new Style({
        stroke: new Stroke({
          color: "rgba(255, 0, 0, 1)",
          width: 3,
        }),
        // fill: new Fill({
        //   color: "rgba(255, 0, 0, 0.1)",
        // }),
        image: new CircleStyle({
          radius: 7,
          fill: new Fill({
            color: "rgba(255, 0, 0, 1)",
          }),
        }),
      });
    },
    addBtnClickHandle() {
      // 在绘制前检查是否选择了时间
      // if(!this.form.rdate||this.form.rdate.length!==2) {
      //   this.$message.error("请先选择持续时间");
      //   return;
      // }
      const params={
        rtype: this.form.rtype,
        rlevel: this.form.rlevel,
        title: this.form.title,
        rdate: this.form.rdate&&JSON.stringify(this.form.rdate),
      };
      this.draw(this.form.rtype,params);
    },
    clear() {
      clearFeature();
      this.getData();
    },
    save() {
      // const params = list.map((i) => i.params);
      this.$http.post("/sys/sysMineDynamic/saveDynamic",list).then((res) => {
        this.$message.success("保存成功");
        // this.reset();
        this.getData();
        list=[];
      });
    },
    highlight(row) {
      drawFeatures[0].setStyle([
        new Style({
          stroke: new Stroke({
            color: "rgba(255, 0, 0, 1)",
            width: 3,
          }),
          // fill: new Fill({
          //   color: "rgba(255, 0, 0, 0.1)",
          // }),
          image: new CircleStyle({
            radius: 7,
            fill: new Fill({
              color: "rgba(255, 0, 0, 1)",
            }),
          }),
        }),
      ]);
      // getLists.forEach((poly) => {
      //   poly.opacity=poly.isShow? 0.8:0;
      //   poly.borderWidth=0.01;
      // });
      // getLists[row.index].opacity=1;
      // getLists[row.index].borderWidth=3;
    },
    back() {
      getLists.forEach((poly) => {
        poly.opacity=poly.isShow? 0.8:0;
        poly.borderWidth=0.01;
      });
    },
    rouClassNameFn({ row,rowIndex }) {
      //把每一行的索引放进row
      row.index=rowIndex;
    },
    isCurrentTimeInRange(rdate) {
      if(!Array.isArray(rdate)||rdate.length!==2) {
        return false;
      }

      const currentYear=this.currentDate.getFullYear();
      const startTime=new Date(`${currentYear}-${rdate[0]}`);
      const endTime=new Date(`${currentYear}-${rdate[1]}`);

      return this.currentDate>=startTime&&this.currentDate<=endTime;
    },
    itemDeleteClick(row) {
      this.$confirm("此操作将永久删除该风险区域, 是否继续?","提示",{
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$http
            .post("/sys/sysMineDynamic/delDynamic",{
              id: row.id,
            })
            .then((res) => {
              this.$message.success("删除成功");
              this.getData();
            });
        })
        .catch(() => { });
    },
    open2() {

    }
  },
};
</script>
<style scoped>
.div {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
}
#map {
  flex: 1;
  margin-top: 20px;
  overflow: hidden;
  position: relative;
}
.search-bar {
  width: 100%;
  height: 50px;
  padding: 20px 20px 0 20px;
  display: block;
}
</style>
