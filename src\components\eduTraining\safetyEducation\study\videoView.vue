<template>
  <el-container class="container" id="safeEducationView">
    <el-main>
      <el-row>
        <el-col :span="24">
          <h1 style="text-align: center;">{{info.courseName}}</h1>
        </el-col>
      </el-row>
      <el-row class="lineHeight">
        <el-col :span="4"  style="text-align: center;">作者：{{info.courseAuthor}}</el-col>
        <el-col :span="4" style="text-align: center;">发布时间：{{formatDateTime(info.createTime)}}</el-col>
        <el-col :span="6" style="text-align: center;">部门：{{info.deptName}}</el-col>
        <el-col :span="8" style="text-align: center;">出处：{{info.companyName}}</el-col>
        <el-col :span="2" style="text-align: center;">学时：{{info.courseTime}}</el-col>
      </el-row>
      <!--<el-row class="lineHeight">
        <el-col :span="24" style="margin:20px;">
          <img :src="info.courseImgPath"/>
        </el-col>
      </el-row>-->
      <el-row class="lineHeight">
        <el-col :span="24">
          <el-button @click="player.play()" type="success">开始</el-button>
          <el-button @click="player.pause()" type="danger">暂停</el-button>
        </el-col>
        <el-col :span="24">
          <!--<video id="myVideo" class="video-js" :src="info.courseViewPath">-->
          <!--<video id="myVideo" class="video-js" :src="info.courseViewPath">
          &lt;!&ndash;<video id="myVideo" class="video-js" >&ndash;&gt;
            &lt;!&ndash; <source  type="video/mp4">&ndash;&gt;
            &lt;!&ndash;<source :src="info.courseViewPath" type="video/mp4">&ndash;&gt;
          </video>-->
          <video-player
            style="height:400px;width:800px;"
            class="video-player vjs-custom-skin"
                         ref="videoPlayer"
                         :playsinline="true"
                          @ready="playerReadied"
                          @timeupdate="onPlayerTimeupdate($event)"
                          :options="playerOptions"
          ></video-player>
        </el-col>
      </el-row>
      <el-row class="lineHeight">
        <el-col :span="24">
          <!--    <vue-editor v-model="info.newsContent"></vue-editor>-->
          <div v-html="info.courseText"></div>
        </el-col>
      </el-row>
      <el-row type="flex" class="row" justify="center">
        <el-button size="small" :span="2"  @click="$router.back();">返回</el-button>
      </el-row>
    </el-main>
  </el-container>
</template>

<script>
  import { VueEditor } from 'vue2-editor'
  import chooseStaff from '@/components/common/chooseStaff'
  export default {
    components: {
      chooseStaff,
      VueEditor
    },
    data(){
      return {
        // info表
        info : {
          // ID
          id : '',

          courseAuthor : '',
          deptName : '',
          companyName : '',
          createTime : '',
          // 视频名称
          courseName : '',
          // 视频类型
          courseType : '',
          // 视频讲解人
          courseTeacher : '',
          // 视频学时
          courseTime : 0,
          // 视频封面图片id
//          courseImg : '',
          // 视频封面图片
          courseImgPath : '',
          // 视频地址
          courseViewPath : '',
          // 视频内容
          courseText : '',




        },

        timer : null,
        myPlayer : null,

        // 视频播放
        playerOptions : {
            playbackRates : [ 0.5, 1.0, 1.5, 2.0 ], //可选择的播放速度
            autoplay : false, //如果true,浏览器准备好时开始回放。
            muted : false, // 默认情况下将会消除任何音频。
            loop : false, // 视频一结束就重新开始。
            preload : 'auto', // 建议浏览器在<video>加载元素后是否应该开始下载视频数据。auto浏览器选择最佳行为,立即开始加载视频（如果浏览器支持）
            language : 'zh-CN',
            aspectRatio : '16:9', // 将播放器置于流畅模式，并在计算播放器的动态大小时使用该值。值应该代表一个比例 - 用冒号分隔的两个数字（例如"16:9"或"4:3"）
            fluid : true, // 当true时，Video.js player将拥有流体大小。换句话说，它将按比例缩放以适应其容器。
            sources : [ {
              type : "",
//              src : 'http://www.html5videoplayer.net/videos/madagascar3.mp4'//url地址
              src : ''//url地址
            } ],
            //设置视频播放器的显示宽度（以像素为单位）
            width: "800px",
            //设置视频播放器的显示高度（以像素为单位）
            height: "400px",
            poster : "", //你的封面地址
            controls: false,
//            controls: true,
//            currentTime : this.$route.params.courseStop,
            // width: document.documentElement.clientWidth,
            notSupportedMessage : '此视频暂无法播放，请稍后再试', //允许覆盖Video.js无法播放媒体源时显示的默认信息。
            controlBar : {
              timeDivider : false,//当前时间和持续时间的分隔符
              durationDisplay : false,//显示持续时间
              remainingTimeDisplay : false,//是否显示剩余时间功能
              fullscreenToggle : false  //全屏按钮
            }
          },

        // 视频总长度、当前视频播放的进度
        duration : 0,
        currentTime : 0,
        // 拖动后重置播放时间
//        resetTime : 0,
        // 本地播放时间
//        getCurTime : 0,

      }
    },
    watch:{
      $route(to,from){
        // 如果来至列表页
        if(from.name === 'safetyEducationStudyIndex'&&this.$route.name==='safetyEducationStudyVideoView'){
          this.init();
        }
      },

    },
    beforeRouteLeave(to, form, next) {
      this.addScore();
      next()
    },
    computed: {
      player() {
        return this.$refs.videoPlayer.player
      }
    },
    mounted() {
      this.init();
    },
    methods:{
      // 初始化
      init(){
        this.searchBtnClickHandle();
      },
      stopVideo(){
        this.player.pause();
      },
      playerReadied(player){
          player.currentTime(this.$route.params.courseStop);
      },
      // 视频播放事件
      onPlayerTimeupdate(player) {
        this.currentTime = player.currentTime();
        this.duration = player.duration();
//         console.log('player duration!', player.duration())
        // 每隔1秒，比对
      /*  clearInterval(this.timer);
        this.timer = setInterval(function(){
          console.log('player Timeupdate!', player.currentTime())
        }.bind(this), 100)*/
      },
      // 视频加分
      addScore(){
        let duration = this.duration;
        let currentTime = this.currentTime;
        let params = {
          studyId : this.$route.params.id,
          isComplete : duration == currentTime ? 1 : 0,
          courseSchedule : this.$tool.getPercent(currentTime, duration),
          courseStop : currentTime
        }
        this.$store.dispatch('eduUserStudyAddUserCourse',params).then(function(res){
          if(res.success){
            this.$message({
              type : 'success',
              message : '阅读√'
            })
          } else {
            this.$message({
              type : 'error',
              message : res.message || '错误'
            })
          }
        }.bind(this));

      },
      formatDateTime(dateStr){
//        let pro = column.property;
        let num = 10;
        let str = this.$tool.formatDateTime(dateStr) || '';
        return str ? str.substring(0, num) : str;
      },


      // 根据id搜索信息
      searchBtnClickHandle(){
        let id = this.$route.params.id;
//        console.log('id = ', id)
//        this.viewOrEdit = true;

        this.$store.dispatch('eduCourseFindById', { id : id }).then(function(res){
          if(res.success){

            this.info.sourceSrc = '';
//            return;
            let list = res.data;
            // 发布培训信息
            Object.entries(list).forEach(function(it){
              if(it[1] && this.info.hasOwnProperty(it[0])){
                this.info[it[0]] = it[1];
              }
            }.bind(this));
            this.playerOptions.sources[0].src = this.info.courseViewPath;
//            is.$refs.videoPlayer.player.src(src);
//            console.log('kkk', this.$route.params.courseStop)
            console.log(this.player);
//            this.player.currentTime = this.$route.params.courseStop;
//          this.player.src(this.$route.params.courseStop);
//            console.log(99999, this.playerOptions.sources.src)
           /* clearTimeout(this.timer)
            this.timer = setTimeout(function(){
              this.initVideo();
            }.bind(this), 300)*/

          } else {
            this.$message({
              type : 'error',
              message : res.message || '错误'
            })
          }
        }.bind(this));
      },

    }
  }
</script>

<style scoped>
  .container{
    background:#fff;
    padding:0px 20px 20px;
  }
  #safeEducationView .lineHeight{
    padding:20px;
    margin-top:20px;
  }
  .title{
    background:rgba(64,158,255,.1);
    color:#0f6fc6;
    border: 1px solid rgba(64,158,255,.2);
    border-radius:5px;
  }
  .row{
    margin-top:10px;
  }
  .hideVideoProcessbar{
    width:800px;
    height:50px;
    background:yellow;
    border:1px solid red;
    z-index:999999999999999999999;
  }
</style>
