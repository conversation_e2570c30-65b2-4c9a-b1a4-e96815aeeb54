<template>
  <div id="emerGroup">
    <div class="background-style">
      <!--队伍查询-->
      <div style="width: 100%">
        <div style="float: left;margin: 10px">
          <div style="width: 100%;">
            <div style="display: inline-block;float: left">
              队伍名称:
              <el-input  v-model="search.groupName" clearable
                         placeholder="请输入队伍名称" style="margin-left: 10px;width: 160px">
              </el-input>
            </div>
            <div style="display: inline-block;float: left;margin-left: 10px;">
              专业:
              <el-input  v-model="search.major" clearable
                         placeholder="请输入专业" style="margin-left: 10px;width: 160px">
              </el-input>
            </div>
            <div style="display: inline-block;float: left;margin-left: 10px">
              <el-button type="primary" icon="el-icon-search" @click="searchGroup()">搜 索</el-button>
            </div>
            <div style="display: inline-block;float: left;margin-left: 20px">
              <el-button type="success" @click="newGroupClick()">添加队伍</el-button>
            </div>
          </div>
        </div>
      </div>
      <!--队伍查询结束-->
      <!--队伍列表-->
      <div style="width: 100%;margin-top: 10px">
        <div style="padding: 10px 10px 20px 10px">
          <el-table
            :data="groupData"
            border
            fit
            highlight-current-row
            style="width: 100%">
            <el-table-column
              type="index"
              label="序号"
              width="60"
              align="center"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="groupName"
              label="队伍名称"
              width="150"
              align="center"
              show-overflow-tooltip
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="major"
              label="专业"
              width="150"
              align="center"
              show-overflow-tooltip
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="leader"
              label="组长"
              width="120"
              align="center"
              show-overflow-tooltip
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="location"
              label="驻扎地"
              width="180"
              align="center"
              show-overflow-tooltip
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="phone"
              label="联系电话"
              width="150"
              align="center"
              show-overflow-tooltip
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="memberCount"
              label="成员数"
              width="100"
              align="center"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="equipment"
              label="装备描述"
              width="300"
              align="center"
              show-overflow-tooltip
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="createTime"
              label="成立日期"
              width="160"
              :formatter="dateFormat"
              align="center"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column fixed="right" label="操作" label-class-name="header-style" align="center" width="250">
              <template slot-scope="scope">
                <el-button size="mini" type="success" @click="newGroupMember(scope.row)" style="float: left">查看</el-button>
                <el-button size="mini" type="primary" style="float: left" @click="editGroupClick(scope.row)">修改
                </el-button>
                <el-button size="mini" type="danger" style="float: left"
                           @click="deleteGroupDialog(scope.row,scope.$index)">删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div >
          <el-pagination
            background
            layout="prev, pager, next"
            :page-size="10"
            :current-page="currentPage"
            :total="totalItem"
            @current-change="currentPageClick">
          </el-pagination>
        </div>
      </div>
      <!--队伍列表结束-->
    </div>

    <el-dialog
      title="删除队伍"
      :visible.sync="dialog.deleteDialogVisible"
      width="30%"
      center>
      <span>确认删除队伍</span>
      <span slot="footer" class="dialog-footer">
          <el-button @click="dialog.deleteDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="deleteGroup()">确 定</el-button>
        </span>
    </el-dialog>
  </div>
</template>
<script>
  export default {
    name: 'emerGroup',
    data() {
      return {
        search:{
          groupName:'',
          major:''
        },
        groupData:[],
        currentPage:0,
        totalItem:0,
        dialog:{
          deleteDialogVisible:false,
          index:0,
          deleteGroupId:0,
        }
      }
    },
    watch: {
      $route(to, from) {
        this.loadGroup(null,null)
      }
    }
    ,
    mounted:function () {
      this.loadGroup(null,null)
    },
    methods:{
      dateFormat(row, column) {
        //.replace(/年|月/g, "-").replace(/日/g, " ")
        return new Date(row.createTime).Format("yyyy-MM-dd").toLocaleString();
      },
      editGroupClick:function (row) {
        this.$router.push({name:'newEmerGroup',params:{edit:true,groupId:row.id
        ,groupName:row.groupName,leader:row.leader,location:row.location
        ,equipment:row.equipment,major:row.major,phone:row.phone,createDate:row.createTime}})
      },
      newGroupMember:function(row){
        this.$router.push({name:'newGroupMember',params:{groupId:row.id
            ,groupName:row.groupName,leader:row.leader,location:row.location
            ,equipment:row.equipment,major:row.major,createTime:row.createTime}})
      },
      newGroupClick:function () {
        this.$router.push({name:'newEmerGroup',params:{edit:false}})
      },
      loadGroup:function (groupName,major) {
        var params=new URLSearchParams()
        if(groupName){
          params.append("groupName",groupName)
        }
        if(major){
          params.append("major",major)
        }
        params.append("companyId",this.$tool.getStorage('LOGIN_USER').companyId);
        this.$http.post("group/find",params).then(function (res) {
          if(res.data.success){
            this.currentPage=1
            this.totalItem=res.data.data.total
            this.groupData=res.data.data.list
            this.groupData.forEach(function (item,index) {
              item.num=index+1
            })
          }else{
            this.$message.error(res.data.message)
          }
        }.bind(this))
      },
      //翻页
      currentPageClick:function (val) {
        var params=new URLSearchParams()
        if(this.search.groupName){
          params.append("groupName",this.search.groupName)
        }
        if(this.search.major){
          params.append("major",this.search.major)
        }
        params.append("pageCurrent",val)
        params.append("companyId",this.$tool.getStorage('LOGIN_USER').companyId);
        this.$http.post("group/find",params).then(function (res) {
          if(res.data.success){
            this.currentPage=1
            this.totalItem=res.data.data.total
            this.groupData=res.data.data.list
            this.groupData.forEach(function (item,index) {
              item.num=(Number(val)-1)*10+index+1
            })
          }else{
            this.$message.error(res.data.message)
          }
        }.bind(this))
      },
      deleteGroupDialog:function(row,index){
        this.dialog.deleteDialogVisible=true
        this.dialog.deleteGroupId=row.id
        this.dialog.index=index
      },
      deleteGroup:function(){
        var params=new URLSearchParams()
        params.append("id",this.dialog.deleteGroupId)
        this.$http.post("group/delete",params).then(function (res) {
          if(res.data.success){
            this.$message.success("删除成功")
            this.groupData.splice(this.dialog.index,1)
            this.dialog.deleteDialogVisible=false
          }
        }.bind(this))
      },
      searchGroup:function(){
       this.loadGroup(this.search.groupName,this.search.major)
      }
    }
  }
</script>
<style>
</style>
