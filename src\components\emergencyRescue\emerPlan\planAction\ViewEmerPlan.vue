<template>
  <div id="viewEmerPlan">
    <div class="background-style">
      <el-col :span="16" :offset="4" class="success-background-title">查看应急预案</el-col>
      <el-col :span="16" :offset="4">
        <el-form :model="form"  ref="ruleForm" label-width="130px" class="demo-ruleForm">
          <el-col :span="24">
            <el-form-item label="预案名称：" prop="planName">
              <el-input v-model="form.planName" readonly="readonly"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-col :span="12">
              <el-form-item label="分类：" prop="classify">
                <el-cascader
                  :options="cascaderOptions"
                  v-model="form.classify"
                  style="width: 100%">
                </el-cascader>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="级别：" prop="level">
                <el-input v-model="form.level" readonly="readonly"></el-input>
              </el-form-item>
            </el-col>
          </el-col>
          <el-col :span="24">
            <el-col :span="12">
              <el-form-item label="预警信号：" prop="planFlag">
                <el-input v-model="form.planFlag" readonly="readonly"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="上报间隔：" prop="interval">
                <el-input v-model="form.interval" type="number" readonly="readonly"><template slot="append">小时</template></el-input>
              </el-form-item>
            </el-col>
          </el-col>
          <!--<el-col :span="24">-->
            <!--<el-col :span="12">-->
              <!--<el-form-item label="通知对象：" prop="noticedPeople">-->
                <!--<el-input v-model="form.noticedPeople" placeholder="请点击右侧按钮选择人员" readonly="readonly"></el-input>-->
              <!--</el-form-item>-->
            <!--</el-col>-->
            <!--<el-col :span="12">-->
              <!--<el-button type="primary" size="medium" style="margin-left: 20px;margin-top: 2px" @click="choosePeople">选择人员</el-button>-->
            <!--</el-col>-->
          <!--</el-col>-->
          <el-col :span="24">
            <el-form-item :label="form.firstDept" prop="firstContent">
              <el-input type="textarea" :autosize="{ minRows: 3}" v-model="form.firstContent" readonly="readonly"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="form.secondDept" prop="secondContent">
              <el-input type="textarea" :autosize="{ minRows: 3}" v-model="form.secondContent" readonly="readonly"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="form.thirdDept" prop="thirdContent01">
              {{form.thirdDept01}}
              <el-input type="textarea" :autosize="{ minRows: 3}" v-model="form.thirdContent01" readonly="readonly"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label=" " prop="thirdContent02">
              {{form.thirdDept02}}
              <el-input type="textarea" :autosize="{ minRows: 3}" v-model="form.thirdContent02" readonly="readonly"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label=" " prop="fourthDept">
              {{form.fourthDept}}
              <el-input type="textarea" :autosize="{ minRows: 3}" v-model="form.fourthDeptContent" readonly="readonly"></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="应急物资：" prop="goodsArray">
              <el-table
                :data="form.goodsArray"
                border
                style="width: 100%">
                <el-table-column
                  type="index"
                  align="center"
                  label-class-name="inner-header-style"
                  width="50">
                </el-table-column>
                <el-table-column
                  prop="label"
                  label="物资名称"
                  align="center"
                  label-class-name="inner-header-style"
                  width="180">
                </el-table-column>
                <el-table-column
                  label="备注"
                  align="center"
                  label-class-name="inner-header-style">
                </el-table-column>
              </el-table>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="附件：">
              <file-list :fileList="fileList"></file-list>
            </el-form-item>
          </el-col>
          <el-col :span="24" style="margin-top: 10px">
            <el-form-item>
              <el-button style="float: right;margin-left: 20px" type="primary" @click="returnClick()">返回</el-button>
            </el-form-item>
          </el-col>
        </el-form>
      </el-col>
    </div>

    <!--侧边框开始-->
    <transition enter-active-class="animated slideInRight" leave-active-class="animated slideOutRight">
      <div style="position: fixed;z-index:3;top: 116px;bottom: 0;right: 20px;width: 400px;border-left: 1px solid #DCDCDC;background-color: white" v-show="asideFlag">
        <!--头部条-->
        <div style="display: inline-block;width: 100%;height: 50px;background-color: rgb(51,122,183)">
          <h3 style="color: white;letter-spacing: 2px;margin: 15px 0 0 20px;width: 100px;float: left">人员选择</h3>
          <img src="../../../../static/imgs/public/cancel.png" class="cancel-icon" @click="cancelAside"/>
        </div>
        <!--头部条结束-->
        <!--边框内容-->
        <div style="display: inline-block;width: 100%;position:absolute;top:80px;right:0;bottom:20px;overflow: auto">
          <el-tree
            :data="peopleTree"
            show-checkbox
            default-expand-all
            node-key="id"
            ref="tree"
            highlight-current
            :props="defaultProps">
          </el-tree>
          <el-button style="float: right;margin-left: 20px;margin-right: 20px" @click="cancelAside">返回</el-button>
          <el-button type="primary" style="float: right" @click="determineChoose">确定</el-button>
        </div>
        <!--边框内容结束-->
      </div>
    </transition>
    <!--侧边框结束-->
  </div>
</template>
<script>
  import FileList from '../../../common/smallComponent/fileList.vue'
  export default {
    name: 'viewEmerPlan',
    data() {
      return {
        //------------------预案分类---------------------
        cascaderOptions: [],
        tempTypeString:[],

        //------------------表单数据---------------------
        form:{
          classify:[],
          level:'',
          planName:'',
          planFlag:'',
          interval:'',
          noticedPeople:'',
          goodsArray:[],
          firstDept:'',
          firstContent:'',
          secondDept:'',
          secondContent:'',
          thirdDept:'',
          thirdDept01:'',
          thirdDept02:'',
          thirdContent01:'',
          thirdContent02:'',
          fourthDept:'解除应急通知模板',
          fourthDeptContent:'',
        },
        //附件列表
        fileList:[],

        //---------------------侧边框----------------------------
        asideFlag:false,
        peopleTree: [{
          id: 1,
          label: '一级 1',
          children: [{
            id: 4,
            label: '二级 1-1',
            children: [{
              id: 9,
              label: '三级 1-1-1'
            }, {
              id: 10,
              label: '三级 1-1-2'
            }]
          }]
        }, {
          id: 2,
          label: '一级 2',
          children: [{
            id: 5,
            label: '二级 2-1'
          }, {
            id: 6,
            label: '二级 2-2'
          }]
        }, {
          id: 3,
          label: '一级 3',
          children: [{
            id: 7,
            label: '二级 3-1'
          }, {
            id: 8,
            label: '二级 3-2'
          }]
        }],
        defaultProps: {
          children: 'children',
          label: 'label'
        }
      }
    },
    components : {
      FileList
    },
    created:function () {
      this.getPlanType();
      this.getPlanData();
    },
    watch:{
      $route(to, from){
        if(from.name==='emerPlan'&&this.$route.name==='viewEmerPlan'){
          this.getPlanType();
          this.getPlanData();
        }
      }
    },
    methods:{
      //------------------------初始操作---------------------
      //获取预案所有分类
      getPlanType:function () {
        this.$http.get('emgType/getAll/'+this.$tool.getStorage('LOGIN_USER').companyId).then(function (res) {
          this.editPlanTypeArray(res.data.data);
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message({
            showClose: true,
            message: '网络错误，请尝试重登录',
            type: 'error'
          });
        }.bind(this));
      },
      //编辑预案所有分类
      editPlanTypeArray:function (typeTree) {
        this.cascaderOptions.splice(0);
        for(let i=0;i<typeTree.length;i++){
          let tempArray={value:typeTree[i].id,label:typeTree[i].typeName};
          if(typeTree[i].subTypes.length){
            tempArray.children=[];
            for(let j=0;j<typeTree[i].subTypes.length;j++){
              tempArray.children.push({value:typeTree[i].subTypes[j].id,label:typeTree[i].subTypes[j].typeName});
            }
          }
          this.cascaderOptions.push(tempArray);
        }
      },
      //-----------------------读取应急预警原有数据------------
      getPlanData:function () {
        if(this.$route.params.planId){

          //获取预案内容
          this.$http.post('emgPlan/find',{id:this.$route.params.planId}).then(function (res) {
            let formData=res.data.data.list[0];
            if(formData.topTypeId>0){
              this.form.classify[0]=formData.topTypeId;
              this.form.classify[1]=formData.typeId;
            }else{
              this.form.classify[0]=formData.typeId;
            }
            this.form.planName=formData.name;
            this.form.level=formData.respLevel;
            this.form.planFlag=formData.warnSignal;
            this.form.interval=formData.timeInterval;
            this.form.goodsArray.splice(0);
            for(let k=0;k<formData.emgPlanGoods.length;k++){
              this.form.goodsArray.push({value:formData.emgPlanGoods[k].id,label:formData.emgPlanGoods[k].name});
            }

            this.form.firstDept=formData.leaderRequireName+'：';
            this.form.firstContent=formData.leaderRequire;
            this.form.secondDept=formData.officeRequireName+'：';
            this.form.secondContent=formData.officeRequire;
            this.form.thirdDept=formData.commandRequireName+'：';
            this.form.thirdDept01=formData.startupRequireName+'：';
            this.form.thirdDept02=formData.levelRequireName+'：';
            this.form.thirdContent01=formData.startupRequire;
            this.form.thirdContent02=formData.levelRequire;
            this.form.fourthDeptContent=formData.relieveTemplate;
          }.bind(this)).catch(function (err) {
            console.log(err);
            this.$message({
              showClose: true,
              message: '网络错误，请尝试重登录',
              type: 'error'
            });
          }.bind(this));

          //附件列表
          let loadParams=new URLSearchParams;
          loadParams.append("contentId",this.$route.params.planId);
          loadParams.append("contentType",0);
          this.$http.post('file/find',loadParams).then(function (res) {
            if(res.data.success){
              this.fileList=res.data.data;
            }else{
              this.fileList=[];
            }
          }.bind(this)).catch(function (err) {
            console.log('获取附件错误'+err);
          }.bind(this));

        }
      },

      //清空表单中的指定项
      clearForm:function () {
        this.form.planFlag='';
        this.form.interval='';
        this.form.noticedPeople='';
        this.form.firstDept='';
        this.form.firstContent='';
        this.form.secondDept='';
        this.form.secondContent='';
        this.form.thirdDept='';
        this.form.thirdDept01='';
        this.form.thirdContent01='';
        this.form.thirdDept02='';
        this.form.thirdContent02='';
        this.form.goodsArray.splice(0);
      },
      //-----------------------表单操作----------------------
      //人员选择
      choosePeople:function () {
        this.asideFlag=true;
      },
      determineChoose:function () {
        alert('该功能未开发完全');
        //console.log(this.$refs.tree.getCheckedKeys());
        this.asideFlag=false;
      },
      cancelAside:function () {
        this.asideFlag=false;
      },

      //------------------------交互操作-----------------------
      returnClick:function () {
        this.$refs['ruleForm'].resetFields();
        this.fileList=[];//清空附件列表
        this.$router.go(-1);
      },

    }
  }
</script>
<style>
</style>
