<template>
  <div id="userAccountKPI" style="height: 100%;width: 100%">

    <div class="background-style">
      <!--搜索区-->
      <div style="height:60px;width: 100%;margin-top: 10px">
        <div id="simpleSearch" style="float: left;width: 100%;height: 100%;background-color: white;">
          <div style="margin-left: 20px;width: 100%">
            <div style="display: inline-block;padding-top: 10px;padding-bottom: 10px;padding-right: 20px;margin: auto">
              <div style="float: left;margin-left: 2px">
                <el-date-picker
                  v-model="year"
                  type="year"
                  placeholder="选择年份"
                  style="margin: 0;width: 120px">
                </el-date-picker>
                <el-input v-model="input" placeholder="请输入人员姓名" style="width: 350px">
                  <template slot="prepend">姓名搜索:</template>
                </el-input>
                <el-date-picker
                  id="investigationDateRange"
                  v-model="dateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期">
                </el-date-picker>
              </div>
              <div style="float: left;margin-left: 10px">
                <el-button class="search-btn" @click="sendRequest(input.trim(),1)">搜&nbsp;&nbsp;索</el-button>
              </div>

              <div style="float: right;margin-left: 50px">
                <!--<el-button type="success" @click="downloadPersonTable">导出表格</el-button>-->
              </div>
            </div>
          </div>
        </div>
      </div>

      <div style="width: 100%;background-color: white;margin-top: 10px;">
        <el-col :xs="7" :sm="7" :md="7" :lg="7" :xl="5">
          <div style="height: 480px;overflow: scroll;width: 100%">
            <el-tree
              :data="deptOptions"
              :props="defaultProps"
              @check="checkHandle"
              empty-text="公司数据加载中"
              show-checkbox
              highlight-current
              node-key="id"
              style="width: 350px">
            </el-tree>
          </div>
        </el-col>
        <el-col :xs="17" :sm="17" :md="17" :lg="17" :xl="19">
          <!--表格区-->
          <div style="margin-bottom: 20px;width: 100%">
            <div style="width: 100%;margin: auto">
              <div style="padding-left: 5px;padding-right: 5px">
                <el-table
                  v-loading="loading"
                  element-loading-text="同步数据中"
                  element-loading-spinner="el-icon-loading"
                  element-loading-background="rgba(255, 255, 255, 0.9)"
                  ref="singleTable"
                  :data="tableData"
                  highlight-current-row
                  style="width: 100%;"
                  stripe
                  border>
                  <el-table-column
                    type="index"
                    width="50"
                    align="center"
                    label-class-name="header-style">
                  </el-table-column>
                  <el-table-column
                    prop="username"
                    label="姓名"
                    width="150"
                    align="center"
                    label-class-name="header-style">
                  </el-table-column>

                  <el-table-column
                    prop="userSelfInspectRO.finishCondition"
                    label="自查完成情况"
                    width="150"
                    align="center"
                    show-overflow-tooltip
                    label-class-name="header-style">
                  </el-table-column>
                  <el-table-column
                    prop="userSelfInspectRO.limitType"
                    label="自查频率"
                    width="150"
                    align="center"
                    show-overflow-tooltip
                    label-class-name="header-style">
                  </el-table-column>
                  <el-table-column
                    prop="eduUserStudyProgressRO.userIntegralCondition"
                    label="积分完成情况"
                    width="150"
                    align="center"
                    show-overflow-tooltip
                    label-class-name="header-style">
                  </el-table-column>
                  <el-table-column
                    prop="eduUserStudyProgressRO.userCourseCondition"
                    label="课时完成情况"
                    width="150"
                    align="center"
                    show-overflow-tooltip
                    label-class-name="header-style">
                  </el-table-column>
                  <el-table-column
                    prop="deptName"
                    label="部门"
                    width="180"
                    align="center"
                    label-class-name="header-style">
                  </el-table-column>
                  <el-table-column
                    prop="companyName"
                    label="所属公司"
                    width="200"
                    align="center"
                    show-overflow-tooltip
                    label-class-name="header-style">
                  </el-table-column>
                 <!-- <el-table-column
                    prop="eduUserStudyProgressRO.userIntegral"
                    label="已完成积分"
                    width="150"
                    align="center"
                    show-overflow-tooltip
                    label-class-name="header-style">
                  </el-table-column>
                  <el-table-column
                    prop="eduUserStudyProgressRO.remainIntegral"
                    label="未完成积分"
                    width="150"
                    align="center"
                    show-overflow-tooltip
                    label-class-name="header-style">
                  </el-table-column>
                  <el-table-column
                    prop="eduUserStudyProgressRO.totalIntegral"
                    label="总积分"
                    width="150"
                    align="center"
                    show-overflow-tooltip
                    label-class-name="header-style">
                  </el-table-column>
                  <el-table-column
                    prop="eduUserStudyProgressRO.userCourseTime"
                    label="已完成课时"
                    width="150"
                    align="center"
                    show-overflow-tooltip
                    label-class-name="header-style">
                  </el-table-column>
                  <el-table-column
                    prop="eduUserStudyProgressRO.remainCourseTime"
                    label="未完成课时"
                    width="150"
                    align="center"
                    show-overflow-tooltip
                    label-class-name="header-style">
                  </el-table-column>
                  <el-table-column
                    prop="eduUserStudyProgressRO.totalCourseTime"
                    label="总课时"
                    width="150"
                    align="center"
                    show-overflow-tooltip
                    label-class-name="header-style">
                  </el-table-column>
                  <el-table-column
                    prop="userSelfInspectRO.finishCount"
                    label="已完成自查"
                    width="150"
                    align="center"
                    show-overflow-tooltip
                    label-class-name="header-style">
                  </el-table-column>
                  <el-table-column
                    prop="userSelfInspectRO.yearCount"
                    label="总自查次数"
                    width="150"
                    align="center"
                    show-overflow-tooltip
                    label-class-name="header-style">
                  </el-table-column>-->
                </el-table>
              </div>
            </div>
            <!--分页-->
            <div style="margin:10px 0 0 20px">
              <el-pagination
                background
                layout="prev, pager, next"
                :total="itemNumber"
                :page-size="pageSize"
                @current-change="currentPage">
              </el-pagination>
            </div>
          </div>
        </el-col>
      </div>

    </div>

  </div>
</template>

<script>
    export default {
      name: 'userAccountKPI',
      data() {
        let checkIdNumber = (rule, value, callback) => {
          if (value === '') {
            callback(new Error('请输入身份证号码'));
          } else if(value.length!==18){
            callback(new Error('需为18位号码'));
          }else{
            callback();
          }
        };
        return {
          //搜索数据
          personOptions: [
            {value: "姓名", label: "姓名"},
          ],
          classValue: ['姓名'],
          input: '',
          year:'',//年份选择器对应的值，为date格式
          //表格数据
          tableData: [],
          itemNumber: 0,
          nowPage: 1,
          pageSize: 13,
          multipleSelection: [],
          loading: false,
          //选择器的数据
          eduDegreeOptions:[
            {value:"初中",label:"初中"},
            {value:"高中",label:"高中"},
            {value:"大专",label:"大专"},
            {value:"本科",label:"本科"},
            {value:"硕士",label:"硕士"},
            {value:"博士",label:"博士"},
          ],
          genderOptions:[
            {value:false,label:'女'},
            {value:true,label:'男'},
          ],
          postList:[],
          currentCompanyPostList:[],//当前公司的岗位，只会查找一次

          dateRange: '',
          /*班组的信息*/

          //-----------------部门选择数据----------------------------
          defaultProps: {
            children: 'subDept',
            label: 'name'
          },
          searchDeptIds:[],
          //-----------------上传下载的baseurl-----------------
          cookies: true,
          uploadBaseUrl:'',
          //对话框数据
          uploadVisible:false,
          fileList:[],

        }
      },
      computed: {
        roleOptions: function () {
          return this.$store.state.sysManageData.roleOptions;
        },
        deptOptions: function () {//当前公司和下级
          return this.$store.state.sysManageData.deptTree;
        },
        teamOptions: function () {
          return this.$store.state.sysManageData.teamData;
        },
        treeArray:function(){//所有公司
          return this.$store.state.sysManageData.topAllDeptTree;
        }

      },
      mounted: function () {
        this.year=new Date();
        //获取所有用户数据
        this.sendRequest(this.input.trim(), 1);
        //当前公司和下级
        this.$store.dispatch("getAllDept");
        //获取所有角色
        this.$store.dispatch("getRoleList");
        //获取班组
        this.$store.dispatch("getTeam");
        //所有公司
        this.$store.dispatch("getTopAllDeptTree");
        //当前公司的岗位
        this.searchPost();

        this.uploadBaseUrl=this.$http.defaults.baseURL + 'user/addBatch';
      },
      watch: {
        $route(to, from) {
          if (this.$route.name === 'userManage') {
            this.$store.dispatch("getRoleList");
          }
        }
      },
      methods: {
        //-----------------------------交互事件-------------------------------
        //向服务器发送请求，查找人员
        sendRequest: function (name, pageCurrent) {
          let params=new URLSearchParams;
          params.append("username",name);
          params.append("pageSize",this.pageSize);
          params.append("pageCurrent",pageCurrent);
          if(this.searchDeptIds.length){
            params.append("deptIds",this.searchDeptIds);
          }
          if (this.dateRange) {
            params.append("startDate",this.dateRange[0]) ;
            params.append("endDate",this.dateRange[1]) ;
          }
          params.append("year",this.year.getFullYear());
          this.$http.post('report/perform/findUserPerform',params).then(function (res) {
            if (res.data.success) {
              this.itemNumber = res.data.data.total;
              this.tableData = res.data.data.list;
              // this.tableData.userSelfInspectRO.finishCount =  res.data.data.list.

            }
          }.bind(this)).catch(function (err) {
            console.log(err);
          });
        },
        //查找所有岗位
        searchPost:function () {
          this.currentCompanyPostList=[];
          this.$http.post('post/find?pageSize=1000&companyId='+this.$tool.getStorage('LOGIN_USER').companyId).then(function (res) {
            if (res.data.success) {
              this.currentCompanyPostList=res.data.data.list;
              this.currentCompanyPostList.forEach(function (item) {
                item.value=item.id;
                item.label=item.postName;
              })
            }
          }.bind(this)).catch(function (err) {
            console.log(err);
            this.$message({
              showClose: true,
              message: '岗位查找失败',
              type: 'error'
            });
          }.bind(this));
        },
        //查找对应公司的岗位
        searchPostByCompanyId:function (companyId) {
          this.postList=[];
          this.$http.post('post/find?pageSize=1000&companyId='+companyId).then(function (res) {
            if (res.data.success) {
              this.postList=res.data.data.list;
              this.postList.forEach(function (item) {
                item.value=item.id;
                item.label=item.postName;
              })
            }
          }.bind(this)).catch(function (err) {
            console.log(err);
            this.$message({
              showClose: true,
              message: '岗位查找失败',
              type: 'error'
            });
          }.bind(this));
        },


        //-----------------------------表格事件-------------------------------
        //翻页
        currentPage: function (val) {
          this.nowPage = val;
          this.sendRequest(this.input.trim(), this.nowPage);
        },

        //-----------------------------------部门侧边框事件---------------------------------
        checkHandle(data, status){
          this.searchDeptIds = status.checkedKeys;
          this.sendRequest(this.input.trim(), 1);
        },

        //--------------------------------导出列表----------------------------------
        downloadPersonTable:function () {
          const loading = this.$loading({
            lock: true,
            text: 'Loading',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });
          let params=new URLSearchParams;
          if(this.searchDeptIds.length){
            params.append("deptIds",this.searchDeptIds);
          }
          this.$http({ // 用axios发送post请求
            method: 'post',
            url: 'user/sysUserOutput', // 请求地址
            data: params, // 参数
            responseType: 'blob' // 表明返回服务器返回的数据类型
          }).then((res) => { // 处理返回的文件流
            loading.close()
            const elink = document.createElement('a') // 创建a标签
            elink.download = '人员名册'+this.transferTime(new Date(),null,true)+'.xlsx' // 文件名
            elink.style.display = 'none'
            const blob = new Blob([res.data])
            elink.href = URL.createObjectURL(blob)
            document.body.appendChild(elink)
            elink.click() // 触发点击a标签事件
            document.body.removeChild(elink)
          })
        },
        uploadPersonTableSuccess:function (res) {
          if(res.success){
            this.$message.success('上传成功！');
            this.uploadVisible=false;
          }else{
            this.$message.warning('上传失败，请检查网络');
            this.uploadVisible=false;
          }
        },
        //--------------------------------更新花名册----------------------------------
        updateUserReport:function () {
          const loading = this.$loading({
            lock: true,
            text: 'Loading',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });
          this.$http.post('sysUserReport/addOrUpdateThisYear').then(function (res) {
            loading.close()

            if (res.data.success) {
              this.$message({
                showClose: true,
                message: '更新成功！',
                type: 'success'
              });
            }
          }.bind(this)).catch(function (err) {
            loading.close()
            console.log(err);
            this.$message({
              showClose: true,
              message: '操作失败',
              type: 'error'
            });
          }.bind(this));
        }
      }
    }
</script>

<style scoped>
  .form-item{
    width: 100%;
  }
</style>
