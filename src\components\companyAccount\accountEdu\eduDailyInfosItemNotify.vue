<template>
  <div class="background-style" style="padding: 10px">
    <el-container>
      <el-main>
        <el-form ref="form" :model="form" label-width="150px">
          <el-row type="flex" style="margin:0">
            <el-col :span="8">
              <el-form-item label="通知标题">
                <span>{{notify.title}}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex" style="margin:10px 0 0 0">
            <el-col :span="24">
              <el-form-item label="通知内容">
                <vue-editor v-model="notify.notify"></vue-editor>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex" class="row" justify="center">
            <el-button size="small" :span="2" type="primary" @click="$router.back();">返回</el-button>
          </el-row>
        </el-form>
      </el-main>
    </el-container>
  </div>
</template>

<script>



  import { VueEditor } from 'vue2-editor'

  export default {
    components: {
      VueEditor
    },
    data(){
      return {
        // 活动的内容
        notify : {},
      }
    },
    created(){
      this.init();
    },
    watch:{
      $route(to,from){
        let row = to.params && to.params.row && to.params.row.data;
        if(to.name === 'eduDailyInfosItemNotify') {
          if(row){
            this.notify = row;
          }
        }
      }
    },
    methods:{
      // 初始化
      init(){
        this.notify = this.$route.params.row.data;
      },
    }
  }
</script>

<style>

</style>
