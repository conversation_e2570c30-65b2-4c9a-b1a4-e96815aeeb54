<template>
  <div id="superiorEmerResponse">
    <div
      class="background-style">
      <!--应急列表区-->
      <div style="width: 100%;">
        <div style="float: left;width: 100%">
          <el-radio-group v-model="radioType" style="margin: 10px;" @change="searchClick">
            <el-radio-button v-for="item in statusRadioArray" :label="item.value" :key="item.value"> <el-badge :value="item.taskNum" class="item">&nbsp&nbsp&nbsp{{item.label}}&nbsp&nbsp&nbsp</el-badge></el-radio-button>
          </el-radio-group>
        </div>
        <div style="float: left;margin-left: 10px;width: 100%">
          <el-cascader
            :options="cascaderOptions"
            v-model="emerClass"
            placeholder="分类选择">
          </el-cascader>
          <el-select v-model="emerLevel" placeholder="级别选择" style="width: 120px">
            <el-option
              v-for="item in levelOptions"
              :key="item.value"
              :label="item.label"
              :value="item.label">
            </el-option>
          </el-select>
          <el-input placeholder="请输入预案名称" v-model="searchInput" style="width: 300px;margin-right: 5px">
          </el-input>
          <el-button icon="el-icon-search" @click="searchClick" type="primary">搜 索</el-button>
        </div>
        <div style="width: 100%;float: left">
          <div style="padding: 10px">
            <el-table
              v-loading="tableLoading"
              :data="tableData"
              border
              highlight-current-row
              style="width: 100%">
              <el-table-column
                prop="num"
                label="编号"
                width="60"
                align="center"
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                label="状态"
                width="150"
                align="center"
                label-class-name="header-style">
                <template slot-scope="scope">
                  <el-tag :type="scope.row.tagType">{{scope.row.emerStatus}}</el-tag>
                </template>
              </el-table-column>
              <el-table-column
                prop="emerName"
                label="应急响应名称"
                width="400"
                show-overflow-tooltip
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="companyName"
                label="发布公司"
                width="250"
                show-overflow-tooltip
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="createDate"
                label="创建日期"
                width="150"
                align="center"
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="emerTopClass"
                label="一级分类"
                width="170"
                align="center"
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="emerClass"
                label="二级分类"
                width="170"
                align="center"
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="emerLevel"
                label="级别"
                min-width="120"
                align="center"
                label-class-name="header-style">
              </el-table-column>
              <el-table-column fixed="right" label="操作" label-class-name="header-style" width="100">
                <template slot-scope="scope">
                  <div v-if="scope.row.actionFlag===1">
                    <el-button size="mini" type="warning" @click="itemControlClick(scope.row)">记录</el-button>
                  </div>
                  <div v-if="scope.row.actionFlag===2">
                    <el-button size="mini" type="primary" @click="itemSummaryClick(scope.row)">总结</el-button>
                  </div>
                  <div v-else>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div >
            <el-pagination
              background
              layout="prev, pager, next"
              :page-size="pageSize"
              :current-page="currentPage"
              :total="totalItem"
              @current-change="currentPageClick">
            </el-pagination>
          </div>
        </div>
      </div>
      <!--应急列表区结束-->

    </div>
  </div>
</template>

<script>
  import {mapGetters} from 'vuex'

  export default {
    name: "superiorEmerResponse",
    data() {
      return {
        //------------------------搜索数据----------------------------
        emerClass: ['全部分类'],
        cascaderOptions: [],
        emerLevel: '全部级别',
        levelOptions: [
          {value: '全部级别', label: '全部级别'},
          {value: '1级', label: '1级'},
          {value: '2级', label: '2级'},
          {value: '3级', label: '3级'},
          {value: '4级', label: '4级'},
          {value: '应急警报', label: '应急警报'}
        ],
        radioType:3,//员工记录页面，只显示应急响应和总结两个状态
        statusRadioArray:[{value:3,label:'须填工作简报',taskNum:0},{value:11,label:'须填应急总结',taskNum:0}],
        searchInput: '',
        //------------------------表格数据---------------------------
        tableLoading: false,
        tableData: [],
        currentPage: 1,
        totalItem: 1,
        pageSize: 10,
      }
    },
    computed:{
      statusOptions:function () {
        return this.$store.state.emergencyData.emergencyStatusArray;
      },
      statusTable:function () {
        return this.$store.state.emergencyData.statusTable;
      },
    },
    mounted: function () {
      this.radioType=3;
      this.$store.dispatch('getEmergencyStatusArray');
      this.getPlanType();
      this.searchClick();
      this.getTaskNum();
    },
    watch:{
      $route(to, from){
        if(this.$route.name==='superiorEmerResponse'){
          this.searchClick();
          this.getTaskNum();
        }
      },
    },
    methods: {
      //--------------------------初始化-------------------------------
      //获取分类
      getPlanType: function () {
        this.$http.get('emgType/getAll/'+this.$tool.getStorage('LOGIN_USER').companyId).then(function (res) {
          this.editPlanTypeArray(res.data.data);
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message({
            showClose: true,
            message: '网络错误，请尝试重登录',
            type: 'error'
          });
        }.bind(this));
      },
      //编写分类
      editPlanTypeArray: function (typeTree) {
        this.cascaderOptions = [];
        this.chooseTypeOptions = [];
        this.cascaderOptions.push({value: '全部分类', label: '全部分类'});

        for (let i = 0; i < typeTree.length; i++) {
          let tempArray = {value: i, label: typeTree[i].typeName, id: typeTree[i].id};
          if (typeTree[i].subTypes.length) {
            tempArray.children = [];
            for (let j = 0; j < typeTree[i].subTypes.length; j++) {
              tempArray.children.push({
                value: j,
                label: typeTree[i].subTypes[j].typeName,
                id: typeTree[i].subTypes[j].id
              });
            }
          }
          this.cascaderOptions.push(tempArray);
          this.chooseTypeOptions.push(tempArray);
        }
      },
      //--------------------------搜索响应事件--------------------------
      searchClick: function () {
        let params = new URLSearchParams;
        this.currentPage = 1;
        params.append("pageCurrent", 1);
        this.sendRequest(params);
      },
      currentPageClick: function (val) {
        if (val) {
          this.currentPage = val;
          let params = new URLSearchParams;
          params.append("pageCurrent", Number(val));
          this.sendRequest(params);
        }
      },
      //--------------------------交互事件------------------------------
      sendRequest: function (params) {
        if (this.emerClass[0] !== '全部分类') {
          if (this.emerClass[1] >= 0) {
            params.append("typeId", this.chooseTypeOptions[this.emerClass[0]].children[this.emerClass[1]].id);
          } else {
            params.append("typeId", this.chooseTypeOptions[this.emerClass[0]].id);
          }
        }
        if (this.emerLevel && this.emerLevel !== '全部级别') {
          params.append("respLevel", this.emerLevel);
        }
        if (this.searchInput.trim()) {
          params.append("name", this.searchInput.trim());
        }
        params.append("history", 0);
        params.append("pageSize", this.pageSize);
        params.append("status",this.radioType);

        //上级发布的应急列表
        params.append("companyId", this.$tool.getStorage('LOGIN_USER').parentCompanyId);
//        params.append("companyId", 4);

        this.tableLoading = true;
        this.$http.post('planPublic/findMotherCompanyEmg', params).then(function (res) {
          if (res.data.success) {
            if (res.data.data.list.length) {
              this.totalItem = res.data.data.total;
              this.tableLoading = false;
              this.editTable(res.data.data.list);
            } else {
              this.totalItem =0;
              this.tableData.splice(0);
              this.tableLoading = false;
            }
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message({
            showClose: true,
            message: '网络错误，请尝试重登录',
            type: 'error'
          });
        }.bind(this));
      },
      editTable:function (list) {
        this.tableData=[];
        for(let i=0;i<list.length;i++){
          let tempObj={
            id:list[i].id,
            num:(this.currentPage-1)*10+i+1,
            status:list[i].status,
            emerStatus:this.statusTable[Number(list[i].status)].name,
            tagType:this.statusTable[Number(list[i].status)].labelType,
            emerName:list[i].name,
            createDate:this.transferTime(list[i].createDate),
            emerTopClass:list[i].topTypeName,
            emerClass:list[i].typeName,
            emerLevel:list[i].respLevel,
            planId:list[i].planId,
            startId:list[i].startPlanId,
            companyId:list[i].companyId,
            companyName:list[i].companyName
          };
          if(tempObj.status===3){
            tempObj.actionFlag=1;
          }else if(tempObj.status===11){
            tempObj.actionFlag=2;
          }else{
            tempObj.actionFlag=3;
          }
          this.tableData.push(tempObj);
        }
        this.tableLoading=false;

      },
      //进入process界面
      itemControlClick:function (row) {
        var addWorkBrief=false
        //TODO 去除硬编码
        if(row.status==3){
          addWorkBrief=true
        }
        this.$router.push({name:'subNewWorkBriefWorkflow',params:{emergencyId:row.id,finish:false,addWorkBrief:addWorkBrief}});
      },
    itemSummaryClick:function (row) {
        this.$router.push({name:'editSummary',params:{emerData:row}});
      },
      //获取须填工作简报和须填工作总结的任务总数
      getTaskNum:function () {
        this.statusRadioArray[0].taskNum=0;
        this.statusRadioArray[1].taskNum=0;
        this.$http.post('planPublic/findMotherCompanyEmg?companyId='+this.$tool.getStorage('LOGIN_USER').parentCompanyId+'&history=0&status=3').then(function (res) {
          if (res.data.success) {
            this.statusRadioArray[0].taskNum=res.data.data.total;
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message({
            showClose: true,
            message: '网络错误，请尝试重登录',
            type: 'error'
          });
        }.bind(this));
        this.$http.post('planPublic/findMotherCompanyEmg?companyId='+this.$tool.getStorage('LOGIN_USER').parentCompanyId+'&history=0&status=11').then(function (res) {
          if (res.data.success) {
            this.statusRadioArray[1].taskNum=res.data.data.total;
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message({
            showClose: true,
            message: '网络错误，请尝试重登录',
            type: 'error'
          });
        }.bind(this));
      }
    }
  }
</script>

<style scoped>

</style>
