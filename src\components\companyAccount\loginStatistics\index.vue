<template>
  <div id="trainingPlanIndex">
    <div class="background-style">

      <!--搜索区-->
      <div class="search-bar">
        <div style="padding:10px 10px 0 10px;float: left">
          <!--年份：-->
          <el-select style="width:100px;" v-model="form.type" clearable placeholder="请选择">
            <el-option label="部门" value="0" ></el-option>
            <el-option label="公司" value="1" ></el-option>
          </el-select>
          <!--年份：-->
          <el-date-picker
            v-model="form.startDate"
            type="date"
            clearable
            placeholder="开始时间">
          </el-date-picker>
          <!--年份：-->
          <el-date-picker
            v-model="form.endDate"
            type="date"
            clearable
            placeholder="结束时间">
          </el-date-picker>
          <el-button
            @click="searchBtnClickHandle"
            type="primary" icon="el-icon-search" style="margin-left: 20px">搜索</el-button>
        </div>
      </div>

      <!--表格区-->
      <div style="width: 100%;">
        <div style="padding: 20px 10px 20px 30px">
          <el-table
            border
            :data="tableData">
            <el-table-column
              type="index"
              label="编号"
              width="50"
              align="center"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="deptName"
              label="部门/公司名称"
              width="300"
              show-overflow-tooltip
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="userCount"
              label="员工数"
              show-overflow-tooltip
              width="100"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="loginCount"
              label="登录次数"
              width="100"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="type"
              label="类型"
              width="100"
              label-class-name="header-style">
              <template slot-scope="scope">
                {{ scope.row.type == 0 ? '部门' : '公司'}}
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'trainingPlanIndex',
    data() {
      return {
        form : {
          // 类型
          type : '',
          // 开始时间
          startDate : '',
          // 结束时间
          endDate : '',
        },
        tableData : [],
      }
    },
    mounted(){
      this.init();
    },
    watch:{
      $route(to,from){
        if(to.name === 'loginStatisticsIndex') {
          this.init();
        }
      }
    },
    methods:{
      // 初始化
      init(){
        // 搜索
        this.getCurrentMonthFirst();
        this.getCurrentMonthLast();
        this.searchBtnClickHandle();
      },

      getCurrentMonthFirst () {
        let date = new Date()
        date.setDate(1)
        let month = parseInt(date.getMonth() + 1)
        let day = date.getDate()
        if (month < 10)  month = '0' + month
        if (day < 10)  day = '0' + day
        this.form.startDate = date.getFullYear() + '-' + month + '-' + day
      },
      getCurrentMonthLast () {
        let date2 = new Date()
        let month2 = parseInt(date2.getMonth() + 1)
        let day2 = date2.getDate()
        if (month2 < 10)  month2 = '0' + month2
        if (day2 < 10)  day2 = '0' + day2
        this.form.endDate = date2.getFullYear() + '-' + month2 + '-' + day2
      },
      // 搜索按钮
      searchBtnClickHandle(){
        this.$store.dispatch('punchRecordFindLoginRecord', this.form).then(function(res){
          if(res.success){
            this.tableData = res.data;
          } else {
            this.$message({
              type : 'error',
              message : res.message || '错误'
            })
          }
        }.bind(this));
      },

    }
  }
</script>
<style>
</style>
