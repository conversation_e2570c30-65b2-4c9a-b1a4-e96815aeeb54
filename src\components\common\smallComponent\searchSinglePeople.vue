<template>
  <div id="searchSinglePeople">
    <el-dialog :title="data.title" :visible.sync="data.isShow">
      <el-select
        v-model="person"
        filterable
        remote
        reserve-keyword
        clearable
        placeholder="请输入姓名后选择"
        :remote-method="remotePerson"
        :loading="personLoading"
        @click=""
        style="width: 100%">
        <el-option
          v-for="item in personOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value">
        </el-option>
      </el-select>
      <span slot="footer" class="dialog-footer">
          <el-button @click="data.isShow = false">取 消</el-button>
          <el-button type="primary" @click="determineClick">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
  export default {
    name: 'searchSinglePeople',
    props : ['data','defaultPersonId'],
    data() {
      return {
        person:'',
        personLoading:false,
        personOptions:[],
      }
    },
    watch:{
      defaultPersonId:function (val) {
        if(val){
          this.personOptions=[];
          this.personOptions.push(this.data.defaultPerson);
          this.person=this.data.defaultPerson.value;
        }else{
          this.personOptions=[];
          this.person='';
        }
      }
    },
    methods:{
      remotePerson:function (val) {
        this.personLoading = true;
        let url='';
        //选择审核人时，搜索范围为全公司
        if(this.data.title==='请选择审核人'||this.data.title==='请选择下一个审核人'){
          url='user/findAllCompanyUser?username='+val;
        }else {
          url='user/find?username='+val;
        }
        this.$http.get(url).then(function (res) {
          if(res.data.success){
            this.personOptions=[];
            for (let i = 0; i < res.data.data.list.length; i++) {
              this.personOptions.push({value:res.data.data.list[i].userId,label:res.data.data.list[i].username});
            }
            this.personLoading = false;
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
        });
      },
      determineClick:function () {
        this.$emit("determineClick", this.person);
      },

    }
  }
</script>
<style>
</style>
