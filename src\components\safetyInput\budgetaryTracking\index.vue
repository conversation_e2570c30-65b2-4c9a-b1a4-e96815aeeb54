<template>
  <div class="background-style">
    <el-tabs v-model="activeName" @tab-click="handleClick" style="margin-left: 10px">
      <el-tab-pane label="公司预算" name="first">
        <el-container style="background:#fff;">
          <el-main>
            <el-row >
              <el-col :span="6">
                <el-date-picker
                  @change="yearChange"
                  v-model="form.year"
                  type="year"
                  style="width: 100px;margin-top:-30px"
                  placeholder="选择日期">
                </el-date-picker>
              </el-col>
              <el-col :span="6" style="font-size:medium">
                费用预算总额：{{ total.budgetFee| rounding}}
              </el-col>
              <el-col :span="6" style="font-size: medium">
                预算执行总额：{{ total.actualFee| rounding}}
              </el-col>
              <el-col :span="6" style="font-size: medium" :style="{ color : isOverran(total.budgetFee - total.actualFee) }">
                预算余额总额：{{ total.budgetFee - total.actualFee| rounding}}
              </el-col>
            </el-row>
            <el-row style="margin-top:20px;">
              <el-col :span="12">
                <el-col >费用预算一级项目（元）：</el-col>
                <el-table
                  border
                  highlight-current-row
                  @current-change="handleCurrentChange"
                  :data="mainTableData.list"
                  style="width: 95%">
                  <el-table-column
                    prop="item"
                    show-overflow-tooltip
                    label="预算计划项目名称"
                    min-width="150"
                    label-class-name="header-style">
                  </el-table-column>
                  <el-table-column
                    prop="itemTotalBudget"
                    label="费用预算"
                    label-class-name="header-style">
                    <template slot-scope="scope">
                      <span>{{scope.row.itemTotalBudget | rounding}}</span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="accountSum"
                    label="预算执行"
                    label-class-name="header-style">
                    <template slot-scope="scope">
                      <span>{{scope.row.accountSum | rounding}}</span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    label="执行率"
                    label-class-name="header-style" >
                    <template slot-scope="scope"  >
                      <span>{{  isNaN((Math.round(scope.row.accountSum / scope.row.itemTotalBudget * 10000) / 100.00)) ? '0%' : (Math.round(scope.row.accountSum / scope.row.itemTotalBudget * 10000) / 100.00) + '%'  }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    label="预算余额"
                    label-class-name="header-style" >
                    <template slot-scope="scope"  >
                      <span :style="{ color : isOverran(scope.row.itemTotalBudget - scope.row.accountSum) }" >{{scope.row.itemTotalBudget - scope.row.accountSum| rounding}}</span>
                    </template>
                  </el-table-column>
                </el-table>
                <el-pagination
                  background
                  layout="prev, pager, next"
                  :current-page="mainTableData.pageNum"
                  :page-size="form.pageSize"
                  :total="mainTableData.total"
                  @current-change ="disasterPageChangeHandle">
                </el-pagination>
              </el-col>
              <el-col :span="12">
                <el-col >费用预算二级项目（元）：</el-col>
                <el-table
                  border
                  :data="detailTableData">
                  <el-table-column
                    prop="item"
                    label="项目明细"
                    show-overflow-tooltip
                    min-width="150"
                    label-class-name="header-style">
                  </el-table-column>
                  <el-table-column
                    prop="itemBudget"
                    label="费用预算"
                    label-class-name="header-style">
                    <template slot-scope="scope">
                      <span>{{scope.row.itemBudget | rounding}}</span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="accountSum"
                    label="预算执行"
                    label-class-name="header-style">
                    <template slot-scope="scope">
                      <span>{{scope.row.accountSum | rounding}}</span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    label="预算余额"
                    prop="accountRemain"
                    label-class-name="header-style">
                    <template slot-scope="scope"  >
                      <span :style="{ color : isOverran(Number(scope.row.itemBudget) - Number(scope.row.accountSum)) }">{{Number(scope.row.itemBudget) - Number(scope.row.accountSum) | rounding}}</span>
                    </template>
                  </el-table-column>
                </el-table>
              </el-col>
            </el-row>
            <el-row >
              <el-col :span="24" style="font-size: small;margin-top:10px;">
                预算计划备注：{{ total.remark }}
              </el-col>
            </el-row>
          </el-main>
        </el-container>
      </el-tab-pane>
      <el-tab-pane label="集团预算" name="second">
        <el-row>
          <span style="margin-left: 20px">{{ ($tool.formatDateTime(form.year) || '').substring(0,4)}}年度安全生产预算跟踪表（单位：万元）</span>
        </el-row>
        <el-row style="margin-top:10px;">
          <el-table
            show-summary
            :data="groupTable">
            <el-table-column
              width="100"
              type="index"
              align="center">
            </el-table-column>
            <el-table-column
              prop="deptName"
              show-overflow-tooltip
              label="公司"
              width="300"
              align="center">
            </el-table-column>
            <el-table-column
              prop="budgetFee"
              show-overflow-tooltip
              label="预算费用"
              width="100"
              align="center">
              <template slot-scope="scope">
                <span>{{scope.row.budgetFee | rounding}}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="actualFee"
              show-overflow-tooltip
              label="预算执行"
              width="100"
              align="center">
              <template slot-scope="scope">
                <span>{{scope.row.actualFee | rounding}}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="remainFee"
              show-overflow-tooltip
              label="预算余额"
              width="100"
              align="center"
            >
              <template slot-scope="scope">
                <span>{{scope.row.remainFee | rounding}}</span>
              </template>
            </el-table-column>
          </el-table>
        </el-row>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
  export default {
    data() {
      return {
        form : {
          // 年份
          year : '',
          // 当前页
          pageCurrent : 1,
          // 页数大小
          pageSize : 10,
        },
        plan:{
          id:-1,
        },
        // 主表----左边
        mainTableData : [],
        // 次表----右边
        detailTableData : [],
        // 统计数据
        total : {
          // 预算费用
          budgetFee : 0,
          // 实际开销
          actualFee : 0,
          // 备注
          remark : '',
        },
        // 选项卡
        activeName : 'first',
        // 选项卡----集团预算，上面的字段都是本部预算
        groupTable : [],
      }
    },
    created(){
      this.init();
    },
    watch:{
      $route(to,from){
        if(to.name == 'budgetaryTrackingIndex') {
          console.log('test')
          this.init();
        }
      }
    },
    filters: {
      rounding (value) {
        return value.toFixed(2)
      }
    },
    methods:{
      // 初始化
      init(){
        this.form.year = new Date();
        this.activeName = 'first';
        // 搜索
        this.searchBtnClickHandle();
      },
      // 清空数据
      clear(){

        this.groupTable = [];
        this.mainTableData = [];
        this.detailTableData = [];
        this.total = this.$tool.clearObj({}, this.total);
      },
      // 年份选择
      yearChange(val){
        // 搜索
        this.searchBtnClickHandle();
      },
      // 分页
      disasterPageChangeHandle(page){
        this.form.pageCurrent = page;
        this.searchBtnClickHandle();
      },
      // 搜索按钮
      searchBtnClickHandle(){
        // 根据年份获取项目
        this.$store.dispatch('costBudgetItemFindByYear', this.form).then(function(res){
          if(res.success){
            this.mainTableData = res.data;
          } else {
            this.$message({
              type : 'warning',
              message : res.message || '很遗憾，没有查询数据！'
            })
          }
        }.bind(this)).catch(function(res){
          this.$message({
            type : 'warning',
            message : res || '很遗憾，没有查询数据！'
          })
          this.clear();
        }.bind(this));
        // 根据年份获取预算费用备注、费用预算和预算执行金额
        this.$store.dispatch('costBudgetPlanSearchByYear', { year : this.form.year }).then(function(res){
          if(res.success){
            let data = res.data;
            this.total.remark = data.remark || '';
            this.total.budgetFee = data.totalBudget || 0;
            this.total.actualFee = data.costAccountRegs[0].totalAccount || 0;
            this.plan.id = data.id;
          }  else {
            this.$message({
              type : 'warning',
              message : res.message || '错误'
            })
            this.clear();
          }
        }.bind(this));

        // 根据planId更新所有项目的投入金额和余额
        this.$store.dispatch('costAccountRecordUpdateAllItemAccount', { id : this.plan.id }).then(function(res){
          if(res.success){

          }  else {
            this.$message({
              type : 'error',
              message : res.message || '错误'
            })
            this.clear();
          }
        }.bind(this));

      },
      // 左边表格---单选
      handleCurrentChange(val){
        let id = val.id;
        // 项目明细
        this.$store.dispatch('costBudgetItemShow', { id : id }).then(function(res){
          if(res.success){
            let list = res.data.costBudgetSubItems;
            this.detailTableData = list;
          } else {
            this.$message({
              type : 'error',
              message : res.message || '错误'
            })
          }
        }.bind(this));
      },
      // 选项卡点击
      handleClick(tab, event) {
        switch (tab.name){
          // 本部预算
          case 'first':
            this.searchBtnClickHandle();
            break;
          // 集团预算
          case 'second':
            this.getGroupFee();
            break;
        }
      },
      // 获取集团预算列表
      getGroupFee(){
        this.clear();
        let params = {
          deptId : this.$tool.getStorage('LOGIN_USER').companyId,
          year : this.form.year,
        }
        // 根据年份获取项目
        this.$store.dispatch('costBudgetPlanFindTrackSubcompany', params).then(function(res){
          if(res.success){
            this.groupTable = res.data.map(function(it){
              return {
                // 公司名称
                deptName : it.deptName || 0,
                // 预算费用
                budgetFee : it.totalBudget || 0,
                // 预算执行
                actualFee : it.costAccountRegs && it.costAccountRegs[0] && it.costAccountRegs[0].totalAccount || 0,
                // 预算余额
                remainFee : (it.totalBudget || 0) - (it.costAccountRegs && it.costAccountRegs[0] && it.costAccountRegs[0].totalAccount || 0)
              }
            }.bind(this))
          } else {
            this.$message({
              type : 'warning',
              message : res.message || '很遗憾，没有查询数据！'
            })
          }
        }.bind(this))
      },
      // 是否超过预算，超出预算变红
      isOverran(account){
        // 返回颜色，红色代表超出预算，黑色正常；
        if(account < 0 ) {
          return 'red';
        }else {
          return 'black';
        }
      },
    }
  }
</script>
<style>
</style>
