<template>
  <div class="background-style" style="padding: 10px">
    <el-row style="margin:0">
      <el-col :span="6">
        <el-button type="primary" size="mini" @click="$router.back()">返回</el-button>
      </el-col>
    </el-row>
    <el-row style="margin:10px 0 0 0">
      <el-col :span="24">
        <egrid class="egrid"
               stripe border
               maxHeight="400"
               :data="egrid.data"
               :columns="egrid.columns"
               :columns-schema="egrid.columnsSchema">
        </egrid>
      </el-col>
    </el-row>
  </div>
</template>

<script>


  export default {
    data(){
      return {
        // 表格
        egrid : {
          data : [],
          columns : [
            { label: '姓名', prop: 'username' },
            { label: '性别', prop: 'gender' },
            { label: '出生年月', prop: 'birthday' },
            { label: '文化程度', prop: 'degreeOfEducation' },
            { label: '入职时间', prop: 'entryDate' },
            { label: '部门', prop: 'deptName' },
            { label: '公司培训时间', prop: 'companyTrainingDate' },
            { label: '公司培训学时', prop: 'companyTrainingHours' },
            { label: '部门培训时间', prop: 'departmentTrainingDate' },
            { label: '部门培训学时', prop: 'departmentTrainingHours' },
            { label: '班组培训时间', prop: 'teamTrainingDate' },
            { label: '班组培训学时', prop: 'teamTrainingHours' },
            { label: '考试成绩', prop: 'score' },
          ],
          // columnsProps 用于定义所有 columns 公共的属性
          columnsProps: {
            fit : true,
            sortable: true,
            align : 'center',
          },
          columnsSchema : {
            '姓名' : {
              width : 120
            },
            '性别' : {
              width : 50
            },
            '出生年月' : {
              width : 120
            },
            '部门' : {
              width : 150
            },
            '入职时间' : {
              width : 120
            },
            '公司培训时间' : {
              width : 120
            },
            '公司培训学时' : {
              width : 120
            },
            '部门培训时间' : {
              width : 120,
            },
            '部门培训学时' : {
              width : 120
            },
            '班组培训时间' : {
              width : 120,
            },
            '班组培训学时' : {
              width : 120
            },
          },
        }
      }
    },
    created(){
      this.init();
    },
    watch:{
      $route(to,from){
        let data = to.params && to.params.row && to.params.row.data;
        if(to.name === 'eduEntryTrainings') {
          if(data){
            this.searchBtnClickHandle();
          }
        }
      }
    },
    methods:{
      // 初始化
      init(){
        let data = this.$route.params.row.data;
        if(data){
          // 搜索
          this.searchBtnClickHandle();
        }
      },
      // 搜索按钮
      searchBtnClickHandle(){
        let data = this.$route.params.row.data;
        let list = data.map(function(it){
          let eduUser = it.eduUser || {};
          return {
            username : eduUser.username || '',
            gender : eduUser.gender ? '男' : '女',
            birthday : (this.$tool.formatDateTime(eduUser.birthday) || '').substring(0, 10),
            degreeOfEducation : eduUser.degreeOfEducation || '',
            entryDate : (this.$tool.formatDateTime(eduUser.entryDate) || '').substring(0, 10),
            deptName : eduUser.deptName || '',
            companyTrainingDate : (this.$tool.formatDateTime(it.ctrainingDate)|| '').substring(0, 10),
            companyTrainingHours : it.ctrainingHours|| '',
            departmentTrainingDate : (this.$tool.formatDateTime(it.dtrainingDate)|| '').substring(0, 10),
            departmentTrainingHours : it.dtrainingHours|| '',
            teamTrainingDate : (this.$tool.formatDateTime(it.mtrainingDate)|| '').substring(0, 10),
            teamTrainingHours : it.mtrainingHours|| '',
            score : it.score || '',
          }
        }.bind(this));
        this.egrid.data = list;
      },
    }
  }
</script>

<style>

</style>
