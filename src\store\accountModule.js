/*
  created by m<PERSON><PERSON><PERSON> on 2018-4-12
  common data in hide danger
*/
import axios from 'axios'
import http from '@/assets/functions/axiosServer'
// 应急救援
export default {
  state: {

  },
  getters: {},
  mutations: {

  },
  actions : {

    // post接口通用
    /*
    * 参数：
    * params 对象
    *   url : 地址
    *   data : 传参对象
    * */
    ajaxPost({ commit }, params){
      return new Promise(function(resolve, project){
        http.post(params.url, params.data).then(function (res) {
          resolve(res.data);
        });
      })
    },




    /*
    * 安全投入
    * */
    // 查找-----费用投入 POST /costAccountReg/searchByYear
    costAccountRegSearchByYear({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('costAccountReg/searchByYear', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 查找-----受伤获赔 POST /costInjuryClaim/searchByYear
    costInjuryClaimSearchByYear({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('costInjuryClaim/searchByYear', params).then(function (res) {
          resolve(res.data);
        });
      })
    },

    /*
     * 教育培训
     * */
    // 查找-----有哪些年份有台账 POST /report/edu/findYear
    reportEduFindYear({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('report/edu/findYear', params).then(function (res) {
          resolve(res.data);
        });
      })
    },



    // 修改文件编号、记录编号
    reportCostUpdateCostReportTableInfo({ commit }, params){
      let url = `/report/cost/updateCostReportTableInfo?id=${params.id}&fileNumber=${params.fileNumber}&recordNumber=${params.recordNumber}`;
      return new Promise(function(resolve, project){
        http.get(url).then(function (res) {
          resolve(res.data);
        });
      })
    },


    // 应急物资--按照年查找文件
    getEmgGoodsReportTableInfo({ commit }, params){
      let url = '/report/getEmgGoodsReportTableInfo?pageSize=10&pageCurrent=1&year='+params.year;
      return new Promise(function(resolve, project){
        http.get(url).then(function (res) {
          resolve(res.data);
        });
      })
    },

    // 安全投入--按年查找文件
    reportCostGetCostReportTableInfo({ commit }, params){
      let url = '/report/cost/getCostReportTableInfo?pageSize=10&pageCurrent=1&year='+params.year;
      return new Promise(function(resolve, project){
        http.get(url).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 教育培训--按年查找文件
    reportCostGetEduReportTableInfo({ commit }, params){
      let url = '/report/cost/getEduReportTableInfo?pageSize=10&pageCurrent=1&year='+params.year;
      return new Promise(function(resolve, project){
        http.get(url).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 查找-----根据年来获取台账信息 POST report/edu/eduList/{year}
    reportEduEduList({ commit }, params){
      let url = 'report/edu/eduList/' + params.year;
      return new Promise(function(resolve, project){
        http.get(url).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 日常培训---活动记录下载 POST /report/edu/eduDailyRecordExcel
    reportEduEduDailyRecordExcel({ commit }, params, filename){
      return new Promise(function(resolve, project){
        http({ // 用axios发送post请求
          method: 'post',
          url: 'report/edu/eduDailyRecordExcel', // 请求地址
          data: params, // 参数
          responseType: 'blob' // 表明返回服务器返回的数据类型
        }).then(function (res) {
          const elink = document.createElement('a') // 创建a标签
          elink.download =  filename + '.xlsx'; // 文件名
          elink.style.display = 'none'
          const blob = new Blob([res.data])
          elink.href = URL.createObjectURL(blob)
          document.body.appendChild(elink)
          elink.click() // 触发点击a标签事件
          document.body.removeChild(elink)
        });
      })
    },
    // 日常培训---考核表下载 POST /report/edu/eduDailyExamineListExcel
    reportEdueduDailyExamineListExcel({ commit }, params, filename){
      return new Promise(function(resolve, project){
        http({ // 用axios发送post请求
          method: 'post',
          url: 'report/edu/eduDailyExamineListExcel', // 请求地址
          data: params, // 参数
          responseType: 'blob' // 表明返回服务器返回的数据类型
        }).then(function (res) {
          const elink = document.createElement('a') // 创建a标签
          elink.download =  filename + '.xlsx'; // 文件名
          elink.style.display = 'none'
          const blob = new Blob([res.data])
          elink.href = URL.createObjectURL(blob)
          document.body.appendChild(elink)
          elink.click() // 触发点击a标签事件
          document.body.removeChild(elink)
          //resolve(res);
        });
      })
    },

    /*在线打印接口*/
    // 入口：台账管理->安全投入->年度安全生产投入预算计划表
    getCostBudgetPlanHtmlInfo({ commit }, params){
      //let url = 'report/edu/eduList/' + params.year;
      let url = 'report/cost/getCostBudgetPlanHtmlInfo/' + params.year;
      return new Promise(function(resolve, project){
        http.get(url).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 入口：台账管理->安全投入->年度安全生产投入预算计划表
    printOnLine({ commit }, params){
      //let url = 'report/cost/getCostBudgetPlanHtmlInfo/' + params.year;
      let url = params.url;
      return new Promise(function(resolve, project){
        http.get(url).then(function (res) {
          resolve(res.data);
        });
      })
    },

    // 入口：台账管理->安全投入->年度安全生产投入预算计划表
    printOnLinePost({ commit }, params){
      //let url = 'report/cost/getCostBudgetPlanHtmlInfo/' + params.year;
      //let url = params.url;
      return new Promise(function(resolve, project){
        http.post("/report/getDangerInspectListPublicHtml", params).then(function (res) {
          resolve(res.data);
        });
      })
    },

    // 入口：台账管理->隐患排查->首页->预览
    reportDangerInspectPublicDetailHtml({ commit }, params){
      let url = 'report/dangerInspectPublicDetailHtml/' + params.id+'/'+params.needChange;
      //let url = 'report/cost/getCostBudgetPlanHtmlInfo/' + params.year;
      // http://172.16.0.43:8087/safe/report/dangerInspectPublicDetailHtml/327
      //let url = params.url;
      return new Promise(function(resolve, project){
        http.get(url).then(function (res) {
          resolve(res.data);
        });
      })
    },


    // 台账->隐患排查->生产安全事故隐患排查治理情况一览表->查看
    dangerInspectPublicReformSummary({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('/danger/inspectPublic/reformSummary', params).then(function (res) {
          resolve(res.data);
        });
      })
    },


    // 节假日值班->左边任意一列->点击
    reportCostGetDutyReportTableInfo({ commit }, params){
      let url = 'report/cost/getDutyReportTableInfo?year=' + params.year+"&name="+params.code;
      return new Promise(function(resolve, project){
        http.get(url).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 应急值班->左边任意一列->点击
    reportCostGetEmgDutyReportTableInfo({ commit }, params){
      let url = 'report/cost/getEmgDutyReportTableInfo?year=' + params.year+"&name="+params.code;
      return new Promise(function(resolve, project){
        http.get(url).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 台账->节假日值班->首页列表
    sysDutyTypeFind({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('sysDutyType/findList', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 台账->应急值班->首页列表
    sysEmgDutyTypeFind({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('sysEmgDutyType/findList', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 节假日值班->下载和预览->打开节假日对话框
    reportGetHolidayDayList({ commit }, params){
      //let url = 'report/cost/getDutyReportTableInfo?year=' + params.year+"&name="+params.code;
      let url = `/report/getHolidayDayList/${params.year}/${params.codeNum}`;
      return new Promise(function(resolve, project){
        http.get(url).then(function (res) {
          resolve(res.data);
        });
      })
    },

    // 应急值班->下载和预览->打开应急值班对话框
    reportGetEmgDutyDayList({ commit }, params){
      //let url = 'report/cost/getDutyReportTableInfo?year=' + params.year+"&name="+params.code;
      let url = `/report/getEmgDutyDayList/${params.year}/${params.codeNum}`;
      return new Promise(function(resolve, project){
        http.get(url).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 登录统计
    punchRecordFindLoginRecord({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('punchRecord/findLoginRecord', params).then(function (res) {
          resolve(res.data);
        });
      })
    },


  }
};
