<template>
    <el-container class="container">
      <el-main>
        <el-form ref="form" label-width="150px">
          <el-row type="flex" class="row">
            <el-col :span="24">
              <el-form-item label="请选择调查项目：">
                <el-table
                  :data="form.eduRequirementItemsSurveyResultss">
                <el-table-column
                  label="选中"
                  prop="itemResult"
                  width="100">
                  <template slot-scope="scope">
                    <el-checkbox
                      v-model="scope.row.itemResult"
                      @click="scope.row.itemResult = !scope.row.itemResult"></el-checkbox>
                  </template>
                </el-table-column>
                <el-table-column
                  type="index"
                  label="序号"
                  width="100">
                </el-table-column>
                <el-table-column
                  prop="items"
                  label="项目"
                  show-overflow-tooltip
                  width="300">
                </el-table-column>
                <el-table-column
                  prop="items"
                  label="人数"
                  width="200">
                  <tempalte slot-scope="scope">
                    <el-input-number
                      v-model="scope.row.people"
                      :disabled="true"
                      :controls="false" :min="0" :step="100" ></el-input-number>
                  </tempalte>
                </el-table-column>
              </el-table>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex" class="row">
            <el-col :span="24">
              <el-form-item label="其他需求">
                <el-input
                  v-model="form.otherRequirement" type="textarea" :rows="4"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex" class="row">
            <el-col :span="24">
              <el-form-item label="意见建议">
                <el-input
                  v-model="form.advice" type="textarea" :rows="4"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex" class="row" justify="center">
            <!--未完成-->
            <!--员工-->
            <el-button
              @click="saveStaffHandle"
              v-if="this.pageStatus == 'edit' "
              size="small" :span="2" type="success" >保存</el-button>
            <el-button size="small" :span="2"  @click="$router.back();">返回</el-button>
          </el-row>
        </el-form>
      </el-main>
      <el-footer>
        <el-dialog title="查看选择人数" :visible.sync="lookNumDialogShow">
          <el-table>
            <el-table-column type="index" label="序号" width="100"></el-table-column>
            <el-table-column property="deptName" label="项目" width="150"></el-table-column>
            <el-table-column property="deptName" label="其他需求" width="150"></el-table-column>
            <el-table-column property="deptName" label="意见建议" width="150"></el-table-column>
            <el-table-column property="count" label="姓名"></el-table-column>
            <el-table-column property="manageUser" label="所属部门"></el-table-column>
          </el-table>
        </el-dialog>
      </el-footer>
    </el-container>
</template>

<script>

    export default {
      data(){
        let that = this;
        return {
          // 发布者发布的数据
          form : {
            // 需求调查的id
            id : '',
            // 用户
            userId : '',
            // 需求调查ID
            reqInvestId : '',
            // 状态  0 未完成 1 已完成
            status : 0,
            // 调查项目
            eduRequirementItemsSurveyResultss : [],
            // 其他需求
            otherRequirement : '',
            // 意见建议
            advice : '',
            // 主表---调查表
            eduRequirementInvestigation : {},
          },
          // 查看人数对话框
          lookNumDialogShow : false,
          // 页面状态
          pageStatus : 'edit',
        }
      },
      watch:{
        $route(to,from){
          // 如果来至列表页
          if(from.name === 'demandSurveyStaffIndex'){
            if(to.params.status === 'view'){
              this.viewPage();
            }
            else if(to.params.status === 'edit'){
              this.editPage();
            } else {
              // 添加页面，清空数据
              this.clear();
            }
          }
        }
      },
      created(){
        if(this.$route.params.status === 'view'){
          this.viewPage();
        }
        else if(this.$route.params.status === 'edit'){
          this.editPage();
        }
        else {
          this.clear();
        }
      },
      mounted(){
        this.init();
      },
      methods:{
        // 初始化
        init(){
        },
        // 清空数据
        clear(){
          this.pageStatus = 'edit';
        },
        // 保存按钮----员工
        saveStaffHandle(){
          let params = Object.assign({}, this.form);
          params.status = 1;
          this.$store.dispatch('eduReqUserRltAddOrUpdate', params).then(function(res){
            if(res.success){
              this.$message({
                type : 'success',
                message : '操作成功'
              })
              this.$router.push({ name : 'demandSurveyStaffIndex' })
            } else {
              this.$message({
                type : 'error',
                message : res.message || '操作失败！！！！'
              })
            }
          }.bind(this));
        },
        // 根据id搜索信息
        searchBtnClickHandle(){
          let id = this.$route.params.id;
          this.$store.dispatch('eduReqUserRltFind', { id : id }).then(function(res){
            if(res.success){
              let list = res.data.list[0];
              Object.entries(list).forEach(function(it){
                if(this.form.hasOwnProperty(it[0])){
                  this.form[it[0]] = it[1];
                }
              }.bind(this))
            } else {
              this.$message({
                type : 'error',
                message : res.message || '错误'
              })
            }
          }.bind(this));
        },
        // 查看页面
        viewPage(){
          this.pageStatus = 'view';
          this.searchBtnClickHandle();
        },
        // 修改页面
        editPage(){
          this.viewPage();
          this.pageStatus = 'edit';
        },
      }
    }
</script>

<style>
  .container{
    background:#fff;
    padding:0px 20px 20px;
  }
  .row{
    margin-top:10px;
  }
  .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner{
    background-color : #0f6fc6;
    border-color : #0f6fc6;
  }
</style>
