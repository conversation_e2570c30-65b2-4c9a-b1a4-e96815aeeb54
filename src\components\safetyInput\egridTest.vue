<template>
    <div id="">
      <egrid class="egrid"
             stripe border
             maxHeight="500"
             :data="data"
             :columns="columns"
             :columns-schema="columnsSchema"
             :column-type="columnType"
             :columns-handler="columnsHandler">
        <template slot="expand" slot-scope="{ row }">
          <section class="expand-detail">
            <div v-for="col in columns" :key="col.label">
              {{ col.label }}：{{ row[col.prop] }}
            </div>
          </section>
        </template>
      </egrid>
    </div>
</template>

<script>
    import Editor from '@/components/safetyInput/Editor'
    import Input from '@/components/common/formComponent/input'
    import Datepicker from '@/components/common/formComponent/datepicker'
    var data = [
      {
        _edit : false,
        date: new Date(),
        name: '王小虎',
        province: '上海',
        city: '普陀区',
        address: '上海市普陀区金沙江路 1518 弄',
        zip: 200333
      },
      {
        _edit : false,
        date: new Date(),
        name: '李小豹',
        province: '上海',
        city: '普陀区',
        address: '上海市普陀区金沙江路 1518 弄',
        zip: 200333
      },
    ];
    var columns = [
    { label: '日期', prop: 'date' },
    { label: '姓名', prop: 'name' },
    { label: '省份', prop: 'province' },
    { label: '市区', prop: 'city' },
    { label: '地址', prop: 'address' },
    { label: '邮编', prop: 'zip' }
  ];
    export default {
      data(){
        let that = this;
        return {
          data : data,
          columns : columns,
          // columnsProps 用于定义所有 columns 公共的属性
          columnsProps: {
            fit : true,
            sortable: true,
            align : 'center',
            // 定义表格列如何渲染
            component: Editor
          },
          columnsSchema : {
            '姓名' : {
              component : Input
            },
            '地址' : {
              showOverflowTooltip : true,
              // propsHandler 可用于转换传给自定义组件的 props 这里将 props 变成了 address
//              propsHandler ({ col, row }) {
//                return { address: row[col.prop] }
//              },
//              // 这里的 props 是 address
//              component: that.extend({
//                props: ['address'],
//                render (h) {
//                  return h('span', {
//                    style: { color: '#20A0FF' }
//                  }, this.address)
//                }
//              })
            },
            '日期' : {
              component : Datepicker
            }
          },
          columnType : 'expand'
        }
      },
      methods: {
        columnsHandler (cols) {
          return cols.concat({
            label: '操作',
            fixed: 'right',
            width: 250,
            component: Editor,
            listeners: {
              'row-view' (row) {
                console.log('查看：', row._edit)
              },
              'row-edit' (row) {
                console.log('编辑：', row._edit)
              }
            }
          })
        },
        selectionChange (rows) {

          console.log(rows)
        }
      }
    }
</script>

<style>

</style>
