<template>
  <div id="tagManage">
    <div class="background-style">
      <el-col :span="14" style="padding-left: 10px">
        <el-col :span="24" class="primary-background-title">知识点标签管理</el-col>
        <el-col :span="24">
          <!--搜索区-->
          <div style="width: 100%;min-height: 30px;padding-bottom: 10px;display: block">
            <div style="float: left;margin-left: 20px">
              <el-switch
                @change="reloadData()"
                v-model="systemData"
                active-text="系统数据"
                inactive-text="公司数据">
              </el-switch>
            </div>
            <div style="padding:0 20px 0 10px;float: right">
                <el-switch
                  style="margin-right: 10px;"
                  @change="reloadData()"
                  v-model="systemData"
                  active-text="系统数据"
                  inactive-text="公司数据">
                </el-switch>
              <el-button type="primary">添加一级标签</el-button>
            </div>
          </div>
          <!--搜索区结束-->
        </el-col>
        <el-col :span="24">
          <!--树形区-->
          <div style="width: 100%;margin-top: 20px">
            <el-row>
              <el-tree
                :data="labelData"
                :props="labelProp"
                :load="labelLoadClick"
                empty-text="标签加载中"
                style="width: 100%"
                :expand-on-click-node="false"
                v-loading="deleteLoading"
                element-loading-text="正在删除知识点中的此标签..."
                element-loading-spinner="el-icon-loading"
                lazy>
            <span class="custom-tree-node" slot-scope="{ node, data }" style="width: 100%;">
              <span style="float: left">{{ node.label }}</span>
              <span style="float: right">
                <el-button
                  type="text"
                  size="mini"
                  @click="update(data)">
                  修改
                </el-button>
                <el-button
                  type="text"
                  size="mini"
                  style="color: #f56c6c"
                  @click="remove(node, data)">
                  删除
                </el-button>
                <el-button
                  type="text"
                  size="mini"
                  style="color: #E6A23C"
                  @click="append(data)">
                  增加子标签
                </el-button>
              </span>
            </span>

              </el-tree>
            </el-row>
          </div>
          <!--树形区结束-->
        </el-col>

      </el-col>

    </div>
    <!--新增应急预警对话框-->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible">
      标签名称：
      <el-input v-model="labelDialog.label"></el-input>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="determineClick">确 定</el-button>
      </div>
    </el-dialog>
    <!--新增应急预警对话框结束-->
  </div>
</template>
<script>
  export default {
    name: 'tagManage',
    data() {
      return {
        //搜索数据
        searchInput: '',

        //树形数据
        labelProp: {
          label: 'label',
          children: 'children'
        },
        labelData: [],
        deleteLoading: false,//删除时间比较长，所以要提示正在删除

        //对话框数据
        labelDialog: {
          id: '',
          parentId: '',
          label: ''
        },
        dialogTitle: '',
        dialogVisible: false,
        systemData: false,
      }
    },
    mounted: function () {
      this.searchLabelClick();
    },
    methods: {
      searchLabelClick: function () {
        let params = new URLSearchParams;
        params.append("parent", 0);
        params.append("companyId", this.getCompanyId());
        this.$store.dispatch('labelFind', params).then(function (res) {
          if (res.success) {
            this.labelData = res.data.list.map(function (it) {
              return {
                label: it.label,
                value: it.id,
                children: [],
              }
            })
          }
        }.bind(this));
      },
      // 标签动态加载次级选项
      labelLoadClick: function (node, resolve) {
        // 找到对应的parent
        let params = new URLSearchParams;
        params.append("parent", node.data.value);
        // params.append("companyId", this.$tool.getStorage('LOGIN_USER').companyId);
        params.append("companyId", this.getCompanyId());
        // 通过parent找到子类
        this.$store.dispatch('labelFind', params).then(function (res) {
          if (res.success) {
            // 找到对应的id,把值放到children数组中去

            let childrenTemp = [];
            if (res.data) {
              let list = res.data.list;
              list.forEach(function (it) {
                childrenTemp.push(
                  {
                    label: it.label,
                    value: it.id,
                    isLeaf: !it.haschild
                  }
                )
              });
              return resolve(childrenTemp)
            } else {
              return resolve([]);//没有子分类
            }
          } else {
            return resolve([]);//没有子分类
          }
        }.bind(this));
      },


      //添加一级分类
      addLabel() {
        this.parentId = 0;
        this.labelInput = '';
        this.dialogTitle = '新增标签';
        this.dialogVisible = true;
      },
      //添加子分类
      append(data) {
        this.labelDialog.parentId = data.value;
        this.labelDialog.label = '';
        this.dialogTitle = '新增标签';
        this.dialogVisible = true;
      },
      //删除分类
      remove(node, data) {
        this.$confirm('确定删除该标签?', '提示', {
          confirmButtonText: '删除',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let params = new URLSearchParams;
          params.append("labelId", data.value);
          params.append("deleteLabelsRoutes", this.getLabelPath(node));
          this.deleteLoading = true;
          this.$http.post('label/delete', params).then(function (res) {
            if (res.data.success) {
              this.searchLabelClick();
              this.$message.success('删除成功！');
              this.deleteLoading = false;
            }
          }.bind(this)).catch(function (err) {
            console.log(err);
            this.$message({
              showClose: true,
              message: '删除标签失败',
              type: 'error'
            });
          }.bind(this));
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });
        });
      },
      //获取完成标签路径
      getLabelPath: function (node) {
        let listTemp = [];
        let nodeTemp = node;
        for (let i = nodeTemp.level; i >= 1; i--) {
          listTemp.push(nodeTemp.data.label);
          nodeTemp = nodeTemp.parent;
        }
        return listTemp.reverse();
      },
      //修改分类名称
      update(data) {
        this.labelDialog.id = data.value;
        this.labelDialog.label = data.label;
        this.dialogTitle = '修改标签';
        this.dialogVisible = true;
      },
      //确定添加或者修改
      determineClick: function () {
        if (this.labelDialog.label.trim()) {
          if (this.dialogTitle === '新增标签') {
            let params = new URLSearchParams;
            params.append("label", this.labelDialog.label.trim());
            params.append("parent", this.labelDialog.parentId);
            // params.append("companyId", this.$tool.getStorage('LOGIN_USER').companyId);
            params.append("companyId", this.getCompanyId());
            this.$http.post('label/add', params).then(function (res) {
              if (res.data.success) {
                this.searchLabelClick();
                this.$message.success('新增成功！');
                this.dialogVisible = false;
              }
            }.bind(this)).catch(function (err) {
              console.log(err);
              this.$message({
                showClose: true,
                message: '新增标签失败',
                type: 'error'
              });
            }.bind(this));

          } else {
            let params = new URLSearchParams;
            params.append("id", this.labelDialog.id);
            params.append("label", this.labelDialog.label.trim());
            params.append("companyId", this.$tool.getStorage('LOGIN_USER').companyId);
            this.$http.post('label/update', params).then(function (res) {
              if (res.data.success) {
                this.searchLabelClick();
                this.$message.success('修改成功！');
                this.dialogVisible = false;
              }
            }.bind(this)).catch(function (err) {
              console.log(err);
              this.$message({
                showClose: true,
                message: '修改标签失败',
                type: 'error'
              });
            }.bind(this));
          }

        } else {
          this.$message.warning('请填写分类名称！');
        }
      },

      getCompanyId: function () {
        if (this.systemData) {
          return 0;
        } else {
          return this.$tool.getStorage('LOGIN_USER').companyId;
        }
      },
      reloadData:function () {
        this.searchLabelClick();
      }
    }
  }
</script>
<style>
</style>
