<template>
  <div id="accountEmerList">
    <div
      style="top: 20px;left:20px;right:20px;
      position:absolute;background-color: white;padding-bottom: 40px;min-height: 100%">
      <el-col :span="20" :offset="2" class="primary-background-title">
        应急响应详情
      </el-col>
      <el-col :span="20" :offset="2">
        <el-form :model="form" label-width="120px" ref="form">
          <!--信息卡-->
          <el-col :span="24">
            <el-col :span="8">
              <el-form-item label="名称：" prop="emerResponseName" style="margin: 0">
                {{form.emerResponseName}}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="发布时间：" prop="signDate" style="margin: 0">
                {{form.signDate}}
              </el-form-item>
            </el-col>

          </el-col>
          <el-col :span="24">
            <el-col :span="8">
              <el-form-item label="分类：" prop="emerTopClass" style="margin: 0">
                {{form.emerTopClass}}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="类别" prop="emerClass" style="margin: 0">
                {{form.emerClass}}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="响应级别：" prop="respLevel" style="margin: 0">
                {{form.respLevel}}
              </el-form-item>
            </el-col>
          </el-col>
          <el-col :span="24">


          </el-col>

          <!--信息卡结束-->
          <el-col :span="24" class="card-shadow-style">
            <div style="width: 100%;padding-top: 10px;padding-bottom:10px;float: left;background-color: #f2f2f2">
              <i class="el-icon-menu" style="color:#049ff1;float: left;margin:3px 10px 0 20px"></i>
              <span style="color:#049ff1;width: 200px;float: left;">调整发布通知</span>
            </div>
            <div style="width: 100%;float:left;">
              <el-col :span="20" style="padding:20px 5px 20px 5px">
                <el-table
                  :data="planPublicList"
                  border
                  style="width: 100%">
                  <el-table-column
                    type="index"
                    align="center"
                    label-class-name="inner-header-style"
                    width="50">
                  </el-table-column>
                  <el-table-column
                    prop="name"
                    label="文件名称"
                    align="center"
                    label-class-name="inner-header-style"
                    min-width="200">
                  </el-table-column>

                  <el-table-column
                    label="操作"
                    align="center"
                    width="140"
                    label-class-name="inner-header-style">
                    <template slot-scope="scope">
                      <el-button type="text" size="medium" style="color: #5daf34" @click="viewAdjustItemClick(scope.row)">查看
                      </el-button>
                      <!--<el-button type="text" size="medium" style="color: #5daf34" @click="otherFileDownClick(scope.row)">下载-->
                      <!--</el-button>-->
                    </template>
                  </el-table-column>
                </el-table>
              </el-col>
            </div>
          </el-col>
          <!--其他相关表格下载-->
          <el-col :span="24" class="card-shadow-style">
            <div style="width: 100%;padding-top: 10px;padding-bottom:10px;float: left;background-color: #f2f2f2">
              <i class="el-icon-menu" style="color:#049ff1;float: left;margin:3px 10px 0 20px"></i>
              <span style="color:#049ff1;width: 200px;float: left;">其他相关文件下载</span>
            </div>
            <div style="width: 100%;float:left;">
              <el-col :span="20" style="padding:20px 5px 20px 5px">
                <el-table
                  :data="planPublicOtherFiles"
                  border
                  style="width: 100%">
                  <el-table-column
                    type="index"
                    align="center"
                    label-class-name="inner-header-style"
                    width="50">
                  </el-table-column>
                  <el-table-column
                    prop="name"
                    label="文件名称"
                    align="center"
                    label-class-name="inner-header-style"
                    min-width="200">
                  </el-table-column>
                  <el-table-column
                    label="操作"
                    align="center"
                    width="140"
                    label-class-name="inner-header-style">
                    <template slot-scope="scope">
                      <el-button type="text" size="medium"
                                 style="color: #5daf34" @click="otherFileClick(scope.row)">查看
                      </el-button>
                      <!--<el-button type="text" size="medium" style="color: #5daf34" @click="goodsItemView(scope.row)">下载-->
                      <!--</el-button>-->
                    </template>
                  </el-table-column>
                </el-table>
              </el-col>
            </div>
          </el-col>

          <el-col :span="24" class="card-shadow-style">
            <fileUpload ref="upload" :data="upload"></fileUpload>
          </el-col>
        </el-form>
        <div style="margin-top: 10px;margin-right: 30px">
          <el-button style="float: right"  @click="$router.go(-1)">返回</el-button>
        </div>
      </el-col>
      <!--查看物资汇总对话框-->
      <el-dialog title="物资汇总" :visible.sync="goodsSummaryVisible">
        <el-table
          :data="goodsSummaryData"
          border
          style="width: 100%">
          <el-table-column
            type="index"
            align="center"
            fixed
            label-class-name="inner-header-style"
            width="50">
          </el-table-column>
          <el-table-column
            prop="goodsName"
            label="物资名称"
            align="center"
            label-class-name="inner-header-style"
            width="120">
          </el-table-column>
          <el-table-column
            prop="count"
            label="数量"
            align="center"
            label-class-name="inner-header-style"
            width="80">
          </el-table-column>
          <el-table-column
            prop="location"
            label="存放地点"
            align="center"
            label-class-name="inner-header-style"
            width="200">
          </el-table-column>
          <el-table-column
            prop="dept"
            label="所属部门"
            align="center"
            label-class-name="inner-header-style"
            width="200">
          </el-table-column>
          <el-table-column
            prop="managername"
            label="管理员"
            align="center"
            label-class-name="inner-header-style"
            width="120">
          </el-table-column>
          <el-table-column
            prop="phone"
            label="联系方式"
            align="center"
            label-class-name="inner-header-style"
            width="140">
          </el-table-column>
        </el-table>
        <div slot="footer" class="dialog-footer">
          <el-button @click="goodsSummaryVisible = false">返回</el-button>
          <!--<el-button @click="downGoodsExcel" type="primary">下载</el-button>-->
        </div>
      </el-dialog>
      <!--查看物资汇总对话框结束-->
      <!--查看不同级别简报对话框-->
      <el-dialog title="选择简报编辑部门" :visible.sync="deptBriefVisible">
        <el-form :model="deptTempForm">
          <el-form-item label="选择编辑部门：" label-position="top">
            <el-select v-model="deptTempForm.editDept" placeholder="请选择">
              <el-option
                v-for="item in deptTempForm.deptOptions"
                :key="item.value"
                :label="item.label"
                :value="item">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="$router.push({name:'viewWorkBriefAccount',params:{dept:deptTempForm.editDept,emerName:form.emerResponseName,startId:emerPlanPublicId}});deptBriefVisible=false">确定</el-button>
        </div>
      </el-dialog>
      <!--查看不同级别简报对话框结束-->

      <!--查看不同级别的总结对话框-->
      <el-dialog title="选择总结编辑部门" :visible.sync="sumVisible">
        <el-form :model="sumTempForm">
          <el-form-item label="选择编辑部门：" label-position="top">
            <el-select v-model="sumTempForm.editDept" placeholder="请选择">
              <el-option
                v-for="item in sumTempForm.deptOptions"
                :key="item.value"
                :label="item.label"
                :value="item">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="$router.push({name:'viewSummaryAccount',params:{dept:sumTempForm.editDept,emerName:form.emerResponseName,emerId:emerPlanPublicId}});sumVisible=false">确定</el-button>
          <el-button @click="sumVisible=false">取消</el-button>
        </div>
      </el-dialog>
      <!--查看不同级别的总结对话框结束-->
    </div>
  </div>
</template>

<script>
  import {mapGetters} from 'vuex'
  import fileUpload from '@/components/common/fileUpload'

  export default {
    name: 'accountEmerList',
    data() {
      var that = this
      return {
        //应急响应基本信息
        form: {
          emerResponseName: '',
          emerTopClass: '',
          emerClass: '',
          respLevel: '',
          signDate: '',
          deptId:0,
          companyId:0,
          department:'',
        },
        emerPlanPublicId: 0,
        planPublicList: [],
        planPublicOtherFiles:[
          {name:"应急工作情况简报",type:"workBrief"},
          {name:"应急工作总结评估表",type:"summary"},
          {name:"应急物资储备情况汇总",type:"goods"},
          {name:"重大信息书面报告单",type:"report"}
        ],
        // 上传文件
        upload: {
          // 地址
          url: that.$http.defaults.baseURL + 'file/upload',
//          url : 'http://************:8080/safe/file/upload',
          // token
          cookies: true,
          // 上传参数
          params: {
            contentId: 0,
            contentType: 1
          },
          // 文件列表
          fileData: [],
        },
        goodsSummaryData:[],
        goodsSummaryVisible:false,
        //查看本公司和子公司的工作简报
        deptBriefVisible:false,
        deptTempForm:{
          editDept:'',
          deptOptions:[]
        },
        currentDeptName:'',
        sumVisible:false,//对话框的开闭
        sumTempForm:{
          editDept:'',
          deptOptions:[]
        },
      }
    },
    components: {
      fileUpload
    },
    computed: mapGetters(['getCurrentUser']),
    created: function () {
      this.initData()
    },
    watch: {
      $route(to, from) {
        if ((from.name === 'accountEmer')) {
          this.initData()
        }
      }
    },
    mounted: function () {
      this.initData();
    },
    methods: {
      initData: function () {
        var emgPlan = this.$route.params.emgPlan
        console.info(emgPlan)
        this.form.emerResponseName = emgPlan.emerName
        this.form.signDate = emgPlan.createDate
        this.form.respLevel = emgPlan.emerLevel
        this.form.emerTopClass = emgPlan.emerTopClass
        this.form.emerClass = emgPlan.emerClass
        this.form.deptId = emgPlan.deptId
        this.form.companyId = emgPlan.companyId
        this.emerPlanPublicId = emgPlan.id
        this.upload.params.contentId = emgPlan.id
        this.loadPlanPublic()
        this.searchDepartmentById(emgPlan.deptId)
      },
      loadPlanPublic: function () {
        this.$http.get("/report/emgReportFileList/" + this.emerPlanPublicId).then(function (res) {
          if (res.data.success) {
            this.planPublicList = res.data.data
          }
        }.bind(this)).catch(function (err) {
          console.log(err)
          this.$message({
            showClose: true,
            message: '网络错误，请尝试重登录',
            type: 'error'
          });
        })
      },
      searchDepartmentById: function (val) {
        let params = new URLSearchParams;
        params.append("id", val);
        this.currentDeptId=val;
        this.$http.post('dept/find', params).then(function (res) {
          if (res.data.success) {
            this.form.department = res.data.data[0].name;
            this.currentDeptName=this.form.department;
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message({
            showClose: true,
            message: '网络错误，请尝试重登录',
            type: 'error'
          });
        }.bind(this));
      },
      //--------------------------------查看调整应急响应--------------------------
      viewAdjustItemClick:function (row) {
        this.$router.push({name: 'viewEmergencyAccount', params: {emergencyId: row.id, onlyShow: row.onlyShow}});
      },

      otherFileClick:function (row) {
        if(row.type=="goods"){
          this.viewGoodsSummary()
        }
        if(row.type=="workBrief"){
          this.loadPlanPublicWriteTarget()
        }
        if(row.type=="summary"){
          this.loadPlanPublicSummaryTarget()
        }
      },
      otherFileDownClick:function (row) {
        if(row.type=="goods"){
          this.downGoodsExcel()
        }
        if(row.type=="workBrief"){
        }
      },
      //--------------------------------查看物资汇总-----------------------------------
      viewGoodsSummary: function () {
        this.goodsSummaryVisible=true
        this.$http.get("/planPublicGoods/planPublicGoodsHistory/"+this.emerPlanPublicId).then(function (res) {
          if(res.data.success){
            this.goodsSummaryData=res.data.data
          }
        }.bind(this))
      },
      //下载物资汇总的excel
      downGoodsExcel:function () {
        const loading = this.$loading({
          lock: true,
          text: 'Loading',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        this.$http({ // 用axios发送post请求
          method: 'get',
          url: '/report/emgPlanGoods/' + this.emerPlanPublicId, // 请求地址
          responseType: 'blob' // 表明返回服务器返回的数据类型
        }).then((res) => { // 处理返回的文件流
          //console.info(res)
          loading.close()
          const content = res
          const elink = document.createElement('a') // 创建a标签
          elink.download = this.form.emerResponseName+"物资汇总表" + ".xlsx" // 文件名
          elink.style.display = 'none'
          const blob = new Blob([res.data])
          elink.href = URL.createObjectURL(blob)
          document.body.appendChild(elink)
          elink.click() // 触发点击a标签事件
          document.body.removeChild(elink)
        })
      },
      loadPlanPublicWriteTarget: function () {
        let params=new URLSearchParams;
        params.append("parentId",this.currentDeptId);
        params.append("type",1);
        this.$http.post('dept/find', params).then(function (res) {
          this.deptTempForm.deptOptions=[];
          if (res.data.success) {
            this.deptTempForm.deptOptions.push({value:this.getCurrentUser.companyId,label:this.currentDeptName});
            this.deptTempForm.editDept={value:this.getCurrentUser.companyId,label:this.currentDeptName};
            for(let i=0;i<res.data.data.length;i++){
              this.deptTempForm.deptOptions.push({value:res.data.data[i].id,label:res.data.data[i].name});
            }
            this.deptBriefVisible=true;
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message({
            showClose: true,
            message: '网络错误，请尝试重登录',
            type: 'error'
          });
        }.bind(this));
      },
      loadPlanPublicSummaryTarget: function () {
        let params=new URLSearchParams;
        params.append("parentId",this.currentDeptId);
        params.append("type",1);
        this.$http.post('dept/find', params).then(function (res) {
          this.sumTempForm.deptOptions=[];
          if (res.data.success) {
            this.sumTempForm.deptOptions.push({value:this.getCurrentUser.companyId,label:this.currentDeptName});
            this.sumTempForm.editDept={value:this.getCurrentUser.companyId,label:this.currentDeptName};
            for(let i=0;i<res.data.data.length;i++){
              this.sumTempForm.deptOptions.push({value:res.data.data[i].id,label:res.data.data[i].name});
            }
            this.sumVisible=true;
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message({
            showClose: true,
            message: '网络错误，请尝试重登录',
            type: 'error'
          });
        }.bind(this));
      },
    }

  }
</script>

<style scoped>

</style>
