<template>
  <div id="">
    <el-container class="container">
      <el-main>
        <el-form label-width="5px">
          <el-row >
<!--            <el-col :span="3">-->
            <el-col style="width: 130px">
              <el-form-item >
                <el-input clearable placeholder="姓名" v-model="form.eduUser.username"></el-input>
              </el-form-item>
            </el-col>
<!--            <el-col :span="4">-->
<!--              <el-form-item >-->
<!--                <el-select clearable v-model="form.eduUser.duty" placeholder="请选择岗位" style="width: 100%">-->
<!--                  <el-option-->
<!--                    v-for="item in assist.duty"-->
<!--                    :key="item.value"-->
<!--                    :label="item.label"-->
<!--                    :value="item.value">-->
<!--                  </el-option>-->
<!--                </el-select>-->
<!--              </el-form-item>-->
<!--            </el-col>-->
<!--            <el-col :span="6">-->
            <el-col style="width: 220px">
              <el-form-item  >
                <el-input clearable  placeholder="证书名称"  v-model="form.certificateName"></el-input>
              </el-form-item>
            </el-col>
<!--            <el-col :span="4">-->
            <el-col style="width: 180px">
              <el-form-item >
                <el-input clearable  placeholder="证书编号" v-model="form.certificateId"></el-input>
              </el-form-item>
            </el-col>
          <!--</el-row>-->
          <!--<el-row>-->
<!--            <el-col :span="6">-->
           <!--   <el-col style="width: 180px">
              <el-form-item >
                <el-input clearable  placeholder="发证单位" v-model="form.issuingDepartment"></el-input>
              </el-form-item>
            </el-col>-->
<!--            <el-col :span="6">-->
<!--              <el-form-item style="margin-bottom:0px;">-->
<!--                <el-date-picker-->
<!--                  clearable-->
<!--                  start-placeholder="复训日期范围"-->
<!--                  end-placeholder=""-->

<!--                  @change="retrainDateChangeHandle"-->
<!--                  style="width: 100%"-->
<!--                  v-model="assist.retrainDate"-->
<!--                  type="daterange">-->
<!--                </el-date-picker>-->
<!--              </el-form-item>-->
<!--            </el-col>-->
            <el-col  :span="8" >
              <el-button style="margin-bottom:0px;" type="primary" @click="searchBtnClickHandle">搜索</el-button>
              <el-button type="primary" icon="el-icon-download" @click="downloadTemplate">导入模板下载</el-button>
              <el-button type="success"
                         style="margin-bottom:0px;"
                         v-if="role === 4"
                         icon="el-icon-plus"
                         @click="$router.push({ name : 'certificateTrainingAdd' });">持证记录</el-button>
<!--              <el-upload-->
<!--                :action="upload.url"-->
<!--                multiple-->
<!--                :show-file-list="false"-->
<!--                :with-credentials="upload.cookies"-->
<!--                :data="upload.params"-->
<!--                :on-success="uploadSuccess"-->
<!--                style="width: 50px;margin-bottom: 10px;">-->
<!--                <el-button type="primary">持证导入</el-button>-->
<!--              </el-upload>-->

            </el-col>
            <!--<el-col  :span="2" >
              <el-button type="success"
                         style="margin-bottom:0px;"
                         v-if="role === 4"
                         icon="el-icon-plus"
                         @click="$router.push({ name : 'certificateTrainingAdd' });">持证记录</el-button>
            </el-col>-->
            <el-col offset="1" :span="2" v-if="role === 4">
              <el-form-item style="margin-bottom:0px;">
                <el-upload
                  :action="upload.url"
                  multiple
                  :show-file-list="false"
                  :with-credentials="upload.cookies"
                  :data="upload.params"
                  :on-success="uploadSuccess"
                  style="width: 50px;margin-bottom: 10px;">
                  <el-button icon="el-icon-upload2" type="primary">持证导入</el-button>
                  </el-upload>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-if="role == 4 && assist.idList.length > 0">
            <el-col :span="6">
              <el-form-item>
                <el-date-picker
                  clearable
                  style="width: 175px;"
                  v-model="assist.retrainDateMore"
                  type="month">
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :offset="1" :span="2">
              <el-button
                type="primary"  @click="moreRetrainingBtnHandle">批量复训</el-button>
            </el-col>
          </el-row>

          <el-row class="row">
            <el-table
              :data="tableData.list"
              @selection-change="handleSelectionChange"
              border>
              <el-table-column
                label-class-name="header-style"
                width="70"
                type="selection">
              </el-table-column>
              <el-table-column
                label-class-name="header-style"
                label="序号"
                width="100"
                type="index">
              </el-table-column>
              <el-table-column
                label-class-name="header-style"
                prop="eduUser.username"
                label="姓名"
                width="100">
              </el-table-column>
              <el-table-column
                label-class-name="header-style"
                prop="eduUser.duty"
                label="职务(岗位)"
                show-overflow-tooltip
                width="120">
              </el-table-column>
              <el-table-column
                label-class-name="header-style"
                prop="eduUser.gender"
                label="性别"
                width="80">
                <template slot-scope="scope">{{scope.row.eduUser && (scope.row.eduUser.gender ? '男' : '女')}}</template>
              </el-table-column>
              <el-table-column
                label-class-name="header-style"
                prop="certificateName"
                label="证件名称"
                show-overflow-tooltip
                width="120">
                <template slot-scope="scope">
                  <span
                    :style="{ color : isExpiredCycle(scope.row) }">
                    {{scope.row.certificateName}}</span>
                </template>
              </el-table-column>
              <el-table-column
                label-class-name="header-style"
                prop="expiryDate"
                :formatter="formatDateTime"
                label="有效期限"
                width="120">
                <template slot-scope="scope">
                  <span
                    :style="{ color : isExpired(scope.row.expiryDate) }">
                    {{($tool.formatDateTime(scope.row.expiryDate) || '').substring(0, 10)}}</span>
                </template>
              </el-table-column>
              <el-table-column
                label-class-name="header-style"
                prop="certificateId"
                label="证书编号"
                width="140">
              </el-table-column>
              <el-table-column
                label-class-name="header-style"
                prop="getDate"
                :formatter="formatDateTime"
                label="取证时间"
                width="120">
              </el-table-column>
              <el-table-column
                label-class-name="header-style"
                prop="retrainDate"
                label="复训日期"
                :formatter="formatDateTime"
                width="120">
              </el-table-column>
              <el-table-column
                label-class-name="header-style"
                prop="retrainCycle"
                label="复训周期（年）"
                width="120">
              </el-table-column>
              <el-table-column
                label-class-name="header-style"
                prop="issuingDepartment"
                label="发证单位"
                width="140">
<!--                label-class-name="header-style"-->
<!--                prop="issuingDepartment"-->
<!--                show-overflow-tooltip-->
<!--                label="发证单位"-->
<!--                width="150">-->
              </el-table-column>
              <el-table-column
                label-class-name="header-style"
                prop="remark"
                show-overflow-tooltip
                label="备注"
                width="120">
              </el-table-column>
              <el-table-column
                fixed="right" label="操作"
                label-class-name="header-style"
                align="left" width="250">
                <template slot-scope="scope">
                  <el-button size="mini" type="success" @click.native="itemViewClick(scope.row)">查看</el-button>
                  <template v-if="role === 4">
                    <el-button size="mini" type="primary" @click="itemUpdateClick(scope.row)">修改</el-button>
                    <el-button size="mini" type="danger" @click="itemDeleteClick(scope.row)">删除</el-button>
                  </template>
                </template>
              </el-table-column>
            </el-table>
            <el-pagination
              background
              layout="prev, pager, next"
              :current-page="tableData.pageNum"
              :page-size="form.pageSize"
              :total="tableData.total"
              @current-change ="disasterPageChangeHandle">
            </el-pagination>
          </el-row>
          <el-row>
            <span style="color:black;font-size: 14px;">*有效期限：</span>
            <span style="color:red;font-size: 14px;">红色表示已过期；</span>
            <span style="color:green;font-size: 14px;">绿色表示快过期；</span>
            <span style="color:black;font-size: 14px;">黑色表示未过期；</span>
          </el-row>
          <el-row>
            <span style="color:black;font-size: 14px;">*证件名称：</span>
            <span style="color:red;font-size: 14px;">红色表示复训时间已过期；</span>
            <span style="color:green;font-size: 14px;">绿色表示复训时间快过期；</span>
            <span style="color:black;font-size: 14px;">黑色表示复训时间未过期；</span>
          </el-row>
        </el-form>
      </el-main>
    </el-container>
  </div>
</template>
<script>
  export default {
    name: '',
    data() {
      let that = this;
      return {
        form : {
          eduUser : {
            // 姓名
            username : '',
            // 职务(岗位)
            duty : '',
          },
          // 证件名称
          certificateName : '',
          // 证书编号
          certificateId : '',
          // 复训时间---开始
          startDate : '',
          // 复训时间---结束
          endDate : '',
          // 发证单位
          issuingDepartment : '',
          // 当前页
          pageCurrent : 1,
          // 页数大小
          pageSize : 8,
        },
        // 表单辅助字段
        assist : {
          // 职务(岗位)
          duty : [
            { value: '董事长', label: '董事长'},
            { value: '副总经理', label: '副总经理'},
            { value: '副总工程师', label: '副总工程师'},
            { value: '资产管理部副主任', label: '资产管理部副主任'},
            { value: '办公室副主任', label: '办公室副主任'},
            { value: '安全管理部经理助理', label: '安全管理部经理助理'},
            { value: '安全管理部员工', label: '安全管理部员工'},
          ],
         /* // 发证单位
          issuingDepartment : [
            { value: '宁波江东区安监局', label: '宁波江东区安监局'},
            { value: '宁波鄞州区安监局', label: '宁波鄞州区安监局'},
          ],*/
          // 复训时间---单个
          retrainDate : [],
          // 批量复训--复训时间
          retrainDateMore : '',
          // 批量复训--多选ID
          idList : [],
        },
        tableData : {},
        // 角色 0 员工 4 发布者
        role : 0,
        // 导入
        // 上传文件
        upload : {
          // 地址
          url: that.$http.defaults.baseURL + 'eduCert/addBatch',
          // token
          cookies: true,
          params : {
            id : -1,
          },
        }
      }
    },
    mounted(){
      this.init();
    },
    watch:{
      $route(to,from){
        if(to.name === 'certificateTrainingIndex') {
          this.init();
        }
      }
    },
    methods:{
      // 导入

      downloadTemplate:function () {
        const loading = this.$loading({
          lock: true,
          text: 'Loading',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        let params=new URLSearchParams;
        this.$http({ // 用axios发送post请求
          method: 'post',
          url: 'eduCert/downloadTemplate', // 请求地址
          data: params, // 参数
          responseType: 'blob' // 表明返回服务器返回的数据类型
        }).then((res) => { // 处理返回的文件流
          loading.close()
          const elink = document.createElement('a') // 创建a标签
          elink.download = '持证信息导入模板'+'.xlsx' // 文件名
          elink.style.display = 'none'
          const blob = new Blob([res.data])
          elink.href = URL.createObjectURL(blob)
          document.body.appendChild(elink)
          elink.click() // 触发点击a标签事件
          document.body.removeChild(elink)
        })
      },

      //上传文件
      uploadSuccess: function (response, file, fileList) {
        if (response.success) {
          this.$message({
            type : 'success',
            message : '导入成功'
          })
          // 获取项目列表
          this.searchBtnClickHandle();
        } else {
          this.$message({
            type : 'error',
            message : response.message || '导入失败'
          })
        }
      },
      // 初始化
      init(){
        this.judgeUserRole();
        // 搜索
        this.searchBtnClickHandle();
      },
      judgeUserRole(){
        // 获取权限按钮
        let btns = this.$tool.getPowerBtns('eduTrainingMenu', this.$route.path);
        // 公司
        if(btns.includes('addBtn')){
          this.role = 4;
        } else {
          this.role = 1;
        }
      },
      // 复训时间
      retrainDateChangeHandle(val){
        if(val){
          this.form.startDate = val[0];
          this.form.endDate = val[1];
        } else {
          this.form.startDate = '';
          this.form.endDate = '';
        }
      },
      // 多选
      handleSelectionChange(val){
        this.assist.idList = val.map(function(it){
          return it.id;
        });
      },
      // 批量复训按钮
      moreRetrainingBtnHandle(){
        // 判断用户身份
        let user = this.$tool.getStorage('LOGIN_USER');
        let params = {
          eduCertificateRetrains : [],
        }
        this.assist.idList.forEach(function(it){
          params.eduCertificateRetrains.push({
            createDate : new Date(),
            createId : user.userId,
            createUser : user.loginName,
            eduCertificateId : it,
            retrainRecord : this.assist.retrainDateMore,
          })
        }.bind(this))
        this.$store.dispatch('eduCertBatchRetrain', params).then(function(res){
          if(res.success){
            this.$message({
              type : 'success',
              message : '操作成功'
            })
            this.searchBtnClickHandle();
          } else {
            this.$message({
              type : 'error',
              message : res.message || '操作失败！！'
            })
          }
        }.bind(this))
      },
      // 格式化时间
      formatDateTime(row, column, cellValue){
        let pro = column.property;
        let num = 10;
        // 年份4位 1999
        if(pro === 'createYear') num = 4;
        let str = this.$tool.formatDateTime(row[pro] || 0);
        return str ? str.substring(0, num) : str;
      },
      // 分页
      disasterPageChangeHandle(page){
        this.form.pageCurrent = page;
        this.searchBtnClickHandle();
      },
      // 搜索按钮
      searchBtnClickHandle(){
//        let params = this.$tool.filterObj({}, this.$tool.filterObj({}, this.form));
        let params = this.form;
//        let params = this.$tool.filterObj({}, this.form);
        // 如果为员工的话，查找和自己有关的数据
        if(this.role !== 4) {
          params['userId'] = this.$tool.getStorage('LOGIN_USER').userId;
        }
        this.$store.dispatch('eduCertFind', params).then(function(res){
          if(res.success){
            this.tableData = res.data;
          } else {
            this.$message({
              type : 'error',
              message : res.message || '错误'
            })
          }
        }.bind(this));
      },
      // 查看
      itemViewClick(row){
        let name = 'certificateTrainingAdd';
        let params = {
          id : row.id,
          status : 'view'
        }
        this.$router.push({ name : name, params : params})
      },
      // 修改
      itemUpdateClick(row){
        let name = 'certificateTrainingAdd';
        let params = {
          id : row.id,
          status : 'edit'
        }
        this.$router.push({ name : name, params : params})
      },
      // 删除按钮
      itemDeleteClick(row){
        this.$confirm('此操作将永久删除, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
          .then(function(){
            this.$store.dispatch('eduCertDelete', {
              id : row.id
            }).then(function(res){
              if(res.success){
                this.$message({
                  type : 'success',
                  message : '删除成功'
                })
                this.searchBtnClickHandle();
              } else {
                this.$message({
                  type : 'error',
                  message : res.message || '删除失败！！'
                })
              }
            }.bind(this))
          }.bind(this))
      },
      // 复训时间是否快到期，或者已经过期
      isExpired(date){
        // 返回颜色，红色代表过期，绿色代表距离过期还有一个月，黑色正常；
        let now = (new Date()).getTime();
        if(now >= date){
          return 'red';
        } else if( date > now && date - now < 30 * 24 * 3600000){
          return 'green';
        } else {
//          return 'black';
        }
      },
      // 复训周期是否快到期
      isExpiredCycle(row){
        // 返回颜色，红色代表过期，绿色代表距离过期还有一个月，黑色正常；
        // 取证日期 getDate
        // 复训日期 retrainDate
        // 复训周期 retrainCycle
        // 当前时间
        let now = (new Date()).getTime();
        // 取证年
        let quZYear = new Date(row.getDate).getFullYear()
        // 当前年
        let nowYear = new Date().getFullYear();
        // 复训周期
        let retrainCycle = row.retrainCycle;

        // 复训到期日期
        let expireDate = '';
        // 复训日期有填写的
        if(row.retrainDate) {
          // 复训到期日期 = （复训日期的年-取证日期月-取证日期日） +  复训周期 * 365天
          let year = new Date(row.retrainDate).getFullYear();
          let month = new Date(row.getDate).getMonth();
          let date = new Date(row.getDate).getDate();
//          console.log(year, '-', month, '-', date);
          expireDate = new Date(year, month, date).getTime() + row.retrainCycle * 365 * 24 * 3600 * 1000;
        }
        // 没有填写
        else {
          // 复训到期日期 = 取证日期 + 复训周期 * 365天
          expireDate = new Date(row.getDate).getTime() + row.retrainCycle * 365 * 24 * 3600 * 1000
        }
        // 过期
        if(now >= expireDate){
          return 'red';
        }
        // 即将过期，1个月内
        else if( now < expireDate && expireDate - now < 3 * 30 * 24 * 3600000){
          return 'green';
        } else {
          return 'black';
        }
      }
    }
  }
</script>
<style>
  .container{
    background:#fff;
    padding:0 20px;
  }
  .row{
    margin-top:10px;
  }
</style>
