/*
  created by m<PERSON><PERSON><PERSON> on 2018-4-3
  common data in emergency Recue
*/
import http from '../../src/assets/functions/axiosServer'
import tool from '../../src/components/common/tool'
// 应急救援
export default {
  state: {
    //参考标签列表
    referLabels:[],
    //应急状态列表
    emergencyStatusArray:[],
    //状态标签对应表
    statusTable:[],
  },
  getters: {},
  mutations: {
    getLabels(state,msg){
      state.referLabels=msg;
    },
    getEmergencyStatusArray(state,msg){
      state.emergencyStatusArray=msg;
    },
    getStatusTable(state,msg){
      state.statusTable=msg;
    },
  },
  actions : {
    getLabels({commit},label){
      let paramsStr='label/topLabel?searchLabels='+label;
      if(label.length){//标签搜索个数超过1，label不能为空
        paramsStr+='&label=1';
      }
      paramsStr+='&companyId='+tool.getStorage('LOGIN_USER').companyId;
      http.get(paramsStr).then(function (res) {
        if(res.data.success){
          let tempList=[];
          for(let i=0;i<res.data.data.length;i++){
            tempList.push({value:res.data.data[i].label,label:res.data.data[i].label});
          }
          commit('getLabels',tempList);
        }
      }.bind(this)).catch(function (err) {
        console.log('获取标签:'+err);
      }.bind(this));
    },
    getEmergencyStatusArray:function ({commit}) {
      let tempList=[];
      let statusTable=[
        {id:0,name:'未提交',labelType:'primary'},
        {id:1,name:'待审核',labelType:'warning'},
        {id:2,name:'被退回',labelType:'danger'},

        {id:3,name:'已签发',labelType:'success'},
        {id:4,name:'调整未提交',labelType:'primary'},
        {id:5,name:'调整待审核',labelType:'warning'},
        {id:6,name:'调整被退回',labelType:'danger'},
        {id:7,name:'调整已签发',labelType:'success'},

        {id:8,name:'解除未提交',labelType:'primary'},
        {id:9,name:'解除被退回',labelType:'success'},
        {id:10,name:'解除待审核',labelType:'warning'},

        {id:11,name:'待总结',labelType:'success'},
        {id:12,name:'总结未提交',labelType:'primary'},
        {id:13,name:'总结已提交',labelType:'success'},
        {id:14,name:'已完结',labelType:'info'},
      ];
      http.get('emgFlow/getEmgFlowStatus').then(function (res) {
        if(res.data.success){
          let remoteData=res.data.data;
          tempList.push({value:'全部状态', label:'全部状态'});
          for(let i=0;i<remoteData.length;i++){
             tempList.push({value:remoteData[i].status,label:remoteData[i].name});
             statusTable[remoteData[i].status].name=remoteData[i].name;
             statusTable[remoteData[i].status].labelType=remoteData[i].statusColor;
          }
          commit('getEmergencyStatusArray',tempList);
          commit('getStatusTable',statusTable);
        }
      }.bind(this)).catch(function (err) {
        console.log('获取标签:'+err);
      }.bind(this));
    },
  }
};
