<template>
  <el-container class="container" id="safeEducationView">
    <el-main>
      <el-row>
        <el-col :span="24">
          <h1 style="text-align: center;">{{info.newsName}}</h1>
        </el-col>
      </el-row>
      <el-row class="lineHeight">
        <el-col :span="6"  style="text-align: center;">作者：{{info.newsAuthor}}</el-col>
        <el-col :span="6" style="text-align: center;">发布时间：{{formatDateTime(info.createTime)}}</el-col>
        <el-col :span="6" style="text-align: center;">出处：{{info.newsSource}}</el-col>
      </el-row>
      <el-row class="lineHeight">
        <el-col :span="24">
      <!--    <vue-editor v-model="info.newsContent"></vue-editor>-->
          <div v-html="info.newsContent"></div>
        </el-col>
      </el-row>
      <el-row type="flex" class="row" justify="center">
        <el-button size="small" :span="2"  @click="$router.back();">返回</el-button>
      </el-row>
    </el-main>

  </el-container>
</template>

<script>

  import _ from 'lodash'
  import { VueEditor } from 'vue2-editor'
  import chooseStaff from '@/components/common/chooseStaff'
  export default {
    components: {
      chooseStaff,
      VueEditor
    },
    data(){
      return {
        // info表
        info : {
          // ID
          id : '',
          // 文章名称
          newsName : '',
          // 文章类型
          newsType : '',
          // 文章内容
          newsContent : '',
          // 文章作者
          newsAuthor : '',
          // 文章出处
          newsSource : '',
          // 停留时间(s)
          stayTime : 0,
          // 所属主题
          // 是否必修
          // 文章积分
          newsScore : 0,
          // 发布时间
          createTime : 0,


        },


        timer : null,




      }
    },
    watch:{
      $route(to,from){
        // 如果来至列表页
        if(from.name === 'safetyEducationArticleIndex'&&this.$route.name==='safetyEducationArticleView'){
          this.debouncedGetAnswer()
        }
      },
    },
    created: function () {
      this.debouncedGetAnswer = _.debounce(this.searchBtnClickHandle, 500)
    },
    methods:{
      init(){
        this.searchBtnClickHandle();
      },
      formatDateTime(dateStr){
        let num = 10;
        let str = this.$tool.formatDateTime(dateStr) || '';
        return str ? str.substring(0, num) : str;
      },


      // 根据id搜索信息
      searchBtnClickHandle(){
        let id = this.$route.params.id;
        this.viewOrEdit = true;
        this.$store.dispatch('eduNewsFindById', { id : id }).then(function(res){
          if(res.success){
            let list = res.data.list[0];
            // 发布培训信息
            Object.entries(list).forEach(function(it){
              if(it[1] && this.info.hasOwnProperty(it[0])){
                this.info[it[0]] = it[1];
              }
            }.bind(this));
            // 阅读完加分，如果没有阅读过，阅读时间>=页面停留时间，那么加分
            if(this.info.stayTime > 0){
              clearTimeout(this.timer);
              this.timer = setTimeout(function(){
                this.$store.dispatch('eduUserStudyAddUserNews', { studyId : id }).then(function(res) {
                  if (res.success) {
                    this.$message({
                      type: 'success',
                      message: '阅读完毕'
                    })
                  }
                }.bind(this))
              }.bind(this), this.info.stayTime * 1000)
            }

          } else {
            this.$message({
              type : 'error',
              message : res.message || '错误'
            })
          }
        }.bind(this));
      },

    }
  }
</script>

<style>
  .container{
    background:#fff;
    padding:0px 20px 20px;
  }
  #safeEducationView .lineHeight{
    margin-top:20px;
  }
  .title{
    background:rgba(64,158,255,.1);
    color:#0f6fc6;
    border: 1px solid rgba(64,158,255,.2);
    border-radius:5px;
  }
  .row{
    margin-top:10px;
  }
</style>
