<template>
    <div id="">
      <div style="width: 100%;padding-top: 10px;padding-bottom:10px;float: left;background-color: #f2f2f2">
        <i class="el-icon-upload" style="color:#049ff1;float: left;margin:12px 10px 0 20px"></i>
        <span style="color:#049ff1;width: 200px;float: left;">其他文件资料</span>
      </div>
      <div style="width: 100%;float:left;padding-top: 5px;padding-bottom: 5px">
        <el-upload
          class="upload-demo"
          :action="upload.url"
          multiple
          :limit="upload.limit"
          :with-credentials="upload.cookies"
          :data="upload.params"
          :on-success="uploadSuccess"
          style="width: 300px;margin-bottom: 10px;">
          <el-button size="small" type="primary" v-if="upload.btns.upload.isShow">点击上传</el-button>
        </el-upload>
        <el-col :span="24">
          <el-table
            :data="upload.fileData"
            border
            style="width: 100%;">
            <el-table-column
              type="index"
              align="center"
              label-class-name="inner-header-style"
              width="50">
            </el-table-column>
            <el-table-column
              prop="fileName"
              label="文件名称"
              align="center"
              label-class-name="inner-header-style"
              width="320">
            </el-table-column>
            <el-table-column
              prop="uploadTime"
              label="上传时间"
              align="center"
              :formatter="formatDateTime"
              label-class-name="inner-header-style"
              min-width="150">
            </el-table-column>
            <el-table-column
              label="操作"
              align="center"
              fixed="right"
              width="140"
              label-class-name="inner-header-style">
              <template slot-scope="scope">
                <el-button type="text" size="medium" style="color: #5daf34"
                           @click="downloadFile(scope.row)">下载
                </el-button>
                <el-button type="text" size="medium" style="color: #dd6161"
                           v-if="upload.btns.delete.isShow"
                           @click="deleteUploadFile(scope.row,scope.$index)">刪除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </div>

    </div>
</template>

<script>
    export default {
      props : ['data'],
      data(){
        let that = this;
        return {
          // 上传文件
          upload : {
            // 地址
            url : that.$http.defaults.baseURL + 'file/upload',
            limit : 10,
            // token
            cookies : true,
            // 上传参数
            params : {
              contentId: that.$route.params.id,
              contentType: 3
            },
            // 上传和删除按钮的显示
            btns : {
              // 上传按钮
              upload : {
                isShow : true,
              },
              // 删除按钮
              delete : {
                isShow : true,
              },
            },
            // 文件列表
            fileData : [],
          },
        }
      },
      mounted(){
        // 初始化数据
        this.init();
        // 获取上传文件
        this.loadFile();
      },
      methods:{
        // 初始化数据
        init(){
          // 把父组件传递的值给子组件
          Object.entries(this.data).forEach(function(it){
            if(this.upload.hasOwnProperty(it[0])){
              this.upload[it[0]] = it[1];
            }
          }.bind(this))
        },
        // 格式化时间
        formatDateTime(row, column, cellValue){
          let pro = column.property;
          let num = 10;
          let str = this.$tool.formatDateTime(row[pro]) || '';
          return str ? str.substring(0, num) : str;
        },
        //上传文件
        uploadSuccess: function (response, file, fileList) {
          if (response.success) {
            this.loadFile();
          }
        },
        // 获取上传文件
        loadFile: function () {
          var params = new URLSearchParams()
          params.append("contentId", this.upload.params.contentId)
          params.append("contentType", this.upload.params.contentType)
          this.$store.dispatch('fileFindAction', params).then(function(res){
            if (res.success) {
              this.upload.fileData = res.data
              this.$emit('fileData', this.upload.fileData);
            }
          }.bind(this));
        },
        // 删除上传文件
        deleteUploadFile: function (row, index) {
          this.$confirm('此操作将删除该文件, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(function(){
            var params = new URLSearchParams()
            params.append("fId", row.fId)
            this.$store.dispatch('fileDeleteAction', params).then(function (res) {
              if (res.success) {
                this.loadFile();
              }
            }.bind(this))
          }.bind(this));
        },
        // 下载文件
        downloadFile: function (row) {
          const loading = this.$loading({
            lock: true,
            text: 'Loading',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });
          var params = new URLSearchParams()
          params.append("fId", row.fId)
          this.$store.dispatch('fileDownloadAction', params).then(function(res){ // 处理返回的文件流
            loading.close()
            // 创建a标签
            const elink = document.createElement('a')
            // 文件名
            elink.download = row.fileName
            elink.style.display = 'none'
            const blob = new Blob([res.data])
            elink.href = URL.createObjectURL(blob)
            document.body.appendChild(elink)
            // 触发点击a标签事件
            elink.click();
            document.body.removeChild(elink)
          })
        },
      }
    }
</script>

<style>

</style>
