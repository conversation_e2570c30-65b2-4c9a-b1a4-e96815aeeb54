<template>
  <div id="trainingPlanIndex">
    <div class="background-style">
      <!--搜索区-->
      <div class="search-bar">
        <el-row>
          <el-col :span="2">
            <el-button
              @click="addBtnClickHandle"
              type="success"
              style="margin-left: 20px"
              >新增</el-button
            >
          </el-col>
        </el-row>
      </div>
      <!--表格区-->
      <div style="width: 100%">
        <div style="padding: 20px 10px 20px 10px">
          <el-table border :data="tableData.list" style="width: 100%">
            <el-table-column
              prop="mineId"
              label="矿山编码"
              align="center"
              label-class-name="header-style"
            />

            <el-table-column
              prop="mineName"
              label="矿山名称"
              align="center"
              label-class-name="header-style"
            />

            <el-table-column
              prop="cityName"
              label="所在市"
              align="center"
              label-class-name="header-style"
            />

            <el-table-column
              prop="countyName"
              label="所在县"
              align="center"
              label-class-name="header-style"
            />

            <el-table-column
              prop="townName"
              align="center"
              label="所在乡镇"
              label-class-name="header-style"
            />

            <el-table-column
              prop="detailAddr"
              show-overflow-tooltip
              label="详细地址"
              label-class-name="header-style"
            />
            <el-table-column
              label="操作"
              label-class-name="header-style"
              align="left"
              width="240"
            >
              <template slot-scope="scope">
                <el-button size="mini" type="warning" @click="view(scope.row)"
                  >查看</el-button
                >
                <el-button
                  size="mini"
                  type="warning"
                  @click="itemEditClick(scope.row)"
                  >修改</el-button
                >
                <el-button
                  size="mini"
                  type="danger"
                  @click="itemDeleteClick(scope.row)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div style="margin-top: 10px">
          <el-pagination
            background
            layout="prev, pager, next"
            :current-page="tableData.pageNum"
            :page-size="form.pageSize"
            :total="tableData.total"
            @current-change="disasterPageChangeHandle"
          >
          </el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import fileUpload from "@/components/common/fileUploadFileServer";
export default {
  components: {
    fileUpload,
  },
  data() {
    return {
      form: {
        // 标题
        name: "",
        // 当前页
        pageCurrent: 1,
        // 页数大小
        pageSize: 10,
      },
      tableData: {},
      // 专题类型
      fileType: [],
      // 对话框
      dialog: {
        // 是否显示
        isShow: false,
        form: {
          id: "",
          name: "",
          fileId: "",
          types: [],
          type: "",
          fileNumber: "",
          version: "",
          effectiveTime: "",
          sort: "",
        },
        assist: {
          planList: [],
        },
      },
      // 角色 0 员工 1 发布者
      role: 0,
      // 权限按钮
      powerBtns: [],
    };
  },
  activated() {
    this.init();
  },
  mounted() {},

  methods: {
    // 初始化
    init() {
      // 搜索
      this.searchBtnClickHandle();
    },
    // 格式化时间
    formatDateTime(row, column, cellValue) {
      let pro = column.property;
      let num = 10;
      // 年份4位 1999
      if (pro === "createYear") num = 4;
      let str = this.$tool.formatDateTime(row[pro] || 0);
      return str ? str.substring(0, num) : str;
    },
    // 分页
    disasterPageChangeHandle(page) {
      this.form.pageCurrent = page;
      this.searchBtnClickHandle();
    },
    // 搜索按钮
    searchBtnClickHandle() {
      let params = {};
      this.$http.post("/sys/sysMine/findMineInfo", params).then((res) => {
        if (res.data.success) {
          this.tableData = res.data.data;
        } else {
          this.$message({
            type: "error",
            message: res.message || "错误",
          });
        }
      });
    },
    // 删除按钮
    itemDeleteClick(row) {
      this.$confirm("此操作将永久删除, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.$http
          .post("/sys/sysMine/delMineInfo", { id: row.id })
          .then((res) => {
            if (res.data.success) {
              this.$message({
                type: "success",
                message: "删除成功",
              });
              this.searchBtnClickHandle();
            } else {
              this.$message({
                type: "error",
                message: res.message || "删除失败！！",
              });
            }
          });
      });
    },
    // 查看按钮
    view(row) {
      this.$router.push({
        name: "mineInfoView",
        params: { type: "view", row },
      });
    },
    // 修改按钮
    itemEditClick(row) {
      this.$router.push({
        name: "mineInfoView",
        params: { type: "edit", row },
      });
    },
    // 添加按钮
    addBtnClickHandle() {
      this.$router.push({ name: "mineInfoView", params: { type: "add" } });
    },
  },
};
</script>
<style>
</style>
