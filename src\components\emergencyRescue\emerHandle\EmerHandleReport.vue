<template>
    <div id="">
      <el-container>
        <el-main>
          <el-form label-width="120px" label-position="left" class="demo-ruleForm">
            <el-row>
              <el-col :span="12">
                <el-form-item label="上报单位：">
                  {{user.deptName}}
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item label="上报时间：">
                  <el-date-picker
                    type="datetime"
                    :readonly="pageStatus.view"
                    v-model="form.reportTime"
                    placeholder="选择日期时间">
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :offset="2" :span="8">
                <el-form-item label="事件名称：">
                  <el-input
                    :readonly="pageStatus.view"
                    v-model="form.eventName"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item label="事发单位：">
                  <el-input
                    :readonly="pageStatus.view"
                    v-model="form.incidentUnit"></el-input>
                </el-form-item>
              </el-col>
              <el-col :offset="2" :span="8">
                <el-form-item label="发生时间：">
                  <el-date-picker
                    :readonly="pageStatus.view"
                    v-model="form.eventTime"
                    type="datetime"
                    placeholder="选择日期时间">
                  </el-date-picker>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item label="发生地点：">
                  <el-input
                    :readonly="pageStatus.view"
                    v-model="form.eventLocation"></el-input>
                </el-form-item>
              </el-col>
              <el-col :offset="2" :span="8">
                <el-form-item label="编号：">
                  <el-input
                    :readonly="pageStatus.view"
                    v-model="form.number"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item label="信息报送联系人：">
                  <el-select
                    v-model="contactUser"
                    filterable
                    remote
                    :readonly="pageStatus.view"
                    reserve-keyword
                    placeholder="请输入关键词"
                    @change="userListChange"
                    :remote-method="remoteMethod">
                    <el-option
                      v-for="item in userList"
                      :key="item.value"
                      :label="item.label"
                      :value="item">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :offset="2" :span="8">
                <el-form-item label="联系电话：">
                  <el-input
                    :readonly="pageStatus.view"
                    v-model="form.phone"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-form-item label="上报给谁看：">
              </el-form-item>
            </el-row>
            <el-row>
              <chooseStaff
                ref="chooseStaff"
                @selectedRows="selectedRows"></chooseStaff>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item label="信息上报单位负责人：">
                  <el-input
                    :readonly="pageStatus.view"
                    v-model="form.reportUnitCharge"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="事件原因、经过及进展情况：">
                  <el-input
                    :readonly="pageStatus.view"
                    v-model="form.eventCause" type="textarea":rows="2"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="死亡、受伤、失踪人数和身份：">
                  <el-input
                    :readonly="pageStatus.view"
                    v-model="form.deathInjured" type="textarea":rows="2"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="预计直接经济损失：">
                  <el-input
                    :readonly="pageStatus.view"
                    v-model="form.economicLoss" type="textarea":rows="2"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="已采取的措施及初步处置情况：">
                  <el-input
                    :readonly="pageStatus.view"
                    v-model="form.measureSituation" type="textarea":rows="2"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="发展趋势及影响预判：">
                  <el-input
                    :readonly="pageStatus.view"
                    v-model="form.developmentPrejudgment" type="textarea":rows="2"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :offset="6" :span="12">
                <el-button type="primary" v-if="pageStatus.add" @click="nextBtnClickHandle">提交</el-button>
                <el-button @click="$router.back()">返回</el-button>
              </el-col>
            </el-row>
          </el-form>
        </el-main>
      </el-container>
    </div>
</template>

<script>
    import chooseStaff from '@/components/common/chooseStaff'
    export default {
      components: {
        chooseStaff,
      },
      data(){
        return {
          form : {
            // 事件id
            eventId : '',
            // 上报时间
            reportTime : '',
            // 事件名称
            eventName : '',
            // 事发单位
            incidentUnit : '',
            // 发生时间
            eventTime : '',
            // 发生地点
            eventLocation : '',
            // 上报给谁看
            noticeUserIds : [],
            // 编号
            number : '',
            // 信息报送联系人
            contactUserId : '',
            // 联系电话
            phone : '',
            // 信息上报单位负责人
            reportUnitCharge : '',
            // 死亡、受伤、失踪人数和身份
            deathInjured : '',
            // 预计直接经济损失
            economicLoss : '',
            // 已采取的措施及初步处置情况
            measureSituation : '',
            // 发展趋势及影响预判
            developmentPrejudgment : '',
            // 事件原因、经过及进展情况
            eventCause : '',
          },
          // 信息报送联系人
          contactUser : '',
          // 登录人信息
          user : {},
          // 联系人列表
          userList : [],
          // 页面状态
          pageStatus : {
            add : true,
            view : false,
          },
          // 报告给上级单位的人员列表，查看的时候使用
          noticeUsers : [],

        }
      },
      created(){
        if(this.$route.params.status === 'view'){
          this.viewPage();
        }
      },
      mounted(){
        this.init();
      },
      computed:{
        // 事件
        emgEvent : function(){
          return this.$store.state.emerHandleModule.emgEvent
        },
      },
      watch:{
        '$route.params.status'(to, from){
          console.log('状态:', to);
          if(to === 'view'){
            this.viewPage();
          } else if(to === 'edit'){
            this.editPage();
          }
        }
      },
      methods:{
        // 初始化
        init(){
          this.form.eventId = this.emgEvent.id;
          // 获取登录人的信息
          this.user = this.$tool.getStorage('LOGIN_USER');
        },
        clear(){
          this.form = this.$tool.clearObj({}, this.form);
        },
        // 查看页面
        viewPage(){
          let that = this;
          //return;
          this.pageStatus.add = false;
          this.pageStatus.view = true;
          // 根据eventId获取数据
          let params = {
            eventId : this.emgEvent.id
          }
          this.$store.dispatch('emgHandleReportFindAction', params).then(function(res){
            if(res.success){
              let list = res.data[0];
              Object.entries(list).forEach(function(it){
                if(that.form.hasOwnProperty(it[0])) that.form[it[0]] = it[1];
              })
              this.contactUser = list.contactUserName;
              // 显示人员列表
              if(this.$refs['chooseStaff'].changeTableDataHandle){
                this.$refs['chooseStaff'].changeTableDataHandle(list.noticeUsers);
              }
              if(this.$refs['chooseStaff'].isShowBtnHandle){
                this.$refs['chooseStaff'].isShowBtnHandle(false);
              }
            }
          }.bind(this));
        },
        // 编辑页面---仅添加
        editPage(){
          this.clear();
          console.log('事件', this.emgEvent)
          this.form.eventId = this.emgEvent.id;
          this.form.contactUser = '';
          // 重大信息上报中的一些事件信息可从事件中自动取来，事件名称、事发时间、发生地点等
          this.form.eventName = this.emgEvent.eventTitle;
          this.form.eventTime = this.emgEvent.eventTime;
          this.form.eventLocation = this.emgEvent.location;
          //return;
          this.pageStatus.view = false;
          this.pageStatus.add = true;

          // 添加人员
          this.$store.dispatch('deptGetFartherCompanyDept', {}).then(function(res){
            if(res.success) {
              this.$refs['chooseStaff'].setDialogNodeTree(res.data);
            }
          }.bind(this));

        },
        // 用户列表搜索
        remoteMethod(query) {
          this.$store.dispatch('userFindAction', query).then(function(res){
            if(res.success){
              this.userList = res.data.list.map(function(it){
                return {
                  value : it.userId,
                  label : it.username,
                  phone : it.mobile
                }
              })
            }
          }.bind(this));
        },
        // 用户列表选择
        userListChange(item){
          this.form.contactUserId = item.value;
          this.form.phone = item.phone;
        },
        // 提交按钮
        nextBtnClickHandle(){
          if(!this.form.eventId){
            this.$message({
              type : 'warning',
              message : '缺失事件ID'
            })
            return;
          }
          this.$store.dispatch('emgHandleReportAddAction', this.form).then(function(res){
            if(res.success){
              this.$message({
                type : 'success',
                message : '操作成功'
              })
              this.$router.push({ name : 'emerHandle' });
            } else {
              this.$message({
                type : 'error',
                message : '操作失败！！！'
              })
            }
          }.bind(this));
        },
        // 人员列表选择组件处理函数
        selectedRows(rows){
          // 参与人员列表----用户userId列表
          let userIds = rows.map(function(it){
            return it.userId
          })
          this.form.noticeUserIds = userIds;
        }
      }
    }
</script>

<style>
  .el-form-item{
    margin-bottom : 0;
  }
  .el-row{
    margin-bottom:10px;
  }
</style>
