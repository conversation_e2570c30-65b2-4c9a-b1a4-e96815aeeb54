<template>
  <div class="map">
    <el-container>
      <el-main>
        <el-row style="margin:0;">
          <el-col :span="6">
            <el-button type="primary" size="mini" @click="$router.back()">返回</el-button>
          </el-col>
        </el-row>
        <el-row style="margin:10px 0 0 0;">
          <el-col :span="24">
            <egrid class="egrid"
                   stripe border
                   maxHeight="500"
                   :data="egrid.data"
                   :columns="egrid.columns"
                   :column-type="egrid.columnType">
            </egrid>
          </el-col>
        </el-row>
      </el-main>
    </el-container>
  </div>
</template>

<script>


  export default {
    data(){
      return {
        form : {
          year : '',
        },
        // 表格
        egrid : {
          data : [],
          columns : [
            { label: '发生时间', prop: 'accountTime' },
            { label: '项目', prop: 'item' },
            { label: '费用金额（元）', prop: 'accountNum' },
            { label: '经办部门', prop: 'department' },
            { label: '备注', prop: 'remark' },
          ],
          // columnsProps 用于定义所有 columns 公共的属性
          columnsProps: {
            fit : true,
            sortable: true,
            align : 'center',
          },
          columnsSchema : {

          },
          columnType : 'index'
        }
      }
    },
    created(){
      this.init();
    },
    watch:{
      $route(to,from){
        let year = to.params && to.params.row && to.params.row.year;
        if(to.name === 'accountSafeItemInput') {
          if(year){
            let date = new Date();
            date.setFullYear(year);
            this.form.year = date;
            this.searchBtnClickHandle();
          }
        }
      }
    },
    methods:{
      // 初始化
      init(){
        let year = this.$route.params.row.year;
        let date = new Date();
        date.setFullYear(year);
        this.form.year = date;
        // 搜索
        this.searchBtnClickHandle();
      },
      // 搜索按钮
      searchBtnClickHandle(){
        this.$store.dispatch('costAccountRegSearchByYear', this.form).then(function(res){
          if(res.success){
            let list = res.data.costAccountRecords.map(function(it){
              return {
                accountTime : this.$tool.formatDateTime(it.accountTime).substring(0,10),
                item : it.item || '',
                accountNum : it.accountNum || 0,
                department : it.department || '',
                remark : it.remark || '',
              }
            }.bind(this));
            this.egrid.data = list;
          } else {
            this.egrid.data = [];
          }
        }.bind(this));
      },
    }
  }
</script>

<style>
  .map{
    background:#fff;
    height:100%;
    overflow: hidden;
    padding:50px 20px;
  }
</style>
