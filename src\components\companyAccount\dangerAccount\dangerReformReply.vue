<template>
  <div id="dangerReformReply">
    <div class="background-style">
      <el-col :span="16" :offset="4" class="success-background-title">{{titleStr}}</el-col>
      <el-col :span="16" :offset="4">
        <el-form :model="form" ref="ruleForm" label-width="120px" class="demo-ruleForm" label-position="left">
          <el-col :span="24" style="margin-top: 20px">
            <vue-editor v-model="reformReply.content"></vue-editor>
          </el-col>
        </el-form>
      </el-col>
      <el-col :span="22" :offset="1" style="margin-top: 10px">
        <el-table
          v-loading="loading"
          border
          :data="inspectListData">
          <el-table-column
            type="index"
            label="序号"
            width="50"
            fixed
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            prop="inspectProject"
            label="检查项目"
            width="150"
            fixed
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            prop="inspectContent"
            min-width="400"
            label="检查标准内容"
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            prop="inspectResult"
            width="300"
            label="检查结果记录"
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            prop="hiddenDangerLevel"
            width="150"
            label="隐患级别"
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            width="200"
            label="隐患照片"
            label-class-name="inner-header-style">
            <template slot-scope="scope">
              <picture-card :picFileList="scope.row.dangerPics"></picture-card>
            </template>
          </el-table-column>
          <el-table-column
            prop="deadline"
            width="150"
            label="整改时间"
            :formatter="changeFormat"
            label-class-name="inner-header-style">
          </el-table-column>
        </el-table>
      </el-col>
      <el-col :span="22" :offset="1">
        <div style="float: right;margin: 20px">
          <el-button type="primary" @click="download">下载文件</el-button>
          <el-button @click="$router.go(-1)">返回</el-button>
        </div>
      </el-col>
    </div>
  </div>

</template>

<script>
  import pictureCard from '../../common/smallComponent/pictureCard.vue'
  import { VueEditor } from 'vue2-editor'
  export default {
    name: "dangerReformReply",
    data(){
      return{
        form:{},
        inspectListData:[],
        reformReply:{},
        content:'',
        loading:false,
        titleStr:''
      }
    },
    components: {
      "picture-card": pictureCard,
      'vue-editor':VueEditor,
    },
    created:function () {
      if(this.$route.params.reformReply){
        this.reformReply=this.$route.params.reformReply;
        this.loadInspectList(this.reformReply.userId,this.reformReply.dangerInspectPublicId);
      }
    },
    watch:{
      $route(to, from){
        if(from.name==='accountDangerIndex') {
          if(this.$route.params.reformReply){
            this.reformReply=this.$route.params.reformReply;
            this.loadInspectList(this.reformReply.userId,this.reformReply.dangerInspectPublicId);
          }
        }
      }
    },
    methods:{
      loadInspectList:function (userId,dangerInspectPublicId) {
        var params={inspectPublicId:dangerInspectPublicId,applyUserId:userId};
        this.loading=true;
        this.$http.post("/danger/inspectListPublic/find",params).then(function (res) {
          if(res.data.success){
            this.inspectListData=res.data.data
          }
          this.loading=false;
        }.bind(this)).catch(function (err) {
          console.log(err)
        })
      },
      //改时间格式
      changeFormat:function (row) {
        return this.transferTime(row.deadline);
      },
      download:function () {

        const loading = this.$loading({
          lock: true,
          text: 'Loading',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        this.$http({ // 用axios发送post请求
          method: 'get',
          url: '/report//dangerReformReplyExcel/' + this.reformReply.id, // 请求地址
          responseType: 'blob' // 表明返回服务器返回的数据类型
        }).then((res) => { // 处理返回的文件流
          //console.info(res)
          loading.close()
          const content = res
          const elink = document.createElement('a') // 创建a标签
          elink.download = "xxxx的回执单" + ".xlsx" // 文件名
          elink.style.display = 'none'
          const blob = new Blob([res.data])
          elink.href = URL.createObjectURL(blob)
          document.body.appendChild(elink)
          elink.click() // 触发点击a标签事件
          document.body.removeChild(elink)
        })
      }
    }
  }
</script>

<style scoped>

</style>
