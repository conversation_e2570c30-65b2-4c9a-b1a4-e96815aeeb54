<template>
  <div id="changeFormViewWorkflow">
    <div class="background-style">
      <el-col :span="16" :offset="4" class="success-background-title">{{titleStr}}</el-col>
      <el-col :span="16" :offset="4">
        <el-form :model="form" ref="ruleForm" label-width="120px" class="demo-ruleForm" label-position="right">
          <el-col :span="24">
            <el-col :span="12">
              <el-form-item label="检查单编号：" prop="checkNum" style="margin: 0"  label-width="100px">
                {{form.checkNum}}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="检查类型：" style="margin: 0" label-width="90px">
                {{dangerData.typeName}}
              </el-form-item>
            </el-col>
          </el-col>
          <div v-if="dangerData.typeName!='自查'">
            <el-col :span="24">
              <el-col :span="12">
                <el-form-item label="受检单位：" prop="targetDeptName" style="margin: 0;" label-width="90px">
                  {{form.targetDeptName}}
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="检查单位：" prop="publicDeptName" style="margin: 0;" label-width="90px">
                  {{form.publicDeptName}}
                </el-form-item>
              </el-col>
            </el-col>
            <el-col :span="24">
              <el-col :span="12">
                <el-form-item label="受检单位承办人：" prop="targetContractorUserName" style="margin: 0;" label-width="140px">
                  {{form.targetContractorUserName}}
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="受检单位现场负责人：" prop="targetLiveChargeUser" style="margin: 0" label-width="160px">
                  {{form.targetLiveChargeUser}}
                </el-form-item>
              </el-col>
            </el-col>
          </div>
          <el-col :span="24" v-show="changeFlag">
            <el-col :span="12">
              <el-form-item label="临时处理时间：" style="margin: 0">
               {{tempMeasureTime}}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="整改通知单：" style="margin: 0" label-width="100px">
                <el-button type="success" size="small" @click="viewNoticeForm">查看通知单</el-button>
              </el-form-item>
            </el-col>
          </el-col>
          <el-col :span="24" v-show="changeFlag">
            <el-form-item label="临时措施：" prop="tempMeasure" label-width="90px">
              {{tempMeasure}}
            </el-form-item>
          </el-col>
          <el-col :span="24" v-show="changeFlag">
            <el-form-item label="临时措施照片：" style="margin: 0">
              <picture-card :picFileList="tempPictureList"></picture-card>
            </el-form-item>
          </el-col>
          <el-col :span="24" v-show="changeFlag">
            <el-form-item label="整改回执：" prop="reformReply" style="margin: 0" label-width="90px">
              {{reformReply}}
            </el-form-item>
          </el-col>
          <el-col :span="24" v-show="changeFlag">
            <el-form-item label="整改附件：" label-width="90px">
             <file-list :fileList="changeFileList"></file-list>
            </el-form-item>
          </el-col>
          <el-col :span="24" v-show="superviseFlag">
            <el-form-item label="督办回执：" style="margin: 0" label-width="90px">
              {{superviseReply}}
            </el-form-item>
          </el-col>
          <el-col :span="24" v-show="superviseFlag">
            <el-form-item label="督办附件：" label-width="90px">
              <file-list :fileList="superviseFileList"></file-list>
            </el-form-item>
          </el-col>
        </el-form>
      </el-col>
      <el-col :span="22" :offset="1">
        <el-table
          border
          :row-class-name="judgeOverTime"
          v-loading="loading"
          :data="tableData">
          <el-table-column
            type="index"
            label="序号"
            width="50"
            fixed
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            prop="inspectProject"
            label="检查项目"
            width="150"
            fixed
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            prop="inspectContent"
            min-width="400"
            label="检查标准内容"
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            prop="applyUserName"
            label="负责人"
            width="120"
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            prop="inspectResult"
            width="300"
            label="检查结果记录"
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            prop="hiddenDangerLevel"
            width="150"
            label="隐患级别"
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            width="320"
            label="隐患照片"
            label-class-name="inner-header-style">
            <template slot-scope="scope">
              <picture-card :picFileList="scope.row.dangerPics"></picture-card>
            </template>
          </el-table-column>
          <el-table-column
            prop="dangerType"
            width="150"
            label="隐患类型"
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            prop="changeRequire"
            width="200"
            label="整改要求"
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            prop="deadline"
            width="120"
            label="整改期限"
            :formatter="deadlineFormat"
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            v-if="changeFlag"
            width="320"
            label="整改照片"
            label-class-name="inner-header-style">
            <template slot-scope="scope">
              <picture-card :picFileList="scope.row.changePics"></picture-card>
            </template>
          </el-table-column>
          <el-table-column
            v-if="changeFlag"
            prop="changeTime"
            width="120"
            label="整改时间"
            :formatter="changeTimeFormat"
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            v-if="changeFlag"
            prop="changeExplain"
            width="180"
            label="整改说明"
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            v-if="changeFlag"
            prop="superviseFinishDate"
            width="180"
            label="督办期限"
            :formatter="superviseFinishDateFormat"
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            v-if="superviseFlag"
            width="320"
            label="督办照片"
            label-class-name="inner-header-style">
            <template slot-scope="scope">
              <picture-card :picFileList="scope.row.supervisePics"></picture-card>
            </template>
          </el-table-column>
          <el-table-column
            v-if="superviseFlag"
            prop="superviseReformDate"
            width="120"
            label="督办时间"
            :formatter="superviseFormat"
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            v-if="superviseFlag"
            prop="superviseReformExplain"
            width="180"
            label="督办说明"
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            v-if="changeFlag===true&&superviseFlag===false"
            width="110"
            label="操作"
            fixed="right"
            label-class-name="inner-header-style">
            <template slot-scope="scope">
              <el-button size="mini" type="primary" @click="itemSuperviseDeadlineClick(scope.row)">督办期限</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
      <el-col :span="16" :offset="4" style="margin-top: 10px">
        <el-form :model="singleForm" ref="singleForm" label-width="100px" class="demo-ruleForm">
          <el-form-item label="审核意见：" prop="examine">
            <el-dropdown @command="editExamine">
              <el-button type="primary" size="small">
                审核参考<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item v-for="item in selectOptions" :key="item.id" :command="item.content">{{item.name}}</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <el-input type="textarea" :autosize="{ minRows: 3}" v-model="singleForm.examine"></el-input>
          </el-form-item>
        </el-form>
      </el-col>
      <el-col :span="16" :offset="4" style="margin-top: 10px" v-show="signBtnVisible">
        <el-form :model="singleForm" ref="singleForm" label-width="100px" class="demo-ruleForm">
          <el-form-item label="签字：" prop="examine">
            <img :src="signUrl" style="width: 150px" />
            <el-button size="small" style="margin-left: 20px" @click="signBtn">{{signButtonStr}}</el-button>
          </el-form-item>
        </el-form>
      </el-col>
      <el-col :span="22" :offset="1">
        <div style="float: right;margin: 20px">
          <el-button type="success" @click="submit">通过</el-button>
          <el-button type="danger" @click="turnBack">退回</el-button>
          <el-button @click="returnBack">返回</el-button>
        </div>
      </el-col>
    </div>

    <!--退回对话框-->
    <el-dialog title="提示" :visible.sync="turnBackVisible" width="30%">
      <span>您希望该整改单，“重新整改”还是“挂牌督办”？</span>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="doTaskClick(1,false,false,0);turnBackVisible=false;">重新整改</el-button>
        <el-button type="warning" @click="superiorClick()">挂牌督办</el-button>
        <el-button @click="turnBackVisible = false">取 消</el-button>
      </span>
    </el-dialog>
    <!--退回对话框结束-->






    <!--扫码签字对话框-->
    <el-dialog title="提示" :visible.sync="turnBackVisible" width="30%">
      <span>您希望该整改单，“重新整改”还是“挂牌督办”？</span>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="doTaskClick(1,false,false,0);turnBackVisible=false;">重新整改</el-button>
        <el-button type="warning" @click="superiorClick()">挂牌督办</el-button>
        <el-button @click="turnBackVisible = false">取 消</el-button>
      </span>
    </el-dialog>
    <!--扫码签字对话框结束-->

    <!--输入督办期限-->
    <el-dialog title="输入督办期限" :visible.sync="superviseDeadlineVisible" width="30%">
      <span>督办期限：</span>
      <el-date-picker
        v-model="tempSuperviseDeadline"
        type="date"
        placeholder="选择日期">
      </el-date-picker>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="saveSuperviseDeadline">保存</el-button>
      </span>
    </el-dialog>
    <!--输入督办期限结束-->

    <!--选择负责人-->
    <search-people-dialog @determineClick="selectPersonClick" :data="selectPersonData" :defaultPersonId="selectPersonData.defaultPerson.value"></search-people-dialog>

    <!--判断对话框-->
    <judge-dialog ref="judgeDialog" @buttonClick="judgeExamine"></judge-dialog>

    <!--签字二维码对话框-->
    <el-dialog title="请扫码签字" :visible.sync="qrcodeVisible" width="30%">
      <div style="margin-left: 50%">
        <div id="qrcode" ref="qrCodeUrl" style="margin-left: -50px"></div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="updateSignDetermine()">确定</el-button>
        <el-button @click="closeSignDialog">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
  import SearchPeopleDialog from '../../../common/smallComponent/searchSinglePeople.vue'
  import PictureCard from '../../../common/smallComponent/pictureCard.vue'
  import FileList from '../../../common/smallComponent/fileList.vue'
  import JudgeDialog from '../../../common/smallComponent/judgeDialog.vue'
  import QRCode from 'qrcodejs2'
  export default {
    name: 'changeFormViewWorkflow',
    data() {
      return {
        titleStr:'',
        currentId:'',
        tableData:[],
        loading:false,
        form:{},
        personLoading:false,
        //-------------------------退回对话框-------------------------
        turnBackVisible:false,
        //审核意见
        singleForm:{examine:''},
        //参考审核数据
        selectOptions:[
          {id:'examine01',name:'同意签发',content:'经审核，同意通过该检查。'},
          {id:'examine02',name:'退回修改',content:'经审核，不同意通过该检查，意见如下：'}
        ],
        //-------------------------督办期限对话框------------------------
        superviseDeadlineVisible:false,
        tempSuperviseId:'',
        tempSuperviseDeadline:'',
        //-------------------------当前检查的数据-------------------------
        dangerData:{},
        changeFormId:'',//整改单的ID
        reformReply:'',//整改回执
        tempMeasureTime:'',//临时措施时间
        tempMeasure:'',//临时措施
        superviseReply:'',//督办回执
        superviseUserId:'',//督办人ID
        //------------------附件和临时措施图片---------------------------
        tempPictureList:[],//临时措施图片
        changeFileList:[],//整改附件
        superviseFileList:[],//督办附件
        //------------------选择负责人的对话框-----------------------
        selectPersonData:{title:'请选择负责人',isShow:false,defaultPerson:{value:0,label:''}},
        leaderUser:{},
        //--------------------当前任务信息-----------------------------
        subProcessStartUserId:'',
        changeFlag:false,
        superviseFlag:false,
        qrcodeVisible:false,
        signUrl:'',//签名图
        openSignDialogTime:'',
        signDialogTimer:'',//定时查询器
        qrcode:'',//签名二维码扫码图
        nodeStatus:''
      }
    },
    computed:{
      personOptions:function () {
        return this.$store.state.sysManageData.personOptions;
      },
      signButtonStr:function () {
        if(this.signUrl){
          return "更换签名"
        }else{
          return "扫码签名"
        }
      },
      signBtnVisible:function () {
        return parseInt(this.nodeStatus)===6||9||13;//节点状态为6，9，13时显示签名
      }
    },
    components : {
      SearchPeopleDialog,
      PictureCard,
      FileList,
      JudgeDialog
    },
    created:function () {
      if(this.$route.params.dangerData){
        this.dangerData=this.$route.params.dangerData;
        this.titleStr=this.$route.params.dangerData.name;
        this.subProcessStartUserId=this.$route.params.dangerData.subProcessStartUserId;
        this.leaderUser={value:this.$route.params.dangerData.examineUserId,label:this.$route.params.dangerData.examineUsername};
        this.nodeStatus=this.$route.params.dangerData.currentStatus;
        this.searchDataById(this.$route.params.dangerData.id);
        this.getUserSign();
      }
    },
    watch:{
      $route(to, from){
        if((from.name==='hideDangerWorkflow'||from.name==='taskNotice')&&this.$route.name==='changeFormViewWorkflow') {
          if(this.$route.params.dangerData){
            this.dangerData=this.$route.params.dangerData;
            this.titleStr=this.$route.params.dangerData.name;
            this.subProcessStartUserId=this.$route.params.dangerData.subProcessStartUserId;
            this.leaderUser={value:this.$route.params.dangerData.examineUserId,label:this.$route.params.dangerData.examineUsername};
            this.nodeStatus=this.$route.params.dangerData.currentStatus;
            this.searchDataById(this.$route.params.dangerData.id);
            this.getUserSign();
          }
        }
      },
      qrcodeVisible:function(val){
        if(!val){
          clearInterval(this.signDialogTimer);
        }
      }
    },
    mounted() {

    },
    beforeDestroy() {
      if(this.signDialogTimer){
        clearInterval(this.signDialogTimer);
      }
    },
    methods: {

      // 创建二维码
      creatQrCode:function(){
        let contentId = this.$route.params.dangerData.id;
        let userId = this.$tool.getLoginer().userId;

        // let url = this.$store.state.mutations.currentBaseUrl+"esign-page?contentId="+contentId+"&userId="+userId;
        let url = window.location.origin+'/#/'+"esign-page?contentId="+contentId+"&userId="+userId;

        if(this.qrcode){

        }else{
          this.qrcode = new QRCode(this.$refs.qrCodeUrl, {
            // text: 'http://localhost:8084/#/esign-page', // 需要转换为二维码的内容
            // text: 'http://**************:8084/#/esign-page', // 需要转换为二维码的内容
            // text: 'http://**********/#/esign-page', // 需要转换为二维码的内容
            // text: 'http://www.baidu.com', // 需要转换为二维码的内容
            text : url,
            width: 100,
            height: 100,
            colorDark: '#000000',
            colorLight: '#ffffff',
            correctLevel: QRCode.CorrectLevel.H
          })
        }
      },



      searchDataById:function (id) {
        //清除之前数据
        this.tableData.splice(0);
        this.reformReply='';
        this.singleForm.examine='';
        //this.personOptions.splice(0);
        for(let item in this.form){
          delete this.form[item];
        }
        this.changeFormId=0;//整改单的ID
        this.reformReply='';
        this.tempMeasure='';
        this.superviseReply='';
        this.changeFlag=false;//未进入整改，不现实整改内容
        this.superviseFlag=false;//未进入督办整改，不显示督办内容
        this.tempPictureList.splice(0);//临时措施图片
        this.changeFileList.splice(0);//整改附件
        this.superviseFileList.splice(0);//督办附件

        //to do 这里要改为工作流中的flag记录
        if(this.dangerData.currentStatus>6){this.changeFlag=true;if(this.dangerData.currentStatus>9&&this.dangerData.currentStatus<14){this.superviseFlag=true}}
        this.currentId=id;
        this.$http.post('danger/inspectPublic/detail', {id:id}).then(function (res) {
          if (res.data.success) {
            this.form=res.data.data;
          }
        }.bind(this)).catch(function (err) {
          this.$message.error('danger/inspectPublic/detail');
          console.log(err);
        });
        //获取整改单信息
        this.$http.get('danger/reformSheet/find?dangerInspectPublicId='+id+'&applyUserId='+this.subProcessStartUserId).then(function (res) {
          if (res.data.success) {
            if(res.data.data.length){
              this.changeFormId=res.data.data[0].id;//整改单的ID
              this.reformReply=res.data.data[0].reformReply;
              this.tempMeasureTime=this.transferTime(res.data.data[0].tempMeasureTime);
              this.tempMeasure=res.data.data[0].tempMeasure;
              this.superviseReply=res.data.data[0].superviseReply;
              this.superviseUserId=res.data.data[0].superviseUserId;
              this.changeFileList=res.data.data[0].reformAttachments;
              for(let i=0;i<this.changeFileList.length;i++){
                this.changeFileList[i].name=this.changeFileList[i].fileName;
              }
              this.tempPictureList=res.data.data[0].measurePics;
              this.superviseFileList=res.data.data[0].superviseAttachments;
              this.superviseFileList.forEach(function (item) {
                item.name=item.fileName;
              })
            }
          }
        }.bind(this)).catch(function (err) {
          this.$message.error('/danger/reformReply/find');
          console.log(err);
        });
        this.loading=true;//让表格缓冲显示
        let tableDataParams={inspectPublicId:id,needChange:1};
        if(this.subProcessStartUserId){
          tableDataParams.applyUserId=Number(this.subProcessStartUserId);
        }
        this.$http.post('danger/inspectListPublic/find',tableDataParams).then(function (res) {
          if (res.data.success) {
            this.tableData=res.data.data;
            this.loading=false;
          }
        }.bind(this)).catch(function (err) {
          this.$message.error('danger/inspectListPublic/find');
          console.log(err);
        });
      },
      //查看检查通知
      viewNoticeForm:function () {
        this.$router.push({name:'investigationViewWorkflow',params:{dangerData:this.dangerData,onlyShow:true}})
      },
      //填写审核意见
      editExamine:function (content) {
        this.singleForm.examine=content;
      },
      //获取用户ID列表
      getUserIdArray:function () {
        let userIds=[];
        this.tableData.forEach(function (item) {
          userIds.push(item.applyUserId);
        });
        return Array.from(new Set(userIds));
      },
      //-----------------------------------表格功能---------------------------------
      //改时间格式
      deadlineFormat:function (row) {
        return this.transferTime(row.deadline);
      },
      changeTimeFormat:function (row) {
        return this.transferTime(row.changeTime);
      },
      superviseFormat:function (row) {
        return this.transferTime(row.superviseReformDate);
      },
      superviseFinishDateFormat:function (row) {
        return this.transferTime(row.superviseFinishDate);
      },
      //判断该条记录是否过期了
      judgeOverTime:function ({row}) {
        if(row.changeOverTime||row.superviseChangeOverTime){
          return 'warning-row';
        }else{
          return '';
        }
      },
      //填写督办期限
      itemSuperviseDeadlineClick:function (row) {
        this.tempSuperviseId=row.id;
        this.tempSuperviseDeadline=row.superviseFinishDate;
        this.superviseDeadlineVisible=true;
      },
      saveSuperviseDeadline:function () {
        if(this.tempSuperviseDeadline){
          this.$http.post('danger/inspectListPublic/update', {id:this.tempSuperviseId,superviseFinishDate:this.tempSuperviseDeadline}).then(function (res) {
            if (res.data.success) {
              let tableDataParams={inspectPublicId:this.dangerData.id,needChange:1};
              if(this.subProcessStartUserId){
                tableDataParams.applyUserId=this.subProcessStartUserId;
              }
              this.$http.post('danger/inspectListPublic/find',tableDataParams).then(function (res) {
                if (res.data.success) {
                  this.$message.success('督办期限保存成功！');
                  this.tableData=res.data.data;
                  this.superviseDeadlineVisible=false;
                }
              }.bind(this)).catch(function (err) {
                console.log('danger/inspectListPublic/find');
                console.log(err);
              });

            }
          }.bind(this)).catch(function (err) {
            console.log('danger/inspectListPublic/update');
            console.log(err);
          });
        }else{
          this.$message.warning('请选择督办期限');
        }
      },
      //--------------------------------提交----------------------------------
      //审核通过
      submit:function () {
        //处理签名
        if(this.signUrl){
          let updateParams=new URLSearchParams;
          updateParams.append("path",this.signUrl);
          updateParams.append("contentId",this.$route.params.dangerData.id);
          updateParams.append("contentType",23);
          updateParams.append("userId",this.$tool.getLoginer().userId);
          updateParams.append("fileName",this.$tool.getLoginer().userId);
          this.$http.post("file/addOrUpdate",updateParams).then(function (res) {

          }.bind(this)).catch( () => {
            this.$message.error('签名图更新失败');
          })
        }
        //处理任务
        if(this.dangerData.nodeData.superviseCheck){//是否为督办验收
          this.doTaskClick(0,true,false,this.superviseUserId);
        }else{
          if(this.dangerData.nodeData.check){
            let noticeStr=this.dangerData.nodeData.acceptFlag?'是否需要审核？':'是否需要其他人员审核？';
            this.$refs.judgeDialog.openJudgeDialog(noticeStr,'不需要','需要');
          }else{
            //不需要审核
            this.doTaskClick(0,false,false,0);
          }
        }
      },
      //人员选择后进入的节点
      judgeExamine:function (val) {
        if(val){
          if(this.dangerData.nodeData.acceptFlag){//当前为验收页面
            this.selectPersonData.title='请选择审核人';
            this.selectPersonData.defaultPerson=this.leaderUser;
          }else{
            this.selectPersonData.title='请选择下一个审核人';
            this.selectPersonData.defaultPerson={value:0,label:''};
          }
          this.selectPersonData.isShow=true;
        }else{
          //选择不审核
          this.doTaskClick(0,false,false,0);
        }
      },
      // 签字
      signBtn(){
        // 给一个二维码，扫描
        this.openSignDialogTime=new Date();
        this.qrcodeVisible=true;
        this.signDialogTimer=setInterval(this.getCurrentSign,1000);//为方法名时不加括号，为方法时加引号
        console.log()
        this.$nextTick(function () {
          this.creatQrCode();
        }.bind(this));
      },
      //审核退回
      turnBack:function () {
        this.$confirm('此操作将退回该整改单, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          if(this.dangerData.nodeData.supervise){//可能进入督办流程
            if(this.judgeHaveSuperviseFinishDate()){//判断是否填写了督办期限
              this.turnBackVisible=true;
            }else{
              this.$message.warning('请填写待督办隐患的督办期限！');
            }
          }else{
            //不需要督办
            this.doTaskClick(1,false,false,0);
          }
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消退回'
          });
        });
      },
      //选择审核人
      selectPersonClick:function (val) {
        if(val){
          this.selectPersonData.isShow=false;
          if(this.selectPersonData.title==='请选择督办人'){
            //进入督办整改
            this.doTaskClick(1,false,true,val);
          }else {
            //进入下一步审核
            this.doTaskClick(0,true,false,val);
          }
        }else{
          this.$message.warning('请选择审核人');
        }
      },
      //执行任务
      doTaskClick:function (result,check,surpervise,val) {
        let params=new URLSearchParams;
        params.append("result",result);
        params.append("check",check);
        params.append("supervise",surpervise);

        if(surpervise){//进入挂牌督办
          params.append("applyUserId", this.subProcessStartUserId);

          //存储督办验收人
          let replyParams=new URLSearchParams;
          replyParams.append("id",this.changeFormId);
          replyParams.append("superviseUserId",val);
          this.$http.post('danger/reformSheet/update', replyParams).then(function (res) {
            if (res.data.success) {
            }
          }.bind(this)).catch(function (err) {
            this.$message.error('存储督办人失败！');
            console.log(err);
          });
        }else{
          if(val){
            params.append("applyUserId",val);
          }else{
            if(result&&this.dangerData.nodeData.returnToApply){//退回返回到责任人处，而不是创建者
              params.append("applyUserId",this.subProcessStartUserId);
            }else{
              if(this.form.checkUserId){params.append("applyUserId",this.form.checkUserId)}//督办整改审核->督办验收，需要验收人
            }
          }
        }
        if(this.dangerData.nodeData.needUserIds){
          if(result===0&&check===false){
            params.append("userIds",this.getUserIdArray());
          }
        }
        params.append("taskId",this.dangerData.taskId);
        params.append("comment",this.singleForm.examine);
        this.$http.post('dangerFlow/doTask', params).then(function (res) {
          if (res.data.success) {
            if(this.selectPersonData.isShow){this.selectPersonData.isShow=false;}
            this.$message.success('操作成功！');
            this.$router.push({name:'hideDangerWorkflow'});
          }
        }.bind(this)).catch(function (err) {
          this.$message.error('操作失败！');
          console.log(err);
        }.bind(this));
      },
      //挂牌督办响应
      superiorClick:function () {
        this.selectPersonData.title='请选择督办人';
        this.turnBackVisible=false;
        this.selectPersonData.isShow=true;
      },
      //判断是否有督办期限
      judgeHaveSuperviseFinishDate:function () {
        for(let i=0;i<this.tableData.length;i++){
          if(this.tableData[i].superviseFinishDate){
            return true;
          }
        }
        return false;
      },
      //返回
      returnBack:function () {
        this.signUrl='';
        if(this.form.attachments){
          this.form.attachments.splice(0);
        }
        this.$router.push({name:'hideDangerWorkflow'});
      },
      //获取用户默认签名
      getUserSign:function () {
        this.signUrl='';
        this.$http.post('user/getUserSignFile').then(function (res) {
          if (res.data.success) {
            this.signUrl=res.data.data.userSignPath;
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
        });
      },
      //实时获取签名
      getCurrentSign:function(){
        let params=new URLSearchParams;
        params.append("contentId",this.$route.params.dangerData.id);
        params.append("contentType",23);
        params.append("userId",this.$tool.getLoginer().userId);
        params.append("uploadTime",this.openSignDialogTime);
        this.$http.post('file/getTimerSignFile',params).then(function (res) {
          if (res.data.success) {
            if(res.data.data){
              this.signUrl=res.data.data.fullPath;
              this.closeSignDialog();
            }
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
        });
      },
      //确定用户签名
      updateSignDetermine:function () {
        let params=new URLSearchParams;
        params.append("contentId",this.$route.params.dangerData.id);
        params.append("contentType",23);
        params.append("userId",this.$tool.getLoginer().userId);
        params.append("uploadTime",this.openSignDialogTime);
        this.$http.post('file/getTimerSignFile',params).then(function (res) {
          if (res.data.success) {
            if(res.data.data){
              this.signUrl=res.data.data.fullPath;
            }else {
              this.$message.info('未获取签名！');
            }
            this.closeSignDialog();
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
        });
      },
      //关闭签字二维码对话框
      closeSignDialog:function () {
        this.qrcodeVisible = false;
      }
    }
  }
</script>
<style scoped>
  .el-table .warning-row {
    color: #f00;
  }
  #qrcode {
    display: inline-block;
  }
  #qrcode img {
    width: 132px;
    height: 132px;
    background-color: #fff;
    padding: 6px;
    box-sizing: border-box;
  }
</style>
