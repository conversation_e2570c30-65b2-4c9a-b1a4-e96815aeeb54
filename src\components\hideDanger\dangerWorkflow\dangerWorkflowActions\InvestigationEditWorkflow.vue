<template>
  <div id="investigationEditWorkflow">
    <div class="background-style">
      <el-col :span="16" :offset="4" class="primary-background-title">{{form.name}}</el-col>
      <el-form :model="form" :rules="rules" ref="ruleForm" label-width="100px" label-position="left" class="demo-ruleForm">
        <el-col :span="16" :offset="4">
          <el-form-item label="检查单名称：" prop="name" style="margin: 10px;margin-left: 0">
            <el-input v-model="form.name"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="16" :offset="4">
          <el-col :span="24">
            <el-col :span="12">
              <el-form-item label="检查单编号：" prop="checkNum" style="margin: 0">
                {{form.checkNum}}
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="dangerTypeName =='自查'">
              <el-form-item label="检查日期：" prop="inspectDate" style="margin: 0" label-width="110px">
                <el-date-picker
                  disabled="true"
                  v-model="form.inspectDate"
                  type="date"
                  style="width: 100%">
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="dangerTypeName !='自查'">
              <el-form-item label="检查日期：" prop="inspectDate" style="margin: 0" label-width="110px">
                <el-date-picker
                  v-model="form.inspectDate"
                  type="date"
                  style="width: 100%">
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-col>
          <el-col :span="24">
            <el-col :span="12" v-if="dangerTypeName!='自查'">
              <el-form-item label="检查组组长：" prop="leaderUserName" style="margin-left: -10px" label-width="110px">
                <el-select
                  v-model="form.leaderUserName"
                  filterable
                  remote
                  reserve-keyword
                  clearable
                  placeholder="请输入姓名后选择"
                  @change="leaderUserClick"
                  :remote-method="remoteMember"
                  :loading="personLoading"
                  style="width: 100%">
                  <el-option
                    v-for="item in personOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="检查类型：" style="margin: 0" label-width="90px">
                {{dangerTypeName}}
              </el-form-item>
            </el-col>
          </el-col>
          <div v-if="dangerTypeName!='自查'">
            <el-col :span="24">
              <el-form-item label="检查组成员：" prop="memberNames" style="margin-left: -10px" label-width="110px">
                <el-select
                  v-model="form.memberNames"
                  @change="$forceUpdate()"
                  multiple
                  filterable
                  remote
                  reserve-keyword
                  clearable
                  placeholder="请输入姓名后选择"
                  :remote-method="remoteMember"
                  :loading="personLoading"
                  style="width: 100%">
                  <el-option
                    v-for="item in personOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-col :span="12">
                <el-form-item label="受检单位：" prop="targetDeptId" style="margin: 0;" label-width="90px">
                  {{form.targetDeptName}}
                  <!--<div v-if="dangerTypeName!='督查'">
                    {{form.targetDeptName}}
                  </div>
                  <div v-else>       此处要正常显示初值需要在targetCompanyArray中按顺序放入从父公司到子公司的ID ，by pdn
                    <el-cascader
                      change-on-select
                      :options="unitOptions"
                      style="width: 100%"
                      @change="handlePickCompany"
                      v-model="targetCompanyArray">
                    </el-cascader>
                  </div>-->
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="检查单位：" prop="publicDeptName" style="margin: 0;" label-width="90px">
                  {{form.publicDeptName}}
                </el-form-item>
              </el-col>
            </el-col>
            <el-col :span="24">
              <el-col :span="12">
                <el-form-item label="受检单位承办人：" prop="targetContractorUserId" style="margin: 10px;" label-width="140px">
                  <el-select
                    v-model="form.targetContractorUserId"
                    filterable
                    remote
                    reserve-keyword
                    clearable
                    placeholder="请输入姓名后选择"
                    :remote-method="remotePerson"
                    :loading="personLoading"
                    style="width: 100%">
                    <el-option
                      v-for="item in personOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="受检单位现场负责人：" prop="targetLiveChargeUser" style="margin: 10px;" label-width="170px">
                  <el-select
                    v-model="form.targetLiveChargeUser"
                    filterable
                    remote
                    reserve-keyword
                    clearable
                    allow-create
                    placeholder="选择人员或自定义"
                    :remote-method="remotePerson"
                    style="width: 100%">
                    <el-option
                      v-for="item in labelOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.label">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-col>
          </div>
        </el-col>
        <el-col :span="22" :offset="1">
          <el-table
            border
            :data="form.dangerInspectListPublicList">
            <el-table-column
              type="index"
              label="序号"
              width="50"
              fixed
              label-class-name="inner-header-style">
            </el-table-column>
            <el-table-column
              prop="inspectProject"
              label="检查项目"
              width="150"
              fixed
              label-class-name="inner-header-style">
            </el-table-column>
            <el-table-column
              prop="inspectContent"
              min-width="400"
              label="检查标准内容"
              label-class-name="inner-header-style">
            </el-table-column>
            <el-table-column
              prop="inspectResult"
              width="300"
              label="检查结果记录"
              label-class-name="inner-header-style">
            </el-table-column>
            <el-table-column
              prop="hiddenDangerLevel"
              width="150"
              label="隐患级别"
              label-class-name="inner-header-style">
            </el-table-column>
            <el-table-column
              width="320"
              label="隐患照片"
              label-class-name="inner-header-style">
              <template slot-scope="scope">
                <picture-card :picFileList="scope.row.dangerPics"></picture-card>
              </template>
            </el-table-column>
            <el-table-column
              prop="deadline"
              width="150"
              label="整改期限"
              :formatter="changeFormat"
              label-class-name="inner-header-style">
            </el-table-column>
            <el-table-column
              width="155"
              label="操作"
              fixed="right"
              label-class-name="inner-header-style">
              <template slot-scope="scope">
                <el-button type="primary" size="mini" @click="itemRecordClick(scope.row,scope.$index)">记录</el-button>
                <el-button type="danger" size="mini" @click="itemDeleteClick(scope.row,scope.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <div style="width: 100%;height: 40px;background-color: rgb(236,248,255);border-bottom: 1px solid #f2f2f2;">
            <div style="width: 100px;margin: auto">
              <el-button type="text" icon="el-icon-plus" @click="turnStatus">添加检查项目</el-button>
            </div>
          </div>
        </el-col>
        <el-col :span="22" :offset="1">
          <div style="float: right;margin: 20px">
            <el-button type="primary" @click="submitClick(2)">提交</el-button>
            <el-button type="success" @click="submitClick(1)">保存</el-button>
            <el-button @click="$router.push({name:'hideDangerWorkflow'})">返回</el-button>
          </div>
        </el-col>
      </el-form>
    </div>

    <!--记录对话框-->
    <el-dialog :title="recordTitle" :visible.sync="recordVisible">
      <el-form :model="recordForm" :rules="recordRules" ref="recordForm" label-position="left" class="demo-ruleForm">
        <el-form-item label="检查标准内容：" prop="inspectContent" >
          {{recordForm.inspectContent}}
        </el-form-item>
        <el-form-item label="是否需要治理：" prop="needChange" >
          <el-radio v-model="recordForm.needChange" label="0">不需要</el-radio>
          <el-radio v-model="recordForm.needChange" label="1">需要</el-radio>
        </el-form-item>
        <el-form-item label="检查结果记录：" prop="inspectResult">
          <el-dropdown @command="editResult">
            <el-button type="primary" size="small">
              参考内容<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item v-for="item in resultOptions" :key="item.id" :command="item.content">{{item.name}}</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <el-input type="textarea" :autosize="{ minRows: 2}" v-model="recordForm.inspectResult"></el-input>
        </el-form-item>
        <el-form-item label="隐患级别：" prop="hiddenDangerLevel"  label-position="left">
          <el-select
            v-model="recordForm.hiddenDangerLevel"
            clearable
            filterable
            placeholder="请选择">
            <el-option
              v-for="item in levelOption"
              :key="item"
              :label="item"
              :value="item">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="整改期限：" prop="deadline"  >
          <el-date-picker
            v-model="recordForm.deadline"
            type="date"
            placeholder="选择日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="检查图片：" style="margin: 5px" >
          <upload-picture :data="fileUploadParams" :picFileList="fileList"></upload-picture>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="success" @click="determineSave">保存</el-button>
      </div>
    </el-dialog>
    <!--记录对话框结束-->


    <!--新增检查项对话框   add by pdn-->
    <el-dialog
      :visible.sync="newdialog.isShow"
      width="50%"
      title="新增检查项"
      :before-close="newdialogCancelBtnClickHandle">
      <el-form label-width="100px">
        <el-row  class="row">
          <el-col :span="24">
            <el-form-item label="检查项目">
              <el-input v-model="newdialog.form.inspectProject"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="检查内容">
              <el-input v-model="newdialog.form.inspectContent"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="newdialogCancelBtnClickHandle">取 消</el-button>
        <el-button
          type="danger"  size="mini"
          @click="newdialogOkBtnClickHandle">确定</el-button>
      </div>
    </el-dialog>


    <!--把治理任务交给其他人-->
    <search-people-dialog @determineClick="selectPersonClick" :data="selectPersonData" :defaultPersonId="selectPersonData.defaultPerson.value"></search-people-dialog>

    <!--判断对话框-->
    <judge-dialog ref="judgeDialog" @buttonClick="judgeExamine"></judge-dialog>

  </div>
</template>
<script>
  import SearchPeopleDialog from '../../../common/smallComponent/searchSinglePeople.vue'
  import PictureCard from '../../../common/smallComponent/pictureCard.vue'
  import UploadPicture from '../../../common/smallComponent/uploadPicture.vue'
  import JudgeDialog from '../../../common/smallComponent/judgeDialog.vue'
  export default {
    name: 'investigationEditWorkflow',
    data() {
      return {
        //检查单名称
        titleStr: '',
        currentId:'',//检查表的ID
        targetCompanyArray:[2,133],

        form: {
          id:'',
          name: '',
          checkNum: '',
          inspectDate: '',
          targetDeptName: '',
          targetDeptId:'',
          leaderUserName: '',
          leaderUserId:'',
          dangerInspectMembers: [],
          dangerInspectListPublicList: [],
          targetContractorUserId: '',
          targetLiveChargeUser: '',
          needFeedback: '',
          memberNames:[],
        },
        dangerTypeName:'',//检查类型
        rules: {
          targetContractorUserId: [{required: true, message: '请选择人员', trigger: 'change'}],
          targetLiveChargeUser: [{required: true, message: '请选择人员', trigger: 'change'}],
          leaderUserName: [{required: true, message: '请选择人员', trigger: 'change'}],
          memberNames: [{required: true, message: '请选择人员', trigger: 'change'}],
        },
        personLoading: false,
        needFeedbackOptions: [{value: 0, label: '否'}, {value: 1, label: '是'}],

        //--------------------------------记录对话框---------------------------------
        recordTitle:'检查结果记录',
        recordVisible:false,
        recordForm:{
          id:'',
          needChange:'',
          inspectProject:'',
          inspectContent:'',
          inspectResult:'',
          hiddenDangerLevel:'',
          deadline:''
        },
        recordRules:{
          inspectContent: [{required: true, message: '请输入内容', trigger: 'change'}],
          needChange:[{required: true, message: '请选择是否需要治理', trigger: 'change'}],
        },
        currentIndex:'',
        resultOptions:[{id:1,content:'合格',name:'合格'},{id:2,content:'待整改的内容如下：',name:'待整改'}],
        levelOption:['一般(C级)','一般(B级)','重大(A级)'],
        //--------------------------上传和预览-----------------------------
        cookies: true,
        fileList:[],
        fileUploadParams: {
          contentId: 0,
          contentType: 4
        },
        //当前流程ID
        taskId:'',
        nodeData:{},//节点信息
        //------------------选择负责人的对话框-----------------------
        selectPersonData:{title:'请选择安全员',isShow:false,defaultPerson:{value:0,label:''}},

        //------------------ 新增检查项对话框 ------------------------- add by pdn
        newdialog : {
          // 是否显示
          isShow : false,
          form : {
            id : '',
            inspectContent : '',
            inspectProject : '',
          },
        },

      }
    },
    computed: {
      unitOptions:function () {//当前公司的子公司
        if(this.$store.state.hideDangerData.targetDept.length){
          return this.$store.state.hideDangerData.targetDept[0].children;
        }else{
          return [];
        }
      },
      personOptions: function () {
        return this.$store.state.sysManageData.personByJson;
      },
      labelOptions:function () {
        return this.$store.state.sysManageData.personByJson;
      },
      memberOptions:function () {
        return this.$store.state.sysManageData.personByJson;
      },
    },
    components : {
      SearchPeopleDialog,
      PictureCard,
      UploadPicture,
      JudgeDialog
    },
    mounted: function () {
      if(this.$route.params.dangerData){
        this.taskId=this.$route.params.dangerData.taskId;
        this.dangerTypeName=this.$route.params.dangerData.typeName;
        this.nodeData=this.$route.params.dangerData.nodeData;
        this.searchDataById(this.$route.params.dangerData.id);
        //add by pdn
        if(this.dangerTypeName=='督查'){
          this.$store.dispatch('getTargetDept');
        }
      }
    },
    watch: {
      $route(to, from) {
        if (from.name === 'hideDangerWorkflow' && this.$route.name === 'investigationEditWorkflow'||from.name==='taskNotice') {
          if(this.$route.params.dangerData){
            this.taskId=this.$route.params.dangerData.taskId;
            this.dangerTypeName=this.$route.params.dangerData.typeName;
            this.nodeData=this.$route.params.dangerData.nodeData;
            this.searchDataById(this.$route.params.dangerData.id);
          }
        }
      },
    },
    methods:{
      searchDataById:function (id) {
        //清除之前数据
        this.form.memberNames=[];
        this.form.id='';
        this.form.leaderUserId='';
        this.form.name='';
        this.form.checkNum='';
        this.form.inspectDate= '';
        this.form.targetDeptName='';
        this.form.leaderUserName='';
        this.form.targetContractorUserId='';
        this.form.targetLiveChargeUser='';
        this.form.needFeedback='';
        this.form.dangerInspectMembers.splice(0);
        this.form.dangerInspectListPublicList.splice(0);
        this.personOptions.splice(0);
        this.labelOptions.splice(0);

        this.currentId=id;
        this.$http.post('danger/inspectPublic/detail', {id:id}).then(function (res) {
          if (res.data.success) {
            let tempForm=res.data.data;
            this.form.name=tempForm.name;
            if(tempForm.targetLiveChargeUser&&tempForm.targetLiveChargeUser!=='0'){
              this.form.targetLiveChargeUser=tempForm.targetLiveChargeUser;
              this.labelOptions.push({value:tempForm.targetLiveChargeUser,label:tempForm.targetLiveChargeUser})
            }else{this.form.targetLiveChargeUser=''}
            this.form=tempForm;
            if(tempForm.inspectDate){
              this.form.inspectDate = this.transferTime(tempForm.inspectDate)
            }else{
              this.form.inspectDate = new Date()
            }
            this.form.memberNames =[];
            for(let i=0; i< this.form.dangerInspectMembers.length;i++){
              if (this.form.dangerInspectMembers[i].userId != null) {
                this.form.memberNames.push({
                  label: tempForm.dangerInspectMembers[i].userName,
                  value: parseInt(tempForm.dangerInspectMembers[i].userId)
                });
              }
            }
            this.remoteMember('');
            // this.leaderUserClick({value:this.form.leaderUserId,label:this.form.leaderUserName})
          }
        }.bind(this)).catch(function (err) {
          this.$message.error('查找数据失败');
          console.log(err);
        });
      },
      //改时间格式
      changeFormat:function (row) {
        return this.transferTime(row.deadline);
      },
      //查找人员
      remotePerson:function (val) {
        this.$store.dispatch('getPersonByJson',{name:val,companyId:this.form.targetDeptId});
      },
      //判断是否需要反馈
      judgeNeedChange:function () {
        for(let i=0;i<this.form.dangerInspectListPublicList.length;i++){
          if(Number(this.form.dangerInspectListPublicList[i].needChange)){
            return 1;
          }
        }
        return 0;
      },
      //提交
      submitClick:function (status) {
       if(this.dangerTypeName==='自查'){
         this.submitRequest(status);
       }else{
         this.$refs["ruleForm"].validate((valid) => {
           if (valid) {
             this.submitRequest(status);
           } else {
             console.log('error submit!!');
             return false;
           }
         });
       }
      },
      submitRequest:function (status) {
        // let tempDate=new Date();
        let confirmStr='';
        this.form.needFeedback=this.judgeNeedChange();
        this.form.needFeedback===1?confirmStr='当前检查单需要整改，将进入隐患治理':confirmStr='当前检查单不需要整改，将置为已完成';
        this.form.memberNames.forEach(function (item) {
          item.userId = item.value;
        })
        if(status===2){//提交
          this.$confirm(confirmStr+'。确认提交吗?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.$http.post('danger/inspectPublic/update', {id:this.form.id,dangerInspectMembers:this.form.memberNames,targetContractorUserId:this.form.targetContractorUserId,targetLiveChargeUser:this.form.targetLiveChargeUser,needFeedback:this.form.needFeedback,targetDeptId:this.form.targetDeptId,leaderUserId:this.form.leaderUserId,name:this.form.name,inspectDate:this.form.inspectDate}).then(function (res) {
              if (res.data.success) {
                this.doTaskClick();
              }
            }.bind(this)).catch(function (err) {
              this.$message.error('操作失败！');
              console.log(err);
            });
          }).catch(() => {
            this.$message({
              type: 'info',
              message: '已取消删除'
            });
          });
        }else{//保存
          this.$http.post('danger/inspectPublic/update', {id:this.form.id,dangerInspectMembers:this.form.memberNames,targetContractorUserId:this.form.targetContractorUserId,targetLiveChargeUser:this.form.targetLiveChargeUser,needFeedback:this.form.needFeedback,targetDeptId:this.form.targetDeptId,leaderUserId:this.form.leaderUserId,name:this.form.name,inspectDate:this.form.inspectDate}).then(function (res) {
            if (res.data.success) {
              this.$message.success('检查结果记录成功！');
              this.$router.push({name:'hideDangerWorkflow'});
            }
          }.bind(this)).catch(function (err) {
            this.$message.error('保存失败！');
            console.log(err);
          });
        }
      },
      doTaskClick:function () {
        let flowParams=new URLSearchParams;
        flowParams.append("taskId",this.taskId);
        flowParams.append("check",false);

        if(this.form.needFeedback===1){//有隐患
          flowParams.append("danger",true);
          if(this.nodeData.allowToChange){//是否可以直接进入整改，现在用于自查
            this.$refs.judgeDialog.openJudgeDialog('隐患内容自行处理还是交由安全员处理','安全员处理','自行处理');
          }else{//不能直接进入整改的，必须走发布审核流程
            flowParams.append("reform",true);
            this.doTaskOperate(flowParams);
          }
        }else{//无隐患
          flowParams.append("danger",false);
          flowParams.append("reform",false);
          this.doTaskOperate(flowParams);
        }
      },
      judgeExamine:function (val) {
        if(val){
          let flowParams=new URLSearchParams;
          flowParams.append("taskId",this.taskId);
          flowParams.append("check",false);
          flowParams.append("danger",true);
          flowParams.append("reform",false);
          flowParams.append("applyUserId",this.$tool.getStorage('LOGIN_USER').userId);
          this.doTaskOperate(flowParams);
        }else{
          //需要安全员为自己处理的，需要选择安全员
          this.selectPersonData.isShow=true;
        }
      },

      doTaskOperate:function (flowParams) {
        let userArray=[];
        userArray.push(this.$tool.getStorage('LOGIN_USER').userId);
        if(this.nodeData.needUserIds){//需要传负责人员的ID,自查且选择自己处理
          flowParams.append("userIds",userArray);
        }
        this.$http.post('dangerFlow/doTask',flowParams).then(function (res) {
          if(res.data.success) {
            this.$message.success('操作成功！');
            this.$router.push({name:'hideDangerWorkflow'});
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message.error('流程执行失败')
        }.bind(this));
      },
      selectPersonClick:function (val) {
        if(val){
          this.selectPersonData.isShow=false;
          let flowParams=new URLSearchParams;

          let userArray=[];
          userArray.push(val);

          if(this.nodeData.needUserIds){//需要传负责人员的ID,自查且选择自己处理
            flowParams.append("userIds",userArray);
          }

          flowParams.append("taskId",this.taskId);
          flowParams.append("check",false);
          flowParams.append("danger",true);
          flowParams.append("reform",false);
          flowParams.append("applyUserId",val);
          this.$http.post('dangerFlow/doTask',flowParams).then(function (res) {
            if(res.data.success) {
              this.$message.success('已经交由安全员处理！');
              this.$router.push({name:'hideDangerWorkflow'});
            }
          }.bind(this)).catch(function (err) {
            console.log(err);
            this.$message.error('流程执行失败')
          }.bind(this));

        }else{
          this.$message.warning('请选择安全员');
        }
      },

      //---------------------------------------检查记录--------------------------------
      //开始结果记录
      itemRecordClick:function (row,index) {
        this.currentIndex=index;
        this.recordForm.id=row.id;
        this.fileUploadParams.contentId=row.id;//上传检查图片对应的ID
        this.recordForm.inspectProject=row.inspectProject;
        this.recordForm.inspectContent=row.inspectContent;
        this.recordForm.inspectResult=row.inspectResult;
        this.recordForm.hiddenDangerLevel=row.hiddenDangerLevel;
        this.recordForm.deadline=row.deadline;
        this.fileList.splice(0);

        row.dangerPics.forEach(function (item) {
          this.fileList.push({name:item.fileName,
            fId:item.fId,
            url:this.fileHttp.defaults.baseURL+item.path+'?x-oss-process=image/resize,m_fixed,h_70,w_70',
            oriUrl:this.fileHttp.defaults.baseURL+item.path});
        }.bind(this));
        if(!this.recordForm.inspectResult){
          this.recordForm.inspectResult='合格';
        }
        this.recordVisible=true;
        if(row.needChange===null){
          this.recordForm.needChange='0';
        }else{
          this.recordForm.needChange=String(row.needChange);
        }
      },
      //删除条目
      itemDeleteClick:function (row,index) {
        this.$confirm('删除该条内容, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$http.post('danger/inspectListPublic/delete',{"id":row.id}).then(function (res) {
            if (res.data.success) {
              this.$message({
                type : 'success',
                message : '删除检查项成功！'
              })

              this.searchDataById(this.currentId);
            }
          }.bind(this)).catch(function (err) {
            this.$message.error('新增检查项失败');
            console.log(err);
          });
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });
        });
      },
      //输入检查结果
      editResult:function (command) {
        this.recordForm.inspectResult=command;
      },
      //检查结果记录确定
      determineSave:function () {
        this.$refs["recordForm"].validate((valid) => {
          if (valid) {
            let tempObj={id:this.recordForm.id,needChange: this.recordForm.needChange,inspectProject:this.recordForm.inspectProject,inspectContent:this.recordForm.inspectContent,inspectResult:this.recordForm.inspectResult,hiddenDangerLevel:this.recordForm.hiddenDangerLevel,deadline:this.recordForm.deadline};
            if(this.dangerTypeName==="自查"){
              tempObj.applyUserId=this.$tool.getStorage('LOGIN_USER').userId
            }
            this.$http.post('/danger/inspectListPublic/update', tempObj).then(function (res) {
              if (res.data.success) {
               this.refreshTable();
              }
            }.bind(this)).catch(function (err) {
              console.log('danger/inspectListPublic/update');
              console.log(err);
            });

          } else {
            console.log('error submit!!');
            return false;
          }
        });
      },
      //某一行的照片
      refreshTable:function () {
        this.$http.post('danger/inspectListPublic/find',{"inspectPublicId":this.currentId}).then(function (res) {
          if (res.data.success) {
           this.form.dangerInspectListPublicList=res.data.data;
            this.recordVisible=false;
          }
        }.bind(this)).catch(function (err) {
          this.$message.error('查找数据失败');
          console.log(err);
        });
      },

      //选择受检单位
      handlePickCompany:function () {
        this.form.targetDeptId=this.targetCompanyArray[this.targetCompanyArray.length-1];
      },

      turnStatus(){
        this.newdialog.isShow = true;
      },

      //新增检查项  确定按钮事件 向后台发送post请求 add by pdn
      newdialogOkBtnClickHandle(){
        if(this.newdialog.form.inspectProject==''||this.newdialog.form.inspectContent==''){
          this.$message({
            type : 'error',
            message : '输入内容不能为空！'
          })
        }else {
          this.$http.post('danger/inspectListPublic/addSelect',{"inspectPublicId":this.currentId,"inspectProject":this.newdialog.form.inspectProject, "inspectContent":this.newdialog.form.inspectContent, "inspectResult":"合格", "needChange":0}).then(function (res) {
            if (res.data.success) {
              this.$message({
                type : 'success',
                message : '新增检查项成功！'
              })

              this.newdialog.isShow = false;
              this.searchDataById(this.currentId);
            }
          }.bind(this)).catch(function (err) {
            this.$message.error('新增检查项失败');
            console.log(err);
          });
        }
      },

      // 新增检查项对话框  取消按钮事件  add by pdn
      newdialogCancelBtnClickHandle(){
        // 关闭对话框
        this.newdialog.isShow = false;
        // 情况对话框
        this.newdialog.form.id = '';
        this.newdialog.form.inspectContent = '';
        this.newdialog.form.inspectProject = '';
      },

      //查找人员
      remoteMember:function (val) {
        this.$store.dispatch('getPersonByJson',{name:val,companyId:this.$tool.getStorage('LOGIN_USER').companyId});
      },

      //选择受检单位
      handlePickCompany:function () {
        this.form.targetDeptId=this.targetCompanyArray[this.targetCompanyArray.length-1];
      },

      //选择组长并加入到小组组员中去
      leaderUserClick:function (val) {
        if(val.value){
          this.form.leaderUserId=val.value;
          if(this.form.memberNames.length){
            if(this.form.memberNames.findIndex(function (item) {return item.value===val.value;})<0){
              this.form.memberNames.push(val);
            }
          }else {
            this.form.memberNames.push(val);
          }
        }
      },

    }
  }
</script>
<style>
</style>
