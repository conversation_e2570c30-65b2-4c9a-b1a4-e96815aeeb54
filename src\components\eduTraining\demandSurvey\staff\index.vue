<template>
  <div id="">
    <div class="background-style">

      <!--搜索区-->
      <div class="search-bar">
        <div style="padding:10px 10px 0 10px;float: left">
          <!--状态：-->
          <el-select clearable v-model="form.status" placeholder="状态">
            <el-option
              v-for="item in statusSelect"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
          <el-button
            @click="searchBtnClickHandle"
            type="primary" icon="el-icon-search" style="margin-left: 20px">搜索</el-button>
        </div>
      </div>

      <!--表格区-->
      <div style="width: 100%;">
        <div style="padding: 20px 10px 20px 10px">
          <el-table
            :data="tableData.list"
            border
            style="width: 100%">
            <el-table-column
              type="index"
              label="编号"
              width="100"
              align="center"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="eduRequirementInvestigation.status"
              label="需求调查状态"
              width="150"
              label-class-name="header-style">
              <template slot-scope="scope">
                <el-tag size="mini" v-if="scope.row.eduRequirementInvestigation.status === 1">进行中</el-tag>
                <el-tag size="mini" v-if="scope.row.eduRequirementInvestigation.status === 2" type="danger">已完成</el-tag>
              </template>
            </el-table-column>
            <el-table-column
              prop="status"
              label="是否完成"
              width="150"
              label-class-name="header-style">
              <template slot-scope="scope">
                <el-tag size="mini" v-if="scope.row.status === 0">未完成</el-tag>
                <el-tag size="mini" v-if="scope.row.status === 1" type="danger">已完成</el-tag>
              </template>
            </el-table-column>
            <el-table-column
              prop="eduRequirementInvestigation.title"
              label="名称"
              min-width="300"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="eduRequirementInvestigation.createYear"
              label="年份"
              :formatter="formatDateTime"
              width="100"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="department"
              label="部门名称"
              min-width="300"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="eduRequirementInvestigation.startTime"
              label="开始时间"
              :formatter="formatDateTime"
              width="150"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              prop="eduRequirementInvestigation.endTime"
              label="结束时间"
              :formatter="formatDateTime"
              width="150"
              label-class-name="header-style">
            </el-table-column>
            <el-table-column
              fixed="right" label="操作"
              label-class-name="header-style"
              align="left" width="150">
              <template slot-scope="scope">
                <!--进行中-->
                <template v-if="scope.row.eduRequirementInvestigation.status === 1">
                  <!--未完成-->
                  <el-button
                    v-if="scope.row.status === 0"
                    size="mini" type="primary" @click="itemUpdateClick(scope.row)">参加</el-button>
                  <!--已完成-->
                  <el-button
                    v-if="scope.row.status === 1"
                    size="mini" type="success" @click.native="itemViewClick(scope.row)">查看</el-button>
                </template>
                <!--已完成-->
                <template v-if="scope.row.eduRequirementInvestigation.status === 2">
                  <!--已完成-->
                  <el-button
                    size="mini" type="success" @click.native="itemViewClick(scope.row)">查看</el-button>
                </template>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div style="margin-top: 10px">
          <el-pagination
            background
            layout="prev, pager, next"
            :current-page="tableData.pageNum"
            :page-size="form.pageSize"
            :total="tableData.total"
            @current-change ="disasterPageChangeHandle">
          </el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
  export default {
    name: '',
    data() {
      return {
        form : {
          // 用户
          userId : '',
          // 状态
          status : '',
          // 当前页
          pageCurrent : 1,
          // 页数大小
          pageSize : 10,
        },
        // 辅助字段--状态
        statusSelect:[
          { value : 0, label : '未完成' },
          { value : 1, label : '已完成' },
        ],
        tableData : {},
      }
    },
    mounted(){
      this.init();
    },
    watch:{
      $route(to,from){
        if(to.name === 'demandSurveyStaffIndex') {
          this.init();
        }
      }
    },
    methods:{
      // 初始化
      init(){
        // 搜索
        this.searchBtnClickHandle();
      },
      // 格式化时间
      formatDateTime(row, column, cellValue){
        let pro = column.property;
        let proArr = pro.split('.');
        let str = '';
        let num = 10;
        // 年份4位 1999
        if(proArr[1] === 'createYear') num = 4;
        if(row[proArr[0]][proArr[1]]){
          str = this.$tool.formatDateTime(row[proArr[0]][proArr[1]]);
        }
        return str ? str.substring(0, num) : str;
      },
      // 分页
      disasterPageChangeHandle(page){
        this.form.pageCurrent = page;
        this.searchBtnClickHandle();
      },
      // 搜索按钮
      searchBtnClickHandle(){
        // 判断用户身份
        let user = this.$tool.getStorage('LOGIN_USER');
        this.form.userId = user.userId;
        // let params = this.$tool.filterObj({}, this.form);
        let params =  this.form;

        this.$store.dispatch('eduReqUserRltFind', params).then(function(res){
          if(res.success){
            this.tableData = res.data;
          } else {
            this.$message({
              type : 'error',
              message : res.message || '错误'
            })
          }
        }.bind(this));
      },
      // 查看
      itemViewClick(row){
        //项目状态1 进行中 2已完成;row.status =1表示已参加；
        let status = row.eduRequirementInvestigation.status;
        if(status == 1 && row.status == 0){
          status = 'edit';
        }else{
          status = 'view'
        }
        let name = '';
        let params = {
          id : row.id,
          status : status
        }
        switch(row.status){
          case 0:
          case 1:
                name = 'demandSurveyStaffAdd';
                break;
          case 2:
                break;
        }
        this.$router.push({ name : name, params : params})
      },
      // 修改
      itemUpdateClick(row){
        let name = '';
        let params = {
          id : row.id,
          status : 'edit'
        }
        switch(row.status){
          case 0:
          case 1:
            name = 'demandSurveyStaffAdd';
            break;
          case 2:
            break;
        }
        this.$router.push({ name : name, params : params})
      },
      // 删除按钮
      itemDeleteClick(row){
        this.$confirm('此操作将永久删除, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(function(){
          // 删除---灾后处置
          this.$store.dispatch('eduReqInvDelete', {
            id : row.id
          }).then(function(res){
            if(res.success){
              this.$message({
                type : 'success',
                message : '删除成功'
              })
              this.searchBtnClickHandle();
            } else {
              this.$message({
                type : 'error',
                message : res.message || '删除失败！！'
              })
            }
          }.bind(this))
        }.bind(this))
      }

    }
  }
</script>
<style>
</style>
