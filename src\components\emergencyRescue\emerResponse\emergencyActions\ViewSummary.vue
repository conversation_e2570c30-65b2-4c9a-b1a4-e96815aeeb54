<template>
  <div id="viewSummary">
    <div class="background-style">
      <el-col :span="16" :offset="4" class="success-background-title">
        {{form.name}}
      </el-col>
      <el-col :span="16" :offset="4">
        <el-form :model="form"  label-width="120px">
          <el-col :span="24">
            <el-col :span="12">
              <el-form-item label="编制公司：">
                {{form.dept}}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="记录：">
                {{form.type}}
              </el-form-item>
            </el-col>
          </el-col>
        </el-form>
      </el-col>
      <el-col :span="20" :offset="2">
        <table class="simple-table">
          <tr><td v-for="item01 in tableHead01">{{item01.title}}</td></tr>
          <tr><td v-for="item01 in tableHead01">{{tableContent[item01.index]}}</td></tr>
          <tr><td v-for="item02 in tableHead02">{{item02.title}}</td></tr>
          <tr><td v-for="item02 in tableHead02">{{tableContent[item02.index]}}</td></tr>
        </table>
        <div style="margin-top: 10px;margin-right: 20px">
          <el-button style="float: right" type="primary" @click="$router.go(-1)">返回</el-button>
        </div>
      </el-col>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'viewSummary',
    data() {
      return {
        form:{
          name:'',
          dept:'',
          type:'应急工作总结评估表',
        },
        emerId:'',
        companyId:'',
        tableContent:{},
        tableHead01:[
          {title:'应急响应起止时间',index:'timeRange'},
          {title:'应急响应类型等级',index:'responseLevel'},
          {title:'各主要控股企业应急响应情况',index:'responseSituation'},
          {title:'应急效果评估',index:'effectEvaluation'},
        ],
        tableHead02:[
          {title:'受损单位',index:'damagedUnit'},
          {title:'人员伤亡情况',index:'deathSituation'},
          {title:'财产损失情况',index:'econLossSituation'},
          {title:'经济损失预估',index:'econLossEstimate'},
        ],
      }
    },
    created:function () {
      if(this.$route.params.emerId&&this.$route.params.emerName){
        this.form.name=this.$route.params.emerName;
        this.form.dept=this.$route.params.dept.label;
        this.emerId=this.$route.params.emerId;
        this.companyId=this.$route.params.dept.value;
        this.searchSum();
      }
    },
    watch:{
      $route(to, from) {
        if (( from.name === 'emergencyProcess') && this.$route.name === 'viewSummary') {
          if(this.$route.params.emerId&&this.$route.params.emerName){
            this.form.name=this.$route.params.emerName;
            this.form.dept=this.$route.params.dept.label;
            this.emerId=this.$route.params.emerId;
            this.companyId=this.$route.params.dept.value;
            this.searchSum();
          }
        }
      }
    },
    methods:{
      searchSum:function () {
        let params=new URLSearchParams;
        params.append("companyId",this.companyId);
        params.append("planPublicId",this.emerId)
        this.$http.post('emgSummary/find', params).then(function (res) {
          if (res.data.success) {
            if(res.data.data.list.length){
              this.tableContent=res.data.data.list[0];
            }
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message({
            showClose: true,
            message: '网络错误，请尝试重登录',
            type: 'error'
          });
        }.bind(this));
      },

    }
  }
</script>
<style scoped>
  .simple-table tr th, .simple-table tr td{width: 25%;}
</style>
