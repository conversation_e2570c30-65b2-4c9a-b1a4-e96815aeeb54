<template>
  <div id="viewEmergency">
    <div class="background-style">
      <el-col :span="16" :offset="4" class="success-background-title">查看应急响应</el-col>
      <el-col :span="16" :offset="4">
        <el-form :model="form" ref="ruleForm" label-width="120px" class="demo-ruleForm">
        <el-col :span="24">
          <el-form-item label="响应名称：" prop="emergencyName">
            <el-input v-model="form.emergencyName"></el-input>
          </el-form-item>
        </el-col>
          <el-col :span="24">
            <el-form-item label="应急启动：" prop="beginMessage">
              <el-input type="textarea" :autosize="{ minRows: 3}" v-model="form.beginMessage"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="应急类别：">
              <el-input v-model="form.typeStr" readonly="readonly"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="预警信号：" prop="emergencyFlag">
              <el-input v-model="form.emergencyFlag" readonly="readonly"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-col :span="12">
              <el-form-item label="级别：">
                <el-input v-model="form.level" readonly="readonly"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="上报间隔：">
                <el-input v-model="form.timeInterval" readonly="readonly"> <template slot="append">小时</template></el-input>
              </el-form-item>
            </el-col>
          </el-col>
          <el-col :span="24">
            <el-form-item label="预警信息：" prop="emerSituation">
              <el-input type="textarea" :autosize="{ minRows: 3}" v-model="form.emerSituation"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="应急响应要求：" prop="emerRequire">
              <el-input type="textarea" :autosize="{ minRows: 3}" v-model="form.emerRequire"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="应急解除通知：" prop="relieveTemplate">
              <el-input type="textarea" :autosize="{ minRows: 3}" v-model="form.relieveTemplate"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="值班安排：">
              <dutyTable :dutyData="dutyTableData" ref="dutyTable"></dutyTable>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-col :span="12">
              <el-form-item label="审核人：" prop="signerUserName">
                <el-input v-model="form.signerUserName" readonly="readonly"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="发布人：" prop="createUserName">
                <el-input v-model="form.createUserName" readonly="readonly"></el-input>
              </el-form-item>
            </el-col>
          </el-col>
          <el-col :span="24">
            <el-form-item label="审核意见：" prop="examine">
              <div  v-if="isLeader">
                <el-dropdown @command="editExamine">
                  <el-button type="primary" size="small">
                    审核参考<i class="el-icon-arrow-down el-icon--right"></i>
                  </el-button>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item v-for="item in selectOptions" :key="item.id" :command="item.content">{{item.name}}</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
                <el-input type="textarea" :autosize="{ minRows: 3}" v-model="form.examine"></el-input>
              </div>
              <div v-else>
                <el-input type="textarea" :autosize="{ minRows: 3}" v-model="form.examine" readonly="readonly"></el-input>
              </div>
             </el-form-item>
          </el-col>
          <el-col :span="24" style="margin-top: 10px">
            <el-form-item>
              <el-button style="float: right;margin-left: 20px" @click="returnClick()">返回</el-button>
              <div v-show="isLeader">
                <el-button style="float: right;margin-left: 20px" type="danger" @click="sendBack()">退回</el-button>
                <el-tooltip class="item" effect="dark" content="继续由创建者负责该任务" placement="bottom">
                  <el-button style="float: right;margin-left: 20px" type="success" @click="doTaskRequire(0)">签发</el-button>
                </el-tooltip>
                <!--<el-tooltip class="item" effect="dark" content="指派其他人员负责该任务" placement="bottom">-->
                  <!--<el-button style="float: right;margin-left: 20px" type="primary" @click="signUp()">任务转发</el-button>-->
                <!--</el-tooltip>-->
              </div>
            </el-form-item>
          </el-col>
        </el-form>
      </el-col>
    </div>

    <search-people-dialog @determineClick="selectPersonClick" :data="selectPersonData" :defaultPersonId="selectPersonId"></search-people-dialog>

  </div>
</template>
<script>
  import SearchPeopleDialog from '../../../common/smallComponent/searchSinglePeople.vue'
  import DutyTable from '../../../common/smallComponent/dutyTable.vue'
  export default {
    name: 'viewEmergency',
    data() {
      return {
        //表单数据
        form:{
          emergencyName:'',
          beginMessage:'',
          emergencyFlag:'',
          emerSituation:'',
          emerRequire:'',
          examine:'',
          typeStr:'',
          level:'',
          timeInterval:'',
          relieveTemplate:''
        },
        dutyTableData:{},//本级值班可操作
        //参考审核数据
        selectOptions:[
          {id:'examine01',name:'同意签发',content:'经审核，同意签发该应急响应。'},
          {id:'examine02',name:'退回修改',content:'经审核，该应急响应内容有待修改，修改意见如下：'},
          {id:'examine03',name:'建议删除',content:'经审核，该应急响应必要性较低，建议删除。'}
        ],
        //暂存数据
        currentEmerId:'',
        currentPlanId:'',
        currentStatus:'',
        startPlanPublicId:'',
        taskId:'',
        doTaskLoading:'',//走流程时的缓冲图
        //身份判断
        isLeader:false,
        //------------------选择负责人的对话框-----------------------
        selectPersonData:{title:'请选择负责人',isShow:false,defaultPerson:{value:0,label:''}},
        selectPersonId:0,
      }
    },
    components : {
      'search-people-dialog' : SearchPeopleDialog,
      DutyTable
    },
    created:function () {
      if(this.$route.params.emerData){

        this.taskId=this.$route.params.emerData.taskId;
        this.currentEmerId=this.$route.params.emerData.id;
        this.editEmerForm(this.$route.params.emerData);
      }
    },
    watch:{
      $route(to, from){
        if((from.name==='emerResponseWorkflow'||from.name==='emergencyProcessWorkflow'||from.name==='taskNotice')&&this.$route.name==='viewEmergencyWorkflow'){
          if(this.$route.params.emerData){
            this.taskId=this.$route.params.emerData.taskId;
            this.currentEmerId=this.$route.params.emerData.id;
            this.editEmerForm(this.$route.params.emerData);
          }
        }
      }
    },
    methods:{
      editEmerForm:function (val) {
        this.currentPlanId=val.planId;
        this.form.emergencyName=val.name;
        this.form.beginMessage=val.startInfo;
        this.form.emergencyFlag=val.warnSignal;
        this.form.emerSituation=val.warnSituation;
        this.form.emerRequire=val.startupRequire;
        this.form.relieveTemplate=val.relieveTemplate;
        this.form.examine=val.auditOpinion;
        this.form.signerUserName=val.signerUserName;
        this.form.createUserId=val.createUserId;
        this.form.createUserName=val.createUserName;
        this.form.level=val.respLevel;
        this.form.timeInterval=val.timeInterval;
        this.form.typeStr=val.topTypeName?(val.topTypeName+' / '+val.typeName):val.typeName;
        this.startPlanPublicId=val.startPlanId?val.startPlanId:this.currentPlanId;
        this.currentStatus=Number(val.status);
        this.isLeader=false;
        if(this.currentStatus===1||this.currentStatus===5){
          if(Number(val.operateId)===this.$tool.getStorage('LOGIN_USER').userId){
            this.isLeader=true;
          }
        }
        //应急响应值班表，原来传currentEmerId，现在传startEmerId
        this.dutyTableData={planPublicId:this.startPlanPublicId,companyId:val.companyId,companyName:val.companyName};
        //值班人员列表
        this.$nextTick(function () {
          this.$refs.dutyTable.searchTable();
        })
      },
      //填写审核意见
      editExamine:function (content) {
        this.form.examine=content;
      },
      //签发
      signUp:function () {
        this.selectPersonData.defaultPerson.value=this.form.createUserId;
        this.selectPersonData.defaultPerson.label=this.form.createUserName;
        this.selectPersonId=this.form.createUserId;
        this.selectPersonData.isShow=true;
      },
      selectPersonClick:function (val) {
        if(!val){this.message.warning('负责人默认为应急响应的创建者')}
        this.selectPersonData.isShow=false;
        this.doTaskRequire(val);

      },
      doTaskRequire:function (val) {
        this.doTaskLoading=this.$loading({
          lock: true,
          text: 'Loading',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        //流程操作
        let flowParams=new URLSearchParams;
        flowParams.append("taskId",this.taskId);
        flowParams.append("comment",this.form.examine);
        flowParams.append("result",1);
        if(val){
          flowParams.append("applyUserId",val);
        }

        this.$http.post('emgFlow/doTask',flowParams).then(function (res) {
          if(res.data.success){
            this.updatePlanPublic(1);
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message.error('签发失败')
        }.bind(this));
      },
      updatePlanPublic:function (flag) {
        let currentTime=new Date();
        let params=new URLSearchParams();
        params.append("id",this.currentEmerId);
        params.append("planId",this.currentPlanId);
        params.append("startPlanId",this.startPlanPublicId);

        params.append("name",this.form.emergencyName);
        if(flag){//签发时加入签发人字段
          params.append("startInfo",this.form.beginMessage+'\n\n签发人：'+this.form.signerUserName+'  签发时间：'+this.transferTime(currentTime.getTime(),'y年m月d日'));
        }else{//退回
          params.append("startInfo",this.form.beginMessage);
        }
        params.append("warnSituation",this.form.emerSituation);
        params.append("startupRequire",this.form.emerRequire);
        params.append("auditOpinion",this.form.examine);
        params.append("relieveTemplate",this.form.relieveTemplate);
        params.append("signDate",currentTime);
        this.$http.post('planPublic/update',params).then(function (res) {
          if(res.data.success){
            this.$message({
              showClose: true,
              message: '操作成功！',
              type: 'success'
            });
            this.doTaskLoading.close();
            this.$router.push({name:'emerResponseWorkflow'});
          }else{
            this.doTaskLoading.close();
            this.$message({
              showClose: true,
              message: res.data.message,
              type: 'danger'
            });
          }
        }.bind(this)).catch(function (err) {
          this.doTaskLoading.close();
          console.log(err);
          this.$message({
            showClose: true,
            message: '数据修改失败',
            type: 'error'
          });
        }.bind(this));
      },
      //签退
      sendBack:function () {
        this.$confirm('此操作将退回该应急响应, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let flowParams=new URLSearchParams;
          flowParams.append("taskId",this.taskId);
          flowParams.append("comment",this.form.examine);
          flowParams.append("result",0);
          this.$http.post('emgFlow/doTask',flowParams).then(function (res) {
            if(res.data.success){
              this.updatePlanPublic(0);
            }
          }.bind(this)).catch(function (err) {
            console.log(err);
            this.$message.error('退回失败')
          }.bind(this));
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消退回'
          });
        });
      },

      //返回
      returnClick:function () {
        this.$refs['ruleForm'].resetFields();
        this.$router.push({name:'emerResponseWorkflow'});
      },
    }
  }
</script>
<style>
</style>
