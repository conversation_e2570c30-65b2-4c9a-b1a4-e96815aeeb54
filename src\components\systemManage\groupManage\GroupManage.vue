<template>
  <div id="groupManage" class="background-style">

    <el-tabs v-model="tabActiveName" style="margin: 0 10px;">
      <el-tab-pane label="分组管理" name="first">
        <div style="width: 100%;margin-top:-20px">
          <el-col :span="12">
            <div style="width: 96%">
              <div class="primary-background-title">分组管理</div>
              <div style="width: 100%;display: block;float: left">
                <el-input placeholder="请输入分组名" v-model="search.groupName" style="width: 300px">
                  <el-button slot="append" icon="el-icon-search" @click="searchClick"></el-button>
                </el-input>
                <el-button type="primary" plain style="float: right;display: inline-block" @click="addGroup">添加分组
                </el-button>
              </div>
              <div style="width: 100%;display: block;float: left;margin-top: 10px">
                <el-table
                  :data="groupData"
                  border
                  style="width: 100%">
                  <el-table-column
                    type="index"
                    label="编号"
                    align="center"
                    width="60">
                  </el-table-column>
                  <el-table-column
                    prop="groupname"
                    label="选项组名"
                    align="center"
                    min-width="250">
                  </el-table-column>
                  <el-table-column
                    label="操作"
                    width="270"
                    fixed="right"
                    align="center">
                    <template slot-scope="scope">
                      <el-button size="mini" @click="updateGroup( scope.row,scope.$index)">修改</el-button>
                      <el-button size="mini" @click="loadSelectedOption(scope.row)">选项编辑</el-button>
                      <el-button size="mini" type="danger" @click="deleteGroupClick(scope.$index, scope.row.id)">删除
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
              <div style="width: 100%;display: block;float: left;margin-top: 10px">
                <el-pagination
                  background
                  layout="prev, pager, next"
                  @current-change="groupCurrentPage"
                  :current-page="groupPage.pageCurrent"
                  :total="groupPage.total">
                </el-pagination>
              </div>

            </div>
          </el-col>
          <el-col :span="12">
            <div class="primary-background-title">分组选项管理</div>
            <el-form ref="form" :model="form" label-width="100px" label-position="right">
              <el-col :span="24">
                <el-form-item label="新增选项：" style="margin-bottom: 10px;">
                  <el-select
                    v-model="selectOptionIds"
                    multiple
                    filterable
                    remote
                    reserve-keyword
                    placeholder="请选择待加入项"
                    :remote-method="searchOptionGroup"
                    :loading="search.loading"
                    style="float: left;width:250px">
                    <el-option
                      v-for="item in optionGroupData"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id">
                    </el-option>
                  </el-select>
                  <el-button type="primary" plain style="margin-left: 10px;float: left"
                             @click="addGroupOption()">加入分组
                  </el-button>
                </el-form-item>
              </el-col>
              <el-col :span="24" style="margin-bottom: 10px;color: #909090">
                当前分组：{{currentGroup}}
              </el-col>
              <el-col :span="24">
                <el-table
                  :data="selectOptionGroupData"
                  border
                  style="width: 100%">
                  <el-table-column
                    prop="num"
                    label="编号"
                    align="center"
                    width="80">
                  </el-table-column>
                  <el-table-column
                    prop="optionName"
                    label="选项内容"
                    align="center"
                    width="250">
                  </el-table-column>
                  <el-table-column
                    align="center"
                    width="250"
                    label="操作">
                    <template slot-scope="scope">
                      <el-button type="text" size="mini" @click="updateOptionGroupNum(scope.row,scope.$index)">编辑
                      </el-button>
                      <el-button type="text" size="mini" style="color: rgb(245,108,108);"
                                 @click="deleteOptionGroup(scope.row.id,scope.$index)">删除
                      </el-button>

                    </template>
                  </el-table-column>
                </el-table>

              </el-col>
            </el-form>
          </el-col>
        </div>
      </el-tab-pane>
      <el-tab-pane label="选项管理" name="second">
        <div style="width: 100%;margin-top:-20px">
          <el-col :span="12">
            <div style="width: 96%">
              <div
                style="width: 100%;height: 60px;background-color: rgb(236,248,255);margin-bottom: 20px;border-radius: 5px;border-left: 5px solid #50BFFF">
                <h4 style="line-height: 60px;margin-left: 20px">选项管理</h4>
              </div>
              <div style="width: 100%;display: block;float: left">
                <el-input placeholder="请输入选项名" v-model="search.optionName" style="width: 300px">
                  <el-button slot="append" icon="el-icon-search" @click="searchOption"></el-button>
                </el-input>
                <el-button type="primary" plain
                           style="float: right;display: inline-block" @click="addOption">添加选项
                </el-button>
              </div>
              <div style="width: 100%;display: block;float: left;margin-top: 25px">
                <el-table
                  :data="optionData"
                  border
                  style="width: 100%">
                  <el-table-column
                    type="index"
                    label="编号"
                    align="center"
                    width="80">
                  </el-table-column>
                  <el-table-column
                    prop="name"
                    label="分组名"
                    align="center"
                    min-width="250">
                  </el-table-column>
                  <el-table-column
                    label="操作"
                    width="170"
                    fixed="right"
                    align="center">
                    <template slot-scope="scope">
                      <el-button size="mini" @click="updateOption( scope.row,scope.$index)">修改</el-button>
                      <el-button size="mini" type="danger" @click="optionDelete(scope.$index, scope.row.id)">删除
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
              <div style="width: 100%;display: block;float: left;margin-top: 10px">
                <el-pagination
                  background
                  layout="prev, pager, next"
                  @current-change="optionCurrentChange"
                  :current-page="optionPage.pageCurrent"
                  :total="optionPage.total">
                </el-pagination>
              </div>

            </div>
          </el-col>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
  export default {
    name: 'groupManage',
    data() {
      return {
        tabActiveName: "first",
        //----------------分组管理数据-----------------------------
        search: {
          groupName: '',
          optionGroupName: '',
          optionName: '',
          searchOptionName: '',
          groupId: 0,
          loading: false
        },
        groupPage: {
          pageCurrent: 1,
          total: 0
        },
        groupData: [],
        itemNumber: 0,

        //----------------选项管理数据-----------------------------
        currentGroup:'',
        form: {
          name: '应急预案级别'
        },
        optionPage:{
          pageCurrent:1,
          total:0,
        },
        optionData: [],
        optionGroupData: [],
        selectOptionGroupData: [],
        selectOptionIds: [],
        loading:false,
      }
    },
    mounted: function () {
      this.loadGroup(null, 1)
      this.loadOption(null,1)
    },
    methods: {
      //--------------------分组管理-----------------------------
      searchClick: function () {
        if (this.search.groupName.trim()) {
          this.loadGroup(this.search.groupName, this.groupPage.pageCurrent)
        } else {
          this.loadGroup(null, this.groupPage.pageCurrent)
        }
      },
      loadGroup: function (name, pageCurrent) {
        var params = new URLSearchParams()
        if (name) {
          params.append("groupname", name)
        }
        params.append("pageCurrent",pageCurrent)
        this.$http.post("sys/group/find", params).then(function (res) {
          //console.info(res)
          this.groupData = res.data.list
          this.groupPage.pageCurrent = res.data.pageNum
          this.groupPage.total = res.data.total
        }.bind(this))
      },
      deleteGroupClick: function (index, groupId) {
        this.$confirm('此操作将永久删除该选项组合, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        }).then(() => {
          var params = new URLSearchParams()
          params.append("id", groupId)
          this.$http.post("sys/group/delete", params).then(function (res) {
            if (res.data.success) {
              this.$message({
                type: 'success',
                message: '删除成功!'
              });
              this.groupData.splice(index, 1)
            }
          }.bind(this))

        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });
        });
      },
      updateGroup: function (row, index) {
        var groupId = row.id
        var groupName = row.groupname
        this.$prompt('请输入分组名', '编辑', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputValue: groupName
        }).then(({value}) => {
          if (value.trim()) {
            var params = new URLSearchParams()
            params.append("groupname", value)
            params.append("id", groupId)
            this.$http.post("sys/group/update", params).then(function (res) {
              if (res.data.success) {
                this.$message({
                  type: 'success',
                  message: '更新成功!'
                });
                this.groupData.splice(index, 1, res.data.data)
              } else {
                this.$message.error("更新失败" + res.data.message)
              }
            }.bind(this))
          } else {
            this.$message({
              type: 'warning',
              message: '分组名不能为空'
            });
          }
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '取消输入'
          });
        });
      },
      addGroup: function () {
        this.$prompt('请输入分组名', '编辑', {
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        }).then(({value}) => {
          if (value.trim()) {
            var params = new URLSearchParams()
            params.append("groupname", value)
            this.$http.post("sys/group/add", params).then(function (res) {
              if (res.data.success) {
                this.$message({
                  type: 'success',
                  message: '创建成功!'
                });
                this.groupData.splice(this.groupData.length, 0, res.data.data)
              }
            }.bind(this))
          } else {
            this.$message({
              type: 'warning',
              message: '分组名不能为空'
            });
          }
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '取消输入'
          });
        });
      },
      currentPage: function (val) {

      },
      groupCurrentPage: function (val) {
        if (this.search.groupName.trim()) {
          this.loadGroup(this.search.groupName, val)
        } else {
          this.loadGroup(null, val)
        }
      },
      //-------------------选项管理-------------------------------
      loadOption: function (name, pageCurrent) {
        var params = new URLSearchParams()
        if (name) {
          params.append("name", name)
        }
        params.append("pageCurrent",pageCurrent)
        this.$http.post("sys/option/find", params).then(function (res) {
          //console.info(res)
          this.optionData = res.data.list
          this.optionPage.pageCurrent=pageCurrent
          this.optionPage.total=res.data.total
        }.bind(this))
      },
      updateOption: function (row, index) {
        var optionId = row.id
        var optionName = row.name
        this.$prompt('请输入分组名', '编辑', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputValue: optionName
        }).then(({value}) => {
          if (value.trim()) {
            var params = new URLSearchParams()
            params.append("name", value)
            params.append("id", optionId)
            this.$http.post("sys/option/update", params).then(function (res) {
              if (res.data.success) {
                this.$message({
                  type: 'success',
                  message: '更新成功!'
                });
                this.optionData.splice(index, 1, res.data.data)
              } else {
                this.$message.error("更新失败" + res.data.message)
              }
            }.bind(this))
          } else {
            this.$message({
              type: 'warning',
              message: '选项名不能为空'
            });
          }
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '取消输入'
          });
        });
      },
      searchOption: function () {
        if (this.search.optionName.trim()) {
          this.loadOption(this.search.optionName,1)
        } else {
          this.loadOption(null,1)
        }
      },
      loadSelectedOption: function (group) {
        this.search.groupId = group.id
        this.currentGroup=group.groupname
        var params = new URLSearchParams()
        params.append("groupid", group.id)
        this.$http.post("sys/group/findOptionGroup", params).then(function (res) {
          //console.info(res)
          this.selectOptionGroupData = res.data.data
        }.bind(this))
      },
      searchOptionGroup: function (query) {
        if (query !== '') {
          this.loadOptionGroup(query)
        } else {
          this.loadOptionGroup(null)
        }
      },
      loadOptionGroup: function (name) {
        var params = new URLSearchParams()
        if (name) {
          params.append("name", name)
        }
        params.append("pageSize",10000)
        this.$http.post("sys/option/find", params).then(function (res) {
          //console.info(res)
          this.optionGroupData = res.data.list
        }.bind(this))
      },
      addOption: function () {
        this.$prompt('请输入选项名称', '编辑', {
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        }).then(({value}) => {
          if (value.trim()) {
            var params = new URLSearchParams()
            params.append("name", value)
            this.$http.post("sys/option/add", params).then(function (res) {
              if (res.data.success) {
                this.$message({
                  type: 'success',
                  message: '创建成功!'
                });
                this.optionData.splice(this.optionData.length, 0, res.data.data)
              }
            }.bind(this))
          } else {
            this.$message({
              type: 'warning',
              message: '选项名称不能为空'
            });
          }
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '取消输入'
          });
        });
      },
      optionDelete: function (index, optionId) {
        this.$confirm('此操作将永久删除该选项, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        }).then(() => {
          var params = new URLSearchParams()
          params.append("id", optionId)
          this.$http.post("sys/option/delete", params).then(function (res) {
            if (res.data.success) {
              this.$message({
                type: 'success',
                message: '删除成功!'
              });
              this.optionData.splice(index, 1)
            }
          }.bind(this))

        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });
        });
      },
      addGroupOption: function () {
        if (this.selectOptionIds.length > 0) {
          var params = new URLSearchParams()
          params.append("ids", this.selectOptionIds)
          params.append("id", this.search.groupId)
          this.$http.post("sys/group/addOptionGroup", params).then(function (res) {
            if (res.data.success) {
              this.selectOptionGroupData = res.data.data
            }
          }.bind(this)).catch(function (err) {
          }.bind(this))
        }
      },
      deleteOptionGroup: function (optionGroupId, index) {
        this.$confirm('此操作将永久删除该选项, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        }).then(() => {
          var params = new URLSearchParams()
          params.append("optionGroupId", optionGroupId)
          this.$http.post("sys/group/deleteOptionGroup", params).then(function (res) {
//            console.info(res)
            if (res.data.success) {
              this.selectOptionGroupData.splice(index, 1)
            }
          }.bind(this))

        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });
        });

      },
      updateOptionGroupNum: function (row, index) {
        var id = row.id
        var num = row.num
        this.$prompt('请输入序号', '编辑', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputValue: num
        }).then(({value}) => {
          if (isRealNum(value)) {
            if (value.trim()) {
              var params = new URLSearchParams()
              params.append("num", value)
              params.append("id", id)
              this.$http.post("sys/group/updateOptionGroup", params).then(function (res) {
                if (res.data.success) {
                  this.$message({
                    type: 'success',
                    message: '保存成功!',
                  });
                  this.selectOptionGroupData.splice(index, 1, res.data.data)
                }
              }.bind(this))
            } else {
              this.$message({
                type: 'warning',
                message: '分组名不能为空'
              });
            }
          } else {
            this.$message({
              type: 'warning',
              message: '填写的不是数字'
            });
          }
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '取消输入'
          });
        });

      },
      optionCurrentChange:function (val) {
        this.loadOption(this.search.optionName,val)
      }
    }
  }

  function isRealNum(val) {
    // isNaN()函数 把空串 空格 以及NUll 按照0来处理 所以先去除
    if (val === "" || val == null) {
      return false;
    }
    if (!isNaN(val)) {
      return true;
    } else {
      return false;
    }
  }
</script>
<style>
  .el-table__body tr > td {
    padding: 5px !important;
  }
</style>
