<template>
    <div id="emerHandleChoiceEvent">
      <el-row class="row">
        <el-col :span="6">事件级别为：</el-col>
        <el-col :span="3">
          <el-select
            @change="eventLevelSelectChangeHandle"
            v-model="emgEvent.eventLevel" placeholder="级别">
            <el-option
              v-for="item in select"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-col>
      </el-row>
      <el-row class="row" style="margin-top:30px;">
        <el-col :span="6">请选择预案：</el-col>
      </el-row>
      <!--表格-->
      <el-row  class="row">
        <el-table
          :data="planList.list"
          highlight-current-row
          @current-change="handleCurrentChange"
          border
          tooltip-effect="light"
          style="width: 100%;margin-top: 10px">
          <el-table-column
            prop="name"
            label="名称"
            show-overflow-tooltip
            min-width="200">
          </el-table-column>
          <el-table-column fixed="right" label="操作" align="center">
            <template slot-scope="scope">
              <el-button size="mini" type="success" @click.native="itemViewClick($event,scope.row)">查看</el-button>
             </template>
          </el-table-column>
          <template slot="empty">
            暂时没有相关预案！！点击<router-link to="/emer-menu/emer-plan">这里</router-link>添加
          </template>
        </el-table>
      </el-row>
      <!--分页-->
      <el-row  class="row">
        <el-pagination
          background
          layout="prev, pager, next"
          :current-page="planList.pageNum"
          :total="planList.total"
          :page-size="planForm.pageSize"
          @current-change="pageChangeHandle">
        </el-pagination>
      </el-row>


      <el-row class="row" style="margin-bottom:50px;">
        <el-col :offset="6" :span="12">
            <el-button type="primary" @click="nextBtnClickHandle">下一步</el-button>
            <el-button size="small" @click="$router.push({ name : 'emerHandle' });">返回首页</el-button>
        </el-col>
      </el-row>
    </div>
</template>

<script>
    export default {
      data(){
        return {
          // 通过 类别和级别 来获取预案列表
          planForm : {
            // 大类别
            topTypeId : 0,
            // 小类别
            typeId : 0,
            // 等级
            //eventLevel : 0,
            // 当前页
            pageCurrent : 1,
            // 页数大小
            pageSize : 8,
          },
          // 当前选择的级别
          eventLevel : 0,
          // 级别
          select: [
            { value : 1, label : '1级' },
            { value : 2, label : '2级' },
            { value : 3, label : '3级' },
            { value : 4, label : '4级' },
          ],
          // 预案列表
          planList : [],
        }
      },
      computed:{
        // 灾后处置--json
        emgEvent : function(){
          return this.$store.state.emerHandleModule.emgEvent;
        },
      },

      watch:{
        $route(to,from){
          if(from.name === 'emerHandleAdd'&&this.$route.name==='emerHandleChoiceEvent') {
            this.init();
          }
        },
      },
      created(){
        // 初始化
        this.init();
      },
      mounted(){
        // 初始化
        this.init();
      },
      methods:{
        // 初始化
        init(){
          this.clear();
          this.planForm.topTypeId = this.emgEvent.topTypeId;
          this.planForm.typeId = this.emgEvent.typeId;
          this.getPlanList();
        },
        // 清除数据
        clear(){
          this.eventLevel = 0;
          this.planList = [];
        },
        // 点击查看预案详情
        itemViewClick(event, row){
          // 阻止冒泡
          event.cancelBubble = true;
          this.$router.push({ name : 'emerHandleLookPlan', params : { planId : row.hId } })
        },
        // 事件级别选择处理函数
        eventLevelSelectChangeHandle(item){
          //this.planForm['eventLevel'] = item;
          this.getPlanList();
        },
        // 通过类别下的级别获取预案列表
        getPlanList(){
          this.$store.dispatch('postDisasterTreatmentListPromiseAction', this.planForm).then(function(res){
            this.planList = res;
          }.bind(this))
        },
        // 分页
        pageChangeHandle(page){
          this.planForm.pageCurrent = page;
          this.getPlanList();
        },
        // 表格数据单选
        handleCurrentChange(val) {
          if(val){
            this.emgEvent['handleId'] = val.id;
          }
        },
        // 查看按钮点击事件处理函数
        lookBtnClickHandle(id){
          this.$router.push({ name : 'emerHandleLookPlan', params : { planId : id } })
        },
        // 下一步按钮点击事件处理函数
        nextBtnClickHandle(){
          if(!this.emgEvent.handleId){
            this.$message({
              type : 'error',
              message : '请选中一条预案，再点击“下一步按钮”!!'
            })
            return;
          }
          this.$store.dispatch('emgEventAddAction', this.emgEvent).then(function(res){
            if(res.success){
              this.$message({
                type : 'success',
                message : '操作成功'
              })
              // 保存事件信息
              this.$store.commit('emgEventMutation', res.data.event);
              // 保存应急处置事件信息
              this.$store.commit('emgHandlePublicMutation', res.data.handlePublic);
              // 跳转传递 eventId
              this.$router.push({ name : 'emerHandleDealEvent', params : { status : 'add' } })
//              this.$router.push({ name : 'emerHandleDealEvent', params : { id : res.data.id, eventId : res.data.event.id } })
            } else {
              this.$message({
                type : 'error',
                message : '操作失败！！'
              })
            }
          }.bind(this));
        }
      }
    }
</script>

<style>
  #emerHandleChoiceEvent{
    background : #fff;
    padding:10px 50px;
    height:600px;
  }
  .row{
    margin-bottom : 20px;
  }
  .row .btn{
    margin-left : 40px;
  }
  .line{
    margin : 10px;
  }
</style>
