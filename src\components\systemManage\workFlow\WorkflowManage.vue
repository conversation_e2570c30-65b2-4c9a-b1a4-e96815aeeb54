<template>
  <div id="workflowManage" class="background-style">
    <div
      style="width: 80%;padding:10px 0 0 10px">
      <el-table
        v-loading="loading"
        element-loading-text="同步数据中"
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(255, 255, 255, 0.9)"
        :data="processDeploymentsData"
        highlight-current-row
        style="width: 100%;"
        stripe
        border>
        <el-table-column
          type="index"
          width="50"
          align="center"
          label-class-name="header-style">
        </el-table-column>
        <el-table-column
          prop="name"
          label="流程名字"
          width="150"
          align="center"
          label-class-name="header-style">
        </el-table-column>
        <el-table-column
          prop="version"
          label="版本"
          min-width="120"
          align="center"
          label-class-name="header-style">
        </el-table-column>

        <el-table-column
          label="操作"
          align="center"
          width="200"
          fixed="right"
          label-class-name="header-style">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="primary"
              @click="loadProcessNode(scope.row)"
            >修改</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
    export default {
        name: "workflowManage",
      data() {
        return {
          userName:'',
          processDeploymentsData:[],
          processNodeData:[],
          loading: false,
        }
      },
      created:function () {
        if(localStorage.SAFE_PLATFORM_USERNAME){
          this.userName=localStorage.SAFE_PLATFORM_USERNAME;
        }
      },
      mounted:function () {
        this.loadDeploymentProcess()
      },
      methods:{
        loadDeploymentProcess:function(){
          this.$http.get("/activiti/getDeployedProcess").then(function (res) {
            if(res.data.success){
                this.processDeploymentsData=res.data.data
            }
          }.bind(this)).catch(function (err) {
            console.log(err)
          })
        },
        loadProcessNode:function(row){
          this.$router.push({name:'processNode',params:{procdefId:row.id}})
        }
      }
    }
</script>

<style scoped>

</style>
