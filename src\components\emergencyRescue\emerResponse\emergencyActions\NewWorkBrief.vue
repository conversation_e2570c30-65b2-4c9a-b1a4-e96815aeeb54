<template>
  <div id="newWorkBrief">
    <div class="background-style">
      <el-col :span="18" :offset="3" class="primary-background-title">新增工作简报</el-col>
      <el-col :span="18" :offset="3">
        <el-form :model="form" :rules="rules" ref="ruleForm" label-width="140px" class="demo-ruleForm">
          <el-col :span="24" style="margin-top: 10px">
            <el-col :span="12">
              <el-form-item label="编制公司：" prop="company" style="margin: 0">
                {{form.company}}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="规定上报频率：" prop="interval" style="margin: 0">
                {{form.interval}}
              </el-form-item>
            </el-col>
          </el-col>
          <el-col :span="24" style="margin-bottom: 10px">
            <el-col :span="12">
              <el-form-item label="最近上报时间：" prop="lastTime" style="margin: 0">
                {{form.lastTime}}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="下次上报期限：" prop="nextTime" style="margin: 0">
                {{form.nextTime}}
              </el-form-item>
            </el-col>
          </el-col>
          <el-col :span="24">
            <!--<el-col :span="12">-->
              <!--<el-form-item label="上报日期时间：" prop="signTime">-->
                <!--<el-date-picker-->
                  <!--v-model="form.signTime"-->
                  <!--type="datetime"-->
                  <!--placeholder="选择日期时间">-->
                <!--</el-date-picker>-->
              <!--</el-form-item>-->
            <!--</el-col>-->
            <el-col :span="12">
              <el-form-item label="显示上报情况：">
                <el-button type="primary" size="medium" @click="showUpdateMessage">显示上报情况</el-button>
              </el-form-item>
            </el-col>
          </el-col>
          <el-col :span="24">
            <div style="color:rgb(99,149,208);font-weight: bold;margin-bottom: 20px;margin-left: 20px">PS：点击“显示上报情况”，即可显示各子公司的上报情况</div>
          </el-col>
          <el-col :span="24">
            <el-form-item label="现阶段防御工作部署落实情况：" prop="defenseWork">
              <el-input type="textarea" :autosize="{ minRows: 3}" placeholder="例：已启动预案，施工现场停工，停止路上作业。" v-model="form.defenseWork"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="值班人员到位情况：" prop="dutyPeople">
              <el-input type="textarea" :autosize="{ minRows: 3}" placeholder="例：已进岗到位。" v-model="form.dutyPeople"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="受损受灾情况：" prop="disasterSituation">
              <el-input type="textarea" :autosize="{ minRows: 3}" placeholder="无相应情况请填写“无”" v-model="form.disasterSituation"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="突发事件情况：" prop="emergency">
              <el-input type="textarea" :autosize="{ minRows: 3}" placeholder="无相应情况请填写“无”" v-model="form.emergency"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="事件现场应急处置及抢险救灾情况：" prop="emergencyHandle">
              <el-input type="textarea" :autosize="{ minRows: 3}" placeholder="无相应情况请填写“无”" v-model="form.emergencyHandle"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="事件应急结束善后处置情况：" prop="emergencyAfter">
              <el-input type="textarea" :autosize="{ minRows: 3}" placeholder="无相应情况请填写“无”" v-model="form.emergencyAfter"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <div style="float: right;margin: 20px ">
              <el-button type="primary" @click="signUpClick">发布</el-button>
              <el-button type="success" @click="saveClick">保存</el-button>
              <el-button @click="returnClick">返回</el-button>
            </div>
          </el-col>
        </el-form>
      </el-col>
    </div>
  </div>
</template>
<script>
  import {mapGetters} from 'vuex'
  export default {
    name: 'newWorkBrief',
    data() {
      return {
        form: {
          company:'',
          interval: '',
          lastTime: '',
          nextTime: '',

          signTime:'',
          defenseWork:'',
          dutyPeople:'',
          disasterSituation:'',
          emergency:'',
          emergencyHandle:'',
          emergencyAfter:'',
        },
        rules:{
          signTime:[{required:true,message:'请选择上报时间',trigger:'change'}],
          defenseWork:[{required:true,message:'请选择填入内容',trigger:'change'}],
          dutyPeople:[{required:true,message:'请选择填入内容',trigger:'change'}],
          disasterSituation:[{required:true,message:'请选择填入内容',trigger:'change'}],
          emergency:[{required:true,message:'请选择填入内容',trigger:'change'}],
          emergencyHandle:[{required:true,message:'请选择填入内容',trigger:'change'}],
          emergencyAfter:[{required:true,message:'请选择填入内容',trigger:'change'}],
        },
        currentEmerId:'',
      }
    },
    created:function () {
      if(this.$route.params.currentEmerId){
        this.currentEmerId=this.$route.params.currentEmerId;//这是start-plan-public-id
        this.form.company=this.$route.params.company;
        this.form.interval=this.$route.params.interval;
        this.form.lastTime=this.$route.params.lastTime;
        this.form.nextTime=this.$route.params.nextTime;
      }
    },
    watch:{
      $route(to, from){
        if(from.name==='emergencyProcess'&&this.$route.name==='newWorkBrief'){
          if(this.$route.params.currentEmerId){
            this.currentEmerId=this.$route.params.currentEmerId;//这是start-plan-public-id
            this.form.company=this.$route.params.company;
            this.form.interval=this.$route.params.interval;
            this.form.lastTime=this.$route.params.lastTime;
            this.form.nextTime=this.$route.params.nextTime;
            this.$refs['ruleForm'].resetFields();
          }
        }
      }
    },
    methods:{
      showUpdateMessage:function () {
        let params=new URLSearchParams;
        params.append("planPublicId",this.currentEmerId);
        this.$http.post('workBrief/findLowerLevelWorkBrief',params).then(function (res) {
          if(res.data.success){
            if(res.data.data){
              for(let i=0;i<res.data.data.defenseWork.length;i++){
                this.form.defenseWork+=res.data.data.defenseWork[i].companyName+'：'+res.data.data.defenseWork[i].date+'，'+this.judgeWrap(res.data.data.defenseWork[i].content);
                this.form.dutyPeople+=res.data.data.dutyPeople[i].companyName+'：'+res.data.data.dutyPeople[i].date+'，'+this.judgeWrap(res.data.data.dutyPeople[i].content);
                this.form.disasterSituation+=res.data.data.disasterSituation[i].companyName+'：'+res.data.data.disasterSituation[i].date+'，'+this.judgeWrap(res.data.data.disasterSituation[i].content);
                this.form.emergency+=res.data.data.emergency[i].companyName+'：'+res.data.data.emergency[i].date+'，'+this.judgeWrap(res.data.data.emergency[i].content);
                this.form.emergencyHandle+=res.data.data.emergencyHandle[i].companyName+'：'+res.data.data.emergencyHandle[i].date+'，'+this.judgeWrap(res.data.data.emergencyHandle[i].content);
                this.form.emergencyAfter+=res.data.data.emergencyAfter[i].companyName+'：'+res.data.data.emergencyAfter[i].date+'，'+this.judgeWrap(res.data.data.emergencyAfter[i].content);
              }
            }else{
              this.$message({
                showClose: true,
                message: '暂无上报数据',
                type: 'warning'
              });
            }
          }else{
            this.$message({
              showClose: true,
              message: '暂无上报数据',
              type: 'warning'
            });
          }
        }.bind(this)).catch(function (err) {
          console.log(err);
          this.$message({
            showClose: true,
            message:err,
            type: 'error'
          });
        }.bind(this));
      },
      judgeWrap:function (content) {
        if(content.charAt(content.length-1)==='\n'||!content.length){
          return content;
        }else {
          return content+'\n';
        }
      },
      signUpClick:function () {
        this.sendRequest(1);
      },
      saveClick:function () {
        this.sendRequest(0);
      },
      sendRequest:function (state) {
        this.$refs['ruleForm'].validate((valid) => {
          if (valid) {
            let params=new URLSearchParams;
            params.append("planPublicId",this.currentEmerId);
            params.append("defenseWork",this.form.defenseWork);
            params.append("dutyPeople",this.form.dutyPeople);
            params.append("disasterSituation",this.form.disasterSituation);
            params.append("emergency",this.form.emergency);
            params.append("emergencyHandle",this.form.emergencyHandle);
            params.append("emergencyAfter",this.form.emergencyAfter);
            params.append("reportUserId",this.$tool.getStorage('LOGIN_USER').userId);
            params.append("deptId",this.$tool.getStorage('LOGIN_USER').deptId);
            params.append("companyId",this.$tool.getStorage('LOGIN_USER').companyId);
            params.append("status",state);
            this.$http.post('workBrief/add',params).then(function (res) {
              if(res.data.success){
                this.$message({
                  showClose: true,
                  message: '操作成功',
                  type: 'success'
                });
                this.$router.push({name:'emergencyProcess'});
              }
            }.bind(this)).catch(function (err) {
              console.log(err);
              this.$message({
                showClose: true,
                message: err,
                type: 'error'
              });
            }.bind(this));
          }else {
            console.log('error submit!!');
            return false;
          }
        });
      },
      returnClick:function () {
        this.$refs['ruleForm'].resetFields();
        this.$router.go(-1);
      },
    }
  }
</script>
<style>
</style>
