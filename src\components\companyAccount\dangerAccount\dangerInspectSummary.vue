<template>
  <div id="dangerInspectSummary">
    <div class="background-style">
      <el-col :span="22" :offset="1" class="success-background-title">{{planTitle}}</el-col>
      <el-col :span="16" :offset="4">
        <el-form :model="form"  label-width="120px">
          <el-col :span="24">
            <el-col :span="12">
              <el-form-item label="文件编号：">
                {{form.num01}}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="记录编号：">
                {{form.num02}}
              </el-form-item>
            </el-col>
          </el-col>
        </el-form>
      </el-col>
      <el-col :span="22" :offset="1" style="margin-top: 10px">
        <table class="simple-table">
          <tr><td v-for="itemHead in tableHead">{{itemHead.title}}</td></tr>
          <tr v-for="(row,index) in tableContent">
            <td >{{index+1}}</td>
            <td >{{row.checkNum}}</td>
            <td >{{transferTime(row.inspectDate)}}</td>
            <td >{{row.targetDeptName}}</td>
            <td >{{row.inspectType}}</td>
            <td >{{row.leaderUserName}}</td>
          </tr>
        </table>
        <el-button style="float: right;margin: 20px" @click="returnClick">返回</el-button>
        <el-button type="primary" style="float: right;margin: 20px" @click="download">下载文件</el-button>
      </el-col>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'dangerInspectSummary',
    data() {
      return {
        planTitle:'',
        tableHead:[
          {index:'num',title:'序号'},
          {index:'checkNum',title:'检查单编号'},
          {index:'inspectDate',title:'检查日期'},
          {index:'targetDeptName',title:'受检单位'},
          {index:'inspectType',title:'检查类型'},
          {index:'leaderUserName',title:'组长'},
        ],
        dangerSafePlanId:0,
        tableContent:[],
        form:{
          num01:'',
          num02:''
        },
        year:0,
        startDate:0,
        endDate:0,
      }
    },
    created:function(){
      this.year=parseInt(this.$tool.formatDateTime(this.$route.params.year).substring(0,4));
      this.planTitle=this.year+"年隐患排查汇总表";
      this.searchTable();
    },
    watch:{
      $route(to, from){
        if(from.name==='accountDangerIndex') {
          this.year=parseInt(this.$tool.formatDateTime(this.$route.params.year).substring(0,4));
          this.planTitle=this.year+"年隐患排查汇总表";
          this.searchTable();
        }
      }
    },
    methods:{
      searchTable:function () {
        this.$http.get('/danger/inspectPublic/summary/'+this.year).then(function (res) {
          if(res.data.success){
            this.tableContent=res.data.data;
            this.tableContent.forEach(function (item,index) {
              item.num=index+1;
            })
          }else{
            console.log('danger/safePlan/summary'+'数据申请失败');
          }
        }.bind(this)).catch(function (err) {
          console.log('检查计划列表查找:'+err);
        }.bind(this));
      },
      download:function () {
        const loading = this.$loading({
          lock: true,
          text: 'Loading',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        this.$http({ // 用axios发送post请求
          method: 'get',
          url: '/report/dangerInspectPublic/' + this.year, // 请求地址
          responseType: 'blob' // 表明返回服务器返回的数据类型
        }).then((res) => { // 处理返回的文件流
          //console.info(res)
          loading.close()
          const content = res
          const elink = document.createElement('a') // 创建a标签
          elink.download = this.planTitle + ".xlsx" // 文件名
          elink.style.display = 'none'
          const blob = new Blob([res.data])
          elink.href = URL.createObjectURL(blob)
          document.body.appendChild(elink)
          elink.click() // 触发点击a标签事件
          document.body.removeChild(elink)
        })
      },
      returnClick:function () {
        this.planTitle='';
        this.tableContent=[];
        this.$router.go(-1);
      },
    }
  }
</script>
<style>
  .el-tag + .el-tag {
    margin-left: 10px;
  }
  .button-new-tag {
    margin-left: 10px;
    height: 32px;
    line-height: 30px;
    padding-top: 0;
    padding-bottom: 0;
  }
  .input-new-tag {
    width: 90px;
    margin-left: 10px;
    vertical-align: bottom;
  }
</style>
