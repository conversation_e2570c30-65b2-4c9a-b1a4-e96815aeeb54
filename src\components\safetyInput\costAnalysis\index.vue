<template>
  <div>
    <div class="background-style"  style="padding:20px">
      <!--查询类型-->
      <div>
        <el-radio-group
          @change="searchTypeChangeEventFn"
          v-model="searchTypeSelected">
          <el-radio-button :label="item.label" :key="item.label" v-for="item in searchTypeList">{{item.value}}</el-radio-button>
        </el-radio-group>
      </div>
      <!--预算跟踪-->
      <template v-if="searchTypeSelected == 1">
        <!--表格-->
        <!--搜索区-->
        <el-row>
          <el-col :span="24">
            <el-form label-width="5px">
              <el-row>
                <el-col :span="3">
                  <el-form-item >
                    <el-input clearable placeholder="项目名称" v-model="ysgz.form.title"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="3">
                  <el-form-item >
                    <el-input clearable placeholder="公司/部门" v-model="ysgz.form.deptName"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="3">
                  <el-form-item >
                    <el-select
                      v-model="ysgz.form.budgetType" clearable placeholder="预算类型">
                      <el-option
                        v-for="item in ysgz.otherData.typeList"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item >
                    <el-date-picker
                      v-model="ysgz.form.year"
                      type="year"
                      placeholder="选择年">
                    </el-date-picker>
                  </el-form-item>
                </el-col>

                <el-col :offset="1" :span="2" >
                  <el-button style="margin-bottom:0px;" type="primary" @click="searchBtnClickHandle">搜索</el-button>
                </el-col>
              </el-row>
            </el-form>
          </el-col>
        </el-row>
        <!--表格区-->
        <el-row>
          <el-col :span="24">
            <el-table
              border
              @row-click="rowClickEventFn"
              :data="ysgz.tableData.list"
              style="width: 100%">
              <el-table-column
                type="index"
                label="编号"
                width="100"
                align="center"
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="title"
                label="名称"
                show-overflow-tooltip
                min-width="150"
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="deptName"
                label="公司"
                show-overflow-tooltip
                width="200"
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="budgetType"
                label="预算类型"
                width="150"
                label-class-name="header-style">
                <template slot-scope="scope">
                  <template v-if="scope.row.budgetType == 1">月度预算</template>
                  <template v-if="scope.row.budgetType == 2">季度预算</template>
                  <template v-if="scope.row.budgetType == 3">年度预算</template>
                </template>
              </el-table-column>
              <el-table-column
                prop="year"
                :formatter="formatDateTime"
                label="年份"
                width="150"
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                label="季/月"
                width="150"
                prop="budgetNo"
                label-class-name="header-style">
                <template slot-scope="scope">
                  <template v-if="scope.row.budgetType == 1">
                    {{scope.row.budgetNo}}月
                  </template>
                  <template v-if="scope.row.budgetType == 2">
                    第{{scope.row.budgetNo}}季度
                  </template>
                </template>
              </el-table-column>
              <el-table-column
                prop="totalBudget"
                label="预算总额（元）"
                width="150"
                label-class-name="header-style">
              </el-table-column>
            </el-table>
          </el-col>
          <el-col :span="24">
            <el-pagination
              background
              layout="prev, pager, next"
              :current-page="ysgz.tableData.pageNum"
              :page-size="ysgz.form.pageSize"
              :total="ysgz.tableData.total"
              @current-change ="disasterPageChangeHandle">
            </el-pagination>
          </el-col>
        </el-row>
        <!--明细-->
        <el-row style="margin-top: 20px">
          <el-col :span="12" style="padding: 10px">
            <el-col :span="24" style="border-bottom: 1px solid #d3d4d6;padding-bottom: 5px">
              <div style="border-left: 5px solid #0168B7;color: #0168B7">&nbsp&nbsp分项明细表</div>
            </el-col>
            <el-col :span="24" style="margin-top: 10px">
              <el-table
                border
                highlight-current-row
                @current-change="loadDetailTable"
                :data="ysgz.detail.form.costBudgetItems">
                <el-table-column
                  type="index"
                  width="100"
                  align="center"
                  label-class-name="header-style">
                </el-table-column>
                <el-table-column
                  prop="item"
                  label="费用大类"
                  min-width="240"
                  show-overflow-tooltip
                  align="left"
                  label-class-name="header-style">
                </el-table-column>
                <el-table-column
                  prop="itemTotalBudget"
                  label="金额"
                  width="100"
                  align="center"
                  label-class-name="header-style">
                </el-table-column>
              </el-table>
            </el-col>
          </el-col>
          <el-col :span="12" style="padding: 10px">
            <el-col :span="24" style="border-bottom: 1px solid #d3d4d6;padding-bottom: 5px">
              <div style="border-left: 5px solid #0168B7;color: #0168B7">&nbsp&nbsp子项目明细表</div>
            </el-col>
            <el-col :span="24" style="margin: 10px 0 10px 0">
              <el-table
                ref="tableTwo"
                height="250px"
                v-if="ysgz.detail.selectedIndex.type > -1"
                border
                :data="ysgz.detail.form.costBudgetItems[this.ysgz.detail.selectedIndex.type]['costBudgetSubItems']||[]">
                <el-table-column
                  label-class-name="header-style"
                  type="selection"
                  width="55">
                </el-table-column>
                <el-table-column
                  type="index"
                  width="40"
                  align="center"
                  label-class-name="header-style">
                </el-table-column>
                <el-table-column
                  prop="item"
                  label="使用范围"
                  min-width="200"
                  show-overflow-tooltip
                  align="left"
                  label-class-name="header-style">
                </el-table-column>
                <el-table-column
                  prop="itemBudget"
                  label="金额"
                  min-width="100"
                  align="center"
                  label-class-name="header-style">
                </el-table-column>

              </el-table>
            </el-col>
          </el-col>
        </el-row>
      </template>
      <!--费用投入统计-->
      <template v-if="searchTypeSelected == 2">
        <el-row style="margin:20px 0;">
          <el-col :span="6">
            <el-date-picker
              v-model="costInput.form.startDate"
              type="date"
              placeholder="开始时间">
            </el-date-picker>
          </el-col>
          <el-col :span="6">
            <el-date-picker
              v-model="costInput.form.endDate"
              type="date"
              placeholder="结束时间">
            </el-date-picker>
          </el-col>
          <el-col :span="6">
            <el-button style="margin-bottom:0px;" type="primary" @click="costInputSearchBtnClickHandle">搜索</el-button>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-table
              border
              :data="costInput.tableData.list"
              style="width: 100%">
              <el-table-column
                type="index"
                label="编号"
                width="100"
                align="center"
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="item"
                label="项目"
                min-width="150"
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="accountTime"
                :formatter="formatDateTime"
                label="发生时间"
                width="150"
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="accountNum"
                label="费用金额（元）"
                width="150"
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="department"
                label="经办部门"
                width="150"
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="remark"
                label="备注"
                width="150"
                label-class-name="header-style">
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>
      </template>
      <!--月度费用投入情况分析-->
      <template v-if="searchTypeSelected == 3">
        <el-row style="margin:20px 0;">
          <el-col :span="6">
            <el-date-picker
              @change="yearChange"
              v-model="monthInput.form.startDate"
              type="year"
              placeholder="年">
            </el-date-picker>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">

            <el-table
              border
              show-summary
              :data="monthInput.tableData"
              style="width: 100%">
              <el-table-column
                type="index"
                label="编号"
                width="100"
                align="center"
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="month"
                label="月份"
                min-width="100"
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="num"
                label="金额"
                width="150"
                label-class-name="header-style">
              </el-table-column>
            </el-table>
          </el-col>
          <el-col :span="12">
            <div id="trendChart" style="width: 600px;height:400px;"></div>
          </el-col>
        </el-row>
      </template>
      <!--预算项目一级分类投入情况分析-->
      <template v-if="searchTypeSelected == 4">
        <el-row style="margin:20px 0;">
          <el-col :span="6">
            <el-date-picker
              @change="nineTypeYearChange"
              v-model="nineType.form.year"
              type="year"
              placeholder="年">
            </el-date-picker>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="10">
            <el-table
              border
              show-summary
              :data="nineType.tableData"
              style="width: 100%">
              <el-table-column
                type="index"
                label="编号"
                width="50"
                align="center"
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="item"
                label="预算项目一级"
                min-width="300"
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="accountSum"
                label="金额"
                width="100"
                label-class-name="header-style">
              </el-table-column>
            </el-table>
          </el-col>
          <el-col :span="14">
            <div id="nineTypeTrendChart" style="width: 600px;height:400px;"></div>
          </el-col>
        </el-row>
      </template>
      <!--各单位预算执行情况比较-->
      <template v-if="searchTypeSelected == 5">
        <el-row style="margin:20px 0;">
          <el-col :span="6">
            <el-date-picker
              @change="companyYearChange"
              v-model="company.form.year"
              type="year"
              placeholder="年">
            </el-date-picker>
          </el-col>
        </el-row>
        <el-row style="margin-top:10px;">
          <el-col :span="12">
            <el-table
              border
              show-summary
              :data="company.tableData"
              style="width: 100%">
              <el-table-column
                type="index"
                label="编号"
                width="50"
                align="center"
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="deptName"
                label="公司"
                min-width="300"
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="actualFee"
                label="预算执行"
                width="100"
                label-class-name="header-style">
              </el-table-column>
            </el-table>


          </el-col>
          <el-col :span="12">
            <div id="companyScore" style="margin-left:80px; width: 700px;height:400px;"></div>
          </el-col>
        </el-row>
      </template>
    </div>
  </div>
</template>

<script>
    export default {
//      components: { VeLine },
      data(){
        return {
          // 选中的搜索类型
          searchTypeSelected : '1',
          // 搜索类型列表
          searchTypeList : [
            { label : 1, value : '预算跟踪'},
            { label : 2, value : '费用投入统计'},
            { label : 3, value : '月度费用投入情况分析'},
            { label : 4, value : '预算项目一级分类投入情况分析'},
            { label : 5, value : '各单位预算执行情况比较'},
          ],
          // 预算跟踪
          ysgz:{
            // 搜索条件
            form : {
              // 项目
              title : '',
              // 部门
              deptName : '',
              // 类型
              budgetType : "",
              // 年份
              year : '',
              // 当前页
              pageCurrent : 1,
              // 页数大小
              pageSize : 10,
            },
            // 表格
            tableData : {},
            // 额外数据
            otherData : {
              // 预算类型
              typeList: [
                {value: '1', label: '月度预算'},
                {value: '2', label: '季度预算'},
                {value: '3', label: '年度预算'},
              ],
            },
            // 明细
            detail : {
              // 搜索条件
              form: {
                id: '',
                cancel: 0,
                // 大类---子项---月份信息
                costBudgetItems: [],
                // 每月统计情况
                costBudgetMonth: {},
              },
              // 当前选中表格的index
              selectedIndex: {
                // 总类别
                type: -1,
                // 明细
                detail: -1,
              },
              // 当前选中的项目，包括总类别和明细
              selectedItem: {
                // 总类别
                type: '',
                // 明细
                detail: '',
              },
            }
          },
          // 费用投入统计
          costInput : {
            form : {
              startDate : '',
              endDate : '',
              pageCurrent : 1,
              // 页数大小
              pageSize : 10,
            },
            tableData : {},
          },
          // 月度费用投入情况分析
          monthInput : {
            form : {
              startDate : '',
            },
            tableData : [],
          },
          // 预算项目一级分类投入情况分析------九大类
          nineType : {
            form : {
              year : '',
              budgetType : '3',
              deptId : this.$tool.getLoginer().companyId,
            },
            tableData : [],
          },
          // 各单位预算执行情况比较-
          company : {
            form : {
              year : '',
              budgetType : '3',
              deptId : this.$tool.getLoginer().companyId,
            },
            tableData : [],
          },

        }
      },
      watch:{
        $route(to,from){
          if(to.name == 'costAnalysisIndex') {
            this.init();
          }
        }
      },
      mounted(){
        this.init();
      },
      methods:{ // 当前页
        clear(){

        },
        // 搜索类型change事件
        searchTypeChangeEventFn(e){
          // 清空数据
//          this.clear();
          // test label value
          switch(e){
            case 1:
              this.searchBtnClickHandle();
              break;
            case 2:
              this.costInputSearchBtnClickHandle();
              break;
            case 3:
              this.monthInputSearchBtnClickHandle();
              break;
            case 4:
              this.nineTypeSearchBtnClickHandle();
              break;
            case 5:
              this.companySearchBtnClickHandle();
              break;
          }
        },
        // 获取预算跟踪列表
        init(){
          // 费用投入统计--默认开始时间--结束时间---当年的1月1号到12月31号
          let sdate = new Date();
          sdate.setDate(1);
          sdate.setMonth(0);
          this.costInput.form.startDate = sdate;
          let edate = new Date();
          edate.setDate(31);
          edate.setMonth(11);
          this.costInput.form.endDate = edate;
          // 月度费用投入情况分析--默认开始时间--结束时间---当年的1月1号到12月31号
          this.monthInput.form.startDate = sdate;
          // 预算项目一级分类投入情况分析---默认开始时间--结束时间---当年的1月1号到12月31号
          this.nineType.form.year = sdate;
          // 各单位预算执行情况比较
          this.company.form.year = sdate;
          // 搜索
          this.searchBtnClickHandle();
        },
        // #####################预算跟踪#####################
        // 预算跟踪--------------分页
        disasterPageChangeHandle(page){
          this.ysgz.form.pageCurrent = page;
          this.searchBtnClickHandle();
        },
        // 预算跟踪------------搜索按钮
        searchBtnClickHandle(){
          // costBudgetPlanListFind
          // this.ysgz.tableData = {};
          this.$store.dispatch('costBudgetPlanAllSubCompany', this.ysgz.form).then(function(res){
            if(res.success){
              this.ysgz.tableData = res.data;
//              console.log(111,this.ysgz.tableData)
            } else {
              this.$message({
                type : 'error',
                message : res.message || '错误'
              })
            }
          }.bind(this));
        },
        // 预算跟踪--------------格式化时间
        formatDateTime(row, column, cellValue){
          let pro = column.property;
          let num = 10;
          // 年份4位 1999
          if(pro === 'year') num = 4;
          let str = this.$tool.formatDateTime(row[pro] || 0);
          return str ? str.substring(0, num) : str;
        },
        // 预算跟踪-------单行点击
        rowClickEventFn(row, event, column){
          this.$store.dispatch('costBudgetPlanFindDetail', {
            id : row.id
          }).then(function(res){
            if(res.success){
              let list = res.data.list[0];
              this.ysgz.detail.form.costBudgetItems = list.costBudgetItems;
            } else {
              this.$message({
                type : 'error',
                message : res.message || '错误'
              })
            }
          }.bind(this));

        },
        // 预算跟踪-------九大类
        // 加载类别明细表格
        loadDetailTable(row){
          if(row){
            let detail = this.ysgz.detail;
            detail.selectedItem.type = row;
            let typeList = detail.form.costBudgetItems;
            for(var i = 0; i < typeList.length; i++){
              if(typeList[i].id === row.id){
                detail.selectedIndex.type = i;
                break;
              }
            }

            //刘杰1120 增加 将当前选择加入缓存，刷新本页后记住所选项
            window.sessionStorage.setItem('costBudgetItemTableOneCurrent',JSON.stringify(detail.selectedIndex.type));
            //window.console.log('大类选项为：'+ window.sessionStorage.getItem('costBudgetItemTableOneCurrent'));

            // 还原
            detail.selectedItem.detail = "";
            detail.selectedIndex.detail = -1;
          }
        },
        // #################费用投入统计#########################
        // 费用投入统计---------查询表格
        // 搜索按钮
        costInputSearchBtnClickHandle(){
          this.$store.dispatch('costAccountRecordFindByTimeCompany', this.costInput.form).then(function(res){
            if(res.success){
              this.costInput.tableData = res.data;
            } else {
              this.$message({
                type : 'error',
                message : res.message || '错误'
              })
            }
          }.bind(this));
        },
        // #################月度费用投入情况分析#########################
        // 月度费用投入情况分析---------查询表格
        monthInputSearchBtnClickHandle(){
          if(this.monthInput.form.startDate == null || this.monthInput.form.startDate == '') return
          let arr = [];
          this.$store.dispatch('costAccountRecordMonthSumRecord', this.monthInput.form).then(function(res){
            if(res.success){
              // 自动生成12个月份的数据
              for(let i = 1; i <= 12; i++){
                arr.push({
                  month : i + "月",
                  num : i
                })
              }
              res.data.forEach(function(it){
                for (let j in it){
                  console.log('j == ', j)
                  for(let i = 1; i <= arr.length; i++) {
                    if (i == j) {
                      arr[i - 1].num = it[j];
                    }
                  }
                }
              }.bind(this))
              this.monthInput.tableData = arr;
              let trendChartData = this.echarts.init(document.getElementById('trendChart'));
              let allMonth = [];
              arr.forEach(function(it){
                allMonth.push(it.num)
              })
              trendChartData.setOption({
                xAxis: {
                  type: 'category',
                  data: ['一月', '二月', '三月', '四月', '五月', '六月', '七月','八月', '九月', '十月', '十一月','十二月']
                },
                yAxis: {
                  name:'金额',
                  type: 'value'
                },
                series: [{
                  data: allMonth,
                  type: 'line'
                }]
              });
            } else {
              this.$message({
                type : 'error',
                message : res.message || '错误'
              })
            }
          }.bind(this));
        },
        // 月度费用投入情况分析---------年发生改变时间----查询
        yearChange(e){
          this.monthInput.form.startDate = e;
          this.monthInputSearchBtnClickHandle();
        },
        // #################月度费用投入情况分析#########################
        // 预算项目一级分类投入情况分析---------查询表格
        nineTypeSearchBtnClickHandle(){
          if(this.nineType.form.year == null || this.nineType.form.year == '') return
          let arr = [];
          this.$store.dispatch('costBudgetItemFindByYearTypeNo', this.nineType.form).then(function(res){
            if(res.success){
              let list = res.data.list;
              list.forEach(function(it){
                arr.push({
                  item : it.item,
                  accountSum : it.accountSum
                })
              }.bind(this))
              this.nineType.tableData = arr;

              let trendChartData = this.echarts.init(document.getElementById('nineTypeTrendChart'));
              trendChartData.setOption({
                title : {
                  text: '预算项目一级分类投入情况分析',
//                  subtext: '纯属虚构',
                  x:'center'
                },
                tooltip : {
                  trigger: 'item',
                  formatter: "{a} <br/>{b} : {c} ({d}%)"
                },
                legend: {
                  orient: 'vertical',
                  left: 'left',
                  data: arr.map(function(it,index){
//                    return "第" + index + "大类"
                    return (index + 1) + "、" + it.item.substring(0,6)
                  })
                },
                series : [
                  {
                    name: '一级分类投入情况',
                    type: 'pie',
                    radius : '55%',
                    center: ['60%', '60%'],
                    data : arr.map(function(it, index){
                      return {
                        value : it.accountSum,
//                        name : it.item
                        name : (index + 1) + "、" + it.item.substring(0,6)
//                        name : "第" + (index + 1) + "大类：（" + it.item + "）"
                      }
                    }),
                    /*data:[
                      {value:335, name:'直接访问'},
                      {value:310, name:'邮件营销'},
                      {value:234, name:'联盟广告'},
                      {value:135, name:'视频广告'},
                      {value:1548, name:'搜索引擎'}
                    ],*/
                    itemStyle: {
                      emphasis: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                      }
                    }
                  }
                ]
              });
            } else {
              this.nineType.tableData =[];
              this.$message({
                type : 'error',
                message : res.message || '错误'
              })
            }
          }.bind(this));
        },
        // 预算项目一级分类投入情况分析---------年发生改变时间----查询
        nineTypeYearChange(e){
          this.nineType.form.year = e;
          this.nineTypeSearchBtnClickHandle();
        },
        // 各单位预算执行情况比较---------查询表格
        companySearchBtnClickHandle(){
          if(this.company.form.year == null || this.company.form.year == '') return
          let arr = [];
          var colors = ['rgba(251,210,73,0.7)'];
          this.$store.dispatch('costBudgetPlanFindTrackSubcompanyAna', this.company.form).then(function(res){
            if(res.success){
              arr = res.data.map(function(it){
                return {
                  // 公司名称
                  deptName : it.deptName || '',
                  // 预算执行
                  actualFee : it.costAccountRegs && it.costAccountRegs[0] && it.costAccountRegs[0].totalAccount || 0,
                }
              }.bind(this))
              this.company.tableData = arr;

              let departmentData = this.echarts.init(document.getElementById('companyScore'));
              departmentData.setOption({
                tooltip: {
                  trigger: 'axis',
                  axisPointer: {
                    type: 'none'
                  },
                  formatter: function (params) {
                    return params[0].name + ': ' + params[0].value;
                  }
                },
                xAxis: {
//                  data: ['驯鹿', '火箭', '飞机', '高铁', '轮船', '汽车', '跑步', '步行', ],
                  data: arr.map(function(it){
                    return it.deptName
                  })
                },
                yAxis: {
                  name:'预算执行'
                },
                color: ['#e54035'],
                series: [{
                  name: 'hill',
                  type: 'pictorialBar',
                  barCategoryGap: '-130%',
                  symbol: 'path://M0,10 L10,10 C5.5,10 5.5,5 5,0 C4.5,5 4.5,10 0,10 z',
                  itemStyle: {
                    normal: {
                      opacity: 0.5
                    },
                    emphasis: {
                      opacity: 1
                    }
                  },
//                  data: [123, 60, 25, 18, 12, 9, 2, 1],
                  data: arr.map(function(it){
                    return it.actualFee || 0
                  }),
                  z: 10
                }]
              });
            } else {
              this.company.tableData = arr;
              this.$message({
                type : 'error',
                message : res.message || '错误'
              })
            }
          }.bind(this));
        },
        // 各单位预算执行情况比较---------年发生改变时间----查询
        companyYearChange(e){
          this.company.form.year = e;
          this.companySearchBtnClickHandle();
        },

      }
    }
</script>

<style>

</style>
