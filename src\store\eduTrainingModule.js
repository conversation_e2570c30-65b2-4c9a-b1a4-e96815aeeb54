/*
  created by m<PERSON><PERSON><PERSON> on 2018-4-12
  common data in hide danger
*/
import axios from 'axios'
import http from '@/assets/functions/axiosServer'
// 应急救援
export default {
  state: {
    // 知识点标签列表
    eduUserFind : [],
    // 文章管理，文章的内容
    articleData : {},

  },
  getters: {},
  mutations: {
    // 获取知识点标签列表
    eduUserFindMut(state, list){
      state.eduUserFind = list;
    },
    articleDataMut (state, list) {
      state.articleData = list
    }
  },
  actions : {
    /*
    * 用户列表
    * */
    // 获取用户标签列表
    eduUserFindAct({ commit }, params){
      http.post('user/find', params).then(function (res) {
        commit('eduUserFindMut',res.data.data.list || [])
      }.bind(this));
    },
    /*
    * 需求调研
    * 安委会/人力资源
    * */
    // 保存
    eduReqInvSave({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduReqInv/save', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 重启---将状态 已完成--->进行中
    eduReqInvRestart({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduReqInv/restart', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 提交
    eduReqInvAddOrUpdate({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduReqInv/addOrUpdate', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 查找----员工查找
    eduReqInvFind({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduReqInv/find', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 查找----经理查找 POST /eduReqInv/managerFind
    eduReqInvManagerFind({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduReqInv/managerFind', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 查找----经理查找 POST /eduReqInv/managerFind
    eduReqInvManagerFindSimple({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduReqInv/findSimple', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 查找----经理查找 POST /eduReqInv/managerFind
    eduReqInvManagerFindDetail({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduReqInv/managerFindDetail', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 获取项目模板
    eduReqTempletFind({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduReqTemplet/find', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 查看下级需求调查 POST /eduReqInv/findSubcompany
    eduReqInvFindSubcompany({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduReqInv/findSubcompany', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 删除
    eduReqInvDelete({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduReqInv/delete', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 查看选择人数
    eduReqUserRltShowItemRes({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduReqUserRlt/showItemRes', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 目前参与调查总人数 POST /eduReqInv/totalRespondent
    eduReqInvTotalRespondent({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduReqInv/totalRespondent', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    /*
     * 需求调研
     * 员工
     * */
    // 查找
    eduReqUserRltFind({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduReqUserRlt/find', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 需求建议
    eduReqUserRltShowAdviceRes({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduReqUserRlt/showAdviceRes', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 评价
    eduReqUserRltAddOrUpdate({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduReqUserRlt/addOrUpdate', params).then(function (res) {
          resolve(res.data);
        });
      })
    },


    /*
     * 培训计划
     * */
    // 计划添加
    eduPlanAddOrUpdate({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduPlan/addOrUpdate', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 计划查找
    eduPlanFind({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduPlan/find', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 查看子公司的培训计划 POST /eduPlan/findSubcompany
    eduPlanFindSubcompany({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduPlan/findSubcompany', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 计划删除
    eduPlanDelete({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduPlan/delete', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 计划项目添加修改
    eduPlanItemAddOrUpdate({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduPlanItem/addOrUpdate', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 计划项目删除
    eduPlanItemDelete({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduPlanItem/delete', params).then(function (res) {
          resolve(res.data);
        });
      })
    },

    /*
     * 持证培训
     * */
    // 添加
    eduCertAddOrUpdate({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduCert/addOrUpdate', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 查找
    eduCertFind({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduCert/find', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 删除
    eduCertDelete({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduCert/delete', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 批量复训
    eduCertBatchRetrain({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduCert/batchRetrain', params).then(function (res) {
          resolve(res.data);
        });
      })
    },

    // POST /eduCertType/addOrUpdate
    // 添加----证书
    eduCertTypeAddOrUpdate({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduCertType/addOrUpdate', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 查找----证书
    eduCertTypeFind({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduCertType/find', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 查找----证书
    eduCertTypeDelete({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduCertType/delete', params).then(function (res) {
          resolve(res.data);
        });
      })
    },



    // POST /eduCertIssueDept/addOrUpdate
    // 添加----发证单位
    eduCertIssueDeptAddOrUpdate({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduCertIssueDept/addOrUpdate', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 查找----发证单位
    eduCertIssueDeptFind({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduCertIssueDept/find', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 删除----发证单位
    eduCertIssueDeptDelete({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduCertIssueDept/delete', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    /*
     * 转岗培训
     * */
    // 添加
    eduReassignAddOrUpdate({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduReassign/addOrUpdate', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 查找
    eduReassignFind ({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduReassign/find', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 删除
    eduReassignDelete ({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduReassign/delete', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    /*
     * 三级培训----组织者
     * */
    // 根据公司ID获取子公司列表
    deptFind({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('dept/find', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 查找---组织者
    eduEntryTrainingCompFindDetail({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduEntryTraining/compFindDetail', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 查找---非组织者
    eduEntryTrainingFind({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduEntryTraining/find', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 批量添加
    eduEntryTrainingAdd({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduEntryTraining/add', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 单条修改
    eduEntryTrainingAddAndUpdateOne({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduEntryTraining/addAndUpdateOne', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 批量打分
    eduEntryTrainingBatchInputScores({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduEntryTraining/batchInputScores', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 删除
    eduEntryTrainingDelete({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduEntryTraining/delete', params).then(function (res) {
          resolve(res.data);
        });
      })
    },



    /*
     * 日常培训----发布培训主表
     * */
    // 信息表---添加
    eduDailyInfoAddOrUpdate({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduDailyInfo/addOrUpdate', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 信息表---员工查找
    eduDailyInfoFind ({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduDailyInfo/find', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 信息表---员工查找
    eduDailyInfoShow ({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduDailyInfo/show', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 信息表---经理查找
    eduDailyInfoManagerFind({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduDailyInfo/managerFind', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 日常培训---经理查找
    eduDailyInfoManagerFindSimple({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduDailyInfo/managerFindSimple', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 日常培训---员工查找
    eduDailyInfoStaffFindSimple({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduDailyInfo/staffFindSimple', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 信息表---删除
    eduDailyInfoDelete ({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduDailyInfo/delete', params).then(function (res) {
          resolve(res.data);
        });
      })
    },


    /*
     * 日常培训----通知
     * */
    // 通知---添加
    eduDailyNotifyAddOrUpdate({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduDailyNotify/addOrUpdate', params).then(function (res) {
          resolve(res.data);
        });
      })
    },


    /*
     * 日常培训----执行记录表
     * */
    // 信息表---添加
    eduDailyRecordAddOrUpdate({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduDailyRecord/addOrUpdate', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 信息表---查找
    eduDailyRecordFind ({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduDailyRecord/find', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 信息表---删除
    eduDailyRecordDelete ({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduDailyRecord/delete', params).then(function (res) {
          resolve(res.data);
        });
      })
    },


    /*
     * 日常培训----考核表
     * */
    // 考核表---添加
    eduDailyExamineListAddOrUpdate({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduDailyExamineList/addOrUpdate', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 考核表---查找
    eduDailyExamineListFind({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduDailyExamineList/find', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 考核表---删除
    eduDailyExamineListDelete({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduDailyExamineList/delete', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 考核表---打分
    eduDailyExamineListBatchInputScores({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduDailyExamineList/batchInputScores', params).then(function (res) {
          resolve(res.data);
        });
      })
    },

    /*
     * 日常培训----用户评价
     * */
    // 添加
    eduDailyUserEvaluateAddOrUpdate({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduDailyUserEvaluate/addOrUpdate', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 查找
    eduDailyUserEvaluateFind({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduDailyUserEvaluate/find', params).then(function (res) {
          resolve(res.data);
        });
      })
    },

    /*
     * 系统管理
     * */
    // 部门节点树
    deptGetOrgDept({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('dept/getOrgDept', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 根据部门id，获取员工列表
    userFind({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('user/find', params).then(function (res) {
          resolve(res.data);
        });
      })
    },


    /*
    * 安全教育
    * */


    // 文章管理-完成文章后，新增积分记录
    eduUserStudyAddUserNews({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduUserStudy/addUserNews', params).then(function (res) {
          resolve(res.data);
        });
      })
    },

    // 文章管理-查找
    eduNewsFind({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduNews/find', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 文章管理-查找
    eduNewsFindById({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduNews/findById', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 文章管理-编辑
    eduNewsAddOrUpdate({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduNews/addOrUpdate', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 文章管理-删除
    eduNewsDelete({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduNews/delete', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    articleStudyConditionFind({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduUserStudy/getArticleStudyCondition', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    videoStudyConditionFind({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduUserStudy/getVideoStudyCondition', params).then(function (res) {
          resolve(res.data);
        });
      })
    },





    // 视频管理-加积分
    eduUserStudyAddUserCourse({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduUserStudy/addUserCourse', params).then(function (res) {
          resolve(res.data);
        });
      })
    },

    // 视频管理-查找
    eduCourseFind({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduCourse/find', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 视频管理-查找详情
    eduCourseFindById({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduCourse/findById', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 视频管理-编辑
    eduCourseAddOrUpdate({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduCourse/addOrUpdate', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 视频管理-删除
    eduCourseDelete({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduCourse/delete', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 视频管理--上传进度
    sysOssGetPercent({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('/sys/oss/getPercent', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 视频管理-异步上传--上传进度--清除
    sysOssResetPercent({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('sys/oss/resetPercent', params).then(function (res) {
          resolve(res.data);
        });
      })
    },


    // 积分排行版-查找
    eduUserStudyGetUserRankScore({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduUserStudy/getUserRankScore', params).then(function (res) {
          resolve(res.data);
        });
      })
    },

    // 用户积分
    eduUserStudyGetUserStudyLogs({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduUserStudy/getUserStudyLogs', params).then(function (res) {
          resolve(res.data);
        });
      })
    },

  // 我的学习---文章---未学习
    eduUserStudyGetUnStudyNewsList({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduUserStudy/getUnStudyNewsList', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 我的学习---文章---已学习
    eduUserStudyGetCompleteStudyNewsList({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduUserStudy/getCompleteStudyNewsList', params).then(function (res) {
          resolve(res.data);
        });
      })
    },



    // 我的学习---视频---未完成
    eduUserStudyGetUnStudyCourseList({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduUserStudy/getUnStudyCourseList', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 我的学习---视频---学习中
    eduUserStudyGetPendingStudyCourseList({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduUserStudy/getPendingStudyCourseList', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 我的学习---视频---已学习
    eduUserStudyGetCompleteStudyCourseList({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduUserStudy/getCompleteStudyCourseList', params).then(function (res) {
          resolve(res.data);
        });
      })
    },

    // 首页--进度
    eduUserStudyGetUserStudyProgress({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduUserStudy/getUserStudyProgress', params).then(function (res) {
          resolve(res.data);
        });
      })
    },


    // 知识库---新增
    safeEduMediumLibraryAddMedium({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduMediumLibrary/addMedium', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 知识库---列表
    safeEduMediumLibraryQueryMedium({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduMediumLibrary/queryMedium', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 知识库---删除
    safeEduMediumLibraryDeleteMedium({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduMediumLibrary/deleteMedium', params).then(function (res) {
          resolve(res.data);
        });
      })
    },

    // 知识库---是否共享
    eduMediumLibraryUpdateShareStatus({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduMediumLibrary/updateShareStatus', params).then(function (res) {
          resolve(res.data);
        });
      })
    },
    // 知识库---查看详情
    eduMediumLibraryGetMediumDetail({ commit }, params){
      return new Promise(function(resolve, project){
        http.post('eduMediumLibrary/getMediumDetail', params).then(function (res) {
          resolve(res.data);
        });
      })
    },



  }
};
