<template>
  <div id="emerMaterial">

    <div class="background-style">
      <el-tabs v-model="activeTabName" style="padding: 10px 10px 0 10px"  type="card" @tab-click="handleTabClick">
        <el-tab-pane name="goodsStatics">
          <span slot="label">物资统计</span>
          <el-col :span="12" style="padding: 0 10px 0 0;margin-top: -10px">
            <el-col :span="24" class="primary-background-title" style="margin-top: 10px;">
              物资分类统计
            </el-col>
            <el-col :span="24">
              <el-table
                :data="staticsData"
                border
                highlight-current-row
                style="width: 100%">
                <el-table-column
                  type="index"
                  width="70"
                  align="center"
                  label-class-name="header-style">
                </el-table-column>
                <el-table-column
                  prop="goodsName"
                  label="物资种类"
                  width="180"
                  align="center"
                  label-class-name="header-style">
                </el-table-column>
                <el-table-column
                  prop="count"
                  label="物资总数"
                  min-width="150"
                  align="center"
                  label-class-name="header-style">
                </el-table-column>
                <el-table-column
                  label="分布情况"
                  fixed="right"
                  width="120"
                  align="center"
                  label-class-name="header-style">
                  <template slot-scope="scope">
                    <el-button type="primary" size="mini" @click="showSingleGoodsDetail(scope.row)">分布情况</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-col>
          <el-col :span="12" style="padding: 0 0 0 10px;margin-top: -10px">
            <el-col :span="24" class="success-background-title" style="margin-top: 10px">
              物资分布情况
            </el-col>
            <el-col :span="24">
              <el-table
                :data="singleGoodsData"
                border
                highlight-current-row
                style="width: 100%">
                <el-table-column
                  prop="goodsName"
                  label="物资名称"
                  width="120"
                  align="center"
                  label-class-name="header-style">
                </el-table-column>
                <el-table-column
                  prop="count"
                  label="数量"
                  width="120"
                  align="center"
                  label-class-name="header-style">
                </el-table-column>
                <el-table-column
                  prop="goodsUnit"
                  label="单位"
                  width="60"
                  align="center"
                  label-class-name="header-style">
                </el-table-column>
                <el-table-column
                  prop="goodsLocation"
                  label="存放地点"
                  min-width="180"
                  align="center"
                  label-class-name="header-style">
                </el-table-column>
              </el-table>
              <div style="margin-top: 10px">
                <el-pagination
                  background
                  layout="prev, pager, next"
                  :page-size="10"
                  :current-page="singleGoodsCurrentPage"
                  :total="singleGoodsTotal"
                  @current-change="singleGoodsPageClick">
                </el-pagination>
              </div>
            </el-col>
          </el-col>

        </el-tab-pane>
        <el-tab-pane name="goodsDetail">
          <span slot="label">物资详情</span>
          <!--物资搜索-->
          <div style="float: left;margin: -10px 10px 10px 10px">
            <div style="width: 100%;">
              <div style="display: inline-block;float: left">
                <el-input  v-model="searchGoodsDept" clearable
                           placeholder="请输入部门" style="margin-left: 10px;width: 160px">
                </el-input>
                <!--<el-select v-model="searchGoodsDept" placeholder="请选择部门" style="margin-left: 10px;width: 160px">
                  <el-option
                    v-for="item in goodsDeptOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                  <el-option
                    key="all"
                    label="全部部门"
                    value="all">
                  </el-option>
                </el-select>-->
              </div>
              <div style="display: inline-block;float: left;">
                <el-select
                  v-model="searchGoodsPlace"
                  filterable
                  remote
                  reserve-keyword
                  clearable
                  placeholder="请输入存放地点后选择"
                  :remote-method="loadDeptWarehouse"
                  style="margin-left: 10px;width: 220px">
                  <el-option
                    key="all"
                    label="全部地点"
                    value="all">
                  </el-option>
                  <el-option
                    v-for="item in goodsPlaceOptions"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id">
                  </el-option>

                </el-select>
              </div>
              <div style="display: inline-block;float: left;">
                <el-select
                  v-model="searchGoodsType"
                  filterable
                  remote
                  reserve-keyword
                  clearable
                  placeholder="请输入物资类型后选择"
                  :remote-method="loadEmgGoodsType"
                  style="margin-left: 10px;width: 220px">
                  <el-option
                    key="all"
                    label="全部类型"
                    value="all">
                  </el-option>
                  <el-option
                    v-for="item in goodsTypeOptions"
                    :key="item.id"
                    :label="item.name"
                    :value="item.name">
                  </el-option>

                </el-select>
              </div>
              <div style="display: inline-block;float: left;margin-left: 10px">
                <el-button type="primary" icon="el-icon-search" @click="searchClick">搜 索</el-button>
              </div>
              <div style="display: inline-block;float: left;margin-left: 20px">
                <el-button v-if="!viewRole" type="success" @click="newGoodsClick">新增物资</el-button>
              </div>
            </div>
          </div>
          <!--物资搜索end-->

          <!--物资列表-->
          <div style="width: 100%;margin-top: 10px">
            <div style="padding: 10px 10px 0 10px">
              <el-table
                :data="goodsData"
                border
                highlight-current-row
                style="width: 100%">
                <el-table-column
                  type="index"
                  label="序号"
                  width="60"
                  align="center"
                  label-class-name="header-style">
                </el-table-column>
                <el-table-column
                  prop="goodsName"
                  label="物资名称"
                  width="120"
                  align="center"
                  show-overflow-tooltip
                  label-class-name="header-style">
                </el-table-column>
                <el-table-column
                  prop="count"
                  label="数量"
                  width="120"
                  align="center"
                  label-class-name="header-style">
                </el-table-column>
                <el-table-column
                  prop="goodsUnit"
                  label="单位"
                  width="60"
                  align="center"
                  show-overflow-tooltip
                  label-class-name="header-style">
                </el-table-column>
                <el-table-column
                  prop="goodsLocation"
                  label="存放地点"
                  width="180"
                  align="center"
                  show-overflow-tooltip
                  label-class-name="header-style">
                </el-table-column>
                <el-table-column
                  prop="deptName"
                  label="所属部门"
                  width="180"
                  align="center"
                  show-overflow-tooltip
                  label-class-name="header-style">
                </el-table-column>
                <el-table-column
                  prop="manageUser"
                  label="管理人"
                  width="80"
                  align="center"
                  show-overflow-tooltip
                  label-class-name="header-style">
                </el-table-column>
                <el-table-column
                  prop="contact"
                  label="联系方式"
                  width="150"
                  align="center"
                  show-overflow-tooltip
                  label-class-name="header-style">
                </el-table-column>
                <el-table-column
                  prop="remarks"
                  label="备注"
                  min-width="250"
                  align="center"
                  show-overflow-tooltip
                  label-class-name="header-style">
                </el-table-column>
                <el-table-column fixed="right" label="操作" label-class-name="header-style" align="center" width="280">
                  <template slot-scope="scope">
                    <el-button v-if="!viewRole" size="mini" type="primary" @click="updateGoods(scope.row)" style="float: left">修改</el-button>
                    <el-button v-if="!viewRole" size="mini" type="danger" style="float: left"
                               @click="delGoodsDialogVisible=true,delGoodsId=scope.row.id,delGoodsIndex=scope.$index">删除
                    </el-button>
                    <el-button  v-if="!viewRole"  size="mini" type="success" style="float: left" @click="recordClick(scope.row)">使用记录
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div style="margin-top: 10px">
              <el-pagination
                background
                layout="prev, pager, next"
                :page-size="10"
                :current-page="currentPage"
                :total="totalItem"
                @current-change="currentPageClick">
              </el-pagination>
            </div>
          </div>
          <!--物资列表结束-->
        </el-tab-pane>
      </el-tabs>

    </div>

    <el-dialog
      title="提示"
      :visible.sync="delGoodsDialogVisible"
      width="30%"
      center>
      <span>确认删除该物资</span>
      <span slot="footer" class="dialog-footer">
    <el-button @click="delGoodsDialogVisible = false">取 消</el-button>
    <el-button type="primary" @click="delGoods()">确 定</el-button>
  </span>
    </el-dialog>

  </div>
</template>
<script>
  import {mapGetters} from 'vuex'
  export default {
    name: 'emerMaterial',
    data() {
      return {
        //-------------------物资统计还是详情--------------------
        activeTabName:'goodsStatics',
        //-------------------公共选项数据------------------------
        goodsDeptOptions: [],
        goodsPlaceOptions: [],
        goodsTypeOptions: [],
        //-------------------新建物资数据------------------------
        goodsDept: '',
        goodsPlace: '',
        goodsType: '',
        //-----------------物资查询数据---------------------------
        searchGoodsDept: '',
        searchGoodsPlace: 'all',
        searchGoodsType: 'all',
        searchName: '',
        //-----------------列表数据--------------------------------
        goodsData: [],
        delGoodsDialogVisible: false,
        delGoodsId: 0,
        delGoodsIndex:0,
        currentPage:0,
        totalItem:0,

        //---------------物资统计数据------------------------------
        staticsData: [],
        singleGoodsData:[],
        currentSingleGoods:'',
        singleGoodsCurrentPage:0,
        singleGoodsTotal:0,
        //浏览角色模式
        viewRole : false,
      }
    },
    watch: {
      $route(to, from) {
        if (this.$route.name === 'emerMaterial') {
          this.init();
        }
      }
    }
    ,
    mounted: function () {
      this.init();

    },
    methods: {
      init(){
        this.viewRole = this.$tool.judgeViewRole();
        this.searchGoods(this.searchGoodsDept, this.searchGoodsPlace, 'all');
        this.searchGoodsStatics();
      },
      newGoodsClick: function () {
        this.$router.push({name: 'newEmerMaterial',params:{edit:false}});
      },
      handleTabClick:function(tab,event){
        console.log(tab,event);
        if(tab.name == 'goodsStatics'){
          this.searchGoodsStatics();
        }else{
          this.searchGoods(this.searchGoodsDept, this.searchGoodsPlace, 'all');
          // this.loadDeptWarehouse();
          // this.loadEmgGoodsType();
        }

      },
      //-----------------------物资列表的函数---------------------
      searchClick: function () {
        this.searchGoods(this.searchGoodsDept, this.searchGoodsPlace, this.searchGoodsType)
      },
      updateGoods: function (row) {
        this.$router.push({name: 'newEmerMaterial',params:{edit:true,emgGoodsId:row.id
            ,unit:row.goodsUnit,goodsName:row.goodsName,warehouseId:row.warehouseId,warehouseName:row.goodsLocation
        ,count:row.count,manageUser:row.manageUser,contact:row.contact,remark:row.remarks}});
      },
      recordClick: function (row) {
        this.$router.push({name: 'recordEmerMaterial',params:{emgGoodsId:row.id,unit:row.goodsUnit,goodsName:row.goodsName}});
      },
      searchGoods: function (deptName, locationId, goodsType) {
        var params = new URLSearchParams()
        if (deptName !='') {
          params.append("deptName", deptName)
        }
        if (locationId > 0) {
          params.append("warehouseId", locationId)
        }
        if (goodsType!='all') {
          params.append("goodsName", goodsType)
        }
        params.append("pageCurrent",1)
        params.append("pageSize",10)
        this.$http.post('emgGoods/find', params).then(function (res) {
          if (res.data.success) {
            this.currentPage=1
            this.totalItem=res.data.data.total
            this.goodsData = res.data.data.list
            this.goodsData.forEach(function (item,index) {
              item.num=index+1
            })
          } else {
            this.$message.error(res.data.message)
          }
        }.bind(this)).catch(function (err) {
          console.info(err)
        })
      }
      ,
      delGoods: function () {
        var params = new URLSearchParams()
        params.append("id",this.delGoodsId)
        this.$http.post('emgGoods/delete',params).then(function (res) {
          if(res.data.success){
            this.$message.success("删除成功")
            this.goodsData.splice(this.delGoodsIndex,1)
          }
          this.delGoodsDialogVisible=false
        }.bind(this)).catch(function (err) {
          console.log(err)
        })
      },
      loadDeptWarehouse: function (val) {
        let params=new URLSearchParams;
        if(val != null && val != ''){
          params.append("name",val);
        }
        this.$http.post('emgWareHouse/findAll',params).then(function (res) {
          if (res.data.success) {
            this.goodsPlaceOptions = res.data.data
          } else {
            this.$message.error("请求失败")
          }
        }.bind(this)).catch(function (err) {
          console.log(err)
        })
      },
      loadEmgGoodsType: function (val) {
        let params=new URLSearchParams;
        if(val != null && val != ''){
          params.append("name",val);
        }
        this.$http.post('emgGoodsType/findAll',params).then(function (res) {
          if (res.data.success) {
            this.goodsTypeOptions = res.data.data
          } else {
            this.$message.error("请求失败")
          }
        }.bind(this)).catch(function (err) {
          console.log(err)
          this.$message.error("发生错误")
        }.bind(this))
      },
      //翻页
      currentPageClick:function (val) {
        if(val){
          var params = new URLSearchParams()
          params.append("pageCurrent",Number(val))
          if (this.searchGoodsDept != '') {
            params.append("deptName", this.searchGoodsDept)
          }
          if (this.searchGoodsPlace > 0) {
            params.append("warehouseId", this.searchGoodsPlace)
          }
          if (this.searchGoodsType!='all') {
            params.append("goodsName", this.searchGoodsType)
          }
          params.append("pageSize",10)
          this.$http.post('emgGoods/find', params).then(function (res) {
            if (res.data.success) {
              this.currentPage=val
              this.totalItem=res.data.data.total
              this.goodsData = res.data.data.list
              this.goodsData.forEach(function (item,index) {
                item.num=(Number(val)-1)*10+index+1
              })
            } else {
              this.$message.error(res.data.message)
            }
          }.bind(this)).catch(function (err) {
            console.info(err)
          })
        }
      },

      //----------------------搜索物资统计数据-----------------------------
      searchGoodsStatics:function () {
        this.$http.get('emgGoods/countByCategory').then(function (res) {
          if (res.data.success) {
            this.staticsData=res.data.data;
          }
        }.bind(this)).catch(function (err) {
          console.info(err)
        })
      },
      showSingleGoodsDetail:function (row) {
        this.currentSingleGoods=row.goodsName;
        this.singleGoodsCurrentPage=1;
        this.singleGoodsSendRequest();
      },
      singleGoodsPageClick:function (val) {
        this.singleGoodsCurrentPage=val;
        this.singleGoodsSendRequest();
      },
      singleGoodsSendRequest:function () {
        let params = new URLSearchParams();
        params.append("goodsName", this.currentSingleGoods);
        params.append("pageCurrent",this.singleGoodsCurrentPage);
        params.append("pageSize",10);
        this.singleGoodsData=[];
        this.$http.post('emgGoods/find', params).then(function (res) {
          if (res.data.success) {
            this.singleGoodsTotal=res.data.data.total;
            this.singleGoodsData=res.data.data.list;
          }
        }.bind(this)).catch(function (err) {
          console.info(err)
        })
      },
    }
  }
</script>
<style>
</style>
