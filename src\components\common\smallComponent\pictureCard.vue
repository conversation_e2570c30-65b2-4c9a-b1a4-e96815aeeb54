<template>
  <div id="pictureCard">
    <img :src="downloadBaseUrl+item.path+'?x-oss-process=image/resize,m_fixed,h_70,w_70'" v-for="item in picFileList" :key="item.fId" @click="previewClick(item.path)"  style="width: 70px;height: 70px;border-radius: 5px;margin-right: 5px"/>
    <el-dialog :visible.sync="dialogVisible" append-to-body width="70%">
      <img width="100%" :src="dialogImageUrl" alt="">
    </el-dialog>
  </div>
</template>
<script>
  export default {
    name: 'pictureCard',
    props:['picFileList'],
    data() {
      return {
        //上传和预览的基本地址
        downloadBaseUrl:'',
        //预览图片信息
        dialogVisible:false,
        dialogImageUrl:'',
      }
    },
    mounted:function () {
      this.downloadBaseUrl=this.fileHttp.defaults.baseURL;
    },
    methods:{
      previewClick:function (path) {
        this.dialogImageUrl= this.downloadBaseUrl+path;
        this.dialogVisible=true;
      }
    }
  }
</script>
<style>
</style>
