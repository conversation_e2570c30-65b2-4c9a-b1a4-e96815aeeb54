<template>
  <div id="">
    <el-container class="container">
      <el-main>
        <el-form ref="form" :model="form" label-width="5px">
          <el-row>
            <el-col :span="4">
              <el-form-item>
                <el-input
                  clearable
                  placeholder="任务名称"
                  v-model="form.title"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :offset="1" :span="1">
              <el-button type="primary" @click="searchBtnClickHandle"
                >搜索</el-button
              >
            </el-col>
          </el-row>
          <el-row>
            <el-table border :data="tableData.list" style="width: 100%">
              <el-table-column
                type="index"
                label="编号"
                width="100"
                align="center"
                label-class-name="header-style"
              >
              </el-table-column>
              <el-table-column
                prop="title"
                label="任务名称"
                min-width="150"
                label-class-name="header-style"
              >
              </el-table-column>
              <el-table-column
                prop="remark"
                label="描述"
                width="150"
                label-class-name="header-style"
              >
              </el-table-column>
              <el-table-column
                prop="createTime"
                :formatter="formatDateTime"
                label="开始时间"
                width="150"
                label-class-name="header-style"
              >
              </el-table-column>
              <el-table-column
                prop="status"
                label="状态"
                width="150"
                label-class-name="header-style"
              >
                <template slot-scope="scope">
                  <span v-if="scope.row.status == 1">待处理</span>
                  <span v-else-if="scope.row.status == 2">已完成</span>
                </template>
              </el-table-column>
              <el-table-column
                label="操作"
                label-class-name="header-style"
                align="center"
                width="100"
              >
                <template slot-scope="scope">
                  <template>
                    <el-button
                      size="mini"
                      plain
                      type="primary"
                      @click="menu(scope.row)"
                      >处理</el-button
                    >
                    <!-- <el-button
                      size="mini"
                      plain
                      type="warning"
                      @click="showPdfDialog(scope.row)"
                      >下载</el-button
                    > -->
                    <!-- <el-button
                      size="mini"
                      plain
                      type="warning"
                      @click="itemViewClick(scope.row)"
                      >上传</el-button
                    >
                    <el-button
                      size="mini"
                      plain
                      type="warning"
                      @click="itemSubmitClick(scope.row)"
                      >提交</el-button
                    > -->
                  </template>
                </template>
              </el-table-column>
            </el-table>
            <el-pagination
              background
              layout="prev, pager, next"
              :current-page="tableData.pageNum"
              :page-size="form.pageSize"
              :total="tableData.total"
              @current-change="disasterPageChangeHandle"
            >
            </el-pagination>
          </el-row>
        </el-form>
      </el-main>
      <el-dialog
        :title="dialog.title"
        width="100%"
        top="0vh"
        :center="true"
        :visible.sync="dialog.isShow"
      >
        <iframe
          :src="dialog.pdfUrl + '#toolbar=0'"
          width="100%"
          height="810"
        ></iframe>
      </el-dialog>
    </el-container>
  </div>
</template>
<script>
export default {
  name: "",
  data() {
    return {
      // pdf查看对话框
      dialog: {
        //查看安全风险告知书
        isShow: false,
        pdfUrl: "http://www.safe360.vip/safeFile.pdf",
        title: "查看安全风险告知书",
      },
      // 搜索
      form: {
        // 名称
        // newsName : '',
        // title : '',
        // 类型
        //          newsType : '',
        // 当前页
        pageCurrent: 1,
        // 页数大小
        pageSize: 10,
      },
      // 查看人数对话框
      lookNumDialog: {
        // 搜索条件
        form: {
          studyId: "",
          // 当前页
          pageCurrent: 1,
          // 页数大小
          pageSize: 10,
          userName: "",
        },
        // 是否显示
        isShow: false,
        // 标题
        title: "",
        // 表格数据
        tableData: [],
      },
      assist: {
        // 类型
        newsType: [
          { value: "党建巡礼", label: "党建巡礼" },
          { value: "廉洁教育", label: "廉洁教育" },
          { value: "理论学习", label: "理论学习" },
          { value: "党务学习", label: "党务学习" },
        ],
      },
      tableData: {},
      // 角色 0 组织者或公司      1 部门        2  班组
      role: 0,
    };
  },
  mounted() {
    this.init();
  },
  watch: {
    $route(to, from) {
      if (to.name === "ReportStatisticsBacklog") {
        this.init();
      }
    },
  },
  methods: {
    // 初始化
    init() {
      // 搜索
      this.searchBtnClickHandle();
    },
    // 文章查看
    showPdfDialog(row) {
      // this.dialog.title = row.title;
      window.open(row.filePath);
      // this.dialog.pdfUrl = row.filePath;
      // this.dialog.isShow = true;
    },

    // 清空数据
    clear() {},
    // 格式化时间
    formatDateTime(row, column, cellValue) {
      let pro = column.property;
      let num = 10;
      let str = this.$tool.formatDateTime(row[pro]) || "";
      return str ? str.substring(0, num) : str;
    },
    formatAllDateTime(row, column, cellValue) {
      let pro = column.property;
      let str = this.$tool.formatDateTime(row[pro]) || "";
      return str;
    },
    // 分页
    disasterPageChangeHandle(page) {
      this.form.pageCurrent = page;
      if (this.isMore) {
        this.saveScoreBtnClickHandle();
      } else {
        this.searchBtnClickHandle();
      }
    },
    // 搜索按钮
    searchBtnClickHandle() {
      let _this = this;
      let params = Object.assign({}, this.form);

      let url = "sys/sysManageFile/findUserReportForm";

      _this.$http.post(url, params).then(function (res) {
        if (res.data.success) {
          _this.tableData = res.data.data;
        } else {
          _this.$message({
            showClose: true,
            message: "操作失败！",
            type: "error",
          });
        }
      });
    },
    // 处理按钮
    menu(row) {
      this.$router.push({
        name: "ReportStatisticsBacklogView",
        params: {
          info: row,
        },
      });
    },
  },
};
</script>
<style>
.container {
  background: #fff;
  padding: 0 20px;
}
.row {
  margin-top: 10px;
}
</style>
