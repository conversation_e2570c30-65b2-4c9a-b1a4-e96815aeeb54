import Vue from 'vue'
import Vuex from 'vuex'

Vue.use(Vuex);

import mutations from  './mutations'
import actions from './actions'
import sysManageData from './sysManageData'
import emergencyData from './emergencyData'
import emerHandleModule from  './emerHandleModule'
import eduTrainingModule from  './eduTrainingModule'
import hideDangerData from './hideDangerData'
import hideDangerModule from './hideDangerModule'
import safetyInputModule from './safetyInputModule'
import accountModule from './accountModule'


export default new Vuex.Store(
  {
    modules:{
      mutations,
      sysManageData,
      emergencyData,
      emerHandleModule,
      eduTrainingModule,
      hideDangerData,
      hideDangerModule,
      safetyInputModule,
      accountModule,
    },
    actions
  }
);
