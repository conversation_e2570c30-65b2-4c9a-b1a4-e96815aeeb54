<template>
  <div class="background-style" style="padding: 10px">
    <el-row style="margin:0">
      <el-col :span="6">
        <el-button type="primary" size="mini" @click="$router.back()">返回</el-button>
      </el-col>
    </el-row>
    <el-row style="margin:10px 0 0 0">
      <el-col :span="24">
        <egrid class="egrid"
               stripe border
               maxHeight="500"
               :data="egrid.data"
               :columns="egrid.columns"
               :columns-schema="egrid.columnsSchema">
        </egrid>
      </el-col>
    </el-row>
  </div>
</template>

<script>


  export default {
    data(){
      return {
        // 表格
        egrid : {
          data : [],
          columns : [
            { label: '姓名', prop: 'username' },
            { label: '性别', prop: 'gender' },
            { label: '原工种', prop: 'oldPost' },
            { label: '现工种', prop: 'newPost' },
            { label: '原单位', prop: 'oldCompany' },
            { label: '现单位', prop: 'newCompany' },
            { label: '教育时间', prop: 'trainingDate' },
            { label: '学时', prop: 'trainingHours' },
            { label: '部门教育者', prop: 'teacher' },
            { label: '考试成绩', prop: 'score' },
          ],
          // columnsProps 用于定义所有 columns 公共的属性
          columnsProps: {
            fit : true,
            sortable: true,
            align : 'center',
          },
          columnsSchema : {
            '姓名' : {
              width : 120
            },
            '性别' : {
              width : 50
            },
            '原工种' : {
              showOverflowTooltip : true,
              width : 100
            },
            '现工种' : {
              showOverflowTooltip : true,
              width : 100
            },
            '原单位' : {
              showOverflowTooltip : true,
              width : 120
            },
            '现单位' : {
              showOverflowTooltip : true,
              width : 120
            },
            '教育时间' : {
              width : 120
            },
            '学时' : {
              width : 50
            },
            '部门教育者' : {
              width : 100
            },
            '学时' : {
              考试成绩 : 50
            },
          },
        }
      }
    },
    created(){
      this.init();
    },
    watch:{
      $route(to,from){
        let data = to.params && to.params.row && to.params.row.data;
        if(to.name === 'eduReassignments') {
          if(data){
            this.searchBtnClickHandle();
          }
        }
      }
    },
    methods:{
      // 初始化
      init(){
        let data = this.$route.params.row.data;
        if(data){
          // 搜索
          this.searchBtnClickHandle();
        }
      },
      // 搜索按钮
      searchBtnClickHandle(){
        let data = this.$route.params.row.data;
        let list = data.map(function(it){
          return {
            username : it.eduUser.username || '',
            gender : it.eduUser.gender ? '男' : '女',
            oldPost : it.oldPost || '',
            newPost : it.newPost || '',
            oldCompany : it.oldCompany || '',
            newCompany : it.newCompany || '',
            trainingDate : (this.$tool.formatDateTime(it.trainingDate)|| '').substring(0, 10),
            trainingHours : it.trainingHours || '',
            teacher : it.teacher || '',
            score : it.score || '',
          }
        }.bind(this));
        this.egrid.data = list;
      },
    }
  }
</script>

<style>

</style>
