import getters from './getters'

const state={
  //用户数据
  currentUser:{},
  //用户头像路径
  headLogoPath:'',
  //应急救援数据
  emergencyPathFlag:false,
  emergencyPath:'',

  //视图按钮的显隐
  viewFlag:{ menuFlag:false, managerViewFlag:true, gisFlag:true}
};

const mutations={
  //用户数据
  sendCurrentUser(state,msg){
    state.currentUser=msg;
  },
  //头像数据
  sendHeadLogoPath(state,msg){
    state.headLogoPath=msg?msg+'?x-oss-process=image/resize,m_fixed,h_60,w_60':'../../static/imgs/public/person_icon.png';
  },
//应急救援消息传递
  sendEmergencyPath(state,msg){
    state.emergencyPath=msg;
    state.emergencyPathFlag=true;
  },
  clearEmergencyPath(state,msg){
    state.emergencyPath='';
    state.emergencyPathFlag=false;
  },

  //视图按钮的显隐
  changeViewFlag(state,msg){
    state.viewFlag=msg;
  },
};

export default {
  state,
  mutations,
  getters
}
