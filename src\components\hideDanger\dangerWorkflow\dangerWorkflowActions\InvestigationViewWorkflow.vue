<template>
  <div id="investigationViewWorkflow">
    <div class="background-style">
      <el-col :span="16" :offset="4" class="success-background-title">{{titleStr}}</el-col>
      <el-col :span="16" :offset="4">
        <el-form :model="form" ref="ruleForm" label-width="100px" class="demo-ruleForm" label-position="left">
          <el-col :span="24">
            <el-col :span="12">
              <el-form-item label="检查单编号：" prop="checkNum" style="margin: 0">
                {{form.checkNum}}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="发起人：" prop="createUserName" style="margin: 0" label-width="70px">
                {{form.createUserName}}
              </el-form-item>
            </el-col>
          </el-col>
          <el-col :span="24">
            <el-col :span="12">
              <el-form-item label="预计检查日期：" prop="predictInspectDate" style="margin: 0" label-width="110px">
                {{form.predictInspectDate}}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="实际检查日期：" prop="inspectDate" style="margin: 0" label-width="110px">
                {{form.inspectDate}}
              </el-form-item>
            </el-col>
          </el-col>
          <el-col :span="24">
            <el-col :span="12" v-if="inspectType!='自查'">
              <el-form-item label="检查组组长：" prop="leaderUserName" style="margin: 0">
                {{form.leaderUserName}}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="检查类型:" style="margin: 0" label-width="90px">
                {{inspectType}}
              </el-form-item>
            </el-col>
          </el-col>
          <div v-if="inspectType!='自查'">
            <el-col :span="24">
              <el-form-item label="检查组成员：" prop="dangerInspectMembers" style="margin: 0">
                <span v-for="item in form.dangerInspectMembers" :key="item.userId">{{item.userName}} , </span>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-col :span="12">
                <el-form-item label="受检单位:" prop="targetDeptName" style="margin: 0;" label-width="80px">
                  {{form.targetDeptName}}
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="检查单位:" prop="publicDeptName" style="margin: 0;" label-width="80px">
                  {{form.publicDeptName}}
                </el-form-item>
              </el-col>
            </el-col>
            <el-col :span="24">
              <el-col :span="12">
                <el-form-item label="受检单位承办人：" prop="targetContractorUserName" style="margin: 0;" label-width="140px">
                  {{form.targetContractorUserName}}
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="受检单位现场负责人：" prop="targetLiveChargeUser" style="margin: 0" label-width="160px">
                  {{form.targetLiveChargeUser}}
                </el-form-item>
              </el-col>
            </el-col>
          </div>
        </el-form>
      </el-col>
      <el-col :span="22" :offset="1">
        <el-table
          v-loading="loading"
          border
          :data="form.dangerInspectListPublicList">
          <el-table-column
            type="index"
            label="序号"
            width="50"
            fixed
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            prop="inspectProject"
            label="检查项目"
            width="150"
            fixed
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            prop="inspectContent"
            min-width="400"
            label="检查标准内容"
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            prop="inspectResult"
            width="300"
            label="检查结果记录"
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            prop="hiddenDangerLevel"
            width="150"
            label="隐患级别"
            label-class-name="inner-header-style">
          </el-table-column>
          <el-table-column
            width="150"
            label="隐患照片"
            label-class-name="inner-header-style">
            <template slot-scope="scope">
              <picture-card :picFileList="scope.row.dangerPics"></picture-card>
            </template>
          </el-table-column>
          <el-table-column
            prop="deadline"
            width="150"
            fixed="right"
            label="整改时间"
            :formatter="changeFormat"
            label-class-name="inner-header-style">
          </el-table-column>
        </el-table>
      </el-col>
      <el-col :span="16" :offset="4" style="margin-top: 10px" v-show="needCheck">
        <el-form :model="singleForm" ref="singleForm" label-width="100px" class="demo-ruleForm">
          <el-form-item label="审核意见：" prop="examine">
            <el-dropdown @command="editExamine">
              <el-button type="primary" size="small">
                审核参考<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item v-for="item in selectOptions" :key="item.id" :command="item.content">{{item.name}}</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <el-input type="textarea" :autosize="{ minRows: 3}" v-model="singleForm.examine"></el-input>
          </el-form-item>
        </el-form>
      </el-col>
      <el-col :span="22" :offset="1">
        <div style="float: right;margin: 20px">
          <el-button  type="primary" v-show="needCheck" @click="passClick" >签发</el-button>
          <el-button  type="danger" v-show="needCheck" @click="backClick" >退回</el-button>
          <el-button @click="$router.push({name:'hideDangerWorkflow'})">返回</el-button>
        </div>
      </el-col>
    </div>

    <!--选择负责人-->
    <search-people-dialog @determineClick="selectPersonClick" :data="selectPersonData" :defaultPersonId="selectPersonData.defaultPerson.value"></search-people-dialog>

    <!--判断对话框-->
    <judge-dialog ref="judgeDialog" @buttonClick="judgeExamine"></judge-dialog>
  </div>
</template>
<script>
  import SearchPeopleDialog from '../../../common/smallComponent/searchSinglePeople.vue'
  import PictureCard from '../../../common/smallComponent/pictureCard.vue'
  import JudgeDialog from '../../../common/smallComponent/judgeDialog.vue'
  export default {
    name: 'investigationViewWorkflow',
    data() {
      return {
        //标题
        titleStr:'',
        currentId:'',

        form:{
          checkNum:'',
          predictInspectDate:'',
          targetDeptName:'',
          publicDeptName:'',
          leaderUserName:'',
          dangerInspectMembers:[],
          targetContractorUserName:'',
          targetLiveChargeUserName:'',
          needFeedback:'',
          dangerInspectListPublicList:[],
        },
        inspectType:'',//当检查的类型
        loading:false,

        //选择数据
        needReply:['否','是'],
        //是否为审核视图
        needCheck:false,
        //审核意见
        singleForm:{examine:''},
        //参考审核数据
        selectOptions:[
          {id:'examine01',name:'同意签发',content:'经审核，同意发布该检查。'},
          {id:'examine02',name:'退回修改',content:'经审核，不同意发布该检查，意见如下：'}
        ],
        //------------------选择负责人的对话框-----------------------
        selectPersonData:{title:'请选择负责人',isShow:false,defaultPerson:{value:0,label:''}},
        //流程ID
        taskId:'',
        //节点数据
        nodeData:{},

      }
    },
    components : {
      SearchPeopleDialog,
      PictureCard,
      JudgeDialog
    },
    created:function () {
      if(this.$route.params.dangerData){
        this.titleStr=this.$route.params.dangerData.name;
        this.nodeData=this.$route.params.dangerData.nodeData;
        this.taskId=this.$route.params.dangerData.taskId;
        this.inspectType=this.$route.params.dangerData.typeName;
        this.needCheck=true;
        if(this.$route.params.onlyShow){
          this.needCheck=false;
        }
        this.searchDataById(this.$route.params.dangerData.id);
      }
    },
    watch:{
      $route(to, from){
        if((from.name==='hideDangerWorkflow'||from.name==='changeFormViewWorkflow'||from.name==='taskNotice')&&this.$route.name==='investigationViewWorkflow') {
          if(this.$route.params.dangerData){
            this.titleStr=this.$route.params.dangerData.name;
            this.nodeData=this.$route.params.dangerData.nodeData;
            this.taskId=this.$route.params.dangerData.taskId;
            this.inspectType=this.$route.params.dangerData.typeName;
            this.needCheck=true;
            if(this.$route.params.onlyShow){
              this.needCheck=false;
            }
            this.searchDataById(this.$route.params.dangerData.id);
          }
        }
      }
    },
    methods:{
      searchDataById:function (id) {
        //清除之前数据
        this.form.checkNum='';
        this.form.predictInspectDate='';
        this.form.targetDeptName='';
        this.form.leaderUserName='';
        this.form.targetContractorUserName='';
        this.form.targetLiveChargeUserName='';
        this.form.needFeedback='';
        this.form.dangerInspectMembers.splice(0);
        this.form.dangerInspectListPublicList.splice(0);

        this.currentId=id;
        this.loading=true;
        this.$http.post('danger/inspectPublic/detail', {id:id}).then(function (res) {
          if (res.data.success) {
            let tempForm=res.data.data;
            tempForm.predictInspectDate=this.transferTime(tempForm.predictInspectDate);
            tempForm.inspectDate=this.transferTime(tempForm.inspectDate);
            this.form=tempForm;
            this.loading=false;
          }
        }.bind(this)).catch(function (err) {
          this.$message.error('查找数据失败');
          console.log(err);
        });
      },
      //改时间格式
      changeFormat:function (row) {
        return this.transferTime(row.deadline);
      },
      //填写审核意见
      editExamine:function (content) {
        this.singleForm.examine=content;
      },
      //通过
      passClick:function () {
        if(this.nodeData.check){
          this.$refs.judgeDialog.openJudgeDialog('是否需要其他人员审核','不需要','需要');
        }else{
          let params=new URLSearchParams;
          params.append("result",0);
          params.append("check",false);
          params.append("taskId",this.taskId);
          params.append("comment",this.singleForm.examine);
          this.doTaskClick(params);
        }
      },
      judgeExamine:function (val) {
        if(val){
          this.selectPersonData.title='请选择下一个审核人';
          this.selectPersonData.defaultPerson={value:0,label:''};
          this.selectPersonData.isShow=true;
        }else{
          let params=new URLSearchParams;
          params.append("result",0);
          params.append("check",false);
          params.append("taskId",this.taskId);
          params.append("comment",this.singleForm.examine);
          this.doTaskClick(params);
        }
      },
      //退回
      backClick:function () {
        let params=new URLSearchParams;
        params.append("result",1);
        params.append("check",false);
        params.append("taskId",this.taskId);
        params.append("comment",this.singleForm.examine);
        this.doTaskClick(params);
      },
      selectPersonClick:function (val) {
        if(val){
          this.selectPersonData.isShow=false;
          let params=new URLSearchParams;
          params.append("result",1);
          params.append("check",true);
          params.append("applyUserId",val);
          params.append("taskId",this.taskId);
          params.append("comment",this.singleForm.examine);
          this.doTaskClick(params);
        }else{
          this.$message.warning('请选择下一个审核人');
        }
      },
      doTaskClick:function (params) {
        this.$http.post('dangerFlow/doTask', params).then(function (res) {
          if (res.data.success) {
            if(this.selectPersonData.isShow){this.selectPersonData.isShow=false;}
            this.$message.success('操作成功！');
            this.$router.push({name:'hideDangerWorkflow'});
          }
        }.bind(this)).catch(function (err) {
          this.$message.error('操作失败！');
          console.log(err);
        });
      },

    }
  }
</script>
<style>
</style>
