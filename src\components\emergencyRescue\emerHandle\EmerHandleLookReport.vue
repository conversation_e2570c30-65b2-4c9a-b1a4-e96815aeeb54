<template>
    <div class="container">
      <el-container>
        <el-main>
          <el-form label-width="120px" label-position="left" class="demo-ruleForm">
            <el-row>
              <el-col :span="8">
                <el-form-item label="上报时间：">
                  <el-date-picker
                    type="datetime"
                    v-model="form.reportTime"
                    placeholder="选择日期时间">
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :offset="2" :span="8">
                <el-form-item label="事件名称：">
                  <el-input
                    v-model="form.eventName"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item label="事发单位：">
                  <el-input
                    v-model="form.incidentUnit"></el-input>
                </el-form-item>
              </el-col>
              <el-col :offset="2" :span="8">
                <el-form-item label="发生时间：">
                  <el-date-picker
                    v-model="form.eventTime"
                    type="datetime"
                    placeholder="选择日期时间">
                  </el-date-picker>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item label="发生地点：">
                  <el-input
                    v-model="form.eventLocation"></el-input>
                </el-form-item>
              </el-col>
              <el-col :offset="2" :span="8">
                <el-form-item label="编号：">
                  <el-input
                    v-model="form.number"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item label="信息报送联系人：">
                  <el-input v-model="form.contactUserName"></el-input>
                </el-form-item>
              </el-col>
              <el-col :offset="2" :span="8">
                <el-form-item label="联系电话：">
                  <el-input
                    v-model="form.phone"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item label="信息上报单位负责人：">
                  <el-input
                    v-model="form.reportUnitCharge"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="事件原因、经过及进展情况：">
                  <el-input
                    v-model="form.eventCause" type="textarea":rows="2"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="死亡、受伤、失踪人数和身份：">
                  <el-input
                    v-model="form.deathInjured" type="textarea":rows="2"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="预计直接经济损失：">
                  <el-input
                    v-model="form.economicLoss" type="textarea":rows="2"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="已采取的措施及初步处置情况：">
                  <el-input
                    v-model="form.measureSituation" type="textarea":rows="2"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="发展趋势及影响预判：">
                  <el-input
                    v-model="form.developmentPrejudgment" type="textarea":rows="2"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :offset="6" :span="12">
               <el-button @click="$router.back()">返回</el-button>
              </el-col>
            </el-row>
          </el-form>
        </el-main>
      </el-container>
    </div>
</template>

<script>
    export default {
      data(){
        return {
          form : {
            // 事件id
            eventId : '',
            // 上报时间
            reportTime : '',
            // 事件名称
            eventName : '',
            // 事发单位
            incidentUnit : '',
            // 发生时间
            eventTime : '',
            // 发生地点
            eventLocation : '',
            // 上报给谁看
            noticeUserIds : [],
            // 编号
            number : '',
            // 信息报送联系人
            contactUserName : '',
            // 联系电话
            phone : '',
            // 信息上报单位负责人
            reportUnitCharge : '',
            // 死亡、受伤、失踪人数和身份
            deathInjured : '',
            // 预计直接经济损失
            economicLoss : '',
            // 已采取的措施及初步处置情况
            measureSituation : '',
            // 发展趋势及影响预判
            developmentPrejudgment : '',
            // 事件原因、经过及进展情况
            eventCause : '',
          },
        }
      },

      watch:{
        $route(to,from){
          if(from.name === 'emerHandleReportLower') {
            this.init();
          }
        }
      },
      created(){
        this.init();
      },
      methods:{
        init(){
          let list = this.$route.params.row;
          Object.entries(list).forEach(function(it){
            if(this.form.hasOwnProperty(it[0])) this.form[it[0]] = it[1];
          }.bind(this))
        },
      }
    }
</script>

<style>
  .container{
    background : #fff;
    padding:10px 100px;
  }
  .title{
    background : #f90;
    text-align:center;
    padding:10px;
    color : #fff;
    font-size:16px;
  }
  h1{
    text-align : center;
  }
</style>
