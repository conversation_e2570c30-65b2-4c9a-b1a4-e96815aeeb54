<template>
  <div id="knowledgeBase">
    <div class="background-style">

      <!--搜索区-->
      <div style="width: 100%;min-height: 50px;padding: 10px 20px 0 20px;display: block">
        <el-row style="margin: 0">
          <div  style="margin:0 0 5px 0;padding-top: 2px;">
            <el-radio-group v-model="systemData" @change="reloadData()" size="medium">
              <el-radio-button label="公司数据"></el-radio-button>
              <el-radio-button label="系统数据"></el-radio-button>
            </el-radio-group>
          </div>
        </el-row>
        <el-row style="margin: 0">
          <div style="float: left;line-height: 40px;display: inline-block;font-size: 15px">筛选标签：</div>
          <!--标签列表-->
          <div style="float: left;display: inline-block">
            <el-tag
              v-for="tag in search.tagShowArray"
              :key="tag"
              @close="handleTagClose(tag)"
              style="margin: 5px 5px 0 0"
              closable>
              {{tag}}
            </el-tag>
          </div>
          <div v-if="inputVisible" style="float: left;margin-left:5px;display: inline-block">
            <el-select
              v-model="search.searchTag"
              filterable
              remote
              reserve-keyword
              clearable
              size="small"
              placeholder="搜索标签"
              :remote-method="remoteTag"
              :loading="search.tagLoading"
              @change="labelSearchChange"
              style="width: 150px">
              <el-option
                v-for="item in search.tagOptions"
                :key="item.label"
                :label="item.label"
                :value="item">
              </el-option>
            </el-select>
            <el-button  type="info" size="small" @click="search.searchTag='';inputVisible = false;" style="margin:5px 10px 0 0">取消</el-button>
          </div>
          <div v-else style="float: left;margin-left:5px;display: inline-block">
            <el-button  type="warning" size="small" @click="inputVisible = true;" icon="el-icon-plus" style="margin:5px 10px 0 0">添加类别</el-button>
          </div>
          <div style="float: right;margin-right: 40px;">
            <el-button type="primary" @click="downloadKnowTemplate" size="small">导入模板下载</el-button>
            <el-button type="primary" @click="fileList=[];uploadKnowDialog.uploadKnowVisible=true" size="small">导入知识点</el-button>
            <el-button type="success" @click="addKnowDialog" size="small">添加知识点</el-button>
            <el-button type="success" @click="batchAddTag(false)" size="small">批量加标签</el-button>
            <el-button type="danger" @click="batchAddTag(true)" size="small">批量删标签</el-button>
          </div>
        </el-row>
      </div>
      <!--搜索区结束-->

      <!--表格区-->
      <div style="width: 100%;margin-top: -10px">

        <div style="width: 100%;float: left;">
          <div style="padding: 10px 10px 5px 10px">
            <el-table
              :data="knowledgeData"
              border
              highlight-current-row
              @selection-change="handleSelectionChange"
              style="width: 100%">
              <el-table-column
                type="selection"
                width="50"
                fixed
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="num"
                label="编号"
                width="60"
                align="center"
                label-class-name="header-style">
              </el-table-column>
              <el-table-column
                prop="content"
                label="知识点"
                width="500"
                show-overflow-tooltip
                label-class-name="header-style">
              </el-table-column>
              <el-table-column label="标签" min-width="300" show-overflow-tooltip label-class-name="header-style">
                <template slot-scope="scope">
                  <el-tag
                    v-for="tag in scope.row.labels"
                    type="success"
                    :key="tag"
                    size="mini"
                    style="margin: 5px 5px 0 0">
                    {{tag}}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column
                prop="userName"
                label="操作人员"
                width="120"
                show-overflow-tooltip
                label-class-name="header-style">
              </el-table-column>
              <el-table-column label="操作" label-class-name="header-style" align="center" width="170" fixed="right">
                <template slot-scope="scope">
                  <el-button size="mini" type="primary" @click="editKnowledgeClick(scope.row,scope.$index)">修改
                  </el-button>
                  <el-button size="mini" type="danger" @click="deleteTagClick(scope.row,scope.$index)" v-show="buttonPermission.deleteButtonFlag">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div>
            <el-pagination
              background
              layout="prev, pager, next"
              :current-page="currentPage"
              :total="totalItem"
              @current-change="currentPageClick">
            </el-pagination>

          </div>
        </div>
      </div>
      <!--表格区结束-->

      <!--修改条目的对话框-->
      <el-dialog :title="knowledgeDialog.title" :visible.sync="knowledgeDialog.updateKnowledgeVisible">
        <el-form :model="knowledgeForm" :rules="knowledgeRules" ref="knowledgeForm" label-position="top"
                 class="demo-ruleForm">
          <el-form-item label="知识点:" label-width="120px" prop="content">
            <el-input type="textarea" :autosize="{ minRows: 2}" v-model="knowledgeForm.content"></el-input>
          </el-form-item>
          <el-form-item label="标签:" label-width="120px" prop="tags">
            <div style="width: 100%;border: 1px solid #DCDFE6;border-radius: 5px;min-height: 30px;">
              <el-tag
                v-for="tag in knowledgeForm.tags"
                :key="tag"
                closable
                @close="handleTagDelete(tag)"
                size="medium"
                style="margin-left: 10px">
                {{tag}}
              </el-tag>
            </div>
          </el-form-item>
          <el-form-item label="添加标签:" label-width="120px">
            <el-select
              v-model="addTag"
              filterable
              remote
              reserve-keyword
              clearable
              placeholder="请输入标签名后选择"
              :remote-method="remoteAddTag"
              @change="labelSelectChange"
              :loading="addTagLoading"
              style="width: 200px">
              <el-option
                v-for="item in addTagOptions"
                :key="item.id"
                :label="item.label"
                :value="item">
              </el-option>
            </el-select>
            <el-button type="primary" icon="el-icon-plus" @click="determineAddTag">添加标签</el-button>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="cancelUpdate">取 消</el-button>
          <el-button type="primary" @click="addOrUpdateLabel">确 定</el-button>
        </div>
      </el-dialog>
      <!--修改条目的对话框结束-->

      <!--批量处理对话框-->
      <el-dialog title="批量处理" :visible.sync="batchTagFlag">
        <el-form :model="batchTagForm" ref="batchTagForm" label-position="top">
          <span style="color: #2d57ae">共选中&nbsp{{batchTagForm.pointNum}}&nbsp个知识点，批量
            <span style="color:red;font-weight:bold">{{batchTagForm.tip}}</span>以下标签</span>
          <el-form-item label="标签:" label-width="120px" prop="tags">
            <div style="width: 100%;border: 1px solid #DCDFE6;border-radius: 5px;min-height: 30px">
              <el-tag
                v-for="tag in knowledgeForm.tags"
                :key="tag"
                closable
                @close="handleTagDelete(tag)"
                size="medium"
                style="margin-left: 10px">
                {{tag}}
              </el-tag>
            </div>
          </el-form-item>
          <el-form-item label="添加标签:" label-width="120px">
            <el-select
              v-model="addTag"
              filterable
              remote
              reserve-keyword
              clearable
              placeholder="请输入标签名后选择"
              :remote-method="remoteAddTag"
              @change="labelSelectChange"
              :loading="addTagLoading"
              style="width: 200px">
              <el-option
                v-for="item in addTagOptions"
                :key="item.id"
                :label="item.label"
                :value="item">
              </el-option>
            </el-select>
            <el-button type="primary" icon="el-icon-plus" @click="determineAddTag">添加标签</el-button>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="cancelBatchAddTag">取 消</el-button>
          <el-button type="primary" @click="determineBatchAddTag">确 定</el-button>
        </div>
      </el-dialog>
      <!--批量处理对话框结束-->

      <!--上传知识点表格对话框-->
      <el-dialog title="上传知识点" :visible.sync="uploadKnowDialog.uploadKnowVisible">
        <el-row>
          <el-upload
            class="upload-demo"
            ref="uploadKnow"
            :action="uploadKnowDialog.uploadBaseUrl"
            :with-credentials="uploadKnowDialog.cookies"
            :file-list="fileList"
            :auto-upload="false"
            :data="uploadKnowDialog.uploadTableParams"
            :before-upload="beforeUpdateClick"
            :on-success="uploadKnowSuccess"
            style="width: 400px;margin-top: 10px;">
            <el-button size="small" type="primary">选取知识点excel表格</el-button>
          </el-upload>
        </el-row>
        <div slot="footer" class="dialog-footer">
          <el-button type="success" @click="determineUpload">上传</el-button>
          <el-button @click="uploadKnowDialog.uploadKnowVisible=false">返回</el-button>
        </div>
      </el-dialog>
      <!--上传检查表对话框结束-->
    </div>
  </div>
</template>
<script>
  export default {
    name: 'knowledgeBase',
    data() {
      return {
        //标签搜索
        search: {
          searchTag: '',
          tagOptions: [],
          tagLoading: false,
          //已选中的标签列表
          tagShowArray: [],
        },
        inputVisible:false,//标签搜索

        //列表数据
        knowledgeData: [],
        totalItem: 1,
        currentPage: 1,

        //对话框数据
        knowledgeForm: {
          content: '',
          tags: [],
          id: 0
        },
        addTag: '',
        addTagOptions: [],
        addTagLoading: false,
        knowledgeRules: {
          content: [{required: true, message: '请选择填写知识点', trigger: 'change'}],
        },

        //批量处理数据
        batchTagFlag: false,
        batchTagForm: {
          pointNum: '',
          tags: [],
          edit:false,
          tip:"添加"
        },
        selectedArray: [],
        batchAddTagTemp: '',
        knowledgeDialog: {
          updateKnowledgeVisible: false,
          title: "添加知识点",
          edit: false,
          index: 0,
          editKnowLedgeId: 0,
          addLabel: {
            id: 0,
            label: ''
          }
        },
        uploadKnowDialog:{
          uploadKnowVisible:false,
          uploadBaseUrl:'',
          uploadTableParams:{companyId:0},
          cookies:true
        },
        fileList:[],
        systemData:'公司数据',
        //---------------------------------按钮权限设置------------------------------
        buttonPermission:{
          updateButtonFlag:true,
          deleteButtonFlag:false
        },
      }
    },
    mounted: function () {
      this.uploadKnowDialog.uploadBaseUrl=this.$http.defaults.baseURL + 'knowledge/importKnowledge';
      this.systemData='公司数据';
      //获取二级目录权限列表
      let buttonList=this.commonFunction.findSecondMenuButtonList(this.$tool.getStorage('SAFE_PLATFORM_MENU').subMenu.manageMenu,'/manage-menu/knowledge-base','/manage-menu/knowledge-base');
      this.buttonPermission.deleteButtonFlag=buttonList.indexOf('deleteKnowledge')>=0;

      this.loadKnowledge(null)
    }
    ,
    methods: {
      //-------------------------标签搜索--------------------------
      remoteTag: function (val) {
        this.search.tagLoading = true
        setTimeout(() => {
          this.search.tagLoading = false
          var params = new URLSearchParams()
          params.append("label", val)
          // params.append("companyId",this.$tool.getStorage('LOGIN_USER').companyId);
          params.append("companyId",this.getCompanyId());
          this.$http.post("label/find", params).then(function (res) {
            if (res.data.success) {
              this.search.tagOptions = res.data.data.list
            } else {
              this.search.tagOptions = [];
            }
          }.bind(this))
        }, 500)
      },
      labelSearchChange: function (label) {
        this.search.searchTag = label.label;
        if(label){
          let index = this.search.tagShowArray.indexOf(label.label);
          if (index<0) {
            this.search.tagShowArray.splice(this.search.tagShowArray.length, 0, label.label);
            this.searchClick();
          }
        }
      },
      searchClick: function () {
        this.loadKnowledge(this.search.tagShowArray)
      },
      //-----------------------添加知识点----------------------
      addKnowDialog: function () {
        this.knowledgeDialog.updateKnowledgeVisible = true
        this.knowledgeDialog.title = "添加知识点"
        this.knowledgeDialog.edit = false
      },
      addKnow: function () {

      },
      //-----------------------标签列表响应事件----------------------
      handleTagClose: function (tag) {
        var index = this.search.tagShowArray.indexOf(tag)
        if (index >= 0) {
          this.search.tagShowArray.splice(index, 1);
          this.searchClick();
        }
      },
      //------------------------批量加删标签-----------------------------
      batchAddTag: function (edit) {
        if (this.batchTagForm.pointNum) {
          this.batchTagFlag = true;
          if(edit){
            this.batchTagForm.tip="删除"
          }else{
            this.batchTagForm.tip="添加"
          }
          this.batchTagForm.edit=edit
        } else {
          this.$message({
            showClose: true,
            message: '请先勾选知识点！',
            type: 'warning'
          });
        }
      },
      handleDeleteTagInArray: function () {
        this.batchTagForm.tags.splice(tag.index, 1);
      },
      handleAddTagInArray: function () {
        this.batchTagForm.tags.push();
      },
      determineBatchAddTag: function () {
        var params = new URLSearchParams()
        params.append("labels", this.knowledgeForm.tags)
        // params.append("companyId",this.$tool.getStorage('LOGIN_USER').companyId);
        params.append("companyId",this.getCompanyId());
        var knowIds=new Array()
        if (this.selectedArray.length) {
          for (var i = 0; i < this.selectedArray.length; i++) {
            knowIds[i]=this.selectedArray[i].id
          }
          params.append("knowIds",knowIds)
        }
        if(this.batchTagForm.edit){
          this.$http.post("knowledge/deleteLabelBatch", params).then(function (res) {
            if(res.data.success){
              this.clearKnowledgeForm()
              this.batchTagFlag=false
              this.loadKnowledge(this.search.tagShowArray)
            }
          }.bind(this)).catch(function (err) {
          }.bind(this))
        }else{
          this.$http.post("knowledge/bindLabelBatch", params).then(function (res) {
            if(res.data.success){
              this.clearKnowledgeForm()
              this.batchTagFlag=false
              this.loadKnowledge(this.search.tagShowArray)
            }
          }.bind(this)).catch(function (err) {
          }.bind(this))
        }
      },
      cancelBatchAddTag: function () {
        this.batchTagFlag = false;
      },
      //------------------------列表条目响应事件--------------------------
      editKnowledgeClick: function (row, index) {
        this.knowledgeForm.content = row.content;
        this.knowledgeForm.tags = row.labels
        this.knowledgeForm.id = row.id
        this.knowledgeDialog.updateKnowledgeVisible = true;
        this.knowledgeDialog.title = "修改知识点"
        this.knowledgeDialog.index = index
        this.knowledgeDialog.edit = true
      },
      deleteTagClick: function (row, index) {
        this.knowledgeDialog.index = index
        this.$confirm('此操作将永久删除该知识点, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          var params = new URLSearchParams()
          params.append("safeKnowId", row.id)
          // params.append("companyId",this.$tool.getStorage('LOGIN_USER').companyId);
          this.$http.post("knowledge/delete", params).then(function (res) {
            if (res.data.success) {
              this.$message.success("删除成功")
              this.knowledgeData.splice(index, 1)
            }
          }.bind(this))
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });
        });
      },
      //多选响应
      handleSelectionChange: function (val) {
        if (val.length) {
          this.batchTagForm.pointNum = val.length;
          this.selectedArray = val;
          //console.info(val)
        }
      },
      //--------------------------页面切换---------------------------------
      currentPageClick: function (val) {
        if (val) {
          this.currentPage=val
          this.loadKnowledge(this.search.tagShowArray)
        }
      },
      //-----------------------对话框响应事件----------------------------
      //不想让两个Tag搜索混在一起，所以分开写
      remoteAddTag: function (val) {
        this.addTagLoading = true
        this.knowledgeDialog.addLabel.label=val
        setTimeout(() => {
          this.addTagLoading = false
          var params = new URLSearchParams()
          params.append("label", val)
          // params.append("companyId",this.$tool.getStorage('LOGIN_USER').companyId);
          params.append("companyId",this.getCompanyId());
          this.$http.post("label/find", params).then(function (res) {
            if (res.data.success) {
              this.addTagOptions = res.data.data.list
            } else {
              this.addTagOptions = [];
            }
          }.bind(this))
        }, 500)
      },
      labelSelectChange: function (label) {
        this.knowledgeDialog.addLabel.id = label.id
        this.knowledgeDialog.addLabel.label = label.label
        this.addTag = label.label
      },
      editAddTag: function () {

      },
      handleTagDelete: function (tag) {
        this.knowledgeForm.tags.splice(this.knowledgeForm.tags.indexOf(tag), 1);
      },
      determineAddTag: function () {
        if(this.knowledgeDialog.addLabel.label){
          if(this.addTagOptions.length===0){//数据库中不存在的标签
            if(this.knowledgeDialog.addLabel.label.length>0){
              var params = new URLSearchParams()
              params.append("label", this.knowledgeDialog.addLabel.label)
              // params.append("companyId",this.$tool.getStorage('LOGIN_USER').companyId);
              params.append("companyId",this.getCompanyId());
              this.$http.post("label/add", params).then(function (res) {
                if (res.data.success) {
                  var label = res.data.data
                  this.$message.success("添加新标签成功！");
                  this.knowledgeForm.tags.splice(this.knowledgeForm.tags.length, 0, label)
                } else {
                  this.$message.error("添加错误")
                }
              }.bind(this)).catch(function (err) {
                this.$message.error(err)
              }.bind(this))
            }
          }
          if (this.knowledgeForm.tags.indexOf(this.knowledgeDialog.addLabel.label) === -1){
            this.knowledgeForm.tags.splice(this.knowledgeForm.tags.length
              , 0, this.knowledgeDialog.addLabel.label)
          }
        }

      },
      cancelUpdate: function () {
        this.clearKnowledgeForm()
      },
      addOrUpdateLabel: function () {
        var params = new URLSearchParams()
        for (var i = 0; i < this.knowledgeForm.tags.length; i++) {
          params.append("labels[" + i + "]", this.knowledgeForm.tags[i])
        }
        params.append("content", this.knowledgeForm.content)
        if (this.knowledgeDialog.edit) {
          params.append("id", this.knowledgeForm.id)
        }
        if (this.knowledgeDialog.edit) {
          this.updateKnowLedge(params)
        } else {
          this.addKnowledge(params)
        }
      },
      addKnowledge: function (params) {
        params.append("companyId",this.getCompanyId());
        this.$http.post("knowledge/add", params).then(function (res) {
          if (res.data.success) {
            this.knowledgeData.splice(this.knowledgeData.length, 0, res.data.data)
            this.knowledgeDialog.updateKnowledgeVisible = false
            this.clearKnowledgeForm()
          }
        }.bind(this))
      },
      updateKnowLedge: function (params) {
        this.$http.post("knowledge/update", params).then(function (res) {
          if (res.data.success) {
            this.knowledgeData.splice(this.knowledgeDialog.index, 1, res.data.data)
            this.knowledgeDialog.updateKnowledgeVisible = false
            this.clearKnowledgeForm()
          }
        }.bind(this))
      },
      loadKnowledge: function (searchLabels) {
        this.$http.post("knowledge/findJson"
          ,
          {labels:searchLabels
            ,pageCurrent:this.currentPage
            ,companyId:this.getCompanyId()}).then(function (res) {
          if (res.data.success) {
            this.knowledgeData = res.data.data.list
            this.totalItem=res.data.data.total
            this.currentPage=res.data.data.pageNum
            for(let i=0;i<this.knowledgeData.length;i++){
              this.knowledgeData[i].num=(this.currentPage-1)*10+i+1
            }
          }
        }.bind(this))
      },
      clearKnowledgeForm: function () {
        this.knowledgeForm.content = '';
        this.knowledgeForm.tags = [];
        this.knowledgeDialog.updateKnowledgeVisible = false;
      },
      beforeUpdateClick:function () {
        this.uploadKnowDialog.uploadTableParams.companyId=this.getCompanyId();
        console.log(this.uploadKnowDialog.uploadTableParams.companyId);
      },
      uploadKnowSuccess:function (res) {
        if(res.success){
          this.$message.success('上传成功！');
          this.uploadKnowDialog.uploadKnowVisible=false;
        }else{
          this.$message.warning('上传失败，请检查网络');
          this.uploadKnowDialog.uploadKnowVisible=false;
        }
      },
      determineUpload:function () {
        this.$refs.uploadKnow.submit();
      },
      downloadKnowTemplate:function () {

        const loading = this.$loading({
          lock: true,
          text: 'Loading',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        this.$http({ // 用axios发送post请求
          method: 'get',
          url: '/knowledge/downloadKnowTemplate/', // 请求地址
          responseType: 'blob' // 表明返回服务器返回的数据类型
        }).then((res) => { // 处理返回的文件流
          //console.info(res)
          loading.close()
          const content = res
          const elink = document.createElement('a') // 创建a标签
          elink.download =   "知识点导入模板.xlsx" // 文件名
          elink.style.display = 'none'
          const blob = new Blob([res.data])
          elink.href = URL.createObjectURL(blob)
          document.body.appendChild(elink)
          elink.click() // 触发点击a标签事件
          document.body.removeChild(elink)
        })
      },
      getCompanyId:function () {
        if(this.systemData==='系统数据'){
          return 0;
        }else{
          return this.$tool.getStorage('LOGIN_USER').companyId;
        }
      },
      reloadData:function () {
        this.loadKnowledge(this.search.tagShowArray)
      }
    }
  }
</script>
<style>
</style>
